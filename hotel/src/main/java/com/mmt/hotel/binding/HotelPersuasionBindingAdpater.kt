package com.mmt.hotel.binding

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.graphics.drawable.GradientDrawable
import android.util.TypedValue
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.webkit.URLUtil
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import com.mmt.core.extensions.secondOrNull
import com.mmt.core.util.DeviceUtil
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.extensions.gone
import com.mmt.data.model.extensions.visible
import com.mmt.hotel.R
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.IconType
import com.mmt.hotel.common.model.response.persuasion.BackgroundGradient
import com.mmt.hotel.common.model.response.persuasion.PersuasionDataModel
import com.mmt.hotel.common.model.response.persuasion.PersuasionStyle
import com.mmt.hotel.common.ui.persuasion.ClickAbleTemplate
import com.mmt.hotel.common.ui.persuasion.PersuasionButtonClickListener
import com.mmt.hotel.common.ui.persuasion.PlaceHolderLayout
import com.mmt.hotel.common.ui.persuasion.TemplateView
import com.mmt.hotel.detail.model.response.BgLinearGradient
import com.mmt.hotel.listingV2.event.SectionOrientation.Companion.HORIZONTAL
import com.mmt.hotel.listingV2.model.response.hotels.TemplatePersuasion
import com.mmt.uikit.MmtTextView
import com.mmt.uikit.binding.BindingAdapters.bindImage
import com.mmt.uikit.binding.BindingAdapters.setBackgroundUrl
import com.mmt.uikit.binding.TextViewBindingAdapters.bindTextHTMLText
import com.mmt.uikit.binding.TextViewBindingAdapters.setTextColorFromHexString
import com.mmt.uikit.binding.setHotelBackgroundDrawable
import com.mmt.uikit.helper.DrawableProvider
import com.mmt.uikit.util.UiMigrationHelper
import com.mmt.uikit.util.isNotNullAndEmpty

/*
*   class for binding HotelPersuasion in hotel listing, room listing etc,
*   currently it contains support for basic persuasion only , extra functionalities can be added in future
* */

private const val TRANSPARENT_COLOR_CODE = "#00000000"

@BindingAdapter(value = ["persuasionDataModel"], requireAll = false)
fun setPersuasion(view: ConstraintLayout, persuasionDataModel: PersuasionDataModel?) {
    if (persuasionDataModel == null) {
        return
    }
    val imageView = view.findViewById<ImageView>(R.id.icon)
    val textView = view.findViewById<MmtTextView>(R.id.text)

    setImageUrl(imageView, persuasionDataModel.imageUrl)

    val style = persuasionDataModel.style
    style?.let {
        setBackground(view, it)
    }

    val isHtmlSupportedText = persuasionDataModel.html
    // if text isHtmlSupportedText we will bind it as html only
    if (isHtmlSupportedText) {
        bindTextHTMLText(textView, persuasionDataModel.persuasionText, 0, true, null)
    } else {
        // otherwise , we will style textView
        style?.let {
            persuasionDataModel.persuasionText?.let { it1 -> setSpannableText(it1, textView, style) }
        }
    }
}

private fun setSpannableText(text: String, textView: MmtTextView, style: PersuasionStyle) {
    val textSize = style.getFontSizeViaResource()
    if (textSize > 0) {
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, ResourceProvider.instance.getDimensionPixelSize(textSize).toFloat())
    }
    textView.text = text
    setTextColorFromHexString(textView, style.textColor)
}

/**
 * View will have default padding is it has any background color applied
 * Default corner radius will be 1
 * horizontal padding will be #{@see R.margin.margin_small}
 * vertical padding will be #{@see R.margin.margin_tiny}
 * */
@BindingAdapter("includeDefaultPaddingFromStyle")
fun includeDefaultPaddingIfStyleHasBackground(view: View, style: PersuasionStyle) {
    if (style.bgColor != null || style.bgGradient != null || style.bgUrl != null || style.borderColor != null || style.borderGradient != null) {
        val resources = view.context.resources
        val verticalSpace = UiMigrationHelper.instance.convertDpToPixel(style.verticalSpace?.toFloat() ?: 4f).toInt()
        val horizontalSpace = UiMigrationHelper.instance.convertDpToPixel(style.horizontalSpace?.toFloat() ?: 8f).toInt()
        if (style.cornerRadii == null) {
            style.cornerRadii = resources.getDimension(R.dimen.margin_1dp).toInt().toString()
        }
        view.setPadding(horizontalSpace, verticalSpace, horizontalSpace, verticalSpace)
    }else{
        view.setPadding(0,0,0,0)
    }
}

@BindingAdapter("backGroundPersuasionStyle")
fun setBackground(view: View, style: PersuasionStyle) {
    val bgGradient = style.bgGradient
    val cornerRadii = style.cornerRadii?.toIntOrNull() ?: 0

    if (style.bgUrl.isNotNullAndEmpty()) {
        if(!URLUtil.isValidUrl(style.bgUrl)){
            when(style.bgUrl){
                IconType.LEFT_BLUE_LINE_PERSUASION_BG -> view.background = ResourceProvider.instance.getDrawable(com.mmt.uikit.R.drawable.blue_persuasion_bg)
            }
            return
        }  else {
            setBackgroundUrl(view, style.bgUrl, null, null, null, true)
        }
    } else if (bgGradient != null) {
        setGradientBackground(view, bgGradient, cornerRadii, style.borderColor, style.borderSize?.toIntOrNull(), style.corners)
    } else if (style.bgColor.isNotNullAndEmpty()) {
        val bgHexCode = style.bgColor

        setHotelBackgroundDrawable(view = view,
                startColorHexCode = bgHexCode,
                endColorHexCode = bgHexCode,
                orientation = GradientDrawable.Orientation.LEFT_RIGHT,
                cornerRadiiInDp = cornerRadii,
                strokeWidthInDp = style.borderSize?.toIntOrNull(),
                strokeColorHexCode = style.borderColor,corners = style.corners)

    } else if (style.borderColor.isNotNullAndEmpty()) {
        setHotelBackgroundDrawable(view = view,
                startColorHexCode = TRANSPARENT_COLOR_CODE,
                endColorHexCode = TRANSPARENT_COLOR_CODE,
                orientation = GradientDrawable.Orientation.LEFT_RIGHT,
                cornerRadiiInDp = cornerRadii,
                strokeWidthInDp = style.borderSize?.toIntOrNull(),
                strokeColorHexCode = style.borderColor,corners = style.corners)
    } else if (style.borderGradient != null) {
        setGradientStrokeDrawable(view = view,
            startColorHexCode = style.borderGradient.start ?: style.borderGradient.color?.firstOrNull(),
            endColorHexCode = style.borderGradient.end ?: style.borderGradient.color?.secondOrNull(),
            angle = style.borderGradient.angle?.toIntOrNull(),
            cornerRadiiInDp = 8,
            strokeWidthInDp = 1
        )
    } else {
        view.setBackgroundResource(0)
    }
}

/*
*   function for setting gradientDrawable
*/
private fun setGradientBackground(view: View, bgGradient: BackgroundGradient, cornerRadiiInDp: Int,
                                  borderColor: String? = null, borderWidth: Int? = null, corners: List<Int>? = null) {
    with(bgGradient) {
        val startColor = start ?: color?.firstOrNull()
        val endColor = end ?: color?.secondOrNull()

        if (startColor == null || endColor == null) {
            return
        }

        val orientation = angle?.toIntOrNull()?.let {
            getGradientOrientation(it)
        } ?: getGradientOrientation(angle)

        setHotelBackgroundDrawable(view = view,
                startColorHexCode = startColor,
                endColorHexCode = endColor,
                orientation = orientation,
                cornerRadiiInDp = cornerRadiiInDp,
                strokeWidthInDp = borderWidth,
                strokeColorHexCode = borderColor,
                corners = corners)
    }
}

private fun getGradientOrientation(angle: String?): GradientDrawable.Orientation {
    return when (angle) {
        HORIZONTAL -> GradientDrawable.Orientation.LEFT_RIGHT
        else -> GradientDrawable.Orientation.TOP_BOTTOM
    }
}

/**
 *  function to get Drawable orientation
 */
 fun getGradientOrientation(angle: Int): GradientDrawable.Orientation = try {
    when (angle) {
        in 0..45 -> {
            GradientDrawable.Orientation.LEFT_RIGHT
        }
        in 46..90 -> {
            GradientDrawable.Orientation.BL_TR
        }
        in 91..145 -> {
            GradientDrawable.Orientation.BOTTOM_TOP
        }
        in 146..190 -> {
            GradientDrawable.Orientation.TL_BR
        }
        in 191..235 -> {
            GradientDrawable.Orientation.RIGHT_LEFT
        }
        in 236..280 -> {
            GradientDrawable.Orientation.TR_BL
        }
        in 281..325 -> {
            GradientDrawable.Orientation.TOP_BOTTOM
        }
        in 326..360 -> {
            GradientDrawable.Orientation.TR_BL
        }
        else -> {
            GradientDrawable.Orientation.LEFT_RIGHT
        }
    }
} catch (e: Exception) {
    GradientDrawable.Orientation.LEFT_RIGHT
}

fun ImageView.bindImageIconTypeOrUrl(icon: String){
    if (URLUtil.isValidUrl(icon)){
        setImageUrl(this,icon)
    }else{
       setImageResource(DrawableProvider.getDrawableResource(icon))
    }
}

/*
*   this function setImageUrl, incase, imageUrl is null or empty , imageVisibilty is set to GONE
* */
fun setImageUrl(imageView: ImageView, imageUrl: String?) {
    if (imageUrl.isNotNullAndEmpty()) {
        bindImage(imageView, imageUrl, 0, null, null, null, null, null, 0, false, false, true, true)
    } else {
        imageView.visibility = View.GONE
    }

}

@BindingAdapter(value = ["setPersuasion", "templatesUnSupported","funnelType"], requireAll = false)
fun setPersuasion(viewGroup: PlaceHolderLayout, persuasion: TemplatePersuasion?, templatesUnSupported: List<String>?, funnelType: FunnelType? = FunnelType.HOTEL_FUNNEL) {
    viewGroup.setUnSupportedTemplates(templatesUnSupported)
    viewGroup.isVisible = persuasion != null
    persuasion?.let { viewGroup.setPersuasion(it,funnelType) }
}

@BindingAdapter("persuasionClickListener")
fun setPersuasionClickListener(viewGroup: PlaceHolderLayout, listener: PersuasionButtonClickListener) {
    viewGroup.setOnClickListener(listener)
}

@BindingAdapter(value = ["bgImageUrl", "bgColor", "bgGradient"],requireAll = false)
fun applyBackgroundFromAny(view: View, bgImageUrl: String?, bgColor: String?, bgGradient: BgLinearGradient?) {
    val gradient = bgGradient?.let {
        val direction = (it.direction?.firstOrNull()?.toUpperCase() ?: HORIZONTAL).toString()
        BackgroundGradient(start = it.start,end = it.end,angle = direction) }
    val style = PersuasionStyle(bgUrl = bgImageUrl,bgColor = bgColor,bgGradient = gradient)
    setBackground(view,style)
}

@BindingAdapter("bindSpecificPersuasion")
fun bindSpecificPersuasion(view: View, persuasion: PersuasionDataModel?) {
    persuasion?.let {
        (view as? TemplateView)?.bind(listOf(it))
            (view as? TemplateView)?.applyStyle(it.style)
    }
}

@BindingAdapter("bindSpecificPersuasionV2")
fun bindSpecificPersuasionV2(view: View, persuasion: PersuasionDataModel?) {
    persuasion?.let {
        (view as? TemplateView)?.bind(listOf(it))
    }
}

@BindingAdapter("specificPersuasionButtonClickListener")
fun setSpecificPersuasionButtonClickListener(clickAbleTemplate: View, listener: PersuasionButtonClickListener) {
    if(clickAbleTemplate is ClickAbleTemplate) {
        clickAbleTemplate.setButtonClickListener(listener)
    }
}

@BindingAdapter("persuasionVisibility")
fun persuasionVisibility(viewGroup: PlaceHolderLayout, persuasionVisibility: Boolean) {
    viewGroup.templateVisible = persuasionVisibility
}



