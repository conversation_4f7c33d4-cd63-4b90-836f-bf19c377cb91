package com.mmt.hotel.binding

import android.graphics.Typeface
import android.os.Build
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.mmt.hotel.R
import kotlin.properties.Delegates


/**
 * must use [MATCH_PARENT] for layout_width,
 * as textview width is used to calculate the text that it can fit
 *
 * Helper class to help in restricting text to given number of lines and implement the functionality to expand/collapse the text
 * when clicked on the text itself
 *
 * create by <PERSON><PERSON><PERSON> on 06/05/21
 */
class TextResizingHelper {
    companion object {
        private const val ELLIPSE_TEXT = "... "
        private const val EMPTY_STRING = ""
        private const val DOUBLE_SPACE = "  "
    }

    private var shouldBeExpand = false
    private lateinit var originalText : CharSequence
    private var collapseTextToLines : Int = 2
    private var boldStartIndex : Int = 0
    private var boldEndIndex : Int = 0
    private var colorClickableText by Delegates.notNull<Int>()
    private var isTextResizingApplicable = false
    private lateinit var seeMoreText : String
    private lateinit var seeLessText : String
    private var blockSpanClick = false
    private var shouldBindAsPlainText = false
    private var onClickListener: View.OnClickListener? = null

    fun resizeText(view: TextView, originalText: CharSequence, shouldBindAsPlainText: Boolean = false, resizeLinesTo: Int, onClickListener: View.OnClickListener?,
                   seeMoreTextId: Int = 0, seeLessTextId: Int = 0,
                   shouldExpandByDefault: Boolean = false, blockSpanClick: Boolean = false,
                   boldStartIndex: Int = 0, boldEndIndex: Int = 0) {
        this.originalText = originalText
        this.shouldBindAsPlainText = shouldBindAsPlainText
        this.collapseTextToLines = resizeLinesTo
        this.shouldBeExpand = shouldExpandByDefault
        if (boldStartIndex > 0) {
            this.boldStartIndex = boldStartIndex
        }
        if (boldEndIndex > 0) {
            this.boldEndIndex= boldEndIndex
        }
        this.onClickListener = onClickListener
        this.blockSpanClick = blockSpanClick

        val res = view.context.resources
        this.seeMoreText = res.getString( if (seeMoreTextId > 0) seeMoreTextId else R.string.htl_label_read_more)
        this.seeLessText = res.getString( if (seeLessTextId > 0) seeLessTextId else R.string.htl_label_read_less)

        colorClickableText = ContextCompat.getColor(view.context, R.color.color_008cff)
        view.setOnClickListener(clickListener)

        renderText(view)
    }

    private fun renderText(view: TextView) {
        view.maxLines = collapseTextToLines
        view.ellipsize = TextUtils.TruncateAt.END
        view.post {
            val ellipsizedText = getEllipsisText(view, originalText, collapseTextToLines)
            ellipsizedText?.let {
                // text is spilling over the defined max allowed lines
                isTextResizingApplicable = true
                if (shouldBeExpand) { // if we need to expand byDefault
                    expandText(view)
                } else {
                    // text is going beyond the restricted number of lines, truncate the text to defined number of lines
                    truncateTextToLineEndIndex(view, it.length)
                }
            } ?: run {
                isTextResizingApplicable = false
                expandText(view)
            }
        }
    }

    private fun expandText(view: TextView) {
        view.maxLines = Int.MAX_VALUE
        val ssb = SpannableStringBuilder(originalText)
        if (isTextResizingApplicable) {
            ssb.append(DOUBLE_SPACE).append(seeLessText)
            addColorClickableSpan(ssb, seeLessText)
        }
        view.text = ssb.apply {
            if (boldEndIndex > boldStartIndex) {
                addBoldSpan(this, boldStartIndex, boldEndIndex)
            }
        }
    }

    private fun collapseText(view: TextView) {
        view.maxLines = collapseTextToLines
        view.ellipsize = TextUtils.TruncateAt.END
        view.post {
            val ellipsizedText = getEllipsisText(view, originalText, collapseTextToLines)
            ellipsizedText?.let {
                if (it != originalText) {
                    // text is going beyond the restricted number of lines, truncate the text to defined number of lines
                    truncateTextToLineEndIndex(view, it.length)
                }
            } ?: run {
                // text lies within the desired number of line, hence no need to truncate the text
                // see if we need to bold the text if requested
                if (boldEndIndex > boldStartIndex) {
                    view.text = SpannableStringBuilder(originalText).apply {
                        addBoldSpan(this, boldStartIndex, boldEndIndex)
                    }
                }
            }
        }
    }

    private fun truncateTextToLineEndIndex(view : TextView, maxLinesEndIndexCount : Int) {
        var linesEndIndex = maxLinesEndIndexCount

        val textPrefix : String = if (boldStartIndex in 1 until boldEndIndex) {
            ELLIPSE_TEXT
        } else {
            EMPTY_STRING
        }

        linesEndIndex = (linesEndIndex + boldStartIndex).coerceAtMost(originalText.length)
        val lineStartIndex = (view.layout.getLineStart(collapseTextToLines - 1) + boldStartIndex).coerceAtMost(linesEndIndex)

        linesEndIndex -= computeCharsToRemoveToAccomodateSeeMoreText(view, textPrefix, lineStartIndex, linesEndIndex)

        val ellipsizedTextSsb = SpannableStringBuilder(textPrefix)
                .append(originalText.subSequence(limitIndexToLength(boldStartIndex, originalText.length - 1), limitIndexToLength(linesEndIndex, originalText.length)))
                .append(ELLIPSE_TEXT)
                .append(seeMoreText)


        val offset = textPrefix.length
        val relativeStartIndex = (if (boldStartIndex > linesEndIndex) linesEndIndex else boldStartIndex) - boldStartIndex + offset
        val relativeEndIndex = (if (boldEndIndex > linesEndIndex) linesEndIndex else boldEndIndex) - boldStartIndex + offset
        view.text = ellipsizedTextSsb.apply {
            addColorClickableSpan(this, seeMoreText)
            if (relativeEndIndex > relativeStartIndex) {
                addBoldSpan(this, relativeStartIndex, relativeEndIndex)
            }
        }
    }

    /**
     * function to compute the number of characters to be removed from end so as to fit "... View More" text at end
     */
    private fun computeCharsToRemoveToAccomodateSeeMoreText(view: TextView, textPrefix: String, lineStartIndex: Int, linesEndIndex: Int): Int {
        var count = 0
        while (true) {
            if (lineStartIndex >= linesEndIndex - count) break
            val requiredText = textPrefix + originalText.subSequence(lineStartIndex, linesEndIndex - count) + ELLIPSE_TEXT + seeMoreText
            val textWidth = view.paint.measureText(requiredText)
            if (textWidth < view.measuredWidth) {
                break
            }
            count++
        }
        return count
    }

    private fun getEllipsisText(textView: TextView?, text: CharSequence, resizeLine: Int): CharSequence? {
        try {
            return textView?.layout?.let {
                val end = it.text.length.coerceAtMost(it.getLineEnd(resizeLine - 1) - it.getEllipsisCount(resizeLine - 1))
                return if (end > 0 && end < text.length) {
                    text.subSequence(0, end)
                } else {
                    null
                }
            }
        }
        catch (e: Exception){
            return null
        }
    }

    private fun addColorClickableSpan(ssb : SpannableStringBuilder/*, colorClickableText : Int*/, clickableSpanText : String)  {
        ssb.setSpan(ForegroundColorSpan(colorClickableText), ssb.length - clickableSpanText.length, ssb.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun addBoldSpan(ssb: SpannableStringBuilder, startIndex : Int, endIndex : Int) {
        ssb.setSpan(StyleSpan(Typeface.BOLD), limitIndexToLength(startIndex, ssb.length-1), limitIndexToLength(endIndex, ssb.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    private fun limitIndexToLength(index: Int, maxLenth: Int): Int {
        return if (index < 0) {
            0
        } else if (index > maxLenth) {
            maxLenth
        } else index
    }

    private val clickListener = View.OnClickListener {
        if (isTextResizingApplicable && !blockSpanClick) {
            shouldBeExpand = !shouldBeExpand
            if (it is TextView) {
                if (shouldBeExpand) {
                    expandText(it)
                } else {
                    collapseText(it)
                }
            }
        }
        onClickListener?.onClick(it)
    }
}


