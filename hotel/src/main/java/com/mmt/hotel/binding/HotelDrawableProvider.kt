package com.mmt.hotel.binding

import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.uikit.util.isNotNullAndEmpty

/**
 * Created by <PERSON><PERSON><PERSON> on 06/01/21.
 */
object HotelDrawableProvider {

    const val MMT_BLACK_TICK_ICON = "BLACK"
    const val DOUBLE_GREEN_TICK_ICON = "d_g_tick"
    const val DOUBLETICK_ICON = "DOUBLETICK"
    const val RED_CROSS_ICON = "CROSS"
    const val BIG_CROSS = "BIGCROSS"
    const val GREEN_TICK_ICON = "g_tick"
    const val GREEN_TICK_ICON_V2 = "GreenTickIcon"
    const val LIGHTNING_ICON = "lightning_icon"
    const val LIGHTNING_ICON_V2 = "lightning_icon_v2"
    const val ICON_LIGHTNING = "LIGHTNING"
    const val SINGLE_TICK_ICON = "SINGLETICK"
    const val WHITE_CROSS = "WHITE_CROSS"


    @DrawableRes
    fun getDrawableResource(iconType: String?): Int {
        return when (iconType) {
            MMT_BLACK_TICK_ICON, SINGLE_TICK_ICON -> R.drawable.ic_htl_ic_blacktick_with_padding
            GREEN_TICK_ICON, GREEN_TICK_ICON_V2 -> R.drawable.ic_htl_ic_green_tick_with_padding
            DOUBLE_GREEN_TICK_ICON, DOUBLETICK_ICON -> R.drawable.ic_green_double_tick_with_padding
            RED_CROSS_ICON, BIG_CROSS -> R.drawable.ic_htl_ic_redcross_with_padding_v2
            LIGHTNING_ICON -> R.drawable.ic_lighting_icon
            LIGHTNING_ICON_V2 -> R.drawable.ic_lightning_v2
            ICON_LIGHTNING -> R.drawable.ic_lightning
            WHITE_CROSS -> R.drawable.ic_cross
            else -> R.drawable.ic_htl_ic_bullet_with_padding
        }
    }


    @DimenRes
    fun getDrawablePadding(iconType: String?): Int {
        return when (iconType) {
            LIGHTNING_ICON -> ResourceProvider.instance.getDimensionPixelSize(
                R.dimen.margin_small
            )
            else -> ResourceProvider.instance.getDimensionPixelSize(R.dimen.htl_empty_dimen)
        }
    }
}