package com.mmt.hotel.binding

import androidx.databinding.BindingAdapter
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.HotelBaseRecyclerAdapter
import com.mmt.hotel.widget.CollapsingHeaderMotionLayout

/**
 * Created by <PERSON><PERSON><PERSON> on 14/09/20.
 */

@BindingAdapter(value = ["itemsList", "adapter"], requireAll = false)
fun setGenericAdapterList(header: CollapsingHeaderMotionLayout, itemsList: List<AbstractRecyclerItem>?, adapter: HotelBaseRecyclerAdapter) {
    if (header.getAdapter() == null) header.setAdapter(adapter)
    adapter.updateList(itemsList ?: emptyList(), false)
}

@BindingAdapter(value = ["expandItemsList", "expandAdapter"], requireAll = false)
fun setExpandAdapterList(
    header: CollapsingHeaderMotionLayout, expandItemsList: List<AbstractRecyclerItem>?,
    expandAdapter: HotelBaseRecyclerAdapter
) {
    if (header.getExpandAdapter() == null) header.setExpandAdapter(expandAdapter)
    expandAdapter.updateList(expandItemsList ?: emptyList(), false)
}

@BindingAdapter(value = ["collapsedItemsList", "collapsedAdapter"], requireAll = false)
fun setCollapsedAdapterList(
    header: CollapsingHeaderMotionLayout, collapsedItemsList: List<AbstractRecyclerItem>?,
    collapsedAdapter: HotelBaseRecyclerAdapter
) {
    if (header.getCollapsedAdapter() == null) header.setCollapsedAdapter(collapsedAdapter)
    collapsedAdapter.updateList(collapsedItemsList ?: emptyList(), false)
}