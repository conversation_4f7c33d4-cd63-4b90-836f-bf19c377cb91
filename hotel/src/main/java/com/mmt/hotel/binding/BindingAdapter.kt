package com.mmt.hotel.binding

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.LayerDrawable
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RatingBar
import android.widget.TextView
import androidx.annotation.*
import androidx.core.content.ContextCompat
import androidx.core.view.isInvisible
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.AltAccoLandingFragmentV2
import com.mmt.hotel.common.extensions.addViewWithDataBinding
import com.gommt.logger.LogUtils
import com.mmt.uikit.binding.CornerRadiusHolder
import com.mmt.uikit.binding.TAG
import com.mmt.uikit.util.UiMigrationHelper
import com.mmt.uikit.util.UiUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by sunil.jain on 2019-09-27.
 */



/** This function reuse already created layout if exits or create a new one to bind data
 * This will be useful when layout in which view are inflated used in recycle view or
 * your layout will be updated in future with new values
 * */

@BindingAdapter(value = ["reusedList", "layoutId", "reuseLayout"], requireAll = true)
fun createOrReuseLayout(viewGroup: ViewGroup, reusedList: List<Any>?, @LayoutRes layoutId: Int, reuseLayout: Boolean) {

    with(viewGroup) {
        val inflater = LayoutInflater.from(context)
        // Remove all if invalid view exits (if different layout than already exits)
        if (childCount > 0 && getChildAt(0).getTag(R.id.tagName) != layoutId || !reuseLayout || reusedList.isNullOrEmpty()) {
            removeAllViews()
        }

        reusedList?.withIndex()?.forEach {
            val binding = if (childCount > it.index) {
                getChildAt(it.index).getTag(R.id.tagView) as ViewDataBinding
            } else {
                val binding: ViewDataBinding = DataBindingUtil.inflate(inflater, layoutId, this, false)
                viewGroup.addView(binding.root)
                binding.root.setTag(R.id.tagView, binding)
                binding.root.setTag(R.id.tagName,layoutId)
                binding
            }
            binding.setVariable(BR.model, it.value)
            binding.executePendingBindings()
        }

        val reusedListSize = reusedList?.size ?: childCount
        (reusedListSize until childCount).forEach {
            viewGroup.removeView(getChildAt(it))
        }
    }
}
/** This function will inflate only one view in frameLayout if no other view is exits
 * or ignore if same view is already exits. If layout changed to be add in frameLayout
 * previous one will remove and new one added
 * */
@BindingAdapter(value = ["addSingleViewIfNotExits", "model"], requireAll = false)
fun addView(frameLayout: FrameLayout, @LayoutRes addSingleViewIfNotExits: Int?, model: Any?) {
    with(frameLayout) {
        when {
            addSingleViewIfNotExits == null || addSingleViewIfNotExits == 0 -> removeAllViews()
            childCount == 0 -> addViewAndBindTag(addSingleViewIfNotExits, model)
            getChildAt(0).tag != addSingleViewIfNotExits -> {
                removeAllViews()
                addViewAndBindTag(addSingleViewIfNotExits, model)
            }
            else -> {
                val childBinding: ViewDataBinding? = DataBindingUtil.bind(getChildAt(0))
                childBinding?.setVariable(BR.model, model)
            }
        }
    }
}

private fun FrameLayout.addViewAndBindTag(addViewIfNotExits: Int, model: Any?) {
    addViewWithDataBinding(addViewIfNotExits).also { it.root.tag = addViewIfNotExits; it.setVariable(BR.model, model) }
}

@BindingAdapter(value = ["bgColor","cornerRadiusHolder"])
fun bindRoundDrawable(view: View,  bgColor: String?,cornerRadiusHolder: CornerRadiusHolder) {
    val drawable = GradientDrawable()
    drawable.setColor(Color.parseColor(bgColor ?: "#ffffff"))

    val radii = floatArrayOf(
        cornerRadiusHolder.topLeft,
        cornerRadiusHolder.topLeft,
        cornerRadiusHolder.topRight,
        cornerRadiusHolder.topRight,
        cornerRadiusHolder.bottomRight,
        cornerRadiusHolder.bottomRight,
        cornerRadiusHolder.bottomLeft,
        cornerRadiusHolder.bottomLeft
    )
    drawable.cornerRadii = radii
    view.background = drawable
}

@BindingAdapter(value = ["bgColor", "cornerRadii", "strokeWidthInDp", "strokeColor", "dashWidth", "dashGap", "drawBorder", "shape"])
fun setDashedBorder(
    view: View, @ColorRes bgColor: Int, @DimenRes cornerRadiiId: Int,
    strokeWidthInDp: Int, @ColorRes strokeColor: Int?,
    @DimenRes dashWidth: Int, @DimenRes dashGap: Int, drawBorder: Boolean, shape: Int?
) {
    CoroutineScope(Dispatchers.IO).launch {
        try {
            val bgDrawable = GradientDrawable()
            val resourceProvider = ResourceProvider.instance
            bgDrawable.setColor(resourceProvider.getColor(bgColor))
            val cornerRadius = resourceProvider.getDimension(cornerRadiiId)
            val radiiArray = floatArrayOf(
                cornerRadius, cornerRadius, cornerRadius, cornerRadius,
                cornerRadius, cornerRadius, cornerRadius, cornerRadius
            )
            bgDrawable.cornerRadii = radiiArray
            if (drawBorder && strokeColor != null) {
                bgDrawable.setStroke(
                    UiMigrationHelper.instance.convertDpToPixel(strokeWidthInDp.toFloat()).toInt(),
                    resourceProvider.getColor(strokeColor),
                    resourceProvider.getDimension(dashWidth),
                    resourceProvider.getDimension(dashGap)
                )
            }
            bgDrawable.shape = shape ?: 0 // RECTANGLE = 0,  OVAL = 1, LINE = 2, RING = 3
            withContext(Dispatchers.Main) {
                view.background = bgDrawable
            }
        } catch (e: Exception) {
            LogUtils.error(TAG, e.toString())
        }
    }
}

@BindingAdapter(value = ["htlAlignBelowView", "htlIsVisible"])
fun alignBelowParent(view: View, anchorView: View, isVisible: Boolean) {
    if (!isVisible) {
        view.isInvisible = !isVisible
        return
    }
    anchorView.post {
        view.y = anchorView.y + anchorView.height
        view.x = if (anchorView.x + view.width < (view.parent as ViewGroup).width) {
            anchorView.x
        } else {
            anchorView.x + anchorView.width - view.width
        }
        view.isInvisible = !isVisible
    }
}

@BindingAdapter(value = ["changeBackGround","backgroundColorRes"])
fun setBackGround(view: View, @DrawableRes resourceId: Int,@ColorRes colorRes: Int) {
    if (resourceId == Resources.ID_NULL || resourceId == -1) {
        view.background = null
        return
    }
    val drawable = ContextCompat.getDrawable(view.context,resourceId)?.mutate()
    if (colorRes != 0) {
        drawable?.colorFilter = PorterDuffColorFilter(ResourceProvider.instance.getColor(colorRes), PorterDuff.Mode.SRC_IN)
    }
    view.background = drawable
}

@BindingAdapter(value = ["htlChangeBackGround","htlBgColor"])
fun setBackGroundAndColor(view: View, @DrawableRes resourceId: Int,@ColorInt color: Int) {
    if (resourceId == Resources.ID_NULL || resourceId == -1) {
        view.background = null
        return
    }
    val drawable = ContextCompat.getDrawable(view.context,resourceId)?.mutate()
    if (color != 0) {
        drawable?.colorFilter = PorterDuffColorFilter(color, PorterDuff.Mode.SRC_IN)
    }
    view.background = drawable
}

@BindingAdapter(value = ["numStars", "rating"], requireAll = true)
fun setNumStarAndRating(view: RatingBar, numStar:Int, rating:Float){
    view.numStars = numStar
    view.rating = rating
}

private fun getSmoothScroller(context: Context, duration: Float): RecyclerView.SmoothScroller? {
    //smooth scroll for scrolling RV to a specific item to top of the recycler view
    return object : LinearSmoothScroller(context) {
        override fun getVerticalSnapPreference(): Int {
            return SNAP_TO_START
        }

        override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
            return duration / displayMetrics.densityDpi
        }
    }
}

private fun scrollToPosition(smoothScroller: RecyclerView.SmoothScroller?, pos: Int, linearLayoutManager: LinearLayoutManager?) {
    smoothScroller?.targetPosition = pos
    if (linearLayoutManager != null && smoothScroller != null) {
        linearLayoutManager.startSmoothScroll(smoothScroller)
    }
}
fun htlScrollRecyclerViewOnce(recyclerView: RecyclerView?, towardsLeft: Boolean = false) {
    val context = recyclerView?.context
    if (context != null) {
        (recyclerView.layoutManager as? LinearLayoutManager)?.let { linearLayoutManager ->
            var pos = 0
            try {
                var smoothScroller = getSmoothScroller(context, 25f)
                val totalRecyclerViewItems = recyclerView.adapter?.itemCount ?: 0
                val lastCompletelyVisibleItemPosition = linearLayoutManager.findLastCompletelyVisibleItemPosition()
                if (lastCompletelyVisibleItemPosition == RecyclerView.NO_POSITION) {
                    val lastPartiallyVisibleItemPosition = linearLayoutManager.findLastVisibleItemPosition()
                    if (lastPartiallyVisibleItemPosition != RecyclerView.NO_POSITION && lastPartiallyVisibleItemPosition <= totalRecyclerViewItems) {
                        pos = lastPartiallyVisibleItemPosition
                        smoothScroller = getSmoothScroller(context, 100f)
                    }
                } else if (lastCompletelyVisibleItemPosition < totalRecyclerViewItems - 1) {
                    if (towardsLeft) {
                        pos = lastCompletelyVisibleItemPosition - 1
                        if (pos < 0) {
                            return
                        }
                    } else {
                        pos = lastCompletelyVisibleItemPosition + 1
                    }
                    smoothScroller = getSmoothScroller(context, 100f)
                }
                scrollToPosition(smoothScroller, pos, linearLayoutManager)
            } catch (e: Exception) {
                LogUtils.error(AltAccoLandingFragmentV2.TAG, e)
            }
        }

    }
}
@BindingAdapter("blurView")
fun addBlurView(blurView :com.mmt.uikit.custom.BlurringView, anchorView:View) {
    blurView.setBlurredView(anchorView)
}

@SuppressLint("CheckResult")
@BindingAdapter(value = ["startColorHexCode", "centerColorHexCode","endColorHexCode", "orientation", "cornerRadiiInDp", "strokeWidthInDp", "strokeColorHexCode","allCorners"], requireAll = false)
fun setHotelBackgroundDrawable(view: View, startColorHexCode: String?, centerColorHexCode: String?, endColorHexCode: String?, orientation: GradientDrawable.Orientation?,
                               cornerRadiiInDp: Int?, strokeWidthInDp: Int?, strokeColorHexCode: String?,corners: List<Int>? = null) {

    val bgOrientation = orientation ?: GradientDrawable.Orientation.LEFT_RIGHT
    val gradientDrawable = if (UiUtils.isColorCodeValidHex(startColorHexCode) && UiUtils.isColorCodeValidHex(endColorHexCode)) {
        val startColorCode: Int = Color.parseColor(startColorHexCode)
        val endColorCode: Int = Color.parseColor(endColorHexCode)
        val colors = if(UiUtils.isColorCodeValidHex(centerColorHexCode)){
            val centerColorCode = Color.parseColor(centerColorHexCode)
            intArrayOf(startColorCode, centerColorCode, endColorCode)
        }else{
            intArrayOf(startColorCode, endColorCode)
        }
        GradientDrawable(bgOrientation, colors)
    } else {
        val colorCode: Int = Color.parseColor("#ffffff")
        val colors = intArrayOf(colorCode, colorCode)
        GradientDrawable(bgOrientation, colors)
    }

    if (corners.isNullOrEmpty()) {
        val cornerRadius = UiMigrationHelper.instance.convertDpToPixel(cornerRadiiInDp?.toFloat() ?: 0F)
        val radiiArray = floatArrayOf(cornerRadius, cornerRadius, cornerRadius, cornerRadius,
            cornerRadius, cornerRadius, cornerRadius, cornerRadius)
        gradientDrawable.cornerRadii = radiiArray
    } else if (corners.size >= 4) {
        val radiiArray = floatArrayOf(corners[0].toFloat(), corners[0].toFloat(), corners[1].toFloat(), corners[1].toFloat(),
            corners[2].toFloat(), corners[2].toFloat(), corners[3].toFloat(), corners[3].toFloat())
        gradientDrawable.cornerRadii = radiiArray
    }

    if (UiUtils.isColorCodeValidHex(strokeColorHexCode)) {
        val strokeColorId: Int = Color.parseColor(strokeColorHexCode)
        gradientDrawable.setStroke(UiMigrationHelper.instance.convertDpToPixel(strokeWidthInDp?.toFloat()
            ?: 1F).toInt(), strokeColorId)
    }

    if (view != null) { // don't remove this null check
        view.background = gradientDrawable
    }
}

@SuppressLint("CheckResult")
@BindingAdapter(value = ["borderStartColor", "borderEndColor","borderGradientAngle", "cornerRadiiInDp", "strokeWidthInDp"], requireAll = false)
fun setGradientStrokeDrawable(view: View, startColorHexCode: String?, endColorHexCode: String?, angle : Int?,
                               cornerRadiiInDp: Int?, strokeWidthInDp: Int?) {

    val cornerRadius = UiMigrationHelper.instance.convertDpToPixel(cornerRadiiInDp?.toFloat() ?: 0F)
    val borderWidth = UiMigrationHelper.instance.convertDpToPixel(strokeWidthInDp?.toFloat() ?: 1F).toInt()
    val bgOrientation = angle?.let { getGradientOrientation(it)} ?: GradientDrawable.Orientation.LEFT_RIGHT
    val radiiArray = floatArrayOf(cornerRadius, cornerRadius, cornerRadius, cornerRadius, cornerRadius, cornerRadius, cornerRadius, cornerRadius)

    val innerRadius = if (cornerRadius > borderWidth) {
        cornerRadius - borderWidth
    } else {
        cornerRadius
    }

    val innerRadiiArray = floatArrayOf(innerRadius, innerRadius, innerRadius, innerRadius, innerRadius, innerRadius, innerRadius, innerRadius)

    val defaultColors = intArrayOf(Color.parseColor("#ffffff"), Color.parseColor("#ffffff"))
    val colors = if (UiUtils.isColorCodeValidHex(startColorHexCode) && UiUtils.isColorCodeValidHex(endColorHexCode)) {
        intArrayOf(Color.parseColor(startColorHexCode), Color.parseColor(endColorHexCode))
    }else defaultColors

    val backgroundDrawable = GradientDrawable(bgOrientation, defaultColors)
    backgroundDrawable.cornerRadii = innerRadiiArray

    val gradientDrawable = GradientDrawable(bgOrientation, colors)
    gradientDrawable.cornerRadii = radiiArray

    val layers = arrayOf<Drawable>(gradientDrawable, backgroundDrawable)
    val layerDrawable = LayerDrawable(layers)

    layerDrawable.setLayerInset(1, borderWidth, borderWidth, borderWidth, borderWidth)

    if (view != null) { // don't remove this null check
        view.background = layerDrawable
    }
}

@BindingAdapter("setGravity")
fun setGravity(view: View, gravity: String) {
    val params = view.layoutParams as LinearLayout.LayoutParams
    params.gravity = when (gravity) {
        "center" -> android.view.Gravity.CENTER
        "center_horizontal" -> android.view.Gravity.CENTER_HORIZONTAL
        "center_vertical" -> android.view.Gravity.CENTER_VERTICAL
        "start" -> android.view.Gravity.START
        "end" -> android.view.Gravity.END
        "top" -> android.view.Gravity.TOP
        "bottom" -> android.view.Gravity.BOTTOM
        else -> params.gravity
    }
    view.layoutParams = params
}

@BindingAdapter("premiumStyle")
fun setPremiumStyle(view: TextView, isPremium: Boolean) {
    val styleRes = if (isPremium) {
        R.style.premium_funnel_italic_text
    } else {
        R.style.listing_section_subheader
    }
    view.setTextAppearance(view.context, styleRes)
}