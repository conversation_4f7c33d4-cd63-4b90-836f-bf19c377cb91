package com.mmt.hotel.binding

import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.transition.Slide
import android.transition.Transition
import android.transition.TransitionManager
import android.view.View
import android.view.ViewGroup
import android.webkit.URLUtil
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import androidx.annotation.LayoutRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.asynclayoutinflater.view.AsyncLayoutInflater
import androidx.compose.ui.platform.ComposeView
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import coil.Coil
import com.bumptech.glide.Glide
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.target.ImageViewTarget
import com.mmt.auth.login.model.Employee
import com.mmt.auth.login.widget.BookingForWidget
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.isNotNullAndEmpty
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.data.model.homepage.empeiria.cards.HeaderData
import com.mmt.data.model.homepage.empeiria.cards.Style
import com.mmt.data.model.widget.HomeCardTopWidget
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.constants.ExperimentsHotel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.ImageWidthAllocator
import com.mmt.hotel.selectRoom.compose.AddOnDetailsVM
import com.mmt.hotel.selectRoom.compose.FlexiCancelAddonItem
import com.mmt.hotel.selectRoom.event.RecommendedComboEvents
import com.mmt.hotel.selectRoom.model.response.AddOnDetails
import com.mmt.hotel.shortStays.model.DirectionsUiData
import com.mmt.hotel.widget.ExpandableViewGroup
import com.mmt.uikit.binding.BindingAdapters
import com.mmt.uikit.binding.BindingAdapters.bindImage
import com.mmt.uikit.binding.CornerRadiusHolder
import com.mmt.uikit.binding.hotelsBindImageV2
import com.mmt.uikit.helper.DrawableProvider
import com.mmt.uikit.util.ImageLoader
import com.mmt.uikit.util.UiConstants.GENERIC_PICASSO_TAG
import com.mmt.uikit.util.UiMigrationHelper
import com.mmt.uikit.util.UiMigrationHelper.Companion.instance
import com.mmt.uikit.util.UiUtils.getIntFromString
import com.mmt.uikit.util.isValidContextForGlide
import com.mmt.uikit.util.setDefaults
import com.mmt.uikit.views.RoundedImageView
import com.squareup.picasso.Callback
import com.squareup.picasso.MemoryPolicy
import com.squareup.picasso.Picasso
import com.squareup.picasso.Transformation

@BindingAdapter(value = ["widgetType", "travellerCount", "primaryTraveller"], requireAll = false)
fun updateBookingWidgetData(view: BookingForWidget, widgetType: Int, travellerCount: Int?, primaryTraveller: Employee?) {
    if (view.tag as? Int != widgetType) {
        view.init(view.context as AppCompatActivity, travellerCount ?: 1, widgetType)
        view.tag = widgetType
    }
    view.updateTravellersCount(travellerCount ?: 1)
    primaryTraveller?.let { view.updateData(it) }
}

@BindingAdapter(
    value = ["htlBindText", "htlStartBoldIndex", "htlEndBoldIndex", "htlShouldBeExpanded", "htlBlockToggle"],
    requireAll = false
)
fun setupExpandableViewGroup(
    view: ExpandableViewGroup,
    bindText: String?,
    startBoldIndex: Int = -1,
    endBoldIndex: Int = -1,
    shouldBeExpanded: Boolean = false,
    blockToggle: Boolean = false
) {
    bindText?.let {
        view.bind(it, startBoldIndex, endBoldIndex, shouldBeExpanded, blockToggle)
    }
}

@BindingAdapter("scrollToPosition")
fun scrollToPosition(viewGroup:RecyclerView,index:Int) {
    if(index == -1 ) return
    viewGroup.smoothScrollToPosition(index)
}

@BindingAdapter("fastScrollToPosition")
fun fastScrollToPosition(viewGroup:RecyclerView,index:Int) {
    if(index == -1 ) return
    viewGroup.scrollToPosition(index)
}

@BindingAdapter(value = ["htlAnimatedWebp", "htlPlaceHolder", "htlFallbackImage","htlDefaultImage"], requireAll = false)
fun animatedWebP(view: ImageView, url: String?, placeholder: Int? = null, fallbackImage: Int? = null, defaultImage: Int? = null) {
    defaultImage?.let { view.setImageResource(it) }
    if (url.isNullOrEmpty()) {
        return
    }
    com.mmt.uikit.util.ImageLoader.loadImage(view, url = url, placeholder = placeholder, fallbackImage = fallbackImage)
}

@BindingAdapter(value = ["htlGlideUrl", "htlPlaceHolder", "htlFallbackImage","htlDefaultImage","loadAsBitmap", "htlLoopCount"], requireAll = false)
fun bindImageWithGlide(view: ImageView, url: String?, placeholder: Int? = null, fallbackImage: Int? = null, defaultImage: Int? = null,
                       loadAsBitmap: Boolean? = false, loopCount:Int?= null) {
    defaultImage?.let { view.setImageResource(it) }
    if (url.isNullOrEmpty()) {
        return
    }
    loadImageWithGlide(view, url = url, placeholder = placeholder, fallbackImage = fallbackImage, loadAsBitmap = loadAsBitmap ?: false, loopCount = loopCount)
}

fun loadImageWithGlide(
    imageView: ImageView,
    url: String,
    placeholder: Int? = null,
    fallbackImage: Int? = null,
    errorImage: Int? = null,
    loadAsBitmap: Boolean = false,
    loopCount:Int?= null
) {
    if(isValidContextForGlide(context = imageView.context).not()) {
        return
    }
    val glideUrl = GlideUrl(url, ImageLoader.HEADERS)

    if(loopCount!= null && loopCount > 0) {
        Glide.with(imageView.context)
            .asGif()
            .load(glideUrl)
            .into(object: ImageViewTarget<GifDrawable>(imageView) {
                override fun setResource(resource: GifDrawable?) {
                    resource?.setLoopCount(loopCount)
                    imageView.setImageDrawable(resource)
                }
            });
        return
    }
    val glideRequest = if (loadAsBitmap) {
        Glide.with(imageView.context).asBitmap().load(glideUrl)
    } else {
        Glide.with(imageView.context).load(glideUrl)
    }
    glideRequest
        .setDefaults(placeholder, fallbackImage, errorImage)
        .into(imageView)

}

@BindingAdapter("layoutMargin")
fun setLayoutMargin(view: View, @DimenRes margin: Int) {
    val layoutParams = view.layoutParams as ViewGroup.MarginLayoutParams
    val resourceProvider = ResourceProvider.instance
    if (margin != 0) {
        layoutParams.marginStart = (resourceProvider.getDimension(margin)).toInt()
        layoutParams.marginEnd = (resourceProvider.getDimension(margin)).toInt()
        layoutParams.topMargin = (resourceProvider.getDimension(margin)).toInt()
        layoutParams.bottomMargin = (resourceProvider.getDimension(margin)).toInt()
    }
    view.layoutParams = layoutParams
}

@BindingAdapter(value = ["visibleGoneWithTransition", "transitionDuration","gravity"], requireAll = true)
fun updateVisibilityWithTransition(view: View, show: Boolean, duration: Int, gravity: Int){
    (view.parent as? ViewGroup)?.let {
        val transition: Transition = Slide(gravity)
        transition.duration = duration.toLong()
        transition.addTarget(view)
        TransitionManager.beginDelayedTransition(it, transition)
    }
    view.visibility = if (show) View.VISIBLE else View.GONE
}

@BindingAdapter("headerData","headerStyle", requireAll = false)
fun updateHeaderData(header: HomeCardTopWidget, headerData: HeaderData?, style: Style?){
    header.update(headerData,style, headerDefaultTint = R.color.black)
}

@BindingAdapter("headerClickListener")
fun setHeaderClickListener(header: HomeCardTopWidget, listener: HomeCardTopWidget.HomeCardTopWidgetListener){
    header.setHomeCardTopWidgetListener(listener)
}

@BindingAdapter(value = ["enableDisableView"])
fun enableDisableView(view: View, enabled: Boolean) {
    view.isEnabled = enabled
    if (view is ViewGroup) {
        for (id in 0 until view.childCount) {
            enableDisableView(view.getChildAt(id), enabled)
        }
    }
}


@BindingAdapter(value =["htlBackgroundUrl", "htlApplyTransform"])
fun setBackground(view: View, backgroundUrl: String?, transformation: Transformation?) {
    if (backgroundUrl.isNullOrEmpty()) {
        view.background = null
        return
    }
    BindingAdapters.setBackgroundUrl(view, backgroundUrl, transformation, null, null, false)
}
@BindingAdapter("setDirectionIcon")
fun createDirectionIcon(imageView: AppCompatImageView, directionsUiData: DirectionsUiData?){

    if (directionsUiData != null){
        imageView.visibility = View.VISIBLE

        if (directionsUiData.iconUrl.isNotNullAndEmpty()){

            hotelsBindImageV2(imageView, directionsUiData.iconUrl, true,
                R.drawable.htl_default_bg, 0, 0, directionsUiData.displayText, 1f, null)

            imageView.rotation = 0f

        } else {
            imageView.setImageResource(R.drawable.arrow_map_direction)
            imageView.rotation = (360 - directionsUiData.degrees).toFloat()
        }

    } else {
        imageView.visibility = View.GONE
    }

}
@BindingAdapter(value = ["htlColors", "htlOrientation", "htlCorners"], requireAll = false)
fun htlCreateGradient(view: View, colors: List<String>?, orientation: GradientDrawable.Orientation?, corners: CornerRadiusHolder?) {
    if(colors == null) return
    val colorsArray = mutableListOf<Int>()
    colors.forEach {
        colorsArray.add(Color.parseColor(it))
    }
    val gradientDrawable = GradientDrawable(orientation ?: GradientDrawable.Orientation.LEFT_RIGHT, colorsArray.toIntArray())
    corners?.let {
        val radii = floatArrayOf(
            it.topLeft,
            it.topLeft,
            it.topRight,
            it.topRight,
            it.bottomRight,
            it.bottomRight,
            it.bottomLeft,
            it.bottomLeft
        )
        gradientDrawable.cornerRadii = radii
    }
    view.background = gradientDrawable
}

@BindingAdapter("sideBarColorList")
fun createTimelineSideBar(view: View, colorList: List<Int>?) {
    if (colorList == null || colorList.isEmpty()) {
        view.isVisible = false
        return
    }
    var gd = GradientDrawable()
    if (view.background != null && view.background is GradientDrawable) {
        gd = view.background as GradientDrawable
    }

    gd.colors = if (colorList.size == 1) {
        intArrayOf(colorList[0], colorList[0])
    } else colorList.toIntArray()

    gd.cornerRadius = CoreUtil.dpToPx(4).toFloat()
    view.clipToOutline = true
    gd.orientation = GradientDrawable.Orientation.TOP_BOTTOM
    view.background = gd
}

@BindingAdapter(value = ["shouldTint", "tintColor"], requireAll = true)
fun tintImageView(imageView: ImageView, shouldTint: Boolean, @ColorInt resourceId: Int) {
    if (shouldTint) {
        imageView.setColorFilter(resourceId)
    } else {
        imageView.clearColorFilter()
    }
}

@BindingAdapter("yellowStarCount")
fun createYellowStarRatingBar(linearLayout: LinearLayout, rating:Float){
    linearLayout.removeAllViews()
    val size = ResourceProvider.instance.getDimensionPixelSize(R.dimen.image_dimen_xxxsmall)
    val marginEnd = ResourceProvider.instance.getDimensionPixelSize(R.dimen.margin_extra_xtiny)

    for (i in 0 until rating.toInt()) {
        val imageView = ImageView(linearLayout.context)
        imageView.setImageResource(R.drawable.htl_yellow_star_rating)
        val layoutParams = LinearLayout.LayoutParams(size, size)
        layoutParams.setMargins(0, 0, marginEnd, 0)
        imageView.layoutParams = layoutParams
        linearLayout.addView(imageView)
    }
}


/**
 * This function will load image from server without any alteration in url.
 * One param can be null
 * */
@BindingAdapter(
    value = ["htlLoadIcon", "htlIconType", "htlIconWidth", "htlIconHeight", "htlIconTintColor","setMinSize"],
    requireAll = false
)
fun setImageFromUrlOrFromIconType(
    imageView: ImageView,
    url: String?,
    iconType: String?,
    iconWidth: Int?,
    iconHeight: Int?,
    tintColor: String?,
    setMinSize: Boolean = false
) {
    setIconType(imageView, iconType, tintColor)
    loadImage(imageView, url)
    setIconWidthAndHeight(imageView, iconWidth, iconHeight,setMinSize && url.isNotNullAndEmpty())
}

private fun setIconWidthAndHeight(imageView: ImageView, iconWidth: Int?, iconHeight: Int?, setMinSize: Boolean) {

    if (setMinSize){
        val minWidth = (iconWidth ?: 0).coerceAtLeast(HotelConstants.MIN_ICON_SIZE)
        val minHeight = (iconHeight ?: 0).coerceAtLeast(HotelConstants.MIN_ICON_SIZE)
        val multiplier: Float =
            imageView.resources.getInteger(com.mmt.uikit.R.integer.multiplier) / 100F
        val width = UiMigrationHelper.instance.convertDpToPixel(minWidth * multiplier)
        val height = UiMigrationHelper.instance.convertDpToPixel(minHeight * multiplier)
        BindingAdapters.setViewSize(imageView, width.toInt(), height.toInt())
    }

    // "com.mmt.uikit.R.integer.multiplier" assumes base size is for 411dp. For 360 as base, use R.integer.htl_multiplier
    if (iconWidth != null && iconHeight != null) {
        val multiplier: Float =
            imageView.resources.getInteger(com.mmt.uikit.R.integer.multiplier) / 100F
        val width = UiMigrationHelper.instance.convertDpToPixel(iconWidth * multiplier)
        val height = UiMigrationHelper.instance.convertDpToPixel(iconHeight * multiplier)
        BindingAdapters.setViewSize(imageView, width.toInt(), height.toInt())
    }
}

private fun loadImage(imageView: ImageView, url: String?) {
    if (url.isNullOrEmpty()) return
    url.let {
        BindingAdapters.bindImage(
            imageView, it,
            0, null, null,
            null, null, null, 0,
            false, false, true, false
        )
    }
}

/**
 * @param iconType are the type which are recevied in api and mapped to local drawable
 * resources
 * */

private fun setIconType(view: ImageView, iconType: String?, tintColor: String?) {
    iconType?.let {
        if (HotelDrawableProvider.LIGHTNING_ICON == iconType || HotelDrawableProvider.ICON_LIGHTNING == iconType) {
            val widthAndHeight = ResourceProvider.instance.getDimension(
                DrawableProvider.getDrawableDimensions(iconType)
            )
            BindingAdapters.setViewSize(view, widthAndHeight.toInt(), widthAndHeight.toInt())
        }
        view.setImageResource(HotelDrawableProvider.getDrawableResource(iconType))
        tintColor?.let {
            try {
                view.setColorFilter(Color.parseColor(tintColor))
            } catch (exception: Exception) {

            }
        }
    }
}

@BindingAdapter(value = ["htlBorderColor"])
fun setColor(view:RoundedImageView, @ColorRes circleBackgroundColor:Int) {
    view.setBorderColorResource(circleBackgroundColor)
}

@BindingAdapter(value = ["htlAsyncBindChildModels", "htlAsyncBindChildLayout"], requireAll = false)
fun <T> bindChildViews(parent: ViewGroup, modelList: List<T>?,
                       @LayoutRes childLayoutID: Int) {
    parent.removeAllViews()
    if (modelList.isNullOrEmpty()) {
        return
    }
//    if(ExperimentsHotel.htlAsyncLayout.getPokusValue()) {
        val inflater = AsyncLayoutInflater(parent.context)
        for (model in modelList) {
            inflater.inflate(childLayoutID, parent) { view, _, _ ->
                val binding: ViewDataBinding = DataBindingUtil.bind(view)!!
                binding.setVariable(BR.model, model)
                binding.executePendingBindings()
                parent.addView(view)
            }
        }
//    } else {
//        val inflater = LayoutInflater.from(parent.context)
//        for (model in modelList) {
//            val binding: ViewDataBinding = DataBindingUtil.inflate(inflater, childLayoutID, parent, false)
//            binding.setVariable(BR.model, model)
//            binding.executePendingBindings()
//            parent.addView(binding.root)
//        }
//    }
}

@BindingAdapter(value = ["htlFlexiAddOnDetails", "htlEventStream", "htlIsIncluded","htlIsFreeCancellation"], requireAll = false)
fun addFlexiCancel(view: ComposeView, htlFlexiAddOnDetails: AddOnDetails?, htlEventStream: MutableLiveData<HotelEvent>?, htlIsIncluded: Boolean,
                   htlIsFreeCancellation: Boolean) {
    if (htlFlexiAddOnDetails == null) return
    view.setContent {
        FlexiCancelAddonItem(AddOnDetailsVM(addOnDetails = htlFlexiAddOnDetails, enableCheckBox = true, isIncluded = htlIsIncluded, handleEvent = {
            Handler(Looper.getMainLooper()).postDelayed({
                htlEventStream?.postValue(it)
            }, 100)
        }, addOnInteracted = {
            htlEventStream?.postValue(HotelEvent(RecommendedComboEvents.ADDON_INTERACTED, it))
        }, isFreeCancellationTariff = htlIsFreeCancellation))
    }
}

@BindingAdapter(value = ["htlDisplayImageUrl", "htlPlaceHolder", "htlCallback", "htlScaleFactor","htlFitXY", "htlScaleType"], requireAll = false)
fun loadImage(view: AppCompatImageView?, url: String?, @DrawableRes placeHolder: Int?,
              callback:Callback?, scaleFactor:Float?,htlFitXY: Boolean? = false, scaleType: ImageView.ScaleType? = null) {
    if(url.isNullOrEmpty() || view == null) {
        return
    }
    val isMMTCDN = ImageWidthAllocator.isMMTCDN(url)
    if(ExperimentsHotel.imageVariantReduction.getPokusValue().not()) {
        if (isMMTCDN) {
            BindingAdapters.loadImage(view, url, true, false, placeHolder ?: 0, 1.0f)
        } else {
            bindImage(view,  url,  placeHolder?:0,  CoreConstants.EMPTY_STRING,
                scaleType, null, null,callback,  0,  false, false, false, htlFitXY)
        }
        return
    }

    val picasso = Picasso.get()
    val modifiedImageUrl = ImageWidthAllocator.getModifiedUrl(url, scaleFactor)
    picasso.cancelRequest(view)
    val request = picasso
            .load(Uri.parse(modifiedImageUrl))
            .stableKey(modifiedImageUrl)
            .config(Bitmap.Config.RGB_565)
            .resize(ImageWidthAllocator.getAssignedImageWidth(), 0)
            .onlyScaleDown()
            .priority(Picasso.Priority.HIGH)
            .tag(GENERIC_PICASSO_TAG)
    if (placeHolder!= null && placeHolder != 0) {
        request.placeholder(placeHolder)
    } else {
        request.noPlaceholder()
    }

    if (callback != null) {
        request.into(view, callback)
    } else {
        request.into(view)
    }
}