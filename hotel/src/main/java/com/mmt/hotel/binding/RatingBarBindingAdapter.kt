package com.mmt.hotel.binding

import android.R
import android.annotation.SuppressLint
import android.graphics.BitmapFactory
import android.graphics.BitmapShader
import android.graphics.Shader
import android.graphics.drawable.ClipDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.RoundRectShape
import android.view.Gravity
import android.widget.RatingBar
import androidx.databinding.BindingAdapter
import com.mmt.core.MMTCore
import com.mmt.data.model.extensions.gone
import com.mmt.data.model.extensions.visible
import com.mmt.hotel.common.extensions.viewScope
import com.mmt.hotel.widget.compose.MMTRatingBar
import com.mmt.hotel.listingV2.dataModel.HotelRatingUIData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR> <PERSON> : June 8, 2020
 */


@SuppressLint("CheckResult")
@BindingAdapter(value = ["ratingBarProgressDrawableImageIds"])
fun bindRatingWithDrawable(ratingBar: RatingBar, drawableIds : List<Int>?) {
    ratingBar.viewScope.launch {
        var drawable :Drawable?=null
        withContext(Dispatchers.Default) {
            // statements of this block are running in a worker thread and will be canceled when
            // this view is detached from a window (I.e. after Activity.onDestroy(),
            // Fragment.onDestroyView() called or you manually removed this view from the view tree).
            drawable = buildRatingBarDrawables(drawableIds)
        }
        ratingBar.progressDrawable = drawable
    }
}

@SuppressLint("CheckResult")
@BindingAdapter(value = ["hotelRatingUIData", "avoidViewGone", "forceViewGone"], requireAll = false)
fun bindRatingWithData(
    ratingBar: MMTRatingBar,
    hotelRatingUIData: HotelRatingUIData?,
    avoidViewGone: Boolean,
    forceViewGone: Boolean
) {
    if (forceViewGone) {
        ratingBar.gone()
    } else if (hotelRatingUIData != null && hotelRatingUIData.isRatingAvailable()) {
        ratingBar.visible()
        ratingBar.bind(hotelRatingUIData)
    } else if (!avoidViewGone) {
        ratingBar.gone()
    }
}

/**
 * method to generate RatingBar ProgressDrawable that will be set at the runTime
 */
private fun buildRatingBarDrawables(imageIds : List<Int>?): Drawable? {
    if (imageIds == null || imageIds.size < 3) {
        return null
    }

    val context = MMTCore.mContext.resources
    val images = arrayOf(BitmapFactory.decodeResource(context, imageIds[0]),
            BitmapFactory.decodeResource(context, imageIds[1]),
            BitmapFactory.decodeResource(context, imageIds[2]))

    val requiredIds = intArrayOf(R.id.background,
            R.id.secondaryProgress, R.id.progress)
    val roundedCorners = floatArrayOf(5f, 5f, 5f, 5f, 5f, 5f, 5f, 5f)
    val pieces = arrayOfNulls<Drawable>(3)
    for (i in 0..2) {
        val sd = ShapeDrawable(RoundRectShape(
                roundedCorners, null, null))
        val bitmapShader = BitmapShader(images[i],
                Shader.TileMode.REPEAT, Shader.TileMode.CLAMP)
        sd.getPaint().setShader(bitmapShader)
        val cd = ClipDrawable(sd, Gravity.START,
                ClipDrawable.HORIZONTAL)
        if (i == 0) {
            pieces[i] = sd
        } else {
            pieces[i] = cd
        }
    }
    val ld = LayerDrawable(pieces)
    for (i in 0..2) {
        ld.setId(i, requiredIds[i])
    }
    return ld
}