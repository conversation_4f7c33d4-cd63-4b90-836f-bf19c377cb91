package com.mmt.hotel.binding

import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.TextView
import androidx.core.text.HtmlCompat
import androidx.core.text.HtmlCompat.FROM_HTML_MODE_LEGACY
import androidx.annotation.ColorRes
import androidx.compose.ui.graphics.Color
import androidx.databinding.BindingAdapter
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.extensions.asSpannableBuilder
import com.mmt.uikit.MmtTextView

/**
 *  Binding Adapter to bind handle text related binding on views
 *
 * create by <PERSON><PERSON><PERSON> on 10/05/21
 */

@BindingAdapter(value = ["bindHtmlText", "shouldBindAsPlainText", "resizeLinesTo", "seeMoreTextId", "seeLessTextId",
    "onClickListener", "shouldExpandByDefault", "blockSpanClick", "boldStartIndex", "boldEndIndex"], requireAll = false)
fun bindText(view: TextView, text: String?, shouldBindAsPlainText : Boolean = false, resizeLinesTo: Int = 0, seeMoreTextId : Int = 0, seeLessTextId : Int = 0,
             onClickListener : View.OnClickListener? = null, shouldExpandByDefault : Boolean = false, blockSpanClick : Boolean = false,
             boldStartIndex : Int = 0, boldEndIndex : Int = 0) {

    if (text.isNullOrBlank()) {
        return
    }

    val trimmedText = text.trim()
    view.text = if (shouldBindAsPlainText) trimmedText else HtmlCompat.fromHtml(trimmedText, FROM_HTML_MODE_LEGACY)
    if (resizeLinesTo > 0) {
        TextResizingHelper().resizeText(view = view, originalText = view.text, shouldBindAsPlainText = shouldBindAsPlainText,
                resizeLinesTo = resizeLinesTo, seeMoreTextId = seeMoreTextId, seeLessTextId = seeLessTextId,
                onClickListener = onClickListener, shouldExpandByDefault = shouldExpandByDefault, blockSpanClick = blockSpanClick,
                boldStartIndex = boldStartIndex, boldEndIndex = boldEndIndex)
    }
}

@BindingAdapter(value = ["renderHtmlText", "changeTextColor"])
fun changeTextColor(view: TextView, text: String?, @ColorRes color: Int) {
    view.text = if (color != 0) {
        Html.fromHtml(text.orEmpty()).asSpannableBuilder().also {
            it.setSpan(ForegroundColorSpan(ResourceProvider.instance.getColor(color)), 0, it.length, SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    } else {
        Html.fromHtml(text.orEmpty())
    }
}

@BindingAdapter(value = ["htlHtmlContent", "htlSearchText"], requireAll = false)
fun getSearchedHtmlString(textView: TextView, content: String?, searchText: String?) {
    val contentString = content.orEmpty()

    if(searchText.isNullOrEmpty()){
        textView.text = HtmlCompat.fromHtml(contentString, FROM_HTML_MODE_LEGACY)
        return
    }
    if(contentString.indexOf(string = searchText,ignoreCase = true) == -1){
        textView.text = HtmlCompat.fromHtml(contentString, FROM_HTML_MODE_LEGACY)
        return
    }

    textView.text = getHtmlForSearchedText(contentString, searchText)
}

private fun getHtmlForSearchedText(content: String, searchText: String) : Spanned {
    if(searchText.isEmpty()){
        return HtmlCompat.fromHtml(content, FROM_HTML_MODE_LEGACY)
    }
    // regex to make search case-insensitive ((?i)$searchText)".toRegex()
    //$1 to keep match text case
    val modifiedContent = content.replace("((?i)$searchText)".toRegex(),
            "<span style=\"background-color: #ffda4d\">$1</span>")
    return HtmlCompat.fromHtml(insertFontAroundSpan(modifiedContent), FROM_HTML_MODE_LEGACY)
}


/**
 * Helper method for inserting <font> html tag around <span> html tag
 * inputs :
 * 1) "<font color=\"#ff0000\">Speaks English, Reads English</font>"
 * 2) "<font color=\"#ff0000\">Available at <span style=\"background-color: #FFFF00\">Eng</span>lish Hotel</font>"
 * 3) "<font color=\"#ff0000\">Available at</font> <span style=\"background-color: #FFFF00\"><font color=\"#ff0000\">Eng</font></span>
 * <font color=\"#ff0000\">lish Hotel</font>"
 * 4) "<font color=\"#ff0000\">Available <font color=\"#ffff00\">at <span style=\"background-color: #FFFF00\">Eng</span></font>lish Hotel</font>"
 * 5) "<font color=\"#ff0000\">Available at</font> <span style=\"background-color: #FFFF00\">Eng</span><font color=\"#ff0000\">lish Hotel</font>"
 *
 * Known issue : nested fonts are not handled properly, it will insert first font around all spans
 */
private fun insertFontAroundSpan(content :String) : String{
    val length = content.length
    var startIndex = 0 // to track current string progress
    var start = content.indexOf("<font",startIndex)
    var modifiedContent = if(start != -1){
        startIndex = start
        content.substring(0, start) // add substring before font tag
    } else {
        ""
    }
    while(startIndex < length && start != -1) { // start!=-1 if font doesn't exits break loop
        start = content.indexOf("<font", startIndex)
        val end = content.indexOf("</font>", startIndex)
        val fontStartClosingTag = if (start != -1) {
            content.indexOf(">", start)
        } else {
            content.indexOf(">", startIndex)
        }
        // fontStartClosingTag < end && fontStartClosingTag > start condition is must as it can use <span> closing tag also
        if(start != -1 && fontStartClosingTag < end && fontStartClosingTag > start) {
            // first get substring which have proper <font>...</font> tags, not nested
            val fontTag = content.substring(start, fontStartClosingTag + 1)
            var trimContent = content.substring(start, end + 7) // +7 is </font> length
            val highlightStart = trimContent.indexOf("<span")
            val highlightEnd = trimContent.indexOf("</span>")
            // check whether <span> exists in current substring or not if exits insert fonts around span
            if( highlightStart != -1 && highlightEnd !=- 1 && highlightStart < highlightEnd ) {
                trimContent = trimContent.replace("<span","</font>$fontTag<span")
                trimContent = trimContent.replace("</span>","</span></font>$fontTag")
                modifiedContent += trimContent
            } else if (highlightStart != -1) {
                trimContent = trimContent.replace("<span","</font>$fontTag<span")
                modifiedContent += trimContent
            }else if (highlightEnd != -1) {
                trimContent = trimContent.replace("</span>","</span></font>$fontTag")
                modifiedContent += trimContent
            } else {
                // add substring to modifiedContent
                modifiedContent += trimContent
            }
            // updating start index to last added content length
            startIndex += trimContent.length
        } else if (start != -1) {
            modifiedContent += content.substring(startIndex, start)
            startIndex = start
        }
    }
    if(startIndex < length) {
        modifiedContent += content.substring(startIndex)
    }
    return modifiedContent
}

@BindingAdapter(value = ["htlContent", "htlSearchText"], requireAll = false)
fun getSpannableString(textView: TextView, content: String, searchText: String?) {
    if(searchText.isNullOrEmpty()){
        textView.text = content
        return
    }
    if(content.indexOf(string = searchText, ignoreCase = true) == -1){
        textView.text = content
        return
    }

    textView.text = getSpanForSearchedText(content, searchText)
}

fun getSpanForSearchedText(content: String, searchText: String, color: Int = R.color.yellow) : SpannableStringBuilder {
    if(searchText.isEmpty()){
        return SpannableStringBuilder(content)
    }
    var start = 0
    var end = content.indexOf(string = searchText, ignoreCase = true)
    val searchTextLength = searchText.length

    val spannableStringBuilder = SpannableStringBuilder()
    while (end != -1) {
        val index = end + searchTextLength
        spannableStringBuilder.append(content.substring(start, index))
        spannableStringBuilder.setSpan(BackgroundColorSpan(ResourceProvider.instance.getColor(color)), end, index,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        start = index
        end = content.indexOf(string = searchText , startIndex = index , ignoreCase = true)
    }
    if(start < content.length) {
        spannableStringBuilder.append(content.substring(start, content.length))
    }
    return spannableStringBuilder
}