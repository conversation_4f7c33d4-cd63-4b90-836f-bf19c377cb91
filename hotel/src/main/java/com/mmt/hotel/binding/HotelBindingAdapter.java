/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.mmt.hotel.binding;

import android.graphics.Bitmap;
import android.graphics.Outline;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.ViewTreeObserver;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.VideoView;

import androidx.annotation.DimenRes;
import androidx.databinding.BindingAdapter;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SnapHelper;

import com.mmt.hotel.common.util.HotelUtil;
import com.google.android.exoplayer2.ui.SimpleExoPlayerView;
import com.mmt.uikit.util.UiUtil;
import com.mmt.core.util.ResourceProvider;
import com.mmt.core.util.CollectionUtil;
import com.mmt.core.util.StringUtil;
import com.mmt.network.TaskQueueExecutor;
import com.mmt.hotel.R;
import com.mmt.hotel.databinding.RowFlyfishInsightBinding;
import com.mmt.uikit.util.StartSnapHelper;
import com.mmt.uikit.util.UiMigrationHelper;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.RequestCreator;
import com.squareup.picasso.Target;

import java.util.List;


/**
 * Data Binding adapters specific to the app.
 */
public class HotelBindingAdapter {

    @BindingAdapter(value = {"bindHotelListImageRecyclerView", "snapHelper"}, requireAll = false)
    public static void bindHotelListImageRecyclerView(RecyclerView view, RecyclerView.Adapter adapter
            , SnapHelper snapHelper) {
        view.setAdapter(adapter);
        LinearLayoutManager llm = new LinearLayoutManager(view.getContext());
        llm.setOrientation(LinearLayoutManager.HORIZONTAL);
        view.setLayoutManager(llm);
        view.setOnFlingListener(null);

        if (snapHelper == null) {
            snapHelper = new StartSnapHelper();
        }

        snapHelper.attachToRecyclerView(view);
    }


    @BindingAdapter(value = {"bindFlyfishInsights", "positiveInsight"}, requireAll = true)
    public static void bindHotelDetailInsights(LinearLayout linearLayout, List<String> flyfishInsights, boolean isPositiveInsight) {
        linearLayout.removeAllViews();

        if (CollectionUtil.isEmptyCollection(flyfishInsights)) {
            return;
        }

        if (flyfishInsights.size() > 3) {
            flyfishInsights = flyfishInsights.subList(0, 3);
        }

        LayoutInflater layoutInflater = LayoutInflater.from(linearLayout.getContext());
        for (String insight : flyfishInsights) {
            if (StringUtil.isNullOrEmpty(insight)) {
                continue;
            }

            RowFlyfishInsightBinding binding = DataBindingUtil.inflate(layoutInflater, R.layout.row_flyfish_insight
                    , linearLayout, false);
            binding.setInsightText(insight);
            binding.setPositiveInsight(isPositiveInsight);
            binding.executePendingBindings();
            linearLayout.addView(binding.getRoot());
        }
    }

    @BindingAdapter(value = {"topRoundRadius"})
    public static void bindRoundedDrawable(ImageView imageView, @DimenRes int topRoundRadiusDimen) {
        if (topRoundRadiusDimen == 0) {
            return;
        }
        imageView.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                outline.setRoundRect(0, 0, view.getWidth(),
                        (int) (view.getHeight() + ResourceProvider.Companion.getInstance().getDimension(topRoundRadiusDimen)),
                        ResourceProvider.Companion.getInstance().getDimension(topRoundRadiusDimen));
            }
        });
        imageView.setClipToOutline(true);
    }

    @BindingAdapter(value = {"blurBgUrl", "htlPlaceHolder", "imageWidth",
            "imageHeight", "tag", "imgSizeFactor", "scaleDownFactor", "blurRadius"}, requireAll = false)
    public static void loadBlurBackground(View view, String backgroundUrl, int htlPlaceHolder,
                                          int imageWidth, int imageHeight, String tag, float imgSizeFactor,
                                          int scaleDownFactor, int blurRadius) {
        if (StringUtil.isNullOrEmpty(backgroundUrl)) {
            view.setBackground(ResourceProvider.getInstance().getDrawable(htlPlaceHolder));
            return;
        }
        // below code is written to avoid multiple requests
        if (view.getTag() == null) {
            view.setTag(true);
        } else {
            return;
        }
        if(scaleDownFactor == 0){
            scaleDownFactor = 2;
        }
        if(blurRadius == 0){
            blurRadius = 15;
        }
        // in recyclerview, cached  views are used for binding in that case, if [imageWidth] & [imageHeight] are
        // not provided , then addOnGlobalLayoutListener  will not work
        // to handle this below width & height check are applied.
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams != null) {
            if (layoutParams.height > 0) {
                imageHeight = layoutParams.height;
            }
            if (layoutParams.width > 0) {
                imageWidth = layoutParams.width;
            }
        }
        if (imageHeight > 0 && imageWidth > 0) {
            loadBlurredImage(view, backgroundUrl, htlPlaceHolder, imageWidth, imageHeight, tag, imgSizeFactor,scaleDownFactor,blurRadius);
        } else {
            int finalScaleDownFactor = scaleDownFactor;
            int finalBlurRadius = blurRadius;
            view.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    int width = view.getWidth();
                    int height = view.getHeight();
                    if (width > 0 && height > 0) {
                        view.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                        loadBlurredImage(view, backgroundUrl, htlPlaceHolder, width, height, tag, imgSizeFactor, finalScaleDownFactor, finalBlurRadius);
                    }
                }
            });
        }
    }

    private static void loadBlurredImage(View view, String backgroundUrl, int htlPlaceHolder, int imageWidth,
                                         int imageHeight, String tag, float imgSizeFactor,int scaleDownFactor, int blurRadius) {
        Target target = new Target() {

            @Override
            public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                if(bitmap.getWidth()<view.getHeight()){
                    setBlurredBackground(bitmap, view, 1, blurRadius);
                }else{
                    setBlurredBackground(bitmap, view, scaleDownFactor, blurRadius);
                }
            }

            @Override
            public void onBitmapFailed(Exception e, Drawable errorDrawable) {
            }

            @Override
            public void onPrepareLoad(Drawable placeHolderDrawable) {

            }
        };
        if (htlPlaceHolder != 0) {
            view.setBackgroundResource(htlPlaceHolder);
        }
        if (imgSizeFactor == 0.0f) {
            imgSizeFactor = 1.0f;
        }
        int width = imageWidth;
        int height = imageHeight;


        if (UiMigrationHelper.Companion.getInstance().getPokusValue()) {
            width = (int) (imageWidth * imgSizeFactor);
            height = (int) (imageHeight * imgSizeFactor);
        }
        String url = HotelUtil.INSTANCE.appendImageDownSizeParam(backgroundUrl);
        RequestCreator request = Picasso.get()
                .load(url)
                .config(Bitmap.Config.ARGB_8888); // don't change config as ScriptIntrinsicBlur works only on for this
        if (StringUtil.isNotNullAndEmpty(tag)) {
            request.tag(tag);
        }
        request.into(target);
        view.setTag(target);
    }

    private static void setBlurredBackground(Bitmap bitmap, View view, int scaleDownFactor, int blurRadius) {
        if (bitmap == null || view == null) {
            return;
        }
        view.setTag(null);
        TaskQueueExecutor taskQueueExecutor = TaskQueueExecutor.create();
        ResourceProvider resourceProvider = ResourceProvider.Companion.getInstance();
        taskQueueExecutor.execute(() -> {
            try {
                Bitmap scaledDownBitmap = Bitmap.createBitmap(bitmap, 0, 0,
                        bitmap.getWidth() / scaleDownFactor, bitmap.getHeight() / scaleDownFactor);
                Bitmap blurBitmap = UiUtil.getBlurBitmap(scaledDownBitmap, blurRadius);
                new Handler(Looper.getMainLooper()).post(() -> {
                    view.setBackground(new BitmapDrawable(resourceProvider.getResources(), blurBitmap));
                });
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                taskQueueExecutor.disposable();
            }
        });
    }
}
