package com.mmt.hotel.binding

import android.view.LayoutInflater
import android.view.ViewTreeObserver
import android.widget.TableLayout
import android.widget.TableRow
import androidx.annotation.DrawableRes
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.mmt.hotel.BR
import com.mmt.core.util.CollectionUtil
import com.mmt.hotel.common.data.TableLayoutData

@BindingAdapter(value = ["tableColCount", "tableEntries", "colLayout", "fullColHeight", "bgDrawable","htlScreenWidth","htlTablePadding"], requireAll = false)
fun <T> setDataInTable(tableLayout: TableLayout, colCount: Int, entries: List<T>, layoutId: Int, fullColHeight: Boolean = false,
                       @DrawableRes drawableId: Int? = null, screenWidth: Int? = null, padding: Int? = null) {
    if (colCount < 0 || CollectionUtil.isNullOrEmpty(entries) || layoutId < 0) {
        return
    }
    if (screenWidth != null && screenWidth > 0) {
        tableLayout.removeAllViews()
        addItems(tableLayout, screenWidth - (padding ?: 0) * 2, colCount, entries, layoutId, fullColHeight, drawableId)
        return
    }
    tableLayout.viewTreeObserver.addOnGlobalLayoutListener (object : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            tableLayout.viewTreeObserver.removeOnGlobalLayoutListener(this)
            tableLayout.removeAllViews()
            addItems(tableLayout, tableLayout.width, colCount, entries, layoutId, fullColHeight, drawableId)
        }
    })
}

private fun <T> addItems(tableLayout: TableLayout, width: Int, colCount: Int, entries: List<T>, layoutId: Int, fullColHeight: Boolean, @DrawableRes drawableId: Int? = null) {
    val colWidth = (width - tableLayout.paddingStart - tableLayout.paddingEnd) / colCount
    val inflater = LayoutInflater.from(tableLayout.context)
    var i = 0
    while (i < entries.size) {
        val tableRow = TableRow(tableLayout.context)
        val tabLayoutParams = TableLayout.LayoutParams()
        tableRow.layoutParams = tabLayoutParams
        var col = 0
        while (col < colCount && i < entries.size) {
            val entry = entries[i]
            val binding: ViewDataBinding = DataBindingUtil.inflate(inflater, layoutId, tableLayout, false)
            val layoutParams =
                    if(fullColHeight)
                        TableRow.LayoutParams(colWidth, TableRow.LayoutParams.MATCH_PARENT)
                    else
                        TableRow.LayoutParams(colWidth, TableRow.LayoutParams.WRAP_CONTENT)
            binding.root.layoutParams = layoutParams
            drawableId?.let {
                if(drawableId != -1)
                    binding.root.setBackgroundResource(drawableId)
            }
            binding.setVariable(BR.data, entry)
            tableRow.addView(binding.root)
            col++
            if (col < colCount) {
                i++
            }
        }
        tableLayout.addView(tableRow)
        i++
    }
}

@BindingAdapter(value = ["layoutData"], requireAll = true)
fun bindTableLayoutData(tableLayout: TableLayout, data: TableLayoutData) {
    if (CollectionUtil.isNullOrEmpty(data.itemData)) {
        return
    }

    tableLayout.removeAllViews()

    val layoutInflater: LayoutInflater = LayoutInflater.from(tableLayout.context)
    try {
        if (data.headerData != null) {
            val binding: ViewDataBinding = DataBindingUtil.inflate(layoutInflater, data.headerData!!.layoutId, tableLayout, false)
            binding.setVariable(data.headerData!!.dataVariableId, data.headerData!!.data)
            binding.executePendingBindings()
            tableLayout.addView(binding.root)
        }
        for (dataItem in data.itemData) {
            val binding: ViewDataBinding = DataBindingUtil.inflate(layoutInflater, dataItem.layoutId, tableLayout, false)
            binding.setVariable(dataItem.dataVariableId, dataItem.data)
            binding.executePendingBindings()
            tableLayout.addView(binding.root)
        }
    } catch (e: Exception) {

    }
}