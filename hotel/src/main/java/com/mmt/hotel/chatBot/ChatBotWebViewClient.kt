package com.mmt.hotel.chatBot

import android.net.http.SslError
import android.os.Build
import android.webkit.SslErrorHandler
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.annotation.RequiresApi

class ChatBotWebViewClient(private val listener: WebViewErrorListener) : WebViewClient() {

    interface WebViewErrorListener {
        fun onErrorReceived(errorCode: Int, description: String?, failingUrl: String?)
        fun onHttpErrorReceived(statusCode: Int, description: String?, failingUrl: String?)
    }

    @Deprecated("Deprecated in Java")
    override fun onReceivedError(
        view: WebView?,
        errorCode: Int,
        description: String?,
        failingUrl: String?
    ) {
        listener.onErrorReceived(errorCode, description, failingUrl)
    }

    override fun onReceivedError(
        view: WebView?,
        request: WebResourceRequest?,
        error: WebResourceError?
    ) {
        listener.onErrorReceived(error?.errorCode ?: -1, error?.description?.toString(), request?.url?.toString())
    }

    override fun onReceivedHttpError(
        view: WebView?,
        request: WebResourceRequest?,
        errorResponse: WebResourceResponse?
    ) {
        listener.onHttpErrorReceived(errorResponse?.statusCode ?: -1, errorResponse?.reasonPhrase, request?.url?.toString())
    }

    override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
        handler?.cancel()
    }
}