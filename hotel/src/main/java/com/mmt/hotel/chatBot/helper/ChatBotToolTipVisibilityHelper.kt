package com.mmt.hotel.chatBot.helper

import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.mobconfig.model.response.DetailFabWidgetInfo


object ChatBotToolTipVisibilityHelper {

    var chatBotIconAnimatedInSession = false

    fun canShowToolTip(): Bo<PERSON>an {

        // return false if chat bot icon is clicked once
        val chatBotIconClickedOnce = HotelSharedPrefUtil.instance.getBoolean(SharedPrefKeys.CHAT_BOT_ICON_CLICKED_ONCE, false)
        if(chatBotIconClickedOnce) {
            return false
        }

        // return false if already visible in session
        val shownInSession = HotelSharedPrefUtil.instance.getBoolean(SharedPrefKeys.CHAT_BOT_TOOL_TIP_SHOWN_IN_SESSION, false)
        if(shownInSession) {
            return false
        }

        val detailFabWidgetInfo = HotelSharedPrefUtil.instance.getObject<DetailFabWidgetInfo>(SharedPrefKeys.DETAIL_FAB_WIDGET_INFO, DetailFabWidgetInfo::class.java)
        val toolTipTotalShowCount = detailFabWidgetInfo?.chatBotToolTipShowCount ?: 0

        if (toolTipTotalShowCount > 0) {
            var toolTipShownCountSoFar = HotelSharedPrefUtil.instance.getInt(SharedPrefKeys.CHAT_BOT_TOOL_TIP_SHOWN_COUNT)
            if (toolTipShownCountSoFar < toolTipTotalShowCount) {
                HotelSharedPrefUtil.instance.putInt(SharedPrefKeys.CHAT_BOT_TOOL_TIP_SHOWN_COUNT, ++toolTipShownCountSoFar)
                return true
            }
        }else {
            return true
        }

        return false
    }

    fun canAnimate() = !chatBotIconAnimatedInSession
}