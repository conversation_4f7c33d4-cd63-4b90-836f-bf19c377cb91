package com.mmt.hotel.chatBot

import android.annotation.SuppressLint
import android.view.View
import android.webkit.CookieManager
import android.webkit.JavascriptInterface
import android.webkit.ValueCallback
import android.webkit.WebSettings
import android.webkit.WebView
import androidx.core.view.isVisible
import com.gommt.logger.LogUtils
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.util.CoreUtil
import com.mmt.hotel.BuildConfig
import com.mmt.hotel.R
import com.mmt.hotel.base.getActivity
import com.mmt.hotel.chatBot.dataModel.ChatBotActionData
import com.mmt.hotel.chatBot.helper.IChatBotAndroidBridge
import com.mmt.hotel.chatBot.helper.IChatBotListener
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.network.MMTNetwork
import com.mmt.uikit.util.isNotNullAndEmpty

class ChatBotManager(
    chatBotContainer: WebView,
    private val listener: IChatBotListener,
) : IChatBotAndroidBridge, ChatBotWebViewClient.WebViewErrorListener {

    companion object{
        const val CHAT_BOT_ERROR_TYPE_RETRY = "RETRY"
    }

    private val tag = "ChatBotManager"

    private var chatBotWebView: WebView = chatBotContainer
    var originalUrlInvalidated = true
    var prepolpulationQuestion: String? = null
    var chromeClient: ChatBotChromeClient? = null

    fun isWebViewVisible() : Boolean {
        return chatBotWebView.visibility == View.VISIBLE
    }

    @SuppressLint("SetJavaScriptEnabled")
    fun init() {
        originalUrlInvalidated = true
        chatBotWebView.context.getActivity()?.let {
//            chatBotWebView.webViewClient = ChatBotWebViewClient(this)
            chromeClient = ChatBotChromeClient(it)
        }
//        chromeClient = ChatBotChromeClient(chatBotWebView.context as Activity)

        if (BuildConfig.DEBUG) {
            WebView.setWebContentsDebuggingEnabled(true)
        }

        chatBotWebView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            defaultFontSize = 16
            builtInZoomControls = true
            userAgentString = userAgentString + "MMT_ANDROID_" + CoreUtil.getAppVersionName()

            setGeolocationEnabled(true)
            javaScriptCanOpenWindowsAutomatically = true
            cacheMode = WebSettings.LOAD_DEFAULT
            allowFileAccess = true
            databaseEnabled = true
            displayZoomControls = false

        }

        chatBotWebView.apply {
            scrollBarStyle = View.SCROLLBARS_INSIDE_OVERLAY
            isFocusable = true
            isFocusableInTouchMode = true
            requestFocus()
            setBackgroundColor(resources.getColor(R.color.black_10))
        }

        chatBotWebView.webViewClient = ChatBotWebViewClient(this)
        chatBotWebView.webChromeClient = chromeClient
        chatBotWebView.addJavascriptInterface(this, "androidInterface")
    }

    fun setQuestionToPrepopulate(question: String?) {
        this.prepolpulationQuestion = question
    }
    fun loadUrl(urlString: String) {
        LogUtils.debug(tag, "initMyra: $urlString")
        loadPreviousChat(urlString)
        if(isEligibleForLoadUrl(urlString)) {
            chatBotWebView.loadUrl(urlString, getHeader())
            originalUrlInvalidated = false
        } else {
            onAppLoaded()
        }
        userInteractionStarted()
    }

    private fun isEligibleForLoadUrl(url: String): Boolean {
        return ((url != chatBotWebView.originalUrl) || originalUrlInvalidated)
    }

    private fun invalidateUrl() {
        originalUrlInvalidated = true
    }

    fun loadPreviousChat(url: String) {
        removeCookies()
        setCookie(url)
    }


    private fun getHeader(): Map<String, String?> {
        return MMTNetwork.iNetworkHeaders.getDefaultHeaders(MMTCore.mContext)
    }

    fun userInteractionStarted(){
        evaluateJs("userInteractionStarted(true);")
    }

    fun prepopulateQuestion(){
        prepolpulationQuestion?.let {
            evaluateJs("pushUserMessage(\"$it\");")
        }
    }

    private fun onJsLoaded(){
        if(chatBotWebView.isVisible) userInteractionStarted()
    }

    private fun onAppLoaded(){
        listener.myraChatbotOpened()
        prepopulateQuestion()
    }

    private fun evaluateJs(js: String?, resultCallback: ValueCallback<String?>? = null) {
        LogUtils.debug(tag, "evaluateJs: $js")
        chatBotWebView.apply {
            post {
                js?.let { chatBotWebView.evaluateJavascript(it, resultCallback) }
            }
        }
    }

    private fun checkForJSLoaded(jsonObject: JsonObject) {
        jsonObject.get(HotelConstants.JSON_KEY_JS_LOAD)?.let { jsLoaded ->
            if(jsLoaded.isJsonNull.not()){
                if(jsLoaded.isJsonPrimitive){
                    if(jsLoaded.asBoolean) onJsLoaded()
                }
            }
        }
    }

    private fun checkForAppLoaded(jsonObject: JsonObject) {
        jsonObject.get(HotelConstants.JSON_KEY_APP_LOADED)?.let { appLoaded ->
            if(appLoaded.isJsonNull.not()){
                if(appLoaded.isJsonPrimitive){
                    if(appLoaded.asBoolean) onAppLoaded()
                }
            }
        }
    }

    private fun handleErrorFromMyraWebView(jsonObject: JsonObject?) {
        try {
            Gson().fromJson(jsonObject, ChatBotActionData::class.java)?.let { errorCta ->
                if(CHAT_BOT_ERROR_TYPE_RETRY.equals(errorCta.type, true)){
                    closeWebView()
                }
            }
        }catch (e: Exception){
            LogUtils.error(tag, "Error in parsing error data: $jsonObject")
        }
    }

    private fun checkForError(jsonObject: JsonObject) {
        try {
            jsonObject.get(HotelConstants.JSON_KEY_ERROR)?.let { error ->
                if(error.isJsonNull.not()){
                    handleErrorFromMyraWebView(error.asJsonObject)
                }
            }
        }catch (e: Exception){
            LogUtils.error(tag, "Error in parsing error data: ${jsonObject.asString}")
        }
    }


    // =============================== WebViewErrorListener ==============================================================

    override fun onErrorReceived(errorCode: Int, description: String?, failingUrl: String?) {
        LogUtils.error(tag, "WebView error: $errorCode, $description, $failingUrl")
        invalidateUrl()
    }

    override fun onHttpErrorReceived(statusCode: Int, description: String?, failingUrl: String?) {
        LogUtils.error(tag, "HTTP error: $statusCode, $description, $failingUrl")
        invalidateUrl()
        if(statusCode == 403)
            chatBotWebView.apply {
                setBackgroundColor(resources.getColor(R.color.white))
            }
    }

    // =============================== IChatBotAndroidBridge =======================================

    @JavascriptInterface
    override fun closeWebView() {
        invalidateUrl()
        listener.myraCloseChatBot()
    }

    @JavascriptInterface
    override fun minimizeWebView() {
        listener.myraCloseChatBot()
    }

    @JavascriptInterface
    override fun action(lob: String?, unreadCount: String?, data: String?) {
        LogUtils.debug(tag, "action: lob: $lob, data: $data")
        listener.myraOnChatBotAction(lob, unreadCount, data)
        JsonParser.parseString(data)?.asJsonObject?.let { jsonObject ->
            checkForError(jsonObject)
            checkForJSLoaded(jsonObject)
            checkForAppLoaded(jsonObject)
        }
    }

    // ================================ on dispose + cookies =======================================

    fun dispose() {
        chatBotWebView.isVisible = false
        LogUtils.debug(tag, "destroyMyra")
        chatBotWebView.apply {
            post {
                chatBotWebView.destroy()
            }
        }
    }

    fun setCookie(url: String) {
        CookieManager.getInstance().setCookie(url, "dvid=" + CoreUtil.getDeviceId())
        if(LoginUtils.mmtAuth.isNotNullAndEmpty()){
            CookieManager.getInstance().setCookie(url, "mmt-auth=${LoginUtils.mmtAuth}")
        }
        val headers = getHeader()
        headers.forEach {
            if(it.key.isNotNullAndEmpty() && it.value.isNotNullAndEmpty()){
                CookieManager.getInstance().setCookie(url, "${it.key}=${it.value}")
            }
        }
    }

    fun removeCookies() {
        CookieManager.getInstance().removeSessionCookies(null)
        CookieManager.getInstance().removeAllCookies { value: Boolean ->
        }
        CookieManager.getInstance().flush()
    }
}
