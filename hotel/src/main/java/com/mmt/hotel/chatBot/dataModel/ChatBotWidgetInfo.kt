package com.mmt.hotel.chatBot.dataModel

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ChatBotWidgetInfo(
    @SerializedName("chatBotUrl")
    val chatBotWebViewUrl: String,
    @SerializedName("iconUrl")
    val chatBotIconUrl: String,
    @SerializedName("type")
    val chatBotType: String?,
    @SerializedName("tooltipData")
    val chatBotTooltipData : GenericToolTip?,
    @SerializedName("hooks")
    val myraHooksMap : HashMap<String,MyraHookInfo>?,
    @SerializedName("hooksIconUrl")
    val hooksIconUrl: String?,
    @SerializedName("borderColor")
    val borderColor: List<String>?,
    @SerializedName("expandBgColor")
    val expandBgColor: List<String>?,
    @SerializedName("persuasions")
    val persuasions: List<ChatBotWidgetPersuasions>?,
    @SerializedName("expandDelay")
    val expandDelay: Long? = null,
    @SerializedName("persuasionDelay")
    val persuasionDelay: Long? = null,
    @SerializedName("allowBotExpansion")
    var allowBotExpansion: Boolean? = null,
    @SerializedName("lobMetaData")
    var lobMetaData: String? = null,
) : Parcelable

@Parcelize
data class ChatBotWidgetPersuasions(
    @SerializedName("text")
    val text: String?,
    @SerializedName("textColor")
    val textColor: List<String>?,
): Parcelable

@Parcelize
data class MyraHookInfo(
    @SerializedName("question")
    val question: String?,
    @SerializedName("uiText")
    val uiText: String,
): Parcelable

@Parcelize
data class GenericToolTip(
    @SerializedName("text")
    val text: String,
    @SerializedName("timer")
    val timer: Long?
): Parcelable
