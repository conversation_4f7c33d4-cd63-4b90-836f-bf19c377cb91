package com.mmt.hotel.chatBot.helper

import android.webkit.WebView

interface IChatBotActivityInterface {

    fun getWebView() : WebView?

    fun isWebViewVisible(): Boolean

    fun showHideWebView(show : <PERSON><PERSON><PERSON>)

    fun onUpdateChatBotUnreadMsg(hasUnreadMsg : <PERSON><PERSON>an)

    fun trackChatBotActions(eventName : String)

    fun showOrHideTravelPlex(show: <PERSON><PERSON><PERSON>, question:String?= null)
}