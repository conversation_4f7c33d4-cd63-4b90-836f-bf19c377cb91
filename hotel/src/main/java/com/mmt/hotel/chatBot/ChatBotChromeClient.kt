package com.mmt.hotel.chatBot

import android.Manifest
import android.app.Activity
import android.webkit.PermissionRequest
import android.webkit.WebChromeClient
import com.mmt.core.MMTCore
import com.mmt.core.MPermission.PermissionManager.INativePermissionCallback
import com.mmt.core.MPermission.PermissionUtil
import com.mmt.core.MPermission.ui.PermissionSnackBar
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R

class ChatBotChromeClient(private val activity: Activity) : WebChromeClient(),
    INativePermissionCallback {

    private var mPermCallback: AudioPermissionsCallback? = null

    companion object {
        const val MYRA_AUDIO_PERMISSION_REQUEST_CODE = 1567000
    }

    override fun onGeolocationPermissionsShowPrompt(
        origin: String?,
        callback: android.webkit.GeolocationPermissions.Callback?
    ) {
        callback?.invoke(origin, true, false)
    }

    override fun onPermissionRequest(request: PermissionRequest) {
        var requested = false
        for (permissionReq in request.resources) {
            if (permissionReq == PermissionRequest.RESOURCE_AUDIO_CAPTURE) {
                requestAudioPermission(request, false)
                requested = true
            }
        }
        if (!requested) {
            super.onPermissionRequest(request)
        }
    }

    inner class AudioPermissionsCallback(private val webAudioPermissionRequest: PermissionRequest) :
        INativePermissionCallback {
        override fun permissionGranted(pRequestCode: Int) {
            webAudioPermissionRequest.grant(arrayOf(PermissionRequest.RESOURCE_AUDIO_CAPTURE))
        }

        override fun permissionNotGranted(pRequestCode: Int) {
            webAudioPermissionRequest.deny()
        }

        override fun onNeverAskAgainChecked(pRequestCode: Int) {
            webAudioPermissionRequest.deny()
        }
    }


    fun requestAudioPermission(
        request: PermissionRequest,
        isExplanationSnackBarShown: Boolean
    ) {
        if (MMTCore.coreInterface.getPermissionManager() == null) {
            request.deny()
            return
        }
        mPermCallback = AudioPermissionsCallback(request)
        MMTCore.coreInterface.getPermissionManager()?.checkForPermission(
            object : PermissionSnackBar.SnackBarCallback {
                override fun onGrantPermissionClick(
                    mPermissions: Array<String>?,
                    mRequestCode: Int
                ) {
                    requestAudioPermission(request, true)
                }

                override fun onSettingsClick(mRequestCode: Int) {
                }

                override fun onDismissClick() {
                    request.deny()
                }
            },
            PermissionUtil.getPermissionGroupMap()[Manifest.permission.RECORD_AUDIO],
            isExplanationSnackBarShown,
            activity,
            arrayOf(Manifest.permission.RECORD_AUDIO),
            MYRA_AUDIO_PERMISSION_REQUEST_CODE,
            mPermCallback,
            ChatBotChromeClient::class.java.name,
            null,
            ResourceProvider.instance.getString(R.string.chat_bot_mic_permission_msg)
        )
    }

    override fun permissionGranted(pRequestCode: Int) {
        mPermCallback?.permissionGranted(pRequestCode)
    }

    override fun permissionNotGranted(pRequestCode: Int) {
        mPermCallback?.permissionNotGranted(pRequestCode)
    }

    override fun onNeverAskAgainChecked(pRequestCode: Int) {
        mPermCallback?.onNeverAskAgainChecked(pRequestCode)
    }
}