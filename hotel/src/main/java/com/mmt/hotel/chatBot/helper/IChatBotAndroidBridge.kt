package com.mmt.hotel.chatBot.helper

import android.webkit.JavascriptInterface
import androidx.annotation.Keep


/**
 * This interface defines the methods that are exposed to JavaScript in the WebView.
 * The @JavascriptInterface annotation is required for each method to make them accessible from JavaScript.
 */
@Keep
interface IChatBotAndroidBridge {

    /**
     * Closes the WebView when called from JavaScript.
     */
    @JavascriptInterface
    fun closeWebView()

    /**
     * Minimizes the WebView when called from JavaScript.
     */
    @JavascriptInterface
    fun minimizeWebView()

    /**
     * Performs an action when called from JavaScript.
     * The specific action to be performed is determined by the data parameter.
     * @param data A string containing data for the action to be performed.
     */
    @JavascriptInterface
    fun action(lob: String? = null, unreadCount: String? = null, data: String? = null)

}