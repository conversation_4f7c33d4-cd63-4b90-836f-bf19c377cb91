package com.mmt.hotel.bookingreview.viewmodel

import com.mmt.hotel.bookingreview.model.response.BookingAlerts
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

/**
 * Created by <PERSON><PERSON><PERSON> on 15/04/21.
 */

class CampaignAlertViewModel(val alert : BookingAlerts) : DiffUtilRecycleItem{

    var text = alert.text

    val hideCrossIcon = true

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CAMPAIGN_ALERT
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CAMPAIGN_ALERT
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as <PERSON>AlertViewModel).alert
        return alert == matchedWith
    }

    override fun cardName(): String {
        return "Review Price Detail"
    }
}