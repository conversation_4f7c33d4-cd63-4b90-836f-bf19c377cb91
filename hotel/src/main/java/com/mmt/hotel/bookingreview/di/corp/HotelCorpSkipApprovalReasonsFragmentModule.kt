package com.mmt.hotel.bookingreview.di.corp

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.corp.CorpSkipApprovalReasonsData
import com.mmt.hotel.bookingreview.ui.corp.HotelCorpSkipApprovalReasonsFragment
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpSkipApprovalReasonsViewModel
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

/**
 * model to generate dependencies required at SkipApproval reasons screen
 *
 * create by Varun Airon on 02/02/22
 */

/**
 * This class is created to provide hilt dependency in @HotelCorpSkipApprovalReasonsFragment in case of QuickReviewActivity
 * will delete this when HotelBookingReview will be migrated to hilt
 */

@Module
@InstallIn(FragmentComponent::class)
open class HotelCorpSkipApprovalReasonsFragmentModule {
    @Provides
    @IntoMap
    @ViewModelKey(HotelCorpSkipApprovalReasonsViewModel::class)
    fun provideSkipApprovalReasonsViewModel(skipApprovalReasonsViewModel: HotelCorpSkipApprovalReasonsViewModel): HotelViewModel {
        return skipApprovalReasonsViewModel
    }

    @Provides
    fun provideCorpSkipApprovalReasonsData(fragment: Fragment): CorpSkipApprovalReasonsData {
        if(fragment is HotelCorpSkipApprovalReasonsFragment) {
            return fragment.arguments?.getParcelable(HotelCorpSkipApprovalReasonsFragment.BUNDLE_KEY_SKIP_APPROVAL_DATA) ?: CorpSkipApprovalReasonsData()
        }
        return CorpSkipApprovalReasonsData()
    }
}