package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CorpApprovalInfo(val approvalRequired: Boolean?,
                            val blockOopBooking: Int?,
                            val blockSkipApproval: Int?,
                            val withinPolicy: Boolean?,
                            val failureReasons: List<String>?) : Parcelable