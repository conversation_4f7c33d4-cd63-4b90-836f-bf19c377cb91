package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent.REMOVE_CO_TRAVELLER

class CoTravellerSelectedItemViewModel(var eventStream: MutableLiveData<HotelEvent>, val coTraveller: CoTraveller) {
    fun getName(): String{
        return coTraveller.first_name+" "+coTraveller.last_name
    }

    fun onRemoveClick(){
        eventStream.value = HotelEvent(REMOVE_CO_TRAVELLER, this)
    }
}