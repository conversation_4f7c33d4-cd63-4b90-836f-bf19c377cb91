package com.mmt.hotel.bookingreview.ui.corp

import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.widget.Toast
import androidx.fragment.app.FragmentResultListener
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.ActivityResultLifeCycleObserver
import com.mmt.core.extensions.OnActivityResult
import com.mmt.core.util.executeIfCast
import com.mmt.data.model.network.NetworkUtil
import com.mmt.data.model.util.AppCommonConstants
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.ADD_CO_TRAVELLER_CAPACITY_REACHED
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.APPROVAL_API_ERROR
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.BACK_TO_HOME_BIZ
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.CTA_SHOWN_TRACKING
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.INITIATE_CHECKOUT_WITH_SKIP_REASON
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_ADD_GUEST_CORP_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_CORP_APPROVAL_PAGE
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_CORP_HOTEL_BOOKING_APPROVAL_REQUEST_SENT_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_CORP_MESSAGE_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_REQUEST_APPROVAL
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE_WITH_EMAIL
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_TRAVELLER_SEARCH_BOTTOMSHEET_TO_SELECT_EMPLOYEE
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_SKIP_APPROVAL_REASONS_SCREEN
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.REQUEST_CORP_APPROVAL_API
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.SKIP_APPROVAL_BUTTON_CLICKED_FROM_REQUEST_APPROVAL_SCREEN
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent.APPROVAL_REQUEST_CONFIRMATION
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerSuccessFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpApprovalRequestFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpSkipApprovalReasonsData
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.response.CorpAutoBookRequestorConfig
import com.mmt.hotel.bookingreview.model.response.PostApprovalResponseV2
import com.mmt.hotel.bookingreview.ui.HotelBookingReviewActivity
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpBookingReviewActivityViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.extensions.setFragmentResultListener
import com.mmt.hotel.common.model.HotelError
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.compose.review.helper.ICorpReviewFragment
import com.mmt.hotel.compose.review.ui.fragment.HotelBookingReviewFragmentV2
import com.mmt.hotel.compose.review.ui.fragment.HotelCorpBookingReviewFragmentV2
import com.mmt.hotel.compose.review.ui.fragment.HotelCorpBookingReviewFragmentV2.Companion.OPEN_SEARCH_TRAVELLER_FRAGMENT
import com.mmt.hotel.compose.review.ui.fragment.HotelCorpBookingReviewFragmentV2.Companion.OPEN_SEARCH_TRAVELLER_FRAGMENT_FOR_PRIMARY
import com.mmt.hotel.compose.review.ui.fragment.HotelCorpBookingReviewFragmentV2.Companion.SEARCH_RESULT
import com.mmt.hotel.corpapproval.model.CorpApprovalMessageData
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.corpapproval.ui.HotelCorpApprovalMessageFragment
import com.mmt.hotel.deeplink.helper.HotelDeepLinkIntentHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers

@AndroidEntryPoint
class HotelCorpBookingReviewActivity : HotelBookingReviewActivity(),  OnActivityResult {

    private val fragmentResultListener = FragmentResultListener { requestCode, data ->
        val travellerDetail = data.getParcelable(SEARCH_RESULT) as? CorpTravellerDetail
        val fragment = getReviewFragment() as ICorpReviewFragment?

        if (travellerDetail != null) {
            when (requestCode) {
                OPEN_SEARCH_TRAVELLER_FRAGMENT -> {
                    fragment?.showCoTraveller(travellerDetail)
                    handleEditTravellerIfPresent(travellerDetail)
                }
                OPEN_SEARCH_TRAVELLER_FRAGMENT_FOR_PRIMARY -> {
                    fragment?.showPrimaryTraveller(travellerDetail)
                    handleEditTravellerIfPresent(travellerDetail)
                }
            }
        }
    }

    companion object {
        const val PRIMARY_TRAVELLER = "PRIMARY_TRAVELLER"
        const val PRIMARY_TRAVELLER_EMAIL_ID = "PRIMARY_TRAVELLER_EMAIL_ID"
        const val TRAVELLING_EMPLOYEE_LIST = "TRAVELLING_EMPLOYEE_LIST"
    }
    override fun createViewModel(): HotelCorpBookingReviewActivityViewModel {
        return getViewModel<HotelCorpBookingReviewActivityViewModel>(factory)
    }

    private var observer : ActivityResultLifeCycleObserver? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        observer = ActivityResultLifeCycleObserver(activityResultRegistry,this, activityID)
        lifecycle.addObserver(observer!!)
        initFragmentResultListener()
    }

    private fun initFragmentResultListener() {
        supportFragmentManager.setFragmentResultListener(
            listOf(
                OPEN_SEARCH_TRAVELLER_FRAGMENT, OPEN_SEARCH_TRAVELLER_FRAGMENT_FOR_PRIMARY
            ), lifecycleOwner = this, fragmentResultListener
        )
    }

    fun showCorpCoTravellerAndOpenSuccessFragment(fragment:ICorpReviewFragment?, travellerDetail: CorpTravellerDetail){
        fragment?.showCoTraveller(travellerDetail)
        handleEditTravellerIfPresent(travellerDetail)
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            OPEN_TRAVELLER_SEARCH_BOTTOMSHEET_TO_SELECT_EMPLOYEE -> {
                event.data.executeIfCast<ArrayList<CorpTravellerDetail>> {
                    val bundle = Bundle()
                    val travellers = event.data as ArrayList<CorpTravellerDetail>
                    val travellersList:ArrayList<CorpTravellerDetail> = arrayListOf()
                    travellersList.addAll(travellers)
                    val primaryTraveller = viewModel.getCorpPrimaryTraveller()
                    if(primaryTraveller != null) travellersList.add(primaryTraveller)
                    bundle.putParcelableArrayList(TRAVELLING_EMPLOYEE_LIST, travellersList)
                    bundle.putParcelableArrayList(CorpEmployeeSearchBottomSheetFragment.TAG, travellersList)
                    val fragInstance = CorpEmployeeSearchBottomSheetFragment.newInstance(bundle)
                    fragInstance.setRequestCode(OPEN_SEARCH_TRAVELLER_FRAGMENT)
                    fragInstance.show(supportFragmentManager, CorpEmployeeSearchBottomSheetFragment.TAG)

                }
            }
            OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE -> {
                val bundle = Bundle()
                bundle.putBoolean(PRIMARY_TRAVELLER, true)
                val fragInstance = CorpEmployeeSearchBottomSheetFragment.newInstance(bundle)
                fragInstance.setRequestCode(OPEN_SEARCH_TRAVELLER_FRAGMENT_FOR_PRIMARY)
                fragInstance.show(supportFragmentManager, CorpEmployeeSearchBottomSheetFragment.TAG)
            }
            OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE_WITH_EMAIL -> {
                val bundle = Bundle()
                bundle.putBoolean(PRIMARY_TRAVELLER, true)
                bundle.putString(PRIMARY_TRAVELLER_EMAIL_ID, event.data as String)
                val fragInstance = CorpEmployeeSearchBottomSheetFragment.newInstance(bundle)
                fragInstance.setRequestCode(OPEN_SEARCH_TRAVELLER_FRAGMENT_FOR_PRIMARY)
                fragInstance.show(supportFragmentManager, CorpEmployeeSearchBottomSheetFragment.TAG)
            }
            ADD_CO_TRAVELLER_CAPACITY_REACHED -> {
                /* this is not final as yet. needs to be confirmed */
                val message: CharSequence = "only ${event.data} extra adults can be added"
                Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
            }
            CTA_SHOWN_TRACKING -> {
                viewModel.trackB2BCTAShown(event.data as? String)
            }
            APPROVAL_REQUEST_CONFIRMATION -> {
                event.data.executeIfCast<CorpAutoBookRequestorConfig> {
                    hideLoader()
                    openApprovalRequestConfirmation(this)
                }
            }
            OPEN_CORP_HOTEL_BOOKING_APPROVAL_REQUEST_SENT_FRAGMENT -> {
                hideLoader()
                openCorpHtlBookingApprovalRequestSentFragment(event.data as? String)
            }
            BACK_TO_HOME_BIZ -> {
                onBackToHomeBizClicked()
            }
            OPEN_REQUEST_APPROVAL -> {
                openRequestApproval(event.data as CorpApprovalRequestFragmentData?)
            }
            REQUEST_CORP_APPROVAL_API -> {
                event.data.executeIfCast<MutableMap<String, String>> {
                    viewModel.reasonsForTravel = this
                    sendApprovalRequest()
                }
            }
            APPROVAL_API_ERROR -> {
                hideLoader()
                viewModel.handleApprovalsError(event.data as? PostApprovalResponseV2)
            }
            HotelBookingReviewActivityEvent.OPEN_ERROR_FRAGMENT -> {
                event.data.executeIfCast<HotelError> {
                    openErrorFragment(this)
                }
            }
            SKIP_APPROVAL_BUTTON_CLICKED_FROM_REQUEST_APPROVAL_SCREEN -> {
                val data = event.data as? CorpSkipApprovalReasonsData
                if (data != null) {
                    getCorpBookingReviewFragment()?.bottomSheetNavigator?.navigateTo(event)
                } else {
                    proceedWithCheckOut()
                }
            }

            OPEN_SKIP_APPROVAL_REASONS_SCREEN -> {
                getCorpBookingReviewFragment()?.bottomSheetNavigator?.navigateTo(event)
            }

            INITIATE_CHECKOUT_WITH_SKIP_REASON -> {
                proceedWithCheckOut(event.data as? CorpReasons)
                /*showLoader()
                viewModel.proceedCheckout(event.data as? CorpReasons)*/
            }

            OPEN_CORP_APPROVAL_PAGE -> {
                event.data.executeIfCast<String?> {
                    openCorpApprovalPage(this)
                }
            }
            OPEN_CORP_MESSAGE_FRAGMENT -> {
                hideLoader()
                event.data.executeIfCast<CorpApprovalMessageData> {
                    showMessageDialog(this)
                }
            }
            OPEN_ADD_GUEST_CORP_CO_TRAVELLER,  CorpEmployeeSearchActivityEvent.EDIT_TRAVELLER -> {
                openAddGuestCorpCoTravellerFragment(event.data as Parcelable?)
            }

            CorpEmployeeSearchActivityEvent.TRAVELLER_DETAILS_ADDED -> {
                val data = event.data as CorpAddEditTravellerSuccessFragmentData?
                val travellerDetail = data?.travellerDetail
                val fragment = getReviewFragment() as ICorpReviewFragment?
                if (travellerDetail != null) {
                    showCorpCoTravellerAndOpenSuccessFragment(fragment, travellerDetail)
                }
            }
            HotelCorpBookingReviewActivityEvent.ADD_TO_ITINIRARY_FLOW -> {
                event.data.executeIfCast<String> {
                    addToItinerary(this)
                }
            }
            HotelBookingReviewActivityEvent.OPEN_DEEPLINK -> {
                openDeeplink(event.data as String)
            }
            HotelCorpBookingReviewActivityEvent.INITIATE_ITINERARY_FLOW -> {
                val skipReasons = event.data as CorpReasons?
                viewModel.skipReason = skipReasons
                initItineraryFlow(skipReasons)
            }

            else -> super.handleEvents(event)
        }
    }

    override fun initItineraryFlow(reasons: CorpReasons?) {
        getCorpBookingReviewFragment()?.initItineraryFlow(reasons, viewModel.checkDuplicateBooking)
    }

    override fun sendApprovalRequest() {
        showLoader()
        (viewModel as? HotelCorpBookingReviewActivityViewModel)?.approvalPostRequest()
    }

    private fun getCorpBookingReviewFragment(): HotelCorpBookingReviewFragmentV2? {
        return supportFragmentManager.findFragmentByTag(HotelBookingReviewFragmentV2.TAG) as? HotelCorpBookingReviewFragmentV2
    }

    private fun openDeeplink(deepLinkUrl: String) {
        viewModel.runOnViewModelLaunch(Dispatchers.Main) {
            HotelDeepLinkIntentHelper().getDeeplinkIntent(this, deepLinkUrl, Intent.ACTION_VIEW)?.apply {
                <EMAIL>(this)
                finish()
            }
        }
    }

    private fun addToItinerary(requisitionId : String) {
        //mmyt://corporate/travelInfo?requisitionId='reqId'
        val itineraryUrl = NetworkUtil.getCompleteUrlForGetRequest(AppCommonConstants.DEEP_LINK_CREATE_REQUISITION_REQUEST, mapOf(CoreConstants.REQUISITION_ID to requisitionId))
        HotelMigratorHelper.instance.openDeepLink(itineraryUrl, false, this)
    }

    private fun openAddGuestCorpCoTravellerFragment(data: Parcelable?){
        val bundle = Bundle()
        bundle.putParcelable(CorpAddEditTravellerBottomsheetFragment.TAG, data)
        val fragInstance = CorpAddEditTravellerBottomsheetFragment.newInstance(bundle)
        fragInstance.show(supportFragmentManager, CorpAddEditTravellerBottomsheetFragment.TAG)
    }

    private fun showMessageDialog(data:CorpApprovalMessageData) {
        HotelCorpApprovalMessageFragment.getInstance(data).show(
            supportFragmentManager, HotelCorpApprovalMessageFragment.TAG
        )
    }

    private fun openCorpApprovalPage(workFlowId: String?) {
        workFlowId?.let {
            val intent = HotelScreenIntentUtil.getCorpApprovalIntent()
            intent.putExtra(HotelConstants.APPROVAL_ID, it)
            startActivity(intent)
        }
    }

    override fun openBookingReviewFragment() {
        val fragInstance = HotelCorpBookingReviewFragmentV2.newInstance(intent?.extras ?: Bundle())
        supportFragmentManager.beginTransaction()
            .replace(R.id.container, fragInstance, HotelBookingReviewFragmentV2.TAG)
            .addToBackStack(HotelBookingReviewFragmentV2.TAG)
            .commitAllowingStateLoss()
        supportFragmentManager.executePendingTransactions()
    }

    /**
     * Open Approval Request Sent Fragment
     */

    private fun openCorpHtlBookingApprovalRequestSentFragment(requisitionId: String?) {
        val fragInstance = CorpHtlBookingApprovalRequestSentFragment.newInstance(requisitionId)
        replaceChildFragment(fragInstance, CorpHtlBookingApprovalRequestSentFragment.TAG)
    }

    /**
     *   open the manager request approval fragemnt
     */
    private fun openRequestApproval(data: CorpApprovalRequestFragmentData?) {
        val bundle = Bundle()
        bundle.putParcelable(HotelCorpRequestApprovalReviewFragment.TAG, data)
        val requestApprovalFragment = HotelCorpRequestApprovalReviewFragment.newInstance(bundle)
        addBottomFragment(requestApprovalFragment, HotelCorpRequestApprovalReviewFragment.TAG)
    }

    /**
     *   Open Approval Request Confirmation BottomUp Fragment
     */
    private fun openApprovalRequestConfirmation(data: CorpAutoBookRequestorConfig) {
        val bundle = Bundle()
        bundle.putParcelable(HotelCorpApprovalRequestReconfirmationFragment.TAG, data)
        HotelCorpApprovalRequestReconfirmationFragment.newInstance(bundle).show(supportFragmentManager,HotelCorpApprovalRequestReconfirmationFragment.TAG)
    }

    /**
     *   Sent Back to Home Screen of Biz
     */
    private fun onBackToHomeBizClicked() {
        MMTCore.coreInterface.launchHomeWithFlags(this, launchflags = arrayListOf(Intent.FLAG_ACTIVITY_CLEAR_TOP, Intent.FLAG_ACTIVITY_SINGLE_TOP), isFinish = true)
    }

    private fun handleEditTravellerIfPresent(travellerDetail: CorpTravellerDetail) {
        if(travellerDetail.travellerType.isNotEmpty()) {
            openAddEditTravellerSuccessFragment(CorpAddEditTravellerSuccessFragmentData(travellerDetail = travellerDetail))
        }
    }

    /**
     *  Open Add Edit Traveller Successfully
     */

    private fun openAddEditTravellerSuccessFragment(data: CorpAddEditTravellerSuccessFragmentData?) {
        val bundle = Bundle()
        bundle.putParcelable(CorpAddEditTravellerSuccessFragment.TAG, data)
        val addEditTravellerSuccessFragment = CorpAddEditTravellerSuccessFragment.newInstance(bundle)
        addBottomFragment(addEditTravellerSuccessFragment, CorpAddEditTravellerSuccessFragment.TAG)
    }

}