package com.mmt.hotel.bookingreview.model.response.price

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.detail.model.response.CouponPersuasion
import kotlinx.parcelize.Parcelize

@Parcelize
data class PriceItem(val amount: Double,
                     val label: String,
                     val key: String,
                     val type: String,
                     val subLine: String?,
                     val hotelierCurrencyAmount: Double?,
                     val hotelierCurrencyCode: String?,
                     @SerializedName("breakup")
                     val details: List<PriceItem>?,
                     val couponPersuasion: CouponPersuasion? = null) : Parcelable


@Parcelize
data class PriceUiItemWithCurrency(val basePriceWithTax: String,
                                   val discountedPrice: String,
                                   val taxes: String,
                                   val flexiAddOnPrice: String,
                                   val discountedPriceWithFlexi: String,
                                   val totalPriceWithFlexi: String) : Parcelable