package com.mmt.hotel.bookingreview.model.response.additional

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.SPACE
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.hotel.R
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelAdditionalFeesBreakUp(val type: String,
                                      val amount: Double,
                                      val currency: String,
                                      val title: String?,
                                      val subTitle: String?,
                                      @SerializedName("infoText")
                                      val description: String?,
                                      val categoryDesc: String?,
                                      val amountDesc: String?,
                                      val amountSubText: String?,
                                      val hotelierCurrency: String?,
                                      val hotelierCurrencyAmount: Double?,
                                      val footerText: String?,
                                      @SerializedName("chargesMsgV2")
                                      val chargesMsg: String?,
                                      val inclusions: List<String>?,
                                      val adultAmountSubText: String?,
                                      val childAmountSubText: String?) : Parcelable {

    fun getCombinedAmountSubText() : String?{
        return if(adultAmountSubText.isNotNullAndEmpty() && childAmountSubText.isNotNullAndEmpty()){
            adultAmountSubText.plus("\n").plus(childAmountSubText)
        }else if(childAmountSubText.isNotNullAndEmpty()){
            childAmountSubText
        } else {
            adultAmountSubText
        }
    }

    fun showInfoIcon(): Boolean {
        return description?.isNotEmpty() == true || inclusions?.isNotEmpty() == true
    }

    fun getAmountString() : String {
        return amount.let {
            var hotelierAmount = CoreConstants.EMPTY_STRING
            val showableCurrency = HotelCurrencyUtil.getCurrencySymbolOrDefault(hotelierCurrency)
            if (hotelierCurrencyAmount != null) {
                hotelierAmount = ResourceProvider.instance.getString(
                    R.string.htl_hotelier_amount,
                    showableCurrency,
                    StringUtil.getCommaSeparatedPrice(hotelierCurrencyAmount)
                )
            }
            HotelCurrencyUtil.getCurrencySymbolOrDefault(currency) + SPACE + StringUtil.getCommaSeparatedPrice(it) + SPACE + hotelierAmount
        }
    }

    fun getTitleText(): String {
        return if (title.isNullOrEmpty()) {
            ResourceProvider.instance.getString(R.string.htl_mandatory_fees)
        } else {
            title
        }
    }
}


