package com.mmt.hotel.bookingreview.tracking

import com.mmt.analytics.AnalyticsSDK
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.isNotNullAndEmpty
import com.mmt.hotel.analytics.pdtv2.HotelEventType
import com.mmt.hotel.analytics.pdtv2.HotelPdtEventBuilder
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Helper
import com.mmt.hotel.analytics.pdtv2.model.AddOnData
import com.mmt.hotel.analytics.pdtv2.model.Catalog
import com.mmt.hotel.analytics.pdtv2.model.Category
import com.mmt.hotel.analytics.pdtv2.model.Coupon
import com.mmt.hotel.analytics.pdtv2.model.Discounts
import com.mmt.hotel.analytics.pdtv2.model.Occupancy
import com.mmt.hotel.analytics.pdtv2.model.Price
import com.mmt.hotel.analytics.pdtv2.model.ProductItem
import com.mmt.hotel.analytics.pdtv2.model.SpecialRequest
import com.mmt.hotel.analytics.pdtv2.model.SubCatagory
import com.mmt.hotel.analytics.pdtv2.model.TravellerInfo
import com.mmt.hotel.bookingreview.helper.HotelBookingReviewHelper
import com.mmt.hotel.bookingreview.model.BookingReviewData
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.common.model.response.HotelApiError
import com.mmt.hotel.old.hotelreview.model.request.checkout.SpecialCheckoutRequest
import com.gommt.logger.LogUtils
import com.mmt.hotel.analytics.pdtv2.model.ContentDetailItem
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtErrorListItem
import com.mmt.hotel.analytics.pdtv2.model.Position
import com.mmt.hotel.bookingreview.model.TravellerDetailV2
import com.mmt.hotel.compose.review.helper.InternationalRoamingCardEncodingHelper.getEncodedValue
import com.pdt.eagleEye.constants.EventName
import com.pdt.eagleEye.constants.EventType
import com.pdt.eagleEye.models.ErrorDetailList
import javax.inject.Inject

class HotelBookingReviewPdtV2Tracker @Inject constructor(val bookingReviewHelper: HotelBookingReviewHelper) {

    companion object {
        const val TAG = "HotelBookingReviewPdtV2Tracker"
    }

    fun trackPageEnter(loadedItems: List<String>? = null) {
        try {
            bookingReviewHelper.getBookingReviewData()?.let {
                val commonEventBuilder = createReviewPageLifecycleEvent(it,EventName.PAGE_ENTRY, loadedItems)
                val event = commonEventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }
        } catch (e: Exception) {
            LogUtils.error(TAG, "review pdt page load", e)
        }
    }

    private fun createReviewPageLifecycleEvent(
        bookingReviewData: BookingReviewData,
        eventName: String,
        items: List<String>?
    ): HotelPdtEventBuilder {
        val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
            eventName = eventName,
            eventType = EventType.LIFECYCLE,
            pageName = HotelPdtV2Constants.PageName.review.name,
            userSearchData = bookingReviewData.userSearchData,
            requestId = bookingReviewHelper.getRequestId(HotelPdtV2Constants.BackendApis.availRooms),
            baseTrackingData = bookingReviewData.hotelBaseTrackingData,
            funnelStep = HotelPdtV2Constants.FunnelStep.review,
            allRequestIds = bookingReviewHelper.getAllRequestIds()
        )
        commonEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(bookingReviewData.userSearchData))
        commonEventBuilder.productList(createProductList(bookingReviewHelper))
        items?.let {
            commonEventBuilder.contentDetails(createContentDetails(it))
        }
        return commonEventBuilder
    }

    private fun createContentDetails(cardList: List<String>): List<ContentDetailItem>? {
        val contentDetailsList = mutableListOf<ContentDetailItem>()
        cardList.forEachIndexed { index, cardId ->
            contentDetailsList.add(
                ContentDetailItem(
                    id = cardId,
                    type = CoreConstants.EMPTY_STRING,
                    position = Position(v = index)
                )
            )
        }
        return contentDetailsList.ifEmpty { null }
    }

    private fun createProductList(bookingReviewHelper: HotelBookingReviewHelper): List<ProductItem>? {
        return bookingReviewHelper.getBookingReviewData()?.let {
            var displayPrice = bookingReviewHelper.getPriceAfterDiscount()
            if (displayPrice == 0.0) {
                displayPrice = bookingReviewHelper.getTotalAmount()
            }
            val price = Price(
                sellingPrice = bookingReviewHelper.getTotalAmount(),
                basePrice = bookingReviewHelper.getBaseAmount(),
                totalDiscount = bookingReviewHelper.getDiscountAmount(),
                totalTax = bookingReviewHelper.getTaxAmount(),
                displayPrice = displayPrice,
                currency = bookingReviewHelper.getCurrency(),
                discounts = getDiscounts(bookingReviewHelper.getAppliedCoupon())
            )
            val catalogItems = bookingReviewHelper.getRatePlanListFromAvail()?.map {
                Catalog(
                    id = it.ratePlanCode.orEmpty(),
                    ratePlanCode = it.ratePlanCode.orEmpty(),
                    roomCode = it.roomCode.orEmpty(),
                    tariffOccupancy = it.roomTariff?.map {
                        Occupancy(it)
                    } ?: listOf()
                )
            }
            listOf(
                ProductItem(
                    id = it.userSearchData.hotelId,
                    price = price,
                    catalog = catalogItems,
                    specialRequestData = bookingReviewHelper.getSpecialCheckoutRequest()?.let {
                        createSpecialRequest(
                            it
                        )
                    }
                )
            )
        }
    }

    private fun getDiscounts(appliedCoupon: HotelBookingCoupon?): Discounts? {
        return appliedCoupon?.run {
            Discounts(
                listOf(
                    Coupon(
                        code = this.couponCode,
                        discount = this.amount,
                        isPreApplied = this.autoApplicable,
                        isApplied = true
                    )
                )
            )
        }
    }

    private fun createSpecialRequest(checkoutRequest: SpecialCheckoutRequest): SpecialRequest {
        return SpecialRequest(checkoutRequest.categories.map {
            Category(
                code = it.code,
                name = CoreConstants.EMPTY_STRING,
                subCategories = it.subCategories.map {
                    SubCatagory(
                        code = it.code,
                        name = CoreConstants.EMPTY_STRING,
                        value = it.values?.getOrNull(0).orEmpty()
                    )
                }
            )
        })
    }

    fun trackPageExit(navigation: String, visibleItems: List<String>? = null) {
        try {
            bookingReviewHelper.getBookingReviewData()?.let {
                val commonEventBuilder = createReviewPageLifecycleEvent(
                    it,
                    EventName.PAGE_EXIT,
                    visibleItems
                )
                commonEventBuilder.addOnDetails = getAddOnData(bookingReviewHelper)
                val event = commonEventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }
        } catch (e: Exception) {
            LogUtils.error(TAG, "review pdt page exit", e)
        }
    }

    private fun getAddOnData(bookingReviewHelper: HotelBookingReviewHelper): List<AddOnData>? {
        val availableAddOnList = bookingReviewHelper.getAddOnList()
        return bookingReviewHelper.getSelectedAddons().map { selectedAddOn ->
            val addOnData = availableAddOnList?.find { selectedAddOn.id == it.id }
            AddOnData(
                id = selectedAddOn.id,
                addOnType = selectedAddOn.addOnType,
                includedUnits = selectedAddOn.unitSelected.getTotalCount(),
                unitPrice = 0.0,
                unitType = CoreConstants.EMPTY_STRING,
                heading = CoreConstants.EMPTY_STRING,
                selected = true,
                available = true,
                isPreselected = addOnData?.autoSelect ?: false
            )
        }
    }

    fun trackError(api: HotelPdtV2Constants.BackendApis, apiError: HotelApiError, requestId: String) {
        try {
            bookingReviewHelper.getBookingReviewData()?.let { bookingReviewData->
                val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
                    eventName = EventName.API_STATUS,
                    eventType = HotelEventType.ERROR,
                    pageName = HotelPdtV2Constants.PageName.review.name,
                    userSearchData = bookingReviewData.userSearchData,
                    requestId = requestId,
                    baseTrackingData = bookingReviewData.hotelBaseTrackingData,
                    funnelStep = HotelPdtV2Constants.FunnelStep.review,
                    allRequestIds = bookingReviewHelper.getAllRequestIds()
                )
                commonEventBuilder.eventValue(api.name)
                commonEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(bookingReviewData.userSearchData))
                commonEventBuilder.productList(createProductList(bookingReviewHelper))
                commonEventBuilder.hotelErrorDetailList(
                    listOf(
                        HotelPdtErrorListItem(
                            code = apiError.code.orEmpty(),
                            message = apiError.message.orEmpty()
                        )
                    )
                )
                val event = commonEventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }
        } catch (e: Exception) {
            LogUtils.error(TAG, "review pdt page load", e)
        }
    }

    fun trackCallToBookClicked(version: Int) {
        try {
            bookingReviewHelper.getBookingReviewData()?.let {
                val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
                    eventName = EventName.BUTTON_CLICKED,
                    eventType = EventType.ACTION,
                    pageName = HotelPdtV2Constants.PageName.review.name,
                    userSearchData = it.userSearchData,
                    requestId = bookingReviewHelper.getRequestId(HotelPdtV2Constants.BackendApis.availRooms),
                    baseTrackingData = it.hotelBaseTrackingData,
                    funnelStep = HotelPdtV2Constants.FunnelStep.review,
                    allRequestIds = bookingReviewHelper.getAllRequestIds()
                )
                commonEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(it.userSearchData))
                commonEventBuilder.eventValue(
                    when (version) {
                        1 -> HotelPdtV2Constants.HotelEventValues.request_call_back_clicked_1.name
                        else -> HotelPdtV2Constants.HotelEventValues.request_call_back_clicked_2.name
                    }
                )
                val event = commonEventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }
        } catch (e: Exception) {
            LogUtils.error(TAG, "review page call to book clicked", e)
        }
    }

    fun trackInternationalRoamingConsentClicked(consentGiven: Boolean, travellerData: MutableList<TravellerDetailV2>) {
        try {
            bookingReviewHelper.getBookingReviewData()?.let {
                val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
                    eventName = EventName.BUTTON_CLICKED,
                    eventType = EventType.ACTION,
                    pageName = HotelPdtV2Constants.PageName.review.name,
                    userSearchData = it.userSearchData,
                    requestId = bookingReviewHelper.getRequestId(HotelPdtV2Constants.BackendApis.availRooms),
                    baseTrackingData = it.hotelBaseTrackingData,
                    funnelStep = HotelPdtV2Constants.FunnelStep.review,
                    allRequestIds = bookingReviewHelper.getAllRequestIds()
                )
                commonEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(it.userSearchData))
                getEncodedTravellerData(travellerData).let { travellerInfo ->
                    commonEventBuilder.travellerList(travellerInfo)
                }
                commonEventBuilder.eventValue(
                    if(consentGiven) {
                        HotelPdtV2Constants.HotelEventValues.intl_roaming_consent_given.name
                    } else {
                        HotelPdtV2Constants.HotelEventValues.intl_roaming_consent_removed.name
                    }
                )
                val event = commonEventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }
        } catch (e: Exception) {
            LogUtils.error(TAG, "review page call to book clicked", e)
        }
    }

    private fun getEncodedTravellerData(travellerData: MutableList<TravellerDetailV2>): List<TravellerInfo> {
        return travellerData.map {
            TravellerInfo(
                isPrimary = it.masterPax,
                emailId = getEncodedValue(it.emailID),
                fname = getEncodedValue(it.firstName),
                lname = getEncodedValue(it.lastName),
                title = it.title.orEmpty(),
                gstOpted = it.registerGstinNum.isNotNullAndEmpty(),
                mobileComId = CoreConstants.EMPTY_STRING,
                emailComId = CoreConstants.EMPTY_STRING,
                mobileNo = getEncodedValue(it.mobileNo.orEmpty()),
                gender = it.gender.orEmpty()
            )
        }
    }

}