package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.common.model.response.HotelApiError
import kotlinx.parcelize.Parcelize

@Parcelize
data class PayLaterEligibilityResponse(
    val response: TripMoneyBnplResponse?,
    val correlationKey: String?,
    val error: HotelApiError?
) : Parcelable

@Parcelize
data class TripMoneyBnplResponse(
    val eligible: Boolean?,
    val checkEligibility: Boolean?,
    @SerializedName("payLaterCard")
    val tripMoneyValidationData: TripMoneyBnplData?,
    @SerializedName("currency")
    val currencyCode: String?
) : Parcelable

@Parcelize
data class TripMoneyBnplData(
    val logo: String?,
    val title: String?,
    val subTitle: String?,
    val titleIcon: String?,
    val ctaText: String?,
    val desc: String?,
    @SerializedName("successCard")
    val tripMoneyBnplSuccessCard: TripMoneyBnplSuccessCard?
) : Parcelable

@Parcelize
data class TripMoneyBnplSuccessCard(
    val amount: Double?,
    val currency: String?,
    val title: String?,
    val logo: String?,
) : Parcelable
