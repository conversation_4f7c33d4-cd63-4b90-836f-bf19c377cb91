package com.mmt.hotel.bookingreview.navigationGraph

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Column
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.analytics.pdtMetrics.HotelScreenMetricsTracker
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.bookingreview.dataModel.RoomDetailNavigationData
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RoomDetailUiDataModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.compose.base.HotelComposeToolBar
import com.mmt.hotel.compose.noCostEmi.ui.components.NoCostEmiDetailScreenUI
import com.mmt.hotel.compose.noCostEmi.viewModel.NoCostEMIVM
import com.mmt.hotel.compose.review.ui.components.HtlBnpl0ProgressBarLoaderScreen
import com.mmt.hotel.compose.review.ui.screens.BnplCancelBookingScreen
import com.mmt.hotel.compose.review.ui.screens.ReviewScreen
import com.mmt.hotel.compose.review.viewModel.ReviewFragmentViewModelV2
import com.mmt.hotel.filterV4.navigation.FilterScreenV4MainCompose
import com.mmt.hotel.listingV2.model.bundleModel.FilterScreenV4NavData
import com.mmt.hotel.selectRoom.compose.ComboAndRoomDetailScreen
import com.mmt.hotel.selectRoom.event.ShowRoomDetailEventData
import com.mmt.hotel.selectRoom.viewmodel.ComboAndRoomDetailViewModelV2
import com.mmt.hotel.selectRoom.viewmodel.RatePlanDetailViewModelV2

@Composable
fun ReviewBookingNavigationGraph(
    navController: NavHostController,
    viewModel: ReviewFragmentViewModelV2,
    noCostEMIViewModel: NoCostEMIVM,
    blurView: MutableState<Boolean>,
    ratePlanDetailViewModel: RatePlanDetailViewModelV2,
    comboAndRoomDetailViewModel: ComboAndRoomDetailViewModelV2,
    screenMetricsTracker: HotelScreenMetricsTracker
) {
    NavHost(
        navController = navController,
        startDestination = ReviewBookingDestination.REVIEW_BOOKING.name,
        enterTransition = {
            slideIntoContainer(
                animationSpec = tween(250, easing = LinearEasing),
                towards = AnimatedContentTransitionScope.SlideDirection.Start
            )
        },
        exitTransition = {
            slideOutOfContainer(
                animationSpec = tween(250, easing = LinearEasing),
                towards = AnimatedContentTransitionScope.SlideDirection.Start
            )
        },
        popEnterTransition = {
            slideIntoContainer(
                animationSpec = tween(250, easing = LinearEasing),
                towards = AnimatedContentTransitionScope.SlideDirection.End
            )
        },
        popExitTransition = {
            slideOutOfContainer(
                animationSpec = tween(250, easing = LinearEasing),
                towards = AnimatedContentTransitionScope.SlideDirection.End
            )
        }
    ) {
        composable(route = ReviewBookingDestination.REVIEW_BOOKING.name) {
            Column {
                if(viewModel.screenUIDataModel.showToolbar.value){
                    HotelComposeToolBar(getReviewToolBar(viewModel), blurView)
                }
                ReviewScreen(
                    viewModel = viewModel,
                    blurView = blurView,
                    navController = navController,
                    screenMetricsTracker = screenMetricsTracker
                )
            }
        }

        composable(route = ReviewBookingDestination.ROOM_DETAIL.name + "/{navData}",
            arguments = listOf(navArgument("navData") { type = NavType.StringType })
        ) { entry ->

            val data = Gson().fromJson(entry.arguments?.getString("navData"), RoomDetailNavigationData::class.java)

            if (data != null) {
                val roomDetailEventData = ShowRoomDetailEventData(
                    hotelID = data.hotelId,
                    roomCode = data.roomCode,
                    ratePlanCode = data.ratePlanCode,
                    txnKey = viewModel.screenUIDataModel.txnKey.value,
                    showRatePlanDetail = true,
                    countryCode = viewModel.getDataWrapper().userSearchData?.countryCode
                        ?: HotelConstants.COUNTRY_CODE_UNKNOWN,
                    isEntireProperty = viewModel.getDataWrapper().isEntireProperty()
                )
                ComboAndRoomDetailScreen(
                    navController = navController,
                    comboDetailData = null,
                    roomDetailData = roomDetailEventData,
                    handleEvent = { event -> viewModel.screenUIDataModel.eventStream.postValue(event) },
                    ratePlanDetailViewModel = ratePlanDetailViewModel,
                    title = ResourceProvider.instance.getString(R.string.htl_label_room_detail),
                    isComboScreen = false,
                    comboAndRoomDetailViewModelV2 = comboAndRoomDetailViewModel
                )
            }
        }

        composable(route = ReviewBookingDestination.NO_COST_EMI.name) {
            NoCostEmiDetailScreenUI(requestData = viewModel.getNoCostEmiData(), viewModel = noCostEMIViewModel, {
                navController.popBackStack(navController.graph.startDestinationId, false)
            },{

            })
        }

    }
}

private fun getReviewToolBar(
    viewModel: ReviewFragmentViewModelV2
): HotelToolBarViewModel {

    return object : HotelToolBarViewModel() {
        override fun getTitle(): String {
            return viewModel.getTitle()
        }

        override fun onHandleBackPress() {
            viewModel.onHandleBackPress()
        }

        override fun isBottomSheetToolBar(): Boolean {
            return viewModel.isBottomSheetToolBar()
        }

        override fun showCrossIcon(): Boolean {
            return viewModel.showCrossIcon()
        }
    }
}