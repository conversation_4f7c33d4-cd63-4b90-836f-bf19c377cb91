package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.databinding.BaseObservable
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.R
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.helper.CorpTravelDetailFormHelper
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagFieldV2
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagValuesWithGSTV2

open class HotelCorpTravelDetailFormItemViewModelV2(private val corpTripTagData: CorpTripTagFieldV2,
                                                    private  val eventStream: MutableLiveData<HotelEvent>?): BaseObservable() {

    val showErrorObservable = ObservableBoolean(false)
    val showErrorState = mutableStateOf(false)
    var optionsViewModels=  HashMap<String, CorpTravelDetailOptionViewModelV2>()

    fun onValueSelected(value: Any, checked: Boolean) {
        val possibleValueSet = HashSet<String>()
        val selectedValue = getSelectedValueFromTripTag(value)
        if (selectedValue.isEmpty()) {
            return
        }
        if (!CorpTravelDetailFormHelper.ItemTypes.RADIO_GROUP.type.equals(corpTripTagData.attributeType, true)) {
            corpTripTagData.attributeSelectedValue?.let { allValues -> possibleValueSet.addAll(allValues) }
            if (checked) {
                optionsViewModels[value]?.itemSelectedState?.value =true
                possibleValueSet.add(selectedValue)
            } else {
                optionsViewModels[value]?.itemSelectedState?.value =false
                possibleValueSet.remove(selectedValue)
            }
            corpTripTagData.attributeSelectedValue = ArrayList(possibleValueSet)
        } else {
            if (checked) {
                possibleValueSet.add(selectedValue)
                optionsViewModels.forEach{
                    it.value.itemSelected.set( it.key == selectedValue )
                    it.value.itemSelectedState.value =it.key == selectedValue
                }
                corpTripTagData.attributeSelectedValue = ArrayList(possibleValueSet)
                corpTripTagData.possibleValuesAndGST?.let { items ->
                    items.forEach {
                        if( it.value == selectedValue && corpTripTagData.gstBasedTripTag == true ) {
                            eventStream?.postValue(HotelEvent(HotelCorpBookingReviewFragmentEvent.UPDATE_GST_BASED_ON_TRIP_TAG, it.gstDetails))
                        }
                    }
                }
            }
        }
        showErrorObservable.set(false)
        showErrorState.value = false

    }

    fun getAttributeId(): String {
        return corpTripTagData.attributeId
    }

    private fun getSelectedValueFromTripTag(selectedItem: Any): String {
        return when (selectedItem) {
            is String -> selectedItem
            is CorpTripTagValuesWithGSTV2 -> selectedItem.value
            else -> CoreConstants.EMPTY_STRING
        } as String
    }

    fun getHeading(): SpannableString {
        var heading = corpTripTagData.attributeName
        if (corpTripTagData.mandatoryCheck) {
            heading = "$heading *"
        }
        val spanableString = SpannableString(heading)
        val indexOfStar = heading.indexOf("*")
        if (indexOfStar != -1) {
            spanableString.setSpan(ForegroundColorSpan(ResourceProvider.instance.getColor(R.color.color_e02020)),
                    indexOfStar, indexOfStar + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return spanableString
    }



    @Composable
    fun getAnnotatedHeading(): AnnotatedString {
     return   buildAnnotatedString {
            append(corpTripTagData.attributeName)
            if (corpTripTagData.mandatoryCheck) {
                append(" ")
                withStyle(
                    style = SpanStyle(color = colorResource(id = R.color.color_e02020))
                ) {
                    append("*")
                }
            }
        }
    }

    fun getDesc(): String? {
        return corpTripTagData.attributeDescription
    }
}