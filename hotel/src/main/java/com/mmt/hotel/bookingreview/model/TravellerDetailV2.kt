package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class TravellerDetailV2(
    val firstName: String,
    val lastName: String,
    val masterPax: Boolean,
    val paxType: String,
    val nationality: String? = null,
    val title: String? = null,
    val emailID: String? = null,
    val isdCode: String? = null,
    val mobileNo: String? = null,

    val panCard: String? = null,

    val gender: String? = null,
    val travellerId: Int? = null,
    val saveGstDetails: Boolean? = null,
    val state: String? = null,
    val registerGstinNum: String? = null,
    val gstinCompanyName: String? = null,
    val gstinCompanyAddress: String? = null,
    val roomNo: Int? = null,
    val saveTravellerDetails: Boolean = false,
    val guestTraveller : Boolean? = null
) : Parcelable