package com.mmt.hotel.bookingreview.tracking

import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.hotel.base.tracking.HotelBaseOmnitureTrackingHelper
import com.mmt.hotel.bookingreview.model.PahIntentData
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil
import javax.inject.Inject

class HotelPahPayTrackingHelper @Inject constructor(val pahData: PahIntentData) :HotelBaseOmnitureTrackingHelper(){

    companion object {
        const val PAH_MODEL = "PAHModel"
        const val PAH_DIRECT_SHOWN = "PAH_Loader_Shown"
    }

    /* Base Methods start */
    private fun track(eventMap: MutableMap<String, Any?>) {
        OmnitureTrackingHelper.trackAppState(getScreenEvent(pahData.userSearchData), eventMap)
    }

    fun pageLoadTrack() {
        val userSearchData = pahData.userSearchData
        val eventMap: MutableMap<String, Any?> = commonOmnitureEvent(userSearchData)
        eventMap[OmnitureTrackingHelper.OEPK_V_20] = userSearchData.occupancyData.roomCount ?: 0
        eventMap[OmnitureTrackingHelper.OEPK_V_21] = userSearchData.occupancyData.adultCount
        eventMap[OmnitureTrackingHelper.OEPK_V_23] = userSearchData.occupancyData.childAges.size
        eventMap[OmnitureTrackingHelper.OEPK_V_73] = HotelUtil.getNights(userSearchData.checkInDate, userSearchData.checkOutDate)
        eventMap[OmnitureTrackingHelper.OEPK_V_40] = userSearchData.hotelName + "-" + userSearchData.locationName
        eventMap[OmnitureTrackingHelper.OEPK_m52] = PAH_MODEL
        eventMap[OmnitureTrackingHelper.OPEK_Products] = OmnitureTrackingHelper.OPEK_Products+"-"+userSearchData.hotelId
        track(eventMap)
        trackClicks(PAH_DIRECT_SHOWN)
    }

    fun trackClicks(event: String) {
        val eventMap: MutableMap<String, Any?> = HashMap()
        eventMap[OmnitureTrackingHelper.OEPK_C_54] = event
        track(eventMap)
    }

    fun trackSmsAutoFetchFail(){
        trackClicks("Autofetch fail")
    }

    fun trackOtpSomethingWentWrong(){
        trackClicks("Otp:something went wrong")
    }

    fun trackOtpGenerationFail(){
        trackClicks("Otp generation failed")
    }

    fun trackInvalidOtp(){
        trackClicks("Invalid otp")
    }

    fun trackSendOtp(){
        trackClicks("Send_otp_clicked")
    }

    fun trackResendOtp(){
        trackClicks("Resend_otp_clicked")
    }

    fun trackValidateOtp(){
        trackClicks("OTP_confirm_clicked")
    }

    override fun getScreenEvent(userSearchData: UserSearchData): String {
        return HotelUtil.getPageNameEvent(HotelConstants.PAGE_NAME_PAH_PAY, userSearchData.countryCode, userSearchData.funnelSrc).value
    }
}