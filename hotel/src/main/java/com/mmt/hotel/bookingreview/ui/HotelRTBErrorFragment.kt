package com.mmt.hotel.bookingreview.ui

import android.os.Bundle
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomUpErrorFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.di.HotelErrorFragmentModule
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_MYTRIPS_DEEPLINK
import com.mmt.hotel.common.model.HotelError
import com.mmt.hotel.bookingreview.viewmodel.HotelRTBErrorViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.databinding.HtlRtbErrorFragmentBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelRTBErrorFragment() : HotelBottomUpErrorFragment<HotelRTBErrorViewModel, HtlRtbErrorFragmentBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel : HotelEventSharedViewModel? = null

    companion object {
        val TAG = "HotelRTBErrorFragment"

        @JvmStatic
        fun getInstance(error: HotelError): HotelRTBErrorFragment {
            val fragment = HotelRTBErrorFragment()
            val bundle = Bundle()
            bundle.putParcelable(HotelConstants.ERROR_FRAG_ARGS, error);
            fragment.arguments = bundle;
            return fragment
        }
    }

    override fun initViewModel(): HotelRTBErrorViewModel {
        return getViewModel(factory)
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.htl_rtb_error_fragment
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
    }

    override val backgroundViewId: Int
        get() = R.id.view_bg_above_review_screen

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            INITIATE_CHECKOUT,OPEN_MYTRIPS_DEEPLINK -> {
                activitySharedViewModel?.updateEventStream(event)
            }
        }
        super.handleEvents(event)
    }
}