package com.mmt.hotel.bookingreview.event

object HotelCorpBookingReviewFragmentEvent {
    const val ON_EMPLOYEE_CLICKED = "ON_EMPLOYEE_CLICKED"
    const val REMOVE_CO_TRAVELLER_CLICKED = "REMOVE_CO_TRAVELLER_CLICKED"
    const val UPDATE_CO_TRAVELLER_PRIMARY_DATA = "UPDATE_CO_TRAVELLER_PRIMARY_DATA"
    const val MAKE_CO_TRAVELLER_PRIMARY = "MAKE_CO_TRAVELLER_PRIMARY"
    const val MAKE_PRIMARY_TRAVELLER_CO = "MAKE_PRIMARY_TRAVELLER_CO"
    const val MAKE_PRIMARY_TRAVELLER_VIEW_GONE = "MAKE_PRIMARY_TRAVELLER_VIEW_GONE"
    const val MAKE_PRIMARY_TRAVELLER_VIEW_VISIBLE = "MAKE_PRIMARY_TRAVELLER_VIEW_VISIBLE"
    const val ON_PRIMARY_EMAIL_CHANGE = "ON_PRIMARY_EMAIL_CHANGE"
    const val UPDATE_CO_TRAVELLERS_LIST = "UPDATE_CO_TRAVELLERS_LIST"
    const val RESET_TRIP_TAG_CARD = "RESET_TRIP_TAG_CARD"
    const val RESET_GST_CARD = "RESET_GST_CARD"
    const val ADD_GST_FORM = "ADD_GST_FORM"
    const val UPDATE_GST_BASED_ON_TRIP_TAG = "UPDATE_GST_BASED_ON_TRIP_TAG"
    const val SHOW_UPDATED_PRIMARY_TRAVELLER_NAME = "SHOW_UPDATED_PRIMARY_TRAVELLER_NAME"
    const val SME_CARD_CLICKED = "SME_CARD_CLICKED"
    const val OPEN_SME_BOTTOM_SHEET = "OPEN_SME_BOTTOM_SHEET"
    const val DISMISS_SME_BOTTOM_SHEET = "DISMISS_SME_BOTTOM_SHEET"

}