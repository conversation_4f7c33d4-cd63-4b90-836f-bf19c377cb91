package com.mmt.hotel.bookingreview.event

object HotelBookingReviewActivityEvent {
    const val OPEN_FLEXI_DETAIL_BOTTOM_SHEET = "OPEN_FLEXI_DETAIL_BOTTOM_SHEET"
    const val OPEN_CO_TRAVELLER_FRAGMENT = "OPEN_CO_TRAVELLER_FRAGMENT"
    const val OPEN_SPECIAL_REQUEST_FRAGMENT = "OPEN_SPECIAL_REQUEST_FRAGMENT"
    const val OPEN_HAVE_COUPON_CODE_FRAGMENT = "OPEN_HAVE_COUPON_CODE_FRAGMENT"
    const val OPEN_HOTEL_POLICY_FRAGMENT = "OPEN_HOTEL_POLICY_FRAGMENT"
    const val OPEN_TERMS_CONDITION_WEB_URL = "OPEN_TERMS_CONDITION_WEB_URL"
    const val OPEN_WEB_URL = "OPEN_WEB_URL"
    const val OPEN_PRIVACY_POLICY_WEB_URL = "OPEN_PRIVACY_POLICY_WEB_URL"
    const val UPDATE_SPECIAL_REQUEST_ITEMS = "UPDATE_SPECIAL_REQUEST_ITEMS"
    const val SHOW_CO_TRAVELLER = "SHOW_CO_TRAVELLER"
    const val HANDLE_USER_PROVIDED_COUPON = "HANDLE_USER_PROVIDED_COUPON"
    const val OPEN_LOGIN_ACTIVITY = "OPEN_LOGIN_ACTIVITY"
    const val OPEN_PAY_FRAGMENT = "OPEN_PAY_FRAGMENT"
    const val CONTINUE_FROM_BOOKING_FRAG = "CONTINUE_FROM_BOOKING_FRAG"
    const val INITIATE_CHECKOUT = "INITIATE_CHECKOUT"
    const val INITIATE_CHECKOUT_FLEXI_BOTTOMSHEET = "INITIATE_CHECKOUT_FLEXI_BOTTOMSHEET"
    const val INITIATE_CHECKOUT_BNPL_PRE_TXN = "INITIATE_CHECKOUT_BNPL_PRE_TXN"
    const val CANCEL_BOOKING_BNPL_PRE_TXN = "CANCEL_BOOKING_BNPL_PRE_TXN"
    const val OPEN_PAYMENT_ACTIVITY = "OPEN_PAYMENT_ACTIVITY"
    const val OPEN_THANKYOU_PAGE = "OPEN_THANKYOU_PAGE"
    const val FINISH_BOOKING_REVIEW_ACTIVITY = "FINISH_BOOKING_REVIEW_ACTIVITY"
    const val OPEN_PAH_ACTIVITY = "OPEN_PAH_ACTIVITY"
    const val OPEN_FORCE_SHOW_PAH_ACTIVITY = "OPEN_FORCE_SHOW_PAH_ACTIVITY"
    const val OPEN_CHECKOUT_ERROR_FRAGMENT = "OPEN_CHECKOUT_ERROR_FRAGMENT"
    const val OPEN_DOUBLE_BLACK_ERROR_FRAGMENT = "OPEN_DOUBLE_BLACK_ERROR_FRAGMENT"
    const val OPEN_RTB_ERROR_FRAGMENT = "OPEN_RTB_ERROR_FRAGMENT"
    const val BACK_TO_LIST = "BACK_TO_LIST"
    const val BACK_TO_DETAIL = "BACK_TO_DETAIL"
    const val BACK_TO_PREVIOUS_ACTIVITY = "BACK_TO_PREVIOUS_ACTIVITY"
    const val SHOW_ROOM_POLICY_BOTTOMSHEET = "SHOW_ROOM_POLICY_BOTTOMSHEET"
    const val REFRESH_ACTIVITY = "REFRESH_ACTIVITY"
    const val AVAIL_API_ERROR = "AVAIL_API_ERROR"
    const val DOUBLE_BLACK_NAME_UPDATE = "DOUBLE_BLACK_NAME_UPDATE"
    const val SHOW_PROGRESS_DIALOG = "SHOW_PROGRESS_DIALOG"
    const val HIDE_PROGRESS_DIALOG = "HIDE_PROGRESS_DIALOG"
    const val TRIP_DETAILS_CTA_CLICK_EVENT = "TRIP_DETAILS_CTA_CLICK_EVENT"
    const val TRIP_DETAILS_TEXT_BOX_CLICK_EVENT = "TRIP_DETAILS_TEXT_BOX_CLICK_EVENT"
    const val OPEN_MYTRIPS_DEEPLINK = "OPEN_MYTRIPS_DEEPLINK"
    const val OPEN_DEEPLINK = "OPEN_DEEPLINK"
    const val DAYUSE_ALERT_YES_CLICKED = "DAYUSE_ALERT_YES_CLICKED"
    const val DAYUSE_ALERT_NO_CLICKED = "DAYUSE_ALERT_NO_CLICKED"
    const val TRACK_DAYUSE_ALERT_YES_CLICKED = "TRACK_DAYUSE_ALERT_YES_CLICKED"
    const val WRONG_GSTN_ENTERED = "WRONG_GSTN_ENTERED"
    const val ON_APPLY_COUPON_CLICKED = "ON_APPLY_COUPON_CLICKED"
    const val ON_APPLY_COUPON_CLICKED_ERROR = "ON_APPLY_COUPON_CLICKED_ERROR"
    const val SUBSCRIPTION_REVIEW_ERROR = "SUBSCRIPTION_REVIEW_ERROR"
    const val SHOW_RATE_PLAN_UPGRADE_SHEET = "SHOW_RATE_PLAN_UPGRADE_SHEET"
    const val OPEN_COUNTRY_SELECTION_SCREEN = "OPEN_COUNTRY_SELECTION_SCREEN"
    const val OPEN_NEED_HELP_FORM = "OPEN_NEED_HELP_FORM"
    const val CHAT_BOT_SHOWN = "CHAT_BOT_SHOWN"
    const val NEED_HELP_VIEW_SHOWN = "NEED_HELP_VIEW_SHOWN"
    const val NEED_HELP_VIEW_OPENED = "NEED_HELP_VIEW_OPENED"
    const val NEED_HELP_VIEW_CALL_CLICK = "NEED_HELP_VIEW_CALL_CLICK"
    const val OPEN_ERROR_FRAGMENT = "OPEN_ERROR_FRAGMENT"
    const val OPEN_PAH_ACTIVITY_QUICK_BOOK = "OPEN_PAH_ACTIVITY_QUICK_BOOK"
    const val OPEN_FLEXI_CHECKIN_TIME_SLOT_BOTTOMSHEET = "OPEN_FLEXI_CHECKIN_TIME_SLOT_BOTTOMSHEET"
    const val CHAT_BOT_CLICK = "CHAT_BOT_CLICK"
    const val CHAT_BOT_CLICK_TRAVELPLEX = "CHAT_BOT_CLICK_TRAVELPLEX"
    const val CHAT_BOT_TOOL_TIP_ACTION = "CHAT_BOT_TOOL_TIP_ACTION"
    const val HANDLE_TRAVELLER_INFO_INVALID_CONTACT_NUMBER = "HANDLE_TRAVELLER_INFO_INVALID_CONTACT_NUMBER"
    const val HANDLE_TRAVELLER_INFO_CONTACT_NUMBER_CHANGED = "HANDLE_TRAVELLER_INFO_CONTACT_NUMBER_CHANGED"
    const val HANDLE_TRAVELLER_INFO_COUNTRY_CODE_CHANGED = "HANDLE_TRAVELLER_INFO_COUNTRY_CODE_CHANGED"
    const val OPEN_CONSENT_DIALOG = "OPEN_CONSENT_DIALOG"

}