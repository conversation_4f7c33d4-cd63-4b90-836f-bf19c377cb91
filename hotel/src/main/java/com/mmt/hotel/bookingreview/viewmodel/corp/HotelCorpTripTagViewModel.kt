package com.mmt.hotel.bookingreview.viewmodel.corp

import com.mmt.core.util.ProcessLifecycleHelper.getString
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent
import com.mmt.hotel.bookingreview.model.corp.CorpApprovalRequestFragmentData
import com.mmt.hotel.bookingreview.event.HotelQuickReviewEvents
import javax.inject.Inject

class HotelCorpTripTagViewModel @Inject constructor(
    val data: CorpApprovalRequestFragmentData?) : HotelToolBarViewModel() {
    override fun getTitle(): String {
        return getString(R.string.htl_travel_related_detail)
    }

    override fun onHandleBackPress() {
        updateEventStream(HotelEvent(HotelRequestApprovalReviewEvent.DISMISS_FRAGMENT))
    }

    fun onBackPressed(){
        eventStream.postValue(HotelEvent(HotelQuickReviewEvents.BACK_PRESSED))
    }

}
