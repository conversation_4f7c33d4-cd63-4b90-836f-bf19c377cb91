package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.compose.review.uiModel.ReviewAdditionalChargesUIData


class MandatoryChargesVM(var data: ReviewAdditionalChargesUIData) {

    fun getHeader() = data.headerText
    fun getTitle() = data.title
    fun getSubTitle() = data.subTitle.orEmpty()
    fun getDescription() = data.description.orEmpty()
    fun getBreakupItems() = data.items
    fun showReadAndAgree() = data.showReadAndAgree
    fun isTnCAccepted() = isTncAccepted.value
    fun onTnCClicked() {
        resetCbBg()
        val isAccepted = isTncAccepted.value
        isTncAccepted.value = !isAccepted
    }

    var isTncAccepted = mutableStateOf(data.isTncAccpeted)
    var cbTextColor = mutableIntStateOf(R.color.textColor_charcoal_gray)
    var uncheckedColor = R.color.review_message_color


    fun updateAdditionalFeeError() {
        cbTextColor.intValue = R.color.red_failed
        uncheckedColor = R.color.red_failed
        isTncAccepted.value = false
    }
    fun resetCbBg() {
        cbTextColor.intValue = R.color.textColor_charcoal_gray
        uncheckedColor = R.color.review_message_color
    }

    fun getDisclaimer() = data.disclaimer.orEmpty()
}