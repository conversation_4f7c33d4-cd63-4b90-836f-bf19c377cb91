package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagFieldV2

class CorpReviewTripTagRadioGroupViewModel(private val corpTripTagData: CorpTripTagFieldV2, private val eventStream: MutableLiveData<HotelEvent>): HotelCorpTravelDetailFormItemViewModelV2(corpTripTagData, eventStream) {

    fun getAllPossibleValueVMList(): List<CorpTravelDetailOptionViewModelV2> {
        val vmList = ArrayList<CorpTravelDetailOptionViewModelV2>()
        corpTripTagData.attributePossibleValues?.forEach { value ->
            val isSelected = corpTripTagData.attributeSelectedValue?.contains(value) ?: false
            val model = CorpTravelDetailOptionViewModelV2(value, this, isSelected)
            optionsViewModels[value] = model
            vmList.add(model)
        }
        corpTripTagData.possibleValuesAndGST?.forEach { item ->
            item.value?.let {
                val isSelected = corpTripTagData.attributeSelectedValue?.contains(it) ?: false
                val model = CorpTravelDetailOptionViewModelV2(it, this, isSelected)
                optionsViewModels[it] = model
                vmList.add(model)
            }
        }
        return vmList
    }
}