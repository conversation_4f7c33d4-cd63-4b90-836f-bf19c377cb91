package com.mmt.hotel.bookingreview.ui.hotelTags

import androidx.annotation.StringDef
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder.Companion.PC_BELOW_HOTEL
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder.Companion.PC_HOTEL_TOP
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder.Companion.PC_PRICE_TOP
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder.Companion.PLACEHOLDER_PRICE_BOTTOM
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder.Companion.PLACEHOLDER_PRICE_BOTTOM_M1
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder.Companion.PLACEHOLDER_TOP_RIGHT

@StringDef(
    value = [
        PC_HOTEL_TOP,
        PC_BELOW_HOTEL,
        PC_PRICE_TOP,
        PLACEHOLDER_TOP_RIGHT,
        PLACEHOLDER_PRICE_BOTTOM,
        PLACEH<PERSON>DER_PRICE_BOTTOM_M1
    ]
)

annotation class HotelTagPlaceholder {
    companion object {
        const val PC_HOTEL_TOP = "PC_HOTEL_TOP"
        const val PC_BELOW_HOTEL = "PC_BELOW_HOTEL"
        const val PC_PRICE_TOP = "PC_PRICE_TOP"
        const val PLACEHOLDER_PRICE_BOTTOM = "PLACEHOLDER_PRICE_BOTTOM"
        const val PLACEHOLDER_PRICE_BOTTOM_M1 = "PLACEHOLDER_PRICE_BOTTOM_M1"
        const val PLACEHOLDER_TOP_RIGHT = "PLACEHOLDER_TOP_RIGHT"
    }
}