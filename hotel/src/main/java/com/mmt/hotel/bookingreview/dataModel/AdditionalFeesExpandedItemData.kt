package com.mmt.hotel.bookingreview.dataModel

import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.common.data.LinearLayoutItemData

data class AdditionalFeesExpandedItemData(val title: String,
                                          val description: String?,
                                          val inclusions: List<String>?,
                                          val categoryDescription: String? = null,
                                          val amount: String,
                                          val amountDescription: String?,
                                          val chargesMsg: String?){
    fun getInclusionItems(): List<LinearLayoutItemData> {
       return inclusions?.map {
            LinearLayoutItemData(R.layout.item_additional_fees_inclusion, BR.text, it)
        } ?: emptyList()
    }
}
