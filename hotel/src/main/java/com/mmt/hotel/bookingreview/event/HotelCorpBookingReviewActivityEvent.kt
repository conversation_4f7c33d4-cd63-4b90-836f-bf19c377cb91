package com.mmt.hotel.bookingreview.event

object HotelCorpBookingReviewActivityEvent {
    const val OPEN_TRAVELLER_SEARCH_BOTTOMSHEET_TO_SELECT_EMPLOYEE = "OPEN_TRAVELLER_SEARCH_BOTTOMSHEET_TO_SELECT_EMPLOYEE"
    const val OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE = "OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE"
    const val OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE_WITH_EMAIL = "OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE_WITH_EMAIL"
    const val ADD_CO_TRAVELLER_CAPACITY_REACHED = "ADD_CO_TRAVELLER_CAPACITY_REACHED"
    const val OPEN_CORP_HOTEL_BOOKING_APPROVAL_REQUEST_SENT_FRAGMENT = "OPEN_CORP_HOTEL_BOOKING_APPROVAL_REQUEST_SENT_FRAGMENT"
    const val REQUEST_CORP_APPROVAL_API = "REQUEST_CORP_APPROVAL_API"
    const val OPEN_REQUEST_APPROVAL = "OPEN_REQUEST_APPROVAL"
    const val BACK_TO_HOME_BIZ = "BACK_TO_HOME_BIZ"
    const val REQUEST_APPROVAL_BUTTON_CLICKED = "REQUEST_APPROVAL_BUTTON_CLICKED"
    const val APPROVAL_API_ERROR = "APPROVAL_API_ERROR"
    const val SCROLL_TO_BOTTOM = "SCROLL_TO_BOTTOM"
    const val EVENT_HANDLE_ELEVATION = "EVENT_HANDLE_ELEVATION"
    const val SKIP_APPROVAL_BUTTON_CLICKED = "SKIP_APPROVAL_BUTTON_CLICKED"
    const val SKIP_APPROVAL_BUTTON_CLICKED_FROM_REQUEST_APPROVAL_SCREEN = "SKIP_APPROVAL_BUTTON_CLICKED_FROM_REQUEST_APPROVAL_SCREEN"
    const val OPEN_SKIP_APPROVAL_REASONS_SCREEN = "OPEN_SKIP_APPROVAL_REASONS_SCREEN"
    const val INITIATE_CHECKOUT_WITH_SKIP_REASON = "INITIATE_CHECKOUT_WITH_SKIP_REASON"
    const val OPEN_CORP_APPROVAL_PAGE = "OPEN_CORP_APPROVAL_PAGE"
    const val OPEN_CORP_MESSAGE_FRAGMENT = "OPEN_CORP_MESSAGE_FRAGMENT"
    const val OPEN_ADD_GUEST_CORP_CO_TRAVELLER = "OPEN_ADD_GUEST_CORP_CO_TRAVELLER"
    const val INITIATE_ITINERARY_FLOW = "INITIATE_ITINERARY_FLOW"
    const val ADD_TO_ITINIRARY_FLOW = "ADD_TO_ITINIRARY_FLOW"
    const val CTA_SHOWN_TRACKING = "CTA_SHOWN_TRACKING"
}