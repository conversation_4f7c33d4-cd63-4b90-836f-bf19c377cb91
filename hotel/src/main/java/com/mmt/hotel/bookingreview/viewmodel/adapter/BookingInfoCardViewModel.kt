package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.dataModel.SubTextUiModel
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.DIRECT_CONNECT_CARD_SHOWN
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.model.response.persuasionCards.CardInfo
import com.mmt.hotel.detail.event.HotelDetailClickEvents.Companion.TRACK_EVENT
import com.mmt.hotel.listingV2.viewModel.adapter.CardActionViewModel

class BookingInfoCardViewModel(
    val cardData: CardInfo,
    val eventStream: MutableLiveData<HotelEvent>,
    @DimenRes val marginBottom: Int = R.dimen.margin_0dp,
    @DrawableRes val background: Int = R.drawable.border_circular_edges
) : CardActionViewModel(cardData, MutableLiveData()) {

    val subTextItems = ObservableField<List<LinearLayoutItemData>>()

    init {
        subTextItems.set(cardData.subTextList?.map {
            val uiData = SubTextUiModel(it,cardData.subTextList.size >1)
            LinearLayoutItemData(
                R.layout.layout_booking_info_subtexts,
                BR.model,
                uiData
            )
        })
        eventStream.postValue(HotelEvent(TRACK_EVENT, OmnitureTrackingHelper.OEPK_C_1 to  DIRECT_CONNECT_CARD_SHOWN))
    }


    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.BOOKING_INFO_CARD
    }
}