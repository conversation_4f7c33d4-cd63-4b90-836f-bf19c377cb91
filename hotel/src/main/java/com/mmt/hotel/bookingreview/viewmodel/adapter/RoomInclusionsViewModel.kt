package com.mmt.hotel.bookingreview.viewmodel.adapter

import android.os.Parcelable
import androidx.databinding.BaseObservable
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.binding.HotelDrawableProvider
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RoomInclusionUiData
import com.mmt.hotel.common.constants.IconType.DOUBLETICK_ICON
import com.mmt.hotel.common.util.HotelUtil
import kotlinx.parcelize.Parcelize

/**
 * [byPassCancellationStyling] variable is used to bypass free cancellation styling from title, subtitle
 * (eg. bold font and green color in case of free cancellation)
* */
@Parcelize
class RoomInclusionsViewModel(val data: RoomInclusionUiData, private val byPassCancellationStyling : Boolean = false) : BaseObservable(), Parcelable {

    fun getTitle() = data.title

    fun getSubtitle() = data.subTitle.orEmpty()

    fun showSubTitle() = data.subTitle?.isNotEmpty()

    fun getTitleTextColor() = if(byPassCancellationStyling) R.color.htl_grey else HotelUtil.getInclusionItemTextColor(data.planType)

    fun getMarginStart() = data.marginStart

    fun getSubTitleTextColor(): Int {
        return if (!byPassCancellationStyling && DOUBLETICK_ICON.equals(data.iconType, true)) {
            HotelUtil.getInclusionItemSubTextColor(data.planType)
        } else {
            R.color.grey_1
        }
    }

    fun getImageUrl() = data.imageUrl.orEmpty()

    fun getIcon() = HotelDrawableProvider.getDrawableResource(data.iconType.orEmpty())

    fun getIconType() = data.iconType.orEmpty()

}