package com.mmt.hotel.bookingreview.model.response.validatecoupon

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.UpsellInfo
import com.mmt.hotel.bookingreview.model.response.*
import com.mmt.hotel.bookingreview.model.response.price.HotelPriceBreakUp
import com.mmt.hotel.bookingreview.model.response.room.RoomRatePlan
import com.mmt.hotel.common.model.response.CancellationTimelineModel
import com.mmt.hotel.common.model.response.MsmeOfferCardModel
import com.mmt.hotel.compose.review.dataModel.FlexiDetailBottomSheetData
import kotlinx.parcelize.Parcelize

@Parcelize
data class ValidateCouponResponse(val statusMessage: String?,
                                  @SerializedName("totalPricing")
                                  val priceBreakUp: HotelPriceBreakUp?,
                                  @SerializedName("rateplanlist")
                                  val ratePlanList: List<RoomRatePlan>?, //Room level data which can be changed due to coupon application
                                  val emiDetails: HotelEmiDetailsMessage?,
                                  val bnplDetails: HotelBnplDetails?,
                                  val fullPayment: HotelFullPaymentDetails?,
                                  @SerializedName("cancellationPolicyTimeline")
                                  val payLaterTimeLineModel: CancellationTimelineModel?,
                                  val corpApprovalInfo: CorpApprovalInfo? = null,
                                  @SerializedName("msmeCorpCard")
                                  val msmeCorpCard : MsmeOfferCardModel?,
                                  @SerializedName("paymentPlan")
                                  val paymentPlan: PaymentPlan?,
                                  @SerializedName("ackId")
                                  val ackId: String?,
                                  val flexiDetailBottomSheet: FlexiDetailBottomSheetData? = null,
                                  val updatedUpsellOptions: List<UpsellInfo>? = null,

) : Parcelable