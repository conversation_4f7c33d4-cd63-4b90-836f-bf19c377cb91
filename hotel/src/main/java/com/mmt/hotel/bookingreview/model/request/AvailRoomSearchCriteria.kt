package com.mmt.hotel.bookingreview.model.request

import android.os.Parcel
import android.os.Parcelable
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.base.model.request.SearchCriteria
import com.mmt.hotel.common.model.request.RoomCriteriaV2
import com.mmt.hotel.dayuse.model.request.DayUseApiSlot

/**
 * Created by sunil.jain on 16/09/20.
 */
class AvailRoomSearchCriteria(
        checkIn: String,
        checkOut: String,
        countryCode: String,
        locationId: String,
        locationType: String,
        currency: String,
        val hotelId: String,
        var searchType: String,
        var pricingKey: String?,
        var roomCriteria: List<RoomCriteriaV2>? = null,
        var travellerEmailID: List<String>? = null,
        val personalCorpBooking : Boolean = false,
        var cityCode : String? = EMPTY_STRING,
        val checkUpgrade: Boolean = false,
        val slot: DayUseApiSlot? = null,
        val addOnDetail : Map<String, AddOnRequestData>? = null,
        val hotelType: String? = null,
        val guestHouseAvailable: Boolean? = null,
        val userSearchType: String? = null,
        val tripType:String?= null
) : SearchCriteria(checkIn, checkOut, countryCode, locationId, locationType, currency) {
    constructor(parcel: Parcel) : this(
        parcel.readString() ?: EMPTY_STRING,
        parcel.readString() ?: EMPTY_STRING,
        parcel.readString() ?: EMPTY_STRING,
        parcel.readString() ?: EMPTY_STRING,
        parcel.readString() ?: EMPTY_STRING,
        parcel.readString() ?: EMPTY_STRING,
        hotelId = parcel.readString() ?: EMPTY_STRING,
        searchType = parcel.readString() ?: EMPTY_STRING,
        pricingKey = parcel.readString(),
        roomCriteria = ArrayList<RoomCriteriaV2>().apply {
            parcel.readList(this as List<*>, RoomCriteriaV2::class.java.classLoader)
        },
        travellerEmailID = parcel.createStringArrayList(),
        personalCorpBooking = parcel.readByte() != 0.toByte(),
        cityCode = parcel.readString() ?: EMPTY_STRING,
        checkUpgrade = parcel.readByte() != 0.toByte(),
        tripType = parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        super.writeToParcel(parcel, flags)
        parcel.writeString(hotelId)
        parcel.writeString(searchType)
        parcel.writeString(pricingKey)
        parcel.writeTypedList(roomCriteria)
        parcel.writeStringList(travellerEmailID)
        parcel.writeByte(if(personalCorpBooking) 1 else 0)
        parcel.writeString(cityCode)
        parcel.writeByte(if (checkUpgrade) 1 else 0)
        parcel.writeString(tripType)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<AvailRoomSearchCriteria> {
        override fun createFromParcel(parcel: Parcel): AvailRoomSearchCriteria {
            return AvailRoomSearchCriteria(parcel)
        }

        override fun newArray(size: Int): Array<AvailRoomSearchCriteria?> {
            return arrayOfNulls(size)
        }
    }
}