package com.mmt.hotel.bookingreview.viewmodel.corp

import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import androidx.databinding.BaseObservable
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.viewModelScope
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent.ON_BACK_PRESSED
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent.TRAVELLER_DETAILS_ADDED
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerSuccessFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.response.AddNewEmployeeResponse
import com.mmt.hotel.bookingreview.repository.HotelCorpBookingReviewRepository
import com.mmt.hotel.common.constants.CorpConstants.EMPLOYEE
import com.mmt.hotel.common.constants.CorpConstants.GUEST
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.di.NamedConstants
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.listingV2.observable.UiState.ERROR.notifyChange
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject
import javax.inject.Named

class CorpAddEditTravellerViewModel @Inject constructor(private val data: CorpAddEditTravellerFragmentData?,
                                                        @Named(NamedConstants.CORP_EMPLOYEE_SEARCH_ACTIVITY)
                                                        private val repository: HotelCorpBookingReviewRepository) : HotelToolBarViewModel(){

    val resourceProvider = ResourceProvider.instance
    var firstName: String = CoreConstants.EMPTY_STRING
    var lastName: String = CoreConstants.EMPTY_STRING
    var emailId: String = CoreConstants.EMPTY_STRING
    var contactNo: String = CoreConstants.EMPTY_STRING
    val travellerType = data?.travellerType ?: GUEST

    val activated = ObservableBoolean(false)
    val showEmailError = ObservableBoolean(false)
    val loading = ObservableBoolean(false)
    val titleTextColor = ObservableField<Int>(resourceProvider.getColor(R.color.color_666666))
    val bgStartColor = ObservableField<Int>(resourceProvider.getColor(R.color.color_c2c2c2))
    val bgEndColor = ObservableField<Int>(resourceProvider.getColor(R.color.color_c2c2c2))
    val fnameTitle = ObservableField<SpannableString>(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_fname_title)))
    val lnameTitle = ObservableField<SpannableString>(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_lname_title)))
    val contactNoTitle = ObservableField<SpannableString>(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_mobile_no_title)))
    val emailTitle = ObservableField<SpannableString>()


    init {
        if(travellerType == GUEST) {
            emailTitle.set(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_email_title, GUEST.toUpperCase(Locale.ROOT))))
        }else{
            emailTitle.set(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_email_title, EMPLOYEE.toUpperCase(Locale.ROOT))))
        }
    }

    fun afterTextChanged(s: Editable) {
        if(validateAllFields()) {
            titleTextColor.set(resourceProvider.getColor(R.color.corp_bg_light))
            bgStartColor.set(resourceProvider.getColor(R.color.corp_bg_light))
            bgEndColor.set(resourceProvider.getColor(R.color.corp_bg_dark))
            activated.set(true)
        }else{
            titleTextColor.set(resourceProvider.getColor(R.color.color_666666))
            bgStartColor.set(resourceProvider.getColor(R.color.color_c2c2c2))
            bgEndColor.set(resourceProvider.getColor(R.color.color_c2c2c2))
            activated.set(false)
        }
        fnameTitle.set(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_fname_title)))
        lnameTitle.set(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_lname_title)))
        contactNoTitle.set(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_mobile_no_title)))
        if(travellerType == GUEST) {
            emailTitle.set(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_email_title, GUEST)))
        }else{
            emailTitle.set(applyStarSuffix(resourceProvider.getString(R.string.htl_corp_review_edit_traveller_email_title, EMPLOYEE)))
        }
    }

    private fun validateAllFields(): Boolean {
        var isValid = HotelUtil.validateRegEx(firstName, HotelConstants.FIRST_NAME_REGEX)
        isValid = isValid && HotelUtil.validateRegEx(lastName, HotelConstants.LAST_NAME_REGEX)
        isValid = isValid && HotelUtil.validateRegEx(contactNo, HotelConstants.IND_PHONE_REGEX)

        if(travellerType != GUEST) {
            isValid = isValid && LoginUtil.checkEmail(emailId)
        }
        return isValid
    }

    fun hideEmailBox(): Boolean{
        return travellerType == GUEST
    }

    private fun getCorpTravellerDetail(): CorpTravellerDetail {
        return CorpTravellerDetail(
            firstName = firstName,
            lastName = lastName,
            emailId = emailId,
            contactNo = contactNo,
            travellerType = travellerType
        )
    }


    private fun getCorpAddEditTravellerSuccessFragmentData(): CorpAddEditTravellerSuccessFragmentData {
        return CorpAddEditTravellerSuccessFragmentData(travellerDetail = getCorpTravellerDetail())
    }
    fun submitDetails() {
        if(!activated.get())
            return
        if(travellerType == GUEST) {
            updateEventStream(HotelEvent(TRAVELLER_DETAILS_ADDED, getCorpAddEditTravellerSuccessFragmentData()))
        }else {
            addNewEmployeeAPI()
        }
    }

    private fun addNewEmployeeAPI() {
        showLoader()
        viewModelScope.launch {
            repository.addNewEmployee(getCorpTravellerDetail())
                .catch {
                    hideLoader()
                    handleAddNewEmployeeFailure()
                    it.printStackTrace()
                }
                .collect {
                    hideLoader()
                    handleAddNewEmployeeSuccess(it)
                }
        }

    }

    private fun handleAddNewEmployeeSuccess(response: AddNewEmployeeResponse) {
        if( response.success != null && response.success!!) {
            updateEventStream(HotelEvent(TRAVELLER_DETAILS_ADDED, getCorpAddEditTravellerSuccessFragmentData()))
        }else{
            handleAddNewEmployeeFailure()
        }
    }

    private fun handleAddNewEmployeeFailure() {
        showEmailError.set(true)
    }

    override fun getTitle(): String {
        return if(travellerType == GUEST) {
            resourceProvider.getString(R.string.htl_corp_review_edit_traveller_title, GUEST)
        }else{
            resourceProvider.getString(R.string.htl_corp_review_edit_traveller_title, EMPLOYEE)
        }
    }

    override fun onHandleBackPress() {
        updateEventStream(HotelEvent(ON_BACK_PRESSED))
    }

    override fun showCrossIcon(): Boolean {
        return false
    }

    fun getSubmitBtnText(): String {
        return if(travellerType == GUEST) {
            resourceProvider.getString(R.string.htl_corp_review_edit_traveller_submit_btn_text, GUEST.toUpperCase(Locale.ROOT))
        }else{
            resourceProvider.getString(R.string.htl_corp_review_edit_traveller_submit_btn_text, EMPLOYEE.toUpperCase(Locale.ROOT))
        }
    }

    private fun applyStarSuffix(value: String): SpannableString {
        val spannableString = SpannableString("$value *")
        val indexOfStar = spannableString.indexOf("*")
        spannableString.setSpan(ForegroundColorSpan(titleTextColor.get() ?: resourceProvider.getColor(R.color.color_666666)),
                    0, indexOfStar - 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        spannableString.setSpan(ForegroundColorSpan(resourceProvider.getColor(R.color.color_e02020)),
                indexOfStar, indexOfStar + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        return spannableString
    }

    private fun showLoader() {
        this.loading.set(true)
    }

    private fun hideLoader() {
        this.loading.set(false)
    }

    fun invokeAndNotify(validationMethod: () -> Boolean): Boolean {
        val result = validationMethod.invoke()
        notifyChange()
        return result
    }
}
