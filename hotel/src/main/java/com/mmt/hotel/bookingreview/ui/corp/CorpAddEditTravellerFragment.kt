package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import com.mmt.core.util.KeyBoardUtils
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.ui.fragment.HotelFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent.ON_BACK_PRESSED
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent.TRAVELLER_DETAILS_ADDED
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpAddEditTravellerViewModel
import com.mmt.hotel.databinding.HtlCorpReviewAddEditTravellerBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class CorpAddEditTravellerFragment :  HotelFragment<CorpAddEditTravellerViewModel, HtlCorpReviewAddEditTravellerBinding>(){
    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel : HotelEventSharedViewModel? = null

    companion object {
        const val TAG = "CorpAddEditTravellerFragment"
        @JvmStatic
        fun newInstance(bundle: Bundle): CorpAddEditTravellerFragment {
            val fragment = CorpAddEditTravellerFragment()
            fragment.arguments = bundle;
            return fragment
        }
    }

    override fun initViewModel(): CorpAddEditTravellerViewModel {
        return getViewModel(factory)
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel  = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.htl_corp_review_add_edit_traveller
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            TRAVELLER_DETAILS_ADDED -> {
                sendEventToActivity(event)
            }
            ON_BACK_PRESSED -> {
                KeyBoardUtils.hideKeyboard(activity)
                dismissFragment()
            }
        }
    }

    private fun sendEventToActivity(event:HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }
}