package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.response.checkout.ConsentData
import com.mmt.hotel.common.model.response.HotelApiError
import kotlinx.parcelize.Parcelize


@Parcelize
data class PostApprovalResponseV2(val message: String?,
                                  val approverList: List<CorpApprovingManager>?,
                                  val workflowId: String?,
                                  val status: String?,
                                  val statusCode: Int?,
                                  val responseCode: String?,
                                  val error: HotelApiError?,
                                  val errorTitle: String?,
                                  @SerializedName("corpAutobookRequestorConfig")
                                  val corpAutoBookRequestorConfig:CorpAutoBookRequestorConfig?,
                                  val consentData : ConsentData?): Parcelable