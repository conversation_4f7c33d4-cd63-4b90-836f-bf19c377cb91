package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.snapshotFlow
import androidx.databinding.BaseObservable
import androidx.databinding.ObservableArrayList
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.TRIP_DETAILS_CTA_CLICK_EVENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.TRIP_DETAILS_TEXT_BOX_CLICK_EVENT
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.response.TripDetailsCardInfo
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import kotlinx.coroutines.flow.onEach


class TripDetailsCardViewModel(val tripDetailsCardInfo: TripDetailsCardInfo, var eventStream: MutableLiveData<HotelEvent>):BaseObservable(),DiffUtilRecycleItem {

    var itemsList = ObservableArrayList<LinearLayoutItemData>()
    var tripDetailUserComment:String = EMPTY_STRING
    val  tripDetailUserCommentState =  mutableStateOf(tripDetailUserComment)

    init {
        createItemsList()
        snapshotFlow{tripDetailUserCommentState}.onEach {
            tripDetailUserComment =it.value
        }
    }

    fun getHeaderText():String {
        return tripDetailsCardInfo.headerText?: EMPTY_STRING
    }

    fun getHeaderSubText():String {
        return tripDetailsCardInfo.headerSubText?: EMPTY_STRING
    }

    fun getTitle():String {
        return tripDetailsCardInfo.title?: EMPTY_STRING
    }

    fun getActionText():String {
        return tripDetailsCardInfo.actionText?: EMPTY_STRING
    }

    private fun createItemsList() {
        tripDetailsCardInfo.texts?.forEach{ data ->
            itemsList.add(LinearLayoutItemData(R.layout.htl_booking_review_trip_details_items,BR.model, TripDetailsCardItemsViewModel(data)))
        }
    }
    fun tripDetailsTextBoxClick() {
        eventStream.postValue(HotelEvent(TRIP_DETAILS_TEXT_BOX_CLICK_EVENT))
    }

    fun onActionTextClicked() {
        eventStream.postValue(HotelEvent(TRIP_DETAILS_CTA_CLICK_EVENT,tripDetailsCardInfo.actionUrl))
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.TRIP_DETAILS_CARD
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.TRIP_DETAILS_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as TripDetailsCardViewModel).tripDetailsCardInfo
        return tripDetailsCardInfo == matchedWith
    }

    override fun cardName(): String {
        return "Review Trip Detail"
    }
}