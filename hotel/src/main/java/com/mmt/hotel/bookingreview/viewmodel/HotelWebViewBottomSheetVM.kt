package com.mmt.hotel.bookingreview.viewmodel

import android.content.res.Resources
import android.view.MotionEvent
import android.view.View
import androidx.core.view.ViewCompat.canScrollVertically
import androidx.core.view.ViewCompat.startNestedScroll
import com.mmt.core.util.DeviceUtil
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent.DISMISS_TCS_BOTTOMSHEET
import com.mmt.profile.cowin.CowinTrackingUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class HotelWebViewBottomSheetVM @Inject constructor() : HotelToolBarViewModel() {

    override fun getTitle(): String {
        return ResourceProvider.instance.getString(R.string.htl_learn_more_about_TCS)
    }

    override fun onHandleBackPress() {
        eventStream.value = HotelEvent(DISMISS_TCS_BOTTOMSHEET)
    }


}