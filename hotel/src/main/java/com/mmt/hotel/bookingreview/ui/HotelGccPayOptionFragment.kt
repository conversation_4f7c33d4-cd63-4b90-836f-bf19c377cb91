package com.mmt.hotel.bookingreview.ui

import android.os.Bundle
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.model.PayOptionData
import com.mmt.hotel.databinding.HotelPayOptionFragmentBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HotelGccPayOptionFragment : HotelPayOptionFragment<HotelPayOptionFragmentBinding>() {

    companion object {
        val TAG = "HotelPayOptionFragment"

        @JvmStatic
        fun getInstance(payOptionData: PayOptionData): HotelGccPayOptionFragment {
            val paymentDialogFragment = HotelGccPayOptionFragment()
            val bundle = Bundle()
            bundle.putParcelable(PAYMENT_OPTION_ARGS, payOptionData);
            paymentDialogFragment.arguments = bundle;
            return paymentDialogFragment
        }
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.hotel_pay_option_fragment;
    }
}