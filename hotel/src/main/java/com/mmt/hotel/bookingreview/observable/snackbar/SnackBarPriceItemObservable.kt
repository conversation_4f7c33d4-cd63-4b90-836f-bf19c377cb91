package com.mmt.hotel.bookingreview.observable.snackbar

import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.dataModel.snackbar.SnackBarPriceItemUiData

class SnackBarPriceItemObservable(private val data: SnackBarPriceItemUiData) {

    fun getLabel() = data.label

    fun labelMarginStart(): Float {
        return if(showIcon()){
            ResourceProvider.instance.getDimension(R.dimen.margin_10dp)
        }else{
            ResourceProvider.instance.getDimension(R.dimen.margin_0dp)
        }
    }

    fun getAmount() = data.amount

    fun getAmountTextColor() = data.amountTextColor

    fun showIcon() = data.showIcon

    fun showDivider() = data.showDivider

    fun getIconDrawable(): Int {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
            R.drawable.ic_corp_tick_review
        } else {
            R.drawable.ic_green_tick_new
        }
    }

}