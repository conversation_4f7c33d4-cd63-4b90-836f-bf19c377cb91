package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.runtime.mutableStateOf
import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.events.TrackEvent
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RoomDetailUiDataModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SHOW_ROOM_POLICY_BOTTOMSHEET
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.common.data.TimelineUiData
import com.mmt.hotel.compose.base.dataModel.TimelineUiDataV2

class RoomDetailViewModel(var data: RoomDetailUiDataModel, val eventStream: MutableLiveData<HotelEvent>) : BaseObservable(), AbstractRecyclerItem {

    var timelineUiDataV2 = mutableStateOf<TimelineUiDataV2?>(null)
    init {
        timelineUiDataV2.value = getPayLaterTimeLineV2()
    }
    fun updateData(data: RoomDetailUiDataModel){
        this.data = data.copy()
        timelineUiDataV2.value = getPayLaterTimeLineV2()
        notifyChange()
    }

    fun getRoomName() = data.roomName

    fun getRoomOccupancy() = data.roomOccupancy

    fun getInclusionItems() = data.inclusionItems

    fun getPayLaterTimeLine(): TimelineUiData? {
        data.cancellationTimeLine?.getTimelineUiData()?.timelineItems?.let {
            if(it.isNotEmpty()){
                return data.cancellationTimeLine?.getTimelineUiData()
            }
        }
        return null
    }

    fun getPayLaterTimeLineV2(): TimelineUiDataV2? {
        data.cancellationTimeLine?.getTimelineUiDataV2()?.timelineItems?.let {
            if(it.isNotEmpty()){
                return data.cancellationTimeLine?.getTimelineUiDataV2()?.copy()
            }
        }
        return null
    }

    fun getCancellationPolicyAlert() = data.cancellationPolicyAlert

    fun showCancellationPolicyAlert() = data.cancellationPolicyAlert?.isNotEmpty()

    fun onRoomCancellationPolicyClicked() {
        eventStream.postValue(HotelEvent(SHOW_ROOM_POLICY_BOTTOMSHEET, data))
    }

    fun trackEvent(event: TrackEvent) {
        eventStream.postValue(HotelEvent(HotelBookingReviewFragmentEvent.TRACK_EVENTS, event))
    }

    fun getHighLightImage() = data.highlightImage

    override fun getItemType(): Int {
        return 1
    }

}
