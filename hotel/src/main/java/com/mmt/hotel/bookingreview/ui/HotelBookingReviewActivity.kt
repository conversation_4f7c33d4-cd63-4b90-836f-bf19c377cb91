package com.mmt.hotel.bookingreview.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.EditText
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.google.firebase.analytics.FirebaseAnalytics
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.auth.login.model.LoginPageExtra
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.auth.login.util.AuthMigrationHelper
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.ERROR
import com.mmt.core.extensions.ActivityResultLifeCycleObserver
import com.mmt.core.extensions.OnActivityResult
import com.mmt.core.interfaces.AppInterfaceHelper
import com.mmt.core.model.webview.WebViewBundle
import com.mmt.core.util.GsonUtils
import com.mmt.core.util.KeyBoardUtils
import com.mmt.core.util.LOBS
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.executeIfCast
import com.mmt.data.model.payment.PaymentResponseVO
import com.mmt.data.model.payment.PaymentStatus
import com.mmt.data.model.util.GenericUtils
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.events.TrackEvent
import com.mmt.hotel.base.model.response.PopUpData
import com.mmt.hotel.base.model.tracking.HotelBaseTrackingData
import com.mmt.hotel.base.ui.activity.HotelActivity
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.dataModel.CouponFragmentData
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RoomDetailUiDataModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.AVAIL_API_ERROR
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_DETAIL
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_LIST
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_PREVIOUS_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.CONTINUE_FROM_BOOKING_FRAG
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.DOUBLE_BLACK_NAME_UPDATE
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.FINISH_BOOKING_REVIEW_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.HANDLE_USER_PROVIDED_COUPON
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.HIDE_PROGRESS_DIALOG
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT_FLEXI_BOTTOMSHEET
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_CHECKOUT_ERROR_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_CO_TRAVELLER_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_FORCE_SHOW_PAH_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_HOTEL_POLICY_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_MYTRIPS_DEEPLINK
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_PAH_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_PAYMENT_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_PAY_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_PRIVACY_POLICY_WEB_URL
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_RTB_ERROR_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_SPECIAL_REQUEST_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_TERMS_CONDITION_WEB_URL
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_THANKYOU_PAGE
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.REFRESH_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SHOW_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SHOW_PROGRESS_DIALOG
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SHOW_RATE_PLAN_UPGRADE_SHEET
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SHOW_ROOM_POLICY_BOTTOMSHEET
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.TRACK_DAYUSE_ALERT_YES_CLICKED
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.TRIP_DETAILS_CTA_CLICK_EVENT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.UPDATE_SPECIAL_REQUEST_ITEMS
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent.OPEN_TCS_CTA_BOTTOMSHEET
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent.PAYMENT_BUTTON_CLICKED
import com.mmt.hotel.bookingreview.event.HotelCouponFragmentEvent.BENEFIT_DEAL_CONFIRMATION_APPROVED
import com.mmt.hotel.bookingreview.event.HotelCouponFragmentEvent.BENEFIT_DEAL_POPUP_DISMISSED
import com.mmt.hotel.bookingreview.helper.constants.BookingReviewConstants
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.NO_HOTELS_FOUND_NORATES
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.REVIEW_SOLD_OUT
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.TARIFF_SOLD_OUT
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.TCS_LEARN_MORE_CLICKED
import com.mmt.hotel.bookingreview.helper.constants.CheckoutErrorCodes
import com.mmt.hotel.bookingreview.model.BookingReviewData
import com.mmt.hotel.bookingreview.model.CoTravellerFragmentData
import com.mmt.hotel.bookingreview.model.HotelPolicyBundleData
import com.mmt.hotel.bookingreview.model.PayOptionData
import com.mmt.hotel.bookingreview.model.PaymentIntentData
import com.mmt.hotel.bookingreview.model.response.DayUseInfo
import com.mmt.hotel.bookingreview.model.response.RatePlansUpgrade
import com.mmt.hotel.bookingreview.model.response.addon.InsuranceDataItem
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.bookingreview.model.response.validatecoupon.ValidateApiResponseV2
import com.mmt.hotel.bookingreview.ui.HotelPahPayActivity.Companion.PAH_INTENT_DATA
import com.mmt.hotel.bookingreview.ui.HotelRatePlanUpgradeFragment.Companion.DECLINE_UPGRADE
import com.mmt.hotel.bookingreview.ui.HotelRatePlanUpgradeFragment.Companion.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.ui.HotelRatePlanUpgradeFragment.Companion.SHOW_BLUR_BG
import com.mmt.hotel.bookingreview.viewmodel.HotelBookingReviewActivityViewModel
import com.mmt.hotel.bookingreview.viewmodel.HotelBookingReviewActivityViewModel.Companion.NO_RESULT_CODE
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.PAH_ERROR_CODE
import com.mmt.hotel.common.constants.HotelConstants.PAH_PAYMENT_ACTIVITY_REQUEST_CODE
import com.mmt.hotel.common.constants.HotelConstants.SOLD_OUT_CODE
import com.mmt.hotel.common.constants.HotelPageActionName
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.di.BaseTrackingDataInjector
import com.mmt.hotel.common.di.UserSearchDataInjector
import com.mmt.hotel.common.extensions.openFragment
import com.mmt.hotel.common.model.HotelError
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.HotelRequestConstants.Companion.PAGE_CONTEXT_REVIEW
import com.mmt.hotel.common.request_callback.event.RequestCallbackEvent
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.compose.noCostEmi.NoCostEMIEvent
import com.mmt.hotel.compose.review.dataModel.FlexiDetailBottomSheetData
import com.mmt.hotel.compose.review.helper.IReviewFragment
import com.mmt.hotel.compose.review.ui.fragment.HotelBookingReviewFragmentV2
import com.mmt.hotel.compose.review.ui.fragment.HotelFlexiDetailBottomSheetFragment
import com.mmt.hotel.compose.review.ui.fragment.HotelPolicyRuleFragment
import com.mmt.hotel.compose.widgets.OutOfPolicyBottomSheetWithComposeView
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.databinding.ActivityHotelBookingReviewBinding
import com.mmt.hotel.detail.event.HotelDetailClickEvents
import com.mmt.hotel.detail.model.response.RequestCallBackData
import com.mmt.hotel.gallery.dataModel.HotelFullSizeImageBundleData
import com.mmt.hotel.gallery.dataModel.HotelGalleryTrackingData
import com.mmt.hotel.gallery.dataModel.HotelInfo
import com.mmt.hotel.gallery.ui.HotelFullSizeImageAbstractFragment
import com.mmt.hotel.listingV2.event.HotelListingClickEvents
import com.mmt.hotel.listingV2.event.HotelListingClickEvents.MMT_BLACK_CLICK_EVENT
import com.mmt.hotel.listingV2.event.HotelListingClickEvents.SHOW_WEB_VIEW_CARD_CLICK
import com.mmt.hotel.old.dialog.PahErrorDialog
import com.mmt.hotel.old.model.hotelreview.response.specialRequest.Category
import com.mmt.hotel.old.model.hotelreview.response.specialRequest.SpecialRequestForm
import com.mmt.hotel.selectRoom.event.ComboDetailEvent
import com.mmt.hotel.selectRoom.event.RoomDetailEvent
import com.mmt.hotel.selectRoom.event.ShowRoomDetailEventData
import com.mmt.hotel.selectRoom.tracking.RoomDetailTrackingEvent
import com.mmt.hotel.selectRoom.ui.FragmentRoomDetail
import com.mmt.hotel.userReviews.featured.model.bundle.RoomReviewBundleModel
import com.mmt.hotel.userReviews.featured.ui.FragmentCategoryReviewsV2
import com.gommt.logger.LogUtils
import com.mmt.auth.login.MMTAuth
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT_BNPL_PRE_TXN
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_CONSENT_DIALOG
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.MY_BIZ_DUPLICATE_INTIMATION_CONTINUE_CLICK
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.MY_BIZ_DUPLICATE_INTIMATION_DISMISS_CLICK
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.MY_BIZ_DUPLICATE_INTIMATION_SHOWN
import com.mmt.hotel.bookingreview.model.response.checkout.ConsentData
import com.mmt.hotel.bookingreview.ui.corp.CorpReviewConsentBottomSheetFragment
import com.mmt.hotel.chatBot.dataModel.ChatBotWidgetInfo
import com.mmt.hotel.common.bottomsheet.HeaderSubtitleInclusionsBottomSheet
import com.mmt.hotel.common.extensions.showFragment
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.detail.event.constants.HotelDetailTrackingConstants
import com.mmt.hotel.detail.event.constants.HotelDetailTrackingConstants.Companion.MEAL_INCLUSION_DETAILS_CLICKED
import com.mmt.hotel.listingV2.model.response.hotels.BottomSheetData
import com.mmt.hotel.selectRoom.event.SelectRoomEvent
import com.mmt.payments.qc.model.PaymentQcInfo
import com.mmt.payments.qc.model.TrackingData
import com.mmt.payments.qc.ui.callback.PaymentQcWidget
import com.mmt.payments.qc.ui.fragment.PaymentQcFragment
import com.mmt.uikit.util.fragment.put
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
open class HotelBookingReviewActivity :
    HotelActivity<HotelBookingReviewActivityViewModel, ActivityHotelBookingReviewBinding>(),
    PahErrorDialog.OnPahDialogInteraction, UserSearchDataInjector, BaseTrackingDataInjector, OnActivityResult,
    PaymentQcWidget.QcFragmentInteractionListener{

    private var isUserLoggedInOnReview = false
    @Inject
    lateinit var factory: HotelViewModelFactory
    lateinit var bookingReviewData : BookingReviewData

    companion object{
        const val LOGIN_FROM_REVIEW = 1
        const val SOLD_OUT_ON_REVIEW = 2
        const val ON_BACK_PRESSED = 3
        const val TAG_PAH_ERROR_FRAG = "PAHErrorDialog"
        const val SELECTED_RATE_PLAN_CODE = "selected_rate_plan_code"
        const val SELECTED_ROOM_CODE = "selected_room_code"

        const val REQUEST_CODE_PAYMENT = 1089
        const val RESULT_CODE_PAYMENT = 1089
        const val LOGIN_FROM_REVIEW_CODE = 9876
    }
    private var observer : ActivityResultLifeCycleObserver? = null

    override fun pageName(): String {
        return PAGE_CONTEXT_REVIEW
    }

    override fun countryCode(): String? {
        return intent?.extras?.getParcelable<BookingReviewData?>(HotelBookingReviewFragmentV2.KEY_BUNDLE_PARAM)?.userSearchData?.countryCode
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setFirebaseProperty()
        initActivity()

    }

    private fun setFirebaseProperty() {
        FirebaseAnalytics.getInstance(MMTCore.mContext).setUserProperty("htlReviewPageCompose","true")
    }

    private fun initActivity() {
        if (!isActivityRecreating()) {
            clearFragmentBackStack() // clearing backstack for handling Activity recreation
            lifecycleScope.launchWhenStarted {
                openBookingReviewFragment()
            }
            saveLastReviewHitTime()//Set when get the response of avail room api
        }
        observer = ActivityResultLifeCycleObserver(activityResultRegistry,this, activityID)
        observer?.let {
            it.registerForResult(REQUEST_CODE_PAYMENT, LOGIN_FROM_REVIEW_CODE, PAH_PAYMENT_ACTIVITY_REQUEST_CODE)
            lifecycle.addObserver(it)
        }
    }

    override fun initAndValidate() {
        val bookingReviewData = intent?.extras?.getParcelable(HotelBookingReviewFragmentV2.KEY_BUNDLE_PARAM)  as BookingReviewData?
        if(bookingReviewData == null) {
            finish()
        } else {
            this.bookingReviewData = bookingReviewData
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_hotel_booking_review
    }

    override fun createViewModel(): HotelBookingReviewActivityViewModel {
        return getViewModel(factory)
    }

    override fun createEventSharedViewModel(): HotelEventSharedViewModel {
        return getViewModel(factory)
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            HotelBookingReviewFragmentEvent.TRACK_EVENTS -> {
                event.data.executeIfCast<TrackEvent> {
                    viewModel.trackEvents(this.value, this.key)
                }
            }
            RequestCallbackEvent.REQUEST_CALLBACK -> {
                event.data.executeIfCast<RequestCallBackData> {
                    getReviewFragment()?.openCallToBookBottomSheet(this)
                }
            }
            OPEN_CO_TRAVELLER_FRAGMENT -> {
                openCoTravellerFragment(event.data as CoTravellerFragmentData)
            }
            SHOW_CO_TRAVELLER -> {
                event.data.executeIfCast<MutableList<CoTraveller>> {
                    getReviewFragment()?.showCoTraveller(this)
                }
            }
            DECLINE_UPGRADE -> {
                getComposeReviewFragment()?.updateBlurState(false)
                getReviewFragment()?.declineUpgrade()
            }
            HotelPayOptionFragment.PAY_FRAGMENT_DISMISSED -> {
                viewDataBinding.vDummyBlack.visibility = View.GONE
                getComposeReviewFragment()?.updateBlurState(false)
            }
            DISMISS_FRAGMENT -> {
                getComposeReviewFragment()?.updateBlurState(false)
            }
            SHOW_BLUR_BG -> {
                getComposeReviewFragment()?.updateBlurState(true)
            }
            HANDLE_USER_PROVIDED_COUPON -> {
                event.data.executeIfCast<Triple<ValidateApiResponseV2,String,String>> {
                    getReviewFragment()?.updateAppliedCoupons(
                        this.first,
                        this.second,
                        this.third
                    )
                }
            }

            HotelBookingReviewFragmentEvent.OPEN_OUT_OF_POLICY_BOTTOMSHEET -> {
                event.data.executeIfCast<List<String>> {
                    val modalBottomSheet = OutOfPolicyBottomSheetWithComposeView.newInstance(this)
                    modalBottomSheet.show(supportFragmentManager, "OutOfPolicyBottomsheet")
                }
            }
            SelectRoomEvent.SHOW_INCLUSIONS_BOTTOMSHEET -> {
                event.data.executeIfCast<BottomSheetData> {
                    val modalBottomSheet = HeaderSubtitleInclusionsBottomSheet.newInstance(this)
                    modalBottomSheet.show(supportFragmentManager, "HeaderSubtitleInclusionsBottomSheet")
                    viewModel.trackEventsInProp44(MEAL_INCLUSION_DETAILS_CLICKED)
                }
            }
            HotelBookingReviewActivityEvent.OPEN_FLEXI_DETAIL_BOTTOM_SHEET -> {
                event.data.executeIfCast<FlexiDetailBottomSheetData> {
                    viewModel.trackEvents("FLX_Shown_Bottomsheet", OmnitureTrackingHelper.OEPK_C_1)
                    openFlexiDetailBottomSheet(this)
                }
            }
            OPEN_LOGIN_ACTIVITY -> {
                openLoginActivity(event.data as? String)
                getComposeReviewFragment()?.updateBlurState(false)
                viewDataBinding.vDummyBlack.visibility = View.GONE
            }
            OPEN_PAY_FRAGMENT -> {
                openHotelPayOptionFragment(event.data as PayOptionData)
            }
            OPEN_TCS_CTA_BOTTOMSHEET -> {
                event.data.executeIfCast<WebViewBundle> {
                    openWebViewBottomSheet(this)
                }
                viewModel.trackEvents(TCS_LEARN_MORE_CLICKED)
            }
            NoCostEMIEvent.SHOW_NO_COST_EMI_PLANS -> {
                viewModel.trackEvents(BookingTrackingConstants.NO_COST_EMI_PLANS_REVIEW_CLICKED, OmnitureTrackingHelper.OEPK_C_1)
            }

            NoCostEMIEvent.NO_COST_EMI_PLAN_DETAIL_SHOWN -> {
                viewModel.trackEvents(BookingTrackingConstants.NO_COST_EMI_REVIEW_SHOWN, OmnitureTrackingHelper.OEPK_C_1)
            }
            NoCostEMIEvent.NO_COST_EMI_SEARCH_PERFORMED -> {
                viewModel.trackEvents(BookingTrackingConstants.NO_COST_EMI_SEARCH_PERFORMED, OmnitureTrackingHelper.OEPK_C_1)
            }
            NoCostEMIEvent.NO_COST_EMI_SEARCH_NO_RESULTS -> {
                viewModel.trackEvents(BookingTrackingConstants.NO_COST_EMI_SEARCH_NO_RESULTS, OmnitureTrackingHelper.OEPK_C_1)
            }
            NoCostEMIEvent.NO_COST_EMI_BANK_CLICK -> {
                event.data.executeIfCast<String> {
                    viewModel.trackEvents(String.format(BookingTrackingConstants.NO_COST_EMI_BANK_SELECTED, this), OmnitureTrackingHelper.OEPK_C_1)
                }
            }
            CONTINUE_FROM_BOOKING_FRAG -> {
                proceedWithCheckOut()
            }
            INITIATE_CHECKOUT_FLEXI_BOTTOMSHEET -> {
                event.data.executeIfCast<TrackEvent> {
                    viewModel.trackEvents(this.value, this.key)
                }
                showCheckoutLoader()
                viewModel.proceedCheckout(event.data as? CorpReasons)
            }
            INITIATE_CHECKOUT -> {
                /* show loader & initiate checkout request. Remember to hide loader on each success & failure case. */
                showCheckoutLoader()
                viewDataBinding.vDummyBlack.visibility = View.GONE
                getComposeReviewFragment()?.updateBlurState(false)
                viewModel.proceedCheckout(event.data as? CorpReasons)
            }
            INITIATE_CHECKOUT_BNPL_PRE_TXN -> {
                viewModel.proceedCheckout(canShowBNPLPreTxnScreen = false)
            }

            OPEN_MYTRIPS_DEEPLINK -> {
                reDirectUserToMytrips(event.data as String)
            }
            OPEN_THANKYOU_PAGE -> {
                hideCheckoutLoader()
                openHotelThankYouActivity(event.data as? CheckoutResponse)
            }
            OPEN_PAYMENT_ACTIVITY -> {
                hideCheckoutLoader()
                openPaymentActivity(event.data as PaymentIntentData?)
            }
            OPEN_PAH_ACTIVITY -> {
                hideLoader()
                openPahActivity(false)
            }
            OPEN_FORCE_SHOW_PAH_ACTIVITY ->{
                hideLoader()
                openPahActivity(true)
            }
            OPEN_CHECKOUT_ERROR_FRAGMENT -> {
                hideLoader()
                openErrorFragment(event.data as HotelError)
            }
            OPEN_RTB_ERROR_FRAGMENT -> {
                hideLoader()
                openRtbErrorFragment(event.data as HotelError)
            }
            HotelBookingReviewActivityEvent.WRONG_GSTN_ENTERED -> {
                hideLoader()
                getReviewFragment()?.showGSTNError()
            }
            HotelBookingReviewActivityEvent.SUBSCRIPTION_REVIEW_ERROR -> {
                hideLoader()
                event.data.executeIfCast<String> {
                    ResourceProvider.instance.showToast(this,Toast.LENGTH_LONG)
                }
            }
            DOUBLE_BLACK_NAME_UPDATE -> {
                getReviewFragment()?.updateDoubleBlackName()
            }
            BACK_TO_LIST -> {
                moveToListActivity()
            }
            BACK_TO_DETAIL -> {
                moveToDetailActivity()
            }
            BACK_TO_PREVIOUS_ACTIVITY -> {
                moveToPreviousActivityInStack(event.data as Int)
            }
            OPEN_SPECIAL_REQUEST_FRAGMENT -> {
                openSpecialRequestFragment(event)
            }

            SHOW_RATE_PLAN_UPGRADE_SHEET -> {
                event.data.executeIfCast<RatePlansUpgrade> {
                    showRatePlanUpgradeBottomSheet(this)
                }
            }
            OPEN_HOTEL_POLICY_FRAGMENT -> {
                openHotelPolicyFragment(event)
            }
            SHOW_ROOM_POLICY_BOTTOMSHEET->{
                event.data.executeIfCast<Pair<String, RoomDetailUiDataModel>> {
                    openRoomDetailFragment(this)
                }
            }
            OPEN_TERMS_CONDITION_WEB_URL, OPEN_PRIVACY_POLICY_WEB_URL, MMT_BLACK_CLICK_EVENT, TRIP_DETAILS_CTA_CLICK_EVENT ->{
                launchCosmosWebView(url = event.data as String?)
            }
            HotelBookingReviewActivityEvent.OPEN_WEB_URL -> {
                val bundle = event.data as Bundle
                val title = bundle.getString(BookingReviewConstants.KEY_TITLE)
                val url = bundle.getString(BookingReviewConstants.KEY_URL)
                launchCosmosWebView(title = title,url = url)
            }
            AVAIL_API_ERROR->{
                event.data.executeIfCast<Pair<String?, String?>> {
                    handleV2ReviewFailureResponse(this)
                }

            }
            UPDATE_SPECIAL_REQUEST_ITEMS -> {
                event.data.executeIfCast<List<Category>> {
                    getReviewFragment()?.updateSpecialRequest(this)
                }
            }
            REFRESH_ACTIVITY -> {
                refreshActivity()
            }
            SHOW_PROGRESS_DIALOG -> {
                showLoader()
            }
            HIDE_PROGRESS_DIALOG -> {
                hideLoader()
            }
            FINISH_BOOKING_REVIEW_ACTIVITY -> {
                finishBookingActivity(event.data as Boolean)
            }

            RoomDetailEvent.SHOW_GUEST_REVIEWS -> {
                event.data.executeIfCast<RoomReviewBundleModel> {
                    addRoomReviewsFragment(this)
                }
            }

            HotelDetailClickEvents.FLEXI_TIME_SLOT_BOTTOMSHEET_RESET_CLICKED -> {
                getReviewFragment()?.resetFlexiCheckinTimeSlotInfo()
                viewModel.trackFlexiCheckinDropDownReset()
            }

            HotelBookingReviewActivityEvent.DAYUSE_ALERT_NO_CLICKED -> {
                viewModel.trackEvents("Day_USE_NO")
                finish()
            }

            TRACK_DAYUSE_ALERT_YES_CLICKED,
            RoomDetailEvent.TRACK_ROOM_UGC_EVENT -> {
                (event.data as? String)?.let {
                    viewModel.trackEvents(it)
                }
            }
            PAYMENT_BUTTON_CLICKED -> {
                proceedWithCheckOut()
            }
            SHOW_WEB_VIEW_CARD_CLICK ->{
               launchCosmosWebView(event.data as WebViewBundle)
            }
            HotelListingClickEvents.BOOKING_FOR_MYSELF->{
                if(event.data == true){
                    viewModel.trackBookingForMySelf()
                }else{
                    viewModel.trackBookingForOthers()
                }
            }
            HotelBookingReviewFragmentEvent.SHOW_QUICK_CHECKOUT_FLOW -> {
                event.data.executeIfCast<PaymentQcInfo> {
                    showQuickCheckOutFragment(this)
                }
            }

            HotelDetailClickEvents.OPEN_WEB_VIEW -> {
                event.data.executeIfCast<String> {
                    launchCosmosWebView(url = this)
                }
            }

            ComboDetailEvent.OPEN_COMBO_ROOM_FULL_IMAGES_SCREEN -> {
                val data = event.data as HotelFullSizeImageBundleData
                val bundleData = bookingReviewData.let {
                    data.copy(hotelInfo = HotelInfo(it.userSearchData.hotelName,
                        HotelGalleryTrackingData(it.hotelBaseTrackingData,it.locusTrackingData,it.userSearchData)))
                }
                openFullImageScreen(bundleData)
            }

            HotelBookingReviewActivityEvent.OPEN_NEED_HELP_FORM -> {
                event.data.executeIfCast<String> {
                    launchCosmosWebView(WebViewBundle(webViewUrl = this, showHeader = false))
                }
            }

            HotelBookingReviewActivityEvent.CHAT_BOT_CLICK -> {
                event.data.executeIfCast<ChatBotWidgetInfo> {
                    (currentFocus as? EditText)?.clearFocus()
                    loadChatBotWebView(chatBotWebViewUrl, null)
                    val eventName = String.format(HotelDetailTrackingConstants.CHAT_BOT_CLICKED, chatBotType?:"", 2)
                    trackChatBotActions(eventName)
                }
            }

            HotelBookingReviewActivityEvent.CHAT_BOT_CLICK_TRAVELPLEX -> {
                event.data.executeIfCast<ChatBotWidgetInfo> {
                    (currentFocus as? EditText)?.clearFocus()
                    viewModel.getChatBotData()?.let {
                        loadTravelPlex(CoreConstants.EMPTY_STRING, it)
                    }
                    val eventName = String.format(HotelDetailTrackingConstants.CHAT_BOT_CLICKED, chatBotType?:"", 2)
                    trackChatBotActions(eventName)
                }
            }

            HotelBookingReviewActivityEvent.CHAT_BOT_TOOL_TIP_ACTION -> {
                event.data.executeIfCast<String> {
                    trackChatBotActions(this)
                }
            }

            HotelBookingReviewFragmentEvent.OPEN_BENEFIT_DEAL_CONFIRMATION -> {
                event.data.executeIfCast<Pair<PopUpData,HotelBookingCoupon>> {
                    BenefitDealConfirmationFragment.getInstance(first, second).show(supportFragmentManager, BenefitDealConfirmationFragment.TAG)
                }
            }

            BENEFIT_DEAL_CONFIRMATION_APPROVED -> {
                event.data.executeIfCast<HotelBookingCoupon> {
                    getReviewFragment()?.applyBenefitDeal(this)
                    val eventName = String.format(BookingTrackingConstants.COUPON_PopupApplied,this.couponCode)
                    viewModel.trackEvents(eventName, OmnitureTrackingHelper.OEPK_C_1)
                }
            }
            BENEFIT_DEAL_POPUP_DISMISSED -> {
                event.data.executeIfCast<String>{
                    val eventName = String.format(BookingTrackingConstants.COUPON_PopupCancelled,this)
                    viewModel.trackEvents(eventName, OmnitureTrackingHelper.OEPK_C_1)
                }
            }
            OPEN_CONSENT_DIALOG -> {
                hideCheckoutLoader()
                hideLoader()
                event.data.executeIfCast<Pair<ConsentData,String>> {
                    viewModel.trackEvents(MY_BIZ_DUPLICATE_INTIMATION_SHOWN)
                    val modalBottomSheet = CorpReviewConsentBottomSheetFragment.newInstance(first, second)
                    modalBottomSheet.show(supportFragmentManager, "CorpReviewConsentBottomSheet")
                }
            }
            CorpReviewConsentBottomSheetFragment.DISMISS_CONSENT_BOTTOMSHEET ->{
                viewModel.trackEvents(MY_BIZ_DUPLICATE_INTIMATION_DISMISS_CLICK)
            }
            CorpReviewConsentBottomSheetFragment.ON_CONSENT_CONTINUE_CLICK ->{
                event.data.executeIfCast<String> {
                    viewModel.trackEvents(MY_BIZ_DUPLICATE_INTIMATION_CONTINUE_CLICK)
                    viewModel.checkDuplicateBooking = false
                    when(this){
                        CorpReviewConsentBottomSheetFragment.SOURCE_CHECKOUT -> {
                            showCheckoutLoader()
                            viewModel.proceedCheckout(viewModel.skipReason)
                        }
                        CorpReviewConsentBottomSheetFragment.SOURCE_APPROVAL -> {
                            sendApprovalRequest()
                        }
                        CorpReviewConsentBottomSheetFragment.SOURCE_ADD_TO_ITINERARY -> {
                            initItineraryFlow(viewModel.skipReason)
                        }
                    }
                }
            }
        }
    }

    open fun sendApprovalRequest() {
        // implementation provided in child class HotelCorpBookingReviewActivity
    }

    open fun initItineraryFlow(reasons: CorpReasons?) {
        // implementation provided in child class HotelCorpBookingReviewActivity
    }

    private fun openFullImageScreen(hotelFullSizeImageBundleData: HotelFullSizeImageBundleData) {
        with(Intent(HotelPageActionName.HOTEL_DETAIL_PHOTO_GALLERY)) {
            setPackage(MMTCore.mContext.packageName)
            val bundle = Bundle().apply {
                putParcelable(HotelFullSizeImageAbstractFragment.HOTEL_FULL_SIZE_DATA, hotelFullSizeImageBundleData)
            }
            putExtras(bundle)
            startActivity(this)
        }
    }


    /**
     * function to proceed with normal checkout flow
     */
    protected fun proceedWithCheckOut(skipReason : CorpReasons? = null) {
        getReviewFragment()?.continueButtonClick(skipReason = skipReason)
    }

    private fun openHotelThankYouActivity(checkoutResponse: CheckoutResponse?){
        val extraLobInfo = viewModel.getExtraHotelLobInfo()
        checkoutResponse?.bookingID?.let {
            val responseVO = PaymentResponseVO(checkoutResponse.bookingID, "", 0.0f,
                PaymentStatus.PAYMENT_SUCCESS, "")
            val intent = HotelScreenIntentUtil.getThankyouIntent().apply {
                putExtra(CoreConstants.PAYMENT_RESPONSE, GsonUtils.getInstance().serializeToJson(responseVO))
                putExtra(CoreConstants.EXTRA_INFO_FROM_LOB, GsonUtils.getInstance().serializeToJson(extraLobInfo))
                putExtra(HotelConstants.KEY_LOB, LOBS.HOTEL.lob)
            }
            GenericUtils.startActivityInternal(this, intent)
        }?:run{
            LogUtils.debug("ERROR FOUND", "WHILE OPENING THANKYOU PAGE")
        }
    }

    private fun handleV2ReviewFailureResponse(errorPair: Pair<String?, String?>) {
        if (errorPair.first.isNullOrEmpty() or errorPair.second.isNullOrEmpty()) {
            ResourceProvider.instance.showToast(R.string.htl_NETWORK_ERROR_MSG, Toast.LENGTH_SHORT)
            finishBookingActivity(false)
            return
        }
        val errorCode = errorPair.first
        val errorMessage = errorPair.second
        when(errorCode) {
            PAH_ERROR_CODE -> {
                handleAnalyticsError(errorMessage)
            }
            SOLD_OUT_CODE -> {
                viewModel.trackSoldOutErrorEvents(NO_HOTELS_FOUND_NORATES)
                viewModel.trackEvents(REVIEW_SOLD_OUT)
                ResourceProvider.instance.showToast(getString(R.string.htl_HOTEL_SOLD_ERROR_MESSAGE), Toast.LENGTH_LONG)
                finishBookingActivity(true)
            }
            else -> {
                viewModel.trackSoldOutErrorEvents(TARIFF_SOLD_OUT)
                viewModel.trackEvents(REVIEW_SOLD_OUT)
                ResourceProvider.instance.showToast(
                    errorMessage
                        ?: getString(R.string.htl_TARIFF_SOLD_OUT_ERROR_MESSAGE), Toast.LENGTH_LONG
                )
                finishBookingActivity(true)
            }
        }
    }

    private fun openWebViewBottomSheet(webViewBundle: WebViewBundle) {
        val fragment = HotelWebViewBottomSheet.newInstance(webViewBundle)
        fragment.show(supportFragmentManager, HotelWebViewBottomSheet.TAG)
    }

    /**
     * handle analytics error
     * save the phone number and block phone for server controlled time default 1 day
     *
     * @param message
     */
    private fun handleAnalyticsError(message: String?) {
        HotelSharedPrefUtil.instance.putLong(
            SharedPrefKeys.LAST_SAVED_TIME_FOR_MOB,
            System.currentTimeMillis()
        )
        val pahErrorDialog = PahErrorDialog()
        val args = Bundle()
        args.putString(ERROR, message)
        pahErrorDialog.arguments = args
        pahErrorDialog.show(supportFragmentManager, TAG_PAH_ERROR_FRAG)
    }

    private fun finishBookingActivity(isSoldOut:Boolean){
        var intent = Intent()
        var resultCode = ON_BACK_PRESSED
        when {
            isSoldOut -> {
                resultCode = SOLD_OUT_ON_REVIEW
            }
            isUserLoggedInOnReview -> {
                resultCode = LOGIN_FROM_REVIEW
            }
            else -> {
                viewModel.getUpsellIntent()?.let { intent = it }
            }
        }
        setResult(resultCode, intent)
        finish()
    }

    open fun saveLastReviewHitTime() {
        val time = System.currentTimeMillis()
        try {
            HotelSharedPrefUtil.instance.putLong(SharedPrefKeys.LAST_REVIEW_HIT_TIME, time)
        } catch (e: Exception) {
            LogUtils.error(TAG, e)
        }
    }

    private fun refreshActivity(){
        //Remove everything & restart with avail price api
        val currentFragmentTag = getTopFragmentTag()
        when(currentFragmentTag){
            HotelBookingReviewFragmentV2.TAG -> handleFragmentRefresh()
            else -> {}
        }
    }

    private fun showQuickCheckOutFragment( paymentQcInfo : PaymentQcInfo) {
                try {
                    val fragment =
                        (supportFragmentManager.findFragmentById(R.id.pricefooter) as? PaymentQcFragment)
                            ?: PaymentQcFragment.newInstance(paymentQcInfo).apply {
                                supportFragmentManager.beginTransaction().add(
                                    R.id.pricefooter, this
                                ).commitAllowingStateLoss()
                            }
            fragment.refresh(paymentQcInfo)
                } catch (exception: Exception) {
                    exception.printStackTrace()
                }


    }

    fun handleFragmentRefresh(){
        val fragment = getReviewFragment()
        when(fragment){
            is HotelBookingReviewFragmentV2 -> {
                fragment.refreshFragment()
                viewModel.trackCommonPageExitEvents()
            }
            else -> {}
        }
    }

    fun showLoader(){
        getReviewFragment()?.showProgressDialog()
    }

    fun hideLoader(){
        getReviewFragment()?.hideProgressDialog()
    }

    private fun showCheckoutLoader(){
        getReviewFragment()?.showCheckoutProgressDialog()
    }

    private fun hideCheckoutLoader(){
        getReviewFragment()?.hideCheckoutProgressDialog()
    }

    open fun openBookingReviewFragment() {
        val fragInstance =
            HotelBookingReviewFragmentV2.
            newInstance(intent?.extras ?: Bundle())
        supportFragmentManager.beginTransaction()
                .replace(R.id.container, fragInstance, HotelBookingReviewFragmentV2.TAG)
                .addToBackStack(HotelBookingReviewFragmentV2.TAG)
                .commitAllowingStateLoss()
        supportFragmentManager.executePendingTransactions()
    }

    private fun getComposeReviewFragment(): HotelBookingReviewFragmentV2? {
        return supportFragmentManager.findFragmentByTag(HotelBookingReviewFragmentV2.TAG) as? HotelBookingReviewFragmentV2?
    }


    open fun getReviewFragment(): IReviewFragment? {
        return supportFragmentManager.findFragmentByTag(HotelBookingReviewFragmentV2.TAG) as? IReviewFragment?
    }

    /**
     * Open bottomup pay option fragment if -
     *      1. (saved card available && wallet not available) or
     *      2. bnpl or
     *      3. emi available or
     *      4. paymentPlan is available
     */
    private fun openHotelPayOptionFragment(payOptionData: PayOptionData) {
        val paymentDialogFragment = if (ExperimentUtil.showInPaymentOptions()) {
            HotelInPayOptionFragment.getInstance(payOptionData)
        } else {
            HotelGccPayOptionFragment.getInstance(payOptionData)
        }
        try{
            KeyBoardUtils.hideKeyboard(this)
            paymentDialogFragment.showFragment(supportFragmentManager, HotelPayOptionFragment.TAG)
            getComposeReviewFragment()?.updateBlurState(true)
            viewDataBinding.vDummyBlack.visibility = View.VISIBLE
        }catch (exception:Exception) {

        }
    }

    private fun openFlexiDetailBottomSheet(flexiDetailBottomSheetData: FlexiDetailBottomSheetData) {
        val fragment = HotelFlexiDetailBottomSheetFragment()
        val bundle = Bundle()
        bundle.put(HotelFlexiDetailBottomSheetFragment.BUNDLE_DATA, flexiDetailBottomSheetData)
        fragment.arguments = bundle
        fragment.show(supportFragmentManager, HotelFlexiDetailBottomSheetFragment.TAG)
    }


    /**
     * Open bottomup checkout error fragment
     */
    fun openErrorFragment(error: HotelError) {
        val checkoutGenericErrorFragment = HotelCheckoutErrorFragment.getInstance(error)
        addBottomFragment(checkoutGenericErrorFragment,HotelCheckoutErrorFragment.TAG)
    }
    /**
     * Open Request to Book error fragment
     */
    private fun openRtbErrorFragment(error: HotelError) {
        val rtbErrorFragment = HotelRTBErrorFragment.getInstance(error)
        addBottomFragment(rtbErrorFragment,HotelRTBErrorFragment.TAG)
    }

    /**
     * Open co-traveller fragment to add or select co-traveller
     */
    private fun openCoTravellerFragment(coTravellerFragmentData: CoTravellerFragmentData) {
        val fragInstance = HotelCoTravellerFragment.getInstance(coTravellerFragmentData)
        fragInstance.show(supportFragmentManager, HotelCoTravellerFragment.TAG)
    }

    private fun openSpecialRequestFragment(event: HotelEvent) {
    }

    private fun showRatePlanUpgradeBottomSheet(ratePlansUpgrade: RatePlansUpgrade) {
        val fragInstance = HotelRatePlanUpgradeFragment.getInstance(ratePlansUpgrade)
        fragInstance.show(supportFragmentManager, HotelRatePlanUpgradeFragment.TAG)
    }

    private fun openHotelPolicyFragment(event: HotelEvent) {
        val hotelPolicy = event.data as HotelPolicyBundleData? ?: return
        val bundle = Bundle()
        bundle.putParcelable(HotelPolicyRuleFragment.KEY_BUNDLE_PARAM, hotelPolicy)
        val fragInstance = HotelPolicyRuleFragment.newInstance(bundle)
        supportFragmentManager.openFragment(fragInstance, R.id.child_frag_container, tag = HotelPolicyRuleFragment.TAG)
    }

    /**
     * Open payment activity
     */
    private fun openPaymentActivity(data: PaymentIntentData?) {
        if (data == null) {
            return
        }
        viewModel.trackSelectedUpsell()
        val intent = AppInterfaceHelper.get().getPaymentIntent(
            pokusLobName = LOBS.HOTEL,
            checkoutId = data.paymentRequest.bookingInfo.checkoutId ?: "",
            thankYouIntentAction = data.paymentRequest.thankYouActionUrl ?: "",
            lob = LOBS.HOTEL.lob,
            qcMeta = data.paymentRequest.qcMetaData
        )
            .putExtra(CoreConstants.PAYMENT_REQUEST, GsonUtils.getInstance().serializeToJson(data.paymentRequest))
            .putExtra(CoreConstants.EXTRA_INFO_FROM_LOB, GsonUtils.getInstance().serializeToJson(data.extraLobInfo))
            .putExtra(HotelConstants.KEY_LOB, LOBS.HOTEL.lob)
        intent.setPackage(MMTCore.mContext.packageName)
        observer?.startActivityForResult(intent, REQUEST_CODE_PAYMENT)
    }

    /**
     * Open HotelPAHPaymentActivity for PAH1, PAH_WITHOUT_CC payment
     */
    private fun openPahActivity(forceShowPahScreen: Boolean) {
        val pahIntentData = viewModel.getPahIntentData()?:return
        pahIntentData.apply {
            forceShow = forceShowPahScreen
        }
        val pahIntent = Intent(this, HotelPahPayActivity::class.java).apply {
            putExtra(PAH_INTENT_DATA, pahIntentData)
        }
        observer?.startActivityForResult(pahIntent, PAH_PAYMENT_ACTIVITY_REQUEST_CODE)
    }

    private fun openLoginActivity(header: String?) {
        val loginPageExtra = header?.let { LoginPageExtra(header) } ?: LoginPageExtra(R.string.htl_login_page_header_from_hotel_review_booking)
        loginPageExtra.isVerifyMobile = false
        loginPageExtra.isShowAsBottomSheet = true
        val intent = MMTAuth.getIntentForLoginActivity(this, loginPageExtra)
        intent?.let{
            observer?.startActivityForResult(it, LOGIN_FROM_REVIEW_CODE)
        }

    }

    private fun moveToListActivity() {
        if (intent.getBooleanExtra(HotelBookingReviewFragmentV2.IS_FROM_DEEPLINK,false)) {
            finish()
            return
        }
        // currently nothing is implementation, if any changes are required in future
        // do consider deeplink scenraios
    }

    private fun moveToDetailActivity() {
        if (intent.getBooleanExtra(HotelBookingReviewFragmentV2.IS_FROM_DEEPLINK,false)) {
            finish()
            return
        }
        val intent = HotelUtil.getDetailPageIntent()
        intent.putExtra(HotelConstants.BUNDLE_FROM_REVIEW_FRAUD, true)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
    }

    private fun moveToPreviousActivityInStack(resultCode: Int) {
        if (resultCode != NO_RESULT_CODE)
            setResult(resultCode)
        finish()
    }

    private fun handlePaymentResult(resultCode: Int) {
        if(resultCode == RESULT_CODE_PAYMENT){
            viewModel.setUnblockInventory()
            if (viewModel.canShowCallToBook()) {
                viewModel.showCallToBookBottomSheet()
            }
        }
        viewModel.updatePrevPageTrackingInfo()
    }


    private fun handleUserLogin(resultCode: Int, data: Intent?){
        if(resultCode == RESULT_OK){
            isUserLoggedInOnReview = true
            refreshActivity()
        }
    }

    private fun handlePahActivityResult(resultCode: Int, data: Intent?) {
        when (resultCode) {
            CheckoutErrorCodes.CHECKOUT_ERROR -> {
                val checkoutResponse = data?.getParcelableExtra<CheckoutResponse>(HotelConstants.CHECKOUT_RESPONSE)
                checkoutResponse?.let {
                    viewModel.handleCheckoutResponse(it)
                }?:run{
                    viewModel.handleSomethingWrong()
                }
            }
        }
        viewModel.updatePrevPageTrackingInfo()
    }

    /**
     * method to open [FragmentRoomDetail] to display detailed information for the selected Room
     */
    private fun openRoomDetailFragment(pairData : Pair<String, RoomDetailUiDataModel>) {
        viewModel.trackEvents(RoomDetailTrackingEvent.OPEN_ROOM_DETAIL)

        val roomDetailUiDataModel = pairData.second
        val roomDetailEventData = ShowRoomDetailEventData(hotelID = roomDetailUiDataModel.hotelId,
                roomCode = roomDetailUiDataModel.roomCode,
                ratePlanCode = roomDetailUiDataModel.ratePlanCode,
                txnKey = pairData.first,
                showRatePlanDetail = true,
                countryCode = viewModel.getCountryCode(),
                isEntireProperty = viewModel.isEntireProperty())
        val fragmentRatePlanDetail = FragmentRoomDetail.getInstance(roomDetailEventData)
        supportFragmentManager.beginTransaction()
                .add(R.id.bottom_frag_container, fragmentRatePlanDetail, FragmentRoomDetail.TAG)
                .addToBackStack(FragmentRoomDetail.TAG)
                .commitAllowingStateLoss()
        supportFragmentManager.executePendingTransactions()
    }

    override fun onOkGotItClicked() {
        restartDetail()
    }

    private fun restartDetail() {
        if (intent.getBooleanExtra(HotelBookingReviewFragmentV2.IS_FROM_DEEPLINK,false)) {
            finish()
            return
        }
        val intent = HotelUtil.getDetailPageIntent()
        intent.putExtra(HotelConstants.BUNDLE_FROM_REVIEW_FRAUD, true)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP)
        startActivity(intent)
        finish()
    }

    private fun reDirectUserToMytrips(redirectUrl:String) {
        HotelMigratorHelper.instance.openDeepLink(redirectUrl,false) // open myTrips DeepLink url
    }

    /**
     * method to add [FragmentCategoryReviews] to display user's reviews for the given room
     */
    private fun addRoomReviewsFragment(roomReviewBundleModel : RoomReviewBundleModel) {
        // track event

        // open fragment
        viewModel.trackEvents(roomReviewBundleModel.roomReviewEventVal)
        val fragmentRoomReviews = FragmentCategoryReviewsV2.getInstance(reviewBundleModel = roomReviewBundleModel)
        supportFragmentManager.openFragment(fragment = fragmentRoomReviews,
                container = R.id.bottom_frag_container, addFragment = true, addToBackStack = true, tag = FragmentCategoryReviewsV2.TAG)
    }

    override fun onStop() {
        viewModel.trackCommonPageExitEvents()
        super.onStop()
    }

    override fun getUserSearchData(): UserSearchData {
        return bookingReviewData.userSearchData
    }

    override fun getBaseTrackingData(): HotelBaseTrackingData {
        return bookingReviewData.hotelBaseTrackingData
    }

    override fun onActivityResultReceived(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            LOGIN_FROM_REVIEW_CODE -> handleUserLogin(resultCode, data)
            PAH_PAYMENT_ACTIVITY_REQUEST_CODE -> handlePahActivityResult(resultCode, data)
            REQUEST_CODE_PAYMENT -> handlePaymentResult(resultCode)
        }
    }

    override fun onInfoButtonClicked() {
    }

    override fun onPayNowClicked(metaData: Any?, lobSource: String?) {
        viewModel.setQcMetaData(metaData)
        if (viewModel.isRequestToBookFlow() && !LoginUtils.isLoggedIn) {
            val loginHeader = resources.getString(R.string.htl_login_to_book)
            openLoginActivity(loginHeader)
        }else{
            proceedWithCheckOut()
        }
    }

    override fun trackEvent(trackingData: TrackingData) {
        trackingData.omnitureEvent?.let {
            viewModel.trackMMTShownAndClick(it)
        }
    }

    override fun onHeightChange(height: Int) {
        // Nothing
    }

    // ============================== IChatBotActivityInterface ====================================

    override fun getWebView() : WebView? {
        return viewDataBinding.webView
    }

    override fun getTravelPlexContainer(): ViewGroup? {
        return viewDataBinding.travelPlexContainer
    }

    override fun trackChatBotActions(eventName: String) {
        viewModel.trackEvents(eventName, OmnitureTrackingHelper.OEPK_C_1)
    }

    override fun myraChatbotOpened() {
        trackChatBotActions("chatbot_opened")
    }

    override fun onUpdateChatBotUnreadMsg(hasUnreadMsg: Boolean) {
        getComposeReviewFragment()?.updateChatBotUnreadMsg(hasUnreadMsg)
    }

    // =============================================================================================
}



