package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.compose.runtime.mutableStateOf
import androidx.databinding.BaseObservable
import androidx.databinding.ObservableBoolean
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.uikit.util.isNotNullAndEmpty

class CorpBookingGstDetailsViewModel(private var gstData: GSTNDetails?) :  BaseObservable(), DiffUtilRecycleItem {

    var showBillingAddrees = ObservableBoolean(true)
    var showBillingAddreesState = mutableStateOf(true)
    var expandedGSTDetailsCardView  = ObservableBoolean(true)
    var expandedGSTDetailsCardViewState = mutableStateOf(true)

    val billingAddress = mutableStateOf("")
    val gstnDetails = mutableStateOf("")

    init {
        getAddress()
        getOrganisationGSTN()
    }

    fun updateData(data: GSTNDetails?) {
        this.gstData = data
        showBillingAddreesState.value = true
        showBillingAddrees.set(true)
        getAddress()
        getOrganisationGSTN()
        notifyChange()
    }

    fun getData(): GSTNDetails? {
        return this.gstData
    }

    fun getOrganisationName() : String{
        if(getData()?.organizationName == null) return CoreConstants.EMPTY_STRING
        return ResourceProvider.instance.getString(R.string.htl_organisation_name, getData()?.organizationName ?: CoreConstants.EMPTY_STRING)
    }

    fun collapseViewClick(){
        expandedGSTDetailsCardView.set(!expandedGSTDetailsCardView.get())
        expandedGSTDetailsCardViewState.value = !expandedGSTDetailsCardViewState.value
    }

    fun getAddress(): String {
        var address = ""
        gstData?.let {
            if( it.address1.isNullOrEmpty() ){
                showBillingAddreesState.value = false
                showBillingAddrees.set(false)
            } else {
                if(it.address1.isNotNullAndEmpty()){
                    address += it.address1 + CoreConstants.SPACE
                }
                if(it.city.isNotNullAndEmpty()){
                    address += it.city + CoreConstants.SPACE
                }
                if(it.pinCode.isNotNullAndEmpty()){
                    address += it.pinCode
                }
                address = address.trim()
            }
        }
        billingAddress.value = ResourceProvider.instance.getString(R.string.htl_organisation_address, address)
        return billingAddress.value
    }

    fun getOrganisationGSTN(): String{
        gstnDetails.value = if (getData()?.gstn != null) {
            ResourceProvider.instance.getString(R.string.htl_organisation_gstn, getData()?.gstn)
        } else {
            CoreConstants.EMPTY_STRING
        }
        return gstnDetails.value
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CORP_GST
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_GST
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as CorpBookingGstDetailsViewModel).gstData
        return gstData == matchedWith
    }

    override fun cardName(): String {
        return "Corp Review GST Data"
    }
}