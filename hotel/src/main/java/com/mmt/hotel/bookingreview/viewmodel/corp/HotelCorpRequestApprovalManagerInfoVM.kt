package com.mmt.hotel.bookingreview.viewmodel.corp

import android.os.Handler
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.constant.CoreConstants.SPACE
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.model.response.CorpApprovingManager
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalReviewViewModel.Companion.REQUEST_APPROVAL_MANAGER_INFO_ITEM
import com.mmt.hotel.common.data.LinearLayoutItemData

class HotelCorpRequestApprovalManagerInfoVM(val managersList: List<CorpApprovingManager>, val eventStream: MutableLiveData<HotelEvent>) : AbstractRecyclerItem {

    val itemsList = ObservableArrayList<LinearLayoutItemData>()
    val showMangersInfo = ObservableBoolean(false)
    var index = 0
    var backGround: ObservableInt = ObservableInt(R.drawable.ic_request_approval_manager_background_init)

    init {
        managersList.forEach {
            itemsList.add(LinearLayoutItemData(R.layout.hotel_corp_request_approval_manager_item, BR.viewModel, HotelCorpRequestApprovalManagerItemVM(it, ++index, managersList.size)))
        }
    }

    override fun getItemType(): Int {
        return REQUEST_APPROVAL_MANAGER_INFO_ITEM
    }

    fun arrowClick() {
        Handler().postDelayed({
            eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.EVENT_HANDLE_ELEVATION))
        }, 500)
        showMangersInfo.set(!showMangersInfo.get())
        if(showMangersInfo.get()) {
            backGround.set(R.drawable.ic_request_approval_manager_background)
        } else {
            backGround.set(R.drawable.ic_request_approval_manager_background_init)
        }
    }

    fun getTitle(): String {
        if (managersList.size == 1) {
            return managersList[0].name ?: EMPTY_STRING
        } else if (managersList.size == 2) {
            return managersList[0].name + SPACE + "&" + SPACE + managersList[1].name
        } else if (managersList.size > 2) {
            return managersList[0].name + SPACE + "&" + SPACE + managersList[1].name + SPACE + "+" + SPACE + "${managersList.size - 2} Others"
        }
        return EMPTY_STRING
    }
}