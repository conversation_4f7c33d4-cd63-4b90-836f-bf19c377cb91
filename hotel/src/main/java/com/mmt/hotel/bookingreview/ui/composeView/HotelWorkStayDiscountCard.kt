package com.mmt.hotel.bookingreview.ui.composeView


import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.utils.parseHtmlString
import com.mmt.hotel.detail.viewModel.cardsViewModel.WorkStayDiscountCardViewModel
import com.mmt.hotel.widget.compose.MMTAnnotatedTextView
import com.mmt.uikit.util.isNotNullAndEmpty

@Composable
fun HotelWorkStayDiscountCard(
    modifier: Modifier = Modifier,
    viewModel: WorkStayDiscountCardViewModel
) {

    val uiState = viewModel.uiState.value.data

    Box(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(top = dimensionResource(viewModel.cardTopMargin()))
            .clip(RoundedCornerShape(16.dp))
            .border(
                width = 1.dp,
                color = if(uiState.borderColor?.isNotNullAndEmpty() == true) Color(android.graphics.Color.parseColor(uiState.borderColor)) else Color.Transparent,
                shape = RoundedCornerShape(16.dp)
            )
    ) {

        // BG Image
        com.mmt.hotel.common.util.compose.LoadImage(
            imageUrl = uiState.bgImageUrl,
            modifier = Modifier
                .matchParentSize(),
            contentScale = ContentScale.FillBounds
        )

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.Center)
                .padding(
                    dimensionResource(id = R.dimen.margin_medium)
                )
        ) {
            // Title
            uiState.titleText?.parseHtmlString()?.let {
                MMTAnnotatedTextView(
                    text = it,
                    fontSize = spDimensionResource(id = R.dimen.margin_medium_extra),
                    mmtFontStyle = latoBlack,
                    color = Color.Black
                )
            }

            // Subtitle
            uiState.subText?.let {
                MMTAnnotatedTextView(
                    text = it.parseHtmlString(),
                    fontSize = spDimensionResource(id = R.dimen.margin_medium),
                    mmtFontStyle = latoRegular,
                    color = Color.Black
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewHotelWorkStayDiscountCard() {
    // Create a sample CardInfo
    val sampleData = com.mmt.hotel.common.model.response.persuasionCards.CardInfo(
            titleText = "Sample Title",
            subText = "Sample Subtext",
            bgImageUrl = "https://sampleurl.com/bg.png",
            borderColor = "#000000",
            index = 0
    )

    // Create a sample WorkStayDiscountCardViewModel
    val sampleViewModel = WorkStayDiscountCardViewModel(
            data = sampleData,
            callingSource = WorkStayDiscountCardViewModel.CallingSource.DETAIL,
    )

    // Use your HotelWorkStayDiscountCard function with the sample data
    HotelWorkStayDiscountCard(modifier = Modifier, viewModel = sampleViewModel)
}