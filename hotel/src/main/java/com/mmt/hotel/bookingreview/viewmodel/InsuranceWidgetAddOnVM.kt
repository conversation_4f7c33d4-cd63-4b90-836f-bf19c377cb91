package com.mmt.hotel.bookingreview.viewmodel

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.gommt.insurance.action.InsuranceWidgetListener
import com.gommt.insurance.action.InsuranceWidgetListenerData
import com.gommt.insurance.data.InsuranceWidgetData
import com.mmt.core.util.DateUtil
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.request.AddOnUnitSelected
import com.mmt.hotel.bookingreview.model.request.SelectedInsuranceItem
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.userReviews.featured.events.HotelReviewEvents

/**
 * Created by sunil.jain on 31/05/21.
 */
class InsuranceWidgetAddOnVM(
    val insuranceWidgetDataUi: InsuranceWidgetData?,
    val eventStream: MutableLiveData<HotelEvent>,
    val totalGuests: Int,
    val lobPageName: String
) : BaseObservable(), DiffUtilRecycleItem {

    var acceptanceCallback : ((isAccepted: Boolean) -> Unit)? = null
    val widgetListener =  object : InsuranceWidgetListener {
        override fun onSelectionChanged(
            insuranceSelected: Boolean,
            additionalData: InsuranceWidgetListenerData?,
            acceptanceListener: (isAccepted: Boolean) -> Unit
        ) {
            acceptanceCallback = acceptanceListener
//            acceptanceListener.invoke(true)
            additionalData?.let {
                val  startDate = DateUtil.getDateFromMillisecondsWithFormatInEngLocale(it.startDate, DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS)
                val  endDate =  DateUtil.getDateFromMillisecondsWithFormatInEngLocale(it.endDate, DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS)
                val selectedInsuranceItem = SelectedInsuranceItem(insuranceWidgetDataUi?.priceData?.id ?: 0, unitSelected= AddOnUnitSelected(totalGuests,0), startDate = startDate, endDate = endDate, totalPrice = additionalData?.pricePerPax)
                insuranceSelectedClick(selectedInsuranceItem, insuranceSelected)
            }
        }
    }


    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.INSURANCE_CARD_WIDGET
    }


    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.INSURANCE
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        (item as? InsuranceWidgetAddOnVM)?.let {
            return it.insuranceWidgetDataUi == this.insuranceWidgetDataUi && it.totalGuests == this.totalGuests
        }
        return false
    }

    override fun cardName(): String {
        return "Review Insurance Add On Widget"
    }

     fun insuranceSelectedClick(
         additionalData: SelectedInsuranceItem,
         insuranceSelected: Boolean) {
         eventStream.value = HotelEvent(HotelReviewEvents.INSURANCE_WIDGET_SELECTED_OR_UNSELECTED, additionalData.to(insuranceSelected))
    }
}