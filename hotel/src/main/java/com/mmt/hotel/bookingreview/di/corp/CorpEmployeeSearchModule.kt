package com.mmt.hotel.bookingreview.di.corp

import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.repository.HotelCorpBookingReviewRepository
import com.mmt.hotel.bookingreview.repository.HotelCorpBookingReviewRepositoryImpl
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpEmployeeSearchViewModel
import com.mmt.hotel.common.di.NamedConstants
import dagger.Module
import dagger.Binds
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap
import javax.inject.Named

@Module
@InstallIn(FragmentComponent::class)
class CorpEmployeeSearchModule {
    @Provides
    @IntoMap
    @ViewModelKey(CorpEmployeeSearchViewModel::class)
    fun provideActivityCorpAddCoTravellerSearchViewModel(viewModel: CorpEmployeeSearchViewModel): HotelViewModel = viewModel

    @Provides
    @Named(NamedConstants.CORP_EMPLOYEE_SEARCH_ACTIVITY)
    fun provideCorpBookingReviewRepository(repositoryImp: HotelCorpBookingReviewRepositoryImpl): HotelCorpBookingReviewRepository = repositoryImp
}