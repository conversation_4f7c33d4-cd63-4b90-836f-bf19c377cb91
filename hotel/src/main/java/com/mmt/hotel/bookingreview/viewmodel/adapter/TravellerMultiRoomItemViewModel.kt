package com.mmt.hotel.bookingreview.viewmodel.adapter

import com.mmt.hotel.R
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.bookingreview.dataModel.TravellerUiData
import com.mmt.hotel.bookingreview.model.UserInputDetail

class TravellerMultiRoomItemViewModel(private val travellerUiData: TravellerUiData, private val position:Int, val userInputDetail: UserInputDetail) {

    fun getNameHeader(): String {
        var nameHeader = EMPTY_STRING
        nameHeader += ResourceProvider.instance.getString(
            R.string.htl_multi_room_traveller_header_item,
            position
        )
        if (travellerUiData.foreignTravel) {
            nameHeader += ResourceProvider.instance.getString(R.string.htl_name_header_intl_travel)
        } else {
            nameHeader += ResourceProvider.instance.getString(R.string.htl_name_header)
        }
        return nameHeader
    }

    fun getSpinnerArray(): List<String> {
        return ResourceProvider.instance.getStringArray(R.array.TRAVELLER_TITLE).toList()
    }
}