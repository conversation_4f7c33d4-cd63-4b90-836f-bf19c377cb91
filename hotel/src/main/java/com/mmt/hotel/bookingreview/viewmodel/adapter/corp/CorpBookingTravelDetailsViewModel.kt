package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagResponseV2
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

class CorpBookingTravelDetailsViewModel(private  var corpTripTagResponseV2: CorpTripTagResponseV2?, val eventStream: MutableLiveData<HotelEvent>) : BaseObservable(), DiffUtilRecycleItem {

    private var travelDetailFormViewModelMap = HashMap<String, HotelCorpTravelDetailFormItemViewModelV2>()

    fun updateTripTagData(data: CorpTripTagResponseV2) {
        this.corpTripTagResponseV2 = data
        notifyChange()
    }

    fun getTripTagData(): CorpTripTagResponseV2? {
        return this.corpTripTagResponseV2
    }

    fun setTravelDetailFormViewModelMap(data:  HashMap<String, HotelCorpTravelDetailFormItemViewModelV2>) {
        this.travelDetailFormViewModelMap = data
    }

    fun getTravelDetailFormViewModel(key: String): HotelCorpTravelDetailFormItemViewModelV2? {
        return travelDetailFormViewModelMap[key]
    }

    fun validateTravelDetailForm(): Boolean {
        var isValid: Boolean = true
        corpTripTagResponseV2?.attributeList?.forEach {
            if(it.mandatoryCheck && it.attributeSelectedValue.isNullOrEmpty()) {
                travelDetailFormViewModelMap[it.attributeId]?.showErrorObservable?.set(true)
                travelDetailFormViewModelMap[it.attributeId]?.showErrorState?.value = true
                isValid = false
            }
        }
        return isValid
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CORP_TRIP_TAG
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_TRIP_TAG
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as CorpBookingTravelDetailsViewModel).corpTripTagResponseV2
        return corpTripTagResponseV2 == matchedWith
    }

    override fun cardName(): String {
        return "Corp Review Trip Tag"
    }
}