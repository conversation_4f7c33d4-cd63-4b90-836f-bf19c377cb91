package com.mmt.hotel.bookingreview.ui

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.model.response.PopUpData
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelCouponFragmentEvent
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.bookingreview.viewmodel.BenefitDealConfirmationVM
import com.mmt.hotel.databinding.HtlBenefitDealConfirmationFragmentBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class BenefitDealConfirmationFragment : HotelBottomSheetDialogFragment<BenefitDealConfirmationVM, HtlBenefitDealConfirmationFragmentBinding>() {

    companion object {
        const val TAG = "BenefitDealConfirmationFragment"
        const val BUNDLE_KEY_DATA = "bundle_key_data"
        const val BUNDLE_KEY_COUPON_DATA = "bundle_key_coupon_data"

        fun getInstance(data : PopUpData, couponData: HotelBookingCoupon): BenefitDealConfirmationFragment {
            return BenefitDealConfirmationFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(BUNDLE_KEY_DATA, data)
                    putParcelable(BUNDLE_KEY_COUPON_DATA, couponData)
                }
            }
        }
    }

    @Inject
    lateinit var viewModelFactory: HotelViewModelFactory
    var activitySharedViewModel: HotelEventSharedViewModel? = null
    var isDismissedOnApproval = false

    override fun initViewModel() = getViewModel<BenefitDealConfirmationVM>(viewModelFactory)

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId() = R.layout.htl_benefit_deal_confirmation_fragment

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL,R.style.HotelBottomSheetCornerRadiusDialogTheme) // setting style to let window resize itself in order to display editText above keyboard
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        if (dialog is BottomSheetDialog) {
            dialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED // to keep the entire dialog above keyboard
        }
        return dialog
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {

            HotelCouponFragmentEvent.DISMISS_FRAGMENT -> {
                dismissFragment()
            }

            HotelCouponFragmentEvent.BENEFIT_DEAL_CONFIRMATION_APPROVED -> {
                activitySharedViewModel?.updateEventStream(event)
                isDismissedOnApproval = true
                dismissFragment()
            }
        }
    }

    /**
     * method to clear references before dismissing fragment
     */
    fun dismissFragment() {
        dismiss()
    }

    override fun onDismiss(dialog: DialogInterface) {
        if(!isDismissedOnApproval)
            activitySharedViewModel?.updateEventStream(HotelCouponFragmentEvent.BENEFIT_DEAL_POPUP_DISMISSED, viewModel.coupon.couponCode)
        super.onDismiss(dialog)
    }
}