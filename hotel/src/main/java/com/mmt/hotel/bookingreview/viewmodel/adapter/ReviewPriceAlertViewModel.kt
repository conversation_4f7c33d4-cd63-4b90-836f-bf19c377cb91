package com.mmt.hotel.bookingreview.viewmodel.adapter

import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.dataModel.alerts.PriceChangeAlertUiDataModel
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem


class ReviewPriceAlertViewModel(val data: PriceChangeAlertUiDataModel) : DiffUtilRecycleItem {

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.REVIEW_PRICE_ALERT
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as ReviewPriceAlertViewModel).data
        return data == matchedWith
    }

    override fun cardName(): String {
        return "Review Price Alert"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.PRICE_ALERT
    }

}