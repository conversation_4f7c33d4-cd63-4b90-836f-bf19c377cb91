package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.EventType
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.BookingReviewPriceCardAdapterItem
import com.mmt.hotel.bookingreview.dataModel.BookingPriceUIItemData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.common.util.KEY_INSURANCE
import com.mmt.hotel.common.util.KEY_TAXES
import com.mmt.hotel.common.util.KEY_TOTAL_DISCOUNT
import com.mmt.hotel.selectRoom.viewmodel.CouponPersuasionVM


class BookingReviewPriceItemViewModel(
    var data: BookingPriceUIItemData,
    private val eventStream: MutableLiveData<HotelEvent>,
    val type : Int = BookingReviewPriceCardAdapterItem.ITEM_BREAKUP,
    private val eventLambda: ((HotelEvent) -> Unit)? = null
) : AbstractRecyclerItem {

    fun onInfoIconClick() {
        when (data.key) {
            KEY_TOTAL_DISCOUNT -> openDiscountBottomSheet()
            KEY_TAXES -> openTaxesBottomSheet()
            KEY_INSURANCE -> onInsuranceInfoClicked()
        }
    }

    private fun openDiscountBottomSheet() {
        val discountBottomSheetEvent = HotelEvent(
                HotelBookingReviewFragmentEvent.SHOW_DISCOUNT_BREAKUP_BOTTOMSHEET,
                data.itemDetails, eventType = EventType.BOTTOM_SHEET
        )
        eventLambda?.invoke(discountBottomSheetEvent)?:run {
            eventStream.postValue(discountBottomSheetEvent)
        }

    }

    private fun openTaxesBottomSheet() {
        val breakupBottomSheet = HotelEvent(
                HotelBookingReviewFragmentEvent.SHOW_TAXES_BREAKUP_BOTTOMSHEET,
                data.itemDetails, eventType = EventType.BOTTOM_SHEET
        )
        eventLambda?.invoke(breakupBottomSheet)?:run {
            eventStream.postValue(breakupBottomSheet)
        }
    }

    private fun onInsuranceInfoClicked() {
        val priceBottomSheet = HotelEvent(
                HotelBookingReviewFragmentEvent.SHOW_PRICE_ITEM_BREAKUP,
                data, eventType = EventType.BOTTOM_SHEET
        )
        eventLambda?.invoke(priceBottomSheet)?:run {
            eventStream.postValue(priceBottomSheet)
        }
    }

    override fun getItemType(): Int {
        return type
    }

    fun getInfoBannerVM() = data.couponPersuasion?.let { CouponPersuasionVM(data.couponPersuasion, 8f) }

}