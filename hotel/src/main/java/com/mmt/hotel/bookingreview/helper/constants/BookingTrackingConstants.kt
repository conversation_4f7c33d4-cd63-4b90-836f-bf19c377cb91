package com.mmt.hotel.bookingreview.helper.constants

class BookingTrackingConstants {
    companion object {
        const val REVIEW_ROOM_POLICY_CLICKED = "review_roompolicy_click"
        const val REVIEW_MUST_READ_CLICKED = "review_mustread_click"
        const val REVIEW_SPECIAL_REQUEST_CLICKED = "review_request_click"
        const val REVIEW_DISCOUNT_DETAIL_CLICKED = "review_discount_more"
        const val REVIEW_SME_DETAIL_CLICKED = "review_sme_more"
        const val REVIEW_TAXES_DETAIL_CLICKED = "review_taxes_more"
        const val REVIEW_CHARITY_DETAIL_CLICKED = "review_donation_more"
        const val REVIEW_DONATE_SELECT = "review_donate_select"
        const val REVIEW_COUPON_SUCCESS = "HOTEL_TRAVELER_COUPONCODESUCCESS_"
        const val REVIEW_COUPON_FAILURE = "HOTEL_TRAVELER_COUPONCODEFAILURE_"
        const val REVIEW_SOLD_OUT = "review_soldout"
        const val NO_HOTELS_FOUND_NORATES = "NoHotelsFound_NoRates_"
        const val TARIFF_SOLD_OUT = "TariffSoldOut_"
        const val CHECKOUT_ERROR = "Hotel_Checkout_Error_"
        const val PRICE_UP_REVIEW = "PRICE_REVIEW_UP_"
        const val PRICE_DOWN_REVIEW = "PRICE_REVIEW_DOWN_"
        const val INSURANCE_ADDED = "insurance_added_%s_%s_%s"
        const val INSURANCE_REMOVED = "insurance_removed_%s_%s_%s"
        const val INSURANCE_SHOWN = "insurance_shown"
        const val INSURANCE_VIEW_BENEFIT_CLICKED = "view_benefit_clicked"
        const val INSURANCE_DONE_CLICKED = "insurance_done_clicked"
        // upsell related events
        const val UPSELL_OPTIONS_SHOWN ="upsell_shown_" // (Upsell_shown_1_Meal_Cancellation_600 & Upsell_shown_2_Meal_400)
        const val UPSELL_OPTION_SELECTED = "upsell_selected_" //(Upsell_selected_Meal_price_400)
        const val UPSELL_OPTION_REMOVED = "upsell_removed_" //(Upsell_removed_Meal_price_400)
        const val DIRECT_CONNECT_CARD_SHOWN = "direct_connect_card_shown"
        const val HAVE_COUPON_CODE = "review_have_coupon_clicked"
        const val USER_DETAILS_ERROR = "review_userdetails_error"
        const val COUPON_APPLIED = "Coupon_applied_successfully"
        const val TCS_LEARN_MORE_CLICKED = "tcs_education_learnmore_clicked"
        const val COUPON_TnCClicked = "%s_TnCClicked"
        const val COUPON_PopupCancelled = "%s_Popupcancelled"
        const val COUPON_PopupApplied = "%s_PopupApplied"
        const val NO_COST_EMI_REVIEW_SHOWN = "nocost_emi_review_shown"
        const val NO_COST_EMI_PLANS_REVIEW_CLICKED = "nocost_emi_plans_review_clicked"
        const val NO_COST_EMI_SEARCH_PERFORMED = "nocost_search_performed"
        const val NO_COST_EMI_SEARCH_NO_RESULTS = "nocost_search_no_results"
        const val NO_COST_EMI_BANK_SELECTED = "nocost_plan_%s_selected"
        const val COMPLIANCE_UNSELECTED_ERROR ="compliance_unselected_error"
        const val AIRTEL_INTL_ROAMING_CONSENT_GIVEN ="Airtel_Interested"
        const val AIRTEL_INTL_ROAMING_CONSENT_REMOVED ="Airtel_Removed"
        const val AIRTEL_INTL_ROAMING_TNC_CLICKED ="Airtel_Tnc_Clicked"
        const val BNPL_BOOKING_CANCELLED = "bnpl_booking_cancelled"

        const val MY_BIZ_DUPLICATE_INTIMATION_SHOWN ="myBiz_duplicate_intimation_shown"
        const val MY_BIZ_DUPLICATE_INTIMATION_DISMISS_CLICK ="duplicate_intimation_dismiss_clicked"
        const val MY_BIZ_DUPLICATE_INTIMATION_CONTINUE_CLICK ="duplicate_intimation_Continue_clicked"
    }
}