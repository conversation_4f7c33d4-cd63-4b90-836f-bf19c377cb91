package com.mmt.hotel.bookingreview.helper

import android.content.Context
import android.view.ViewGroup
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent

class CorpTravelDetailFormHelper(val context: Context, val parent: ViewGroup, val eventStream: MutableLiveData<HotelEvent>) {

    enum class ItemTypes(val type: String) {
        CHECK_BOX("MultiSelect"),
        DROP_DOWN("DropDown"),
        LABEL("LABEL"),
        DESC("DESC"),
        INPUT_BOX("FreeText"),
        RADIO_GROUP("RadioButton")
    }
}