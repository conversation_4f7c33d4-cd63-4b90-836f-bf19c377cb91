package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class DoubleBlackInfo(val benefitId: String?,
                           val bookingEligible: Boolean?,
                           val userEligible: Boolean?,
                           val moreVerificationRequired: Boolean?,
                           val messageHeader: String?,
                           val messageText: String?,
                           val registeredFirstName: String?,
                           val registeredLastName: String?) : Parcelable
