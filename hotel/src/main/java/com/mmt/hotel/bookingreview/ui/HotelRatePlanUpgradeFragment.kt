package com.mmt.hotel.bookingreview.ui

import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.adapter.RatePlanUpgradeAdapter
import com.mmt.hotel.bookingreview.helper.constants.RatePlanUpgradeTrackingConstants
import com.mmt.hotel.bookingreview.model.response.RatePlansUpgrade
import com.mmt.hotel.bookingreview.viewmodel.RatePlanUpgradeFragmentViewModel
import com.mmt.hotel.databinding.FragmentRatePlanUpgradeBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelRatePlanUpgradeFragment
    : HotelBottomSheetDialogFragment<RatePlanUpgradeFragmentViewModel, FragmentRatePlanUpgradeBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel: HotelEventSharedViewModel? = null
    var isButtonClicked = false

    override fun initViewModel(): RatePlanUpgradeFragmentViewModel = getViewModel(factory)

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int = R.layout.fragment_rate_plan_upgrade

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
        viewDataBinding.selectedRv.adapter = RatePlanUpgradeAdapter(mutableListOf())
        viewDataBinding.upgradedRv.adapter = RatePlanUpgradeAdapter(mutableListOf())
        viewModel.trackingHelper.trackShownEvent(
            OmnitureTrackingHelper.OEPK_c1,
            RatePlanUpgradeTrackingConstants.UPGRADE_SHOWN.format(viewModel.data.upgradeType)
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.HotelBottomSheetCornerRadiusDialogTheme)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (dialog as? BottomSheetDialog)?.behavior?.state = BottomSheetBehavior.STATE_EXPANDED
        sendEventToActivity(HotelEvent(SHOW_BLUR_BG))
    }

    override fun handleEvents(event: HotelEvent) {
        isButtonClicked = true
        sendEventToActivity(event)
        dismiss()
    }

    override fun onDismiss( dialog: DialogInterface){
        if (isButtonClicked.not()) {
            //This is fired to dismiss the Blur Background when sheet is dismissed by backPress or outside click
            sendEventToActivity(HotelEvent(DISMISS_FRAGMENT))
            viewModel.trackClickOutside()
            isButtonClicked = true
        }
    }

    fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }

    companion object {
        val TAG = "HotelCoTravellerFragment"
        val RATE_PLAN_UPGRADE_DATA = "RATE_PLAN_UPGRADE_DATA"
        const val DISMISS_FRAGMENT = "DISMISS_FRAGMENT"
        const val DECLINE_UPGRADE = "DECLINE_UPGRADE"
        const val SHOW_BLUR_BG = "SHOW_BLUR_BG"
        @JvmStatic
        fun getInstance(data: RatePlansUpgrade): HotelRatePlanUpgradeFragment {
            val fragment = HotelRatePlanUpgradeFragment()
            val bundle = Bundle()
            bundle.putParcelable(RATE_PLAN_UPGRADE_DATA, data)
            fragment.arguments = bundle
            return fragment
        }

    }
}