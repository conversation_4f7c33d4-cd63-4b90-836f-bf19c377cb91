package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.EventType
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.dataModel.AdditionalFeesExpandedItemData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent

class AdditionalFeesItemViewModel(private val data: AdditionalFeesExpandedItemData, val eventStream: MutableLiveData<HotelEvent>? = null, val eventLambda: ((HotelEvent) -> Unit)? = null) {

    fun getTitle() = data.title

    fun getAmount() = data.amount

    fun getCategoryDescription() = data.categoryDescription.orEmpty()
    fun getAmountDescription() = data.amountDescription.orEmpty()

    fun showDescriptionIcon() = (data.description?.isNotEmpty() == true || data.inclusions?.isNotEmpty() == true)

    fun getChargesMessage() = data.chargesMsg.orEmpty()

    fun showDescriptionBottomSheet() {
        if(showDescriptionIcon()) {
            val event = HotelEvent(HotelBookingReviewFragmentEvent.SHOW_HOTEL_INFO_BOTTOM_SHEET, data, eventType = EventType.CLICK)
            eventStream?.postValue(event)?:run {
                eventLambda?.invoke(event)
            }

        }
    }
}