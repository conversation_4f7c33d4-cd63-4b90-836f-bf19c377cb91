package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.GstInputDetail
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.corp.PrimaryTraveller
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.bookingreview.viewmodel.TravelerViewModel
import com.mmt.hotel.common.constants.CorpConstants
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.uikit.util.isNotNullAndEmpty

class CorpPrimaryTravelerViewModelV2(
    private var primaryTravellerDetail: CorpTravellerDetail,
    val eventStream: MutableLiveData<HotelEvent>,
    private val isForeignTravel: Boolean,
    val enableCard: Boolean = true
) : BaseObservable(), TravelerViewModel {

    private var isDataModified = false
    private val _uiState =
        mutableStateOf(CorpPrimaryTravellerUiModel(isForeignTraveler = isForeignTravel))
    val uiState: State<CorpPrimaryTravellerUiModel> = _uiState
    companion object {
        val DEFAULT_TITLE: String =
            ResourceProvider.instance.getStringArray(R.array.TRAVELLER_TITLE)[0]
    }

    private var primaryTravellerEmailForGuestType = ""

    data class CorpPrimaryTravellerUiModel(
        val isGcc: Boolean = LoginUtils.getPreferredRegion().isGlobalEntity(),
        val nameHeader: String = CoreConstants.EMPTY_STRING,
        val isUserLoggedIn: Boolean = true,
        val isPrimaryTraveler: PrimaryTraveller = PrimaryTraveller.NOT_PRIMARY,
        val emailId: String = CoreConstants.EMPTY_STRING,
        val isEmailVisible: Boolean = true,
        val isEmailEnabled: Boolean = true,
        val isEmailCrossVisible: Boolean = false,
        val emailError: String = CoreConstants.EMPTY_STRING,
        val title: String = HotelUtil.getTitle(
            LoginUtils.loggedInUser?.title,
            LoginUtils.loggedInUser?.gender
        ),
        val name: String = CoreConstants.EMPTY_STRING,
        val nameEnabled: Boolean = true,
        val surname: String = CoreConstants.EMPTY_STRING,
        val surnameEnabled: Boolean = true,
        val nameError: String = CoreConstants.EMPTY_STRING,
        val pan: String = CoreConstants.EMPTY_STRING,
        val surnameError: String = CoreConstants.EMPTY_STRING,
        val isdCode: Int = HotelUtil.getIsdCodeForTravellerForm(),
        val contactNo: String = CoreConstants.EMPTY_STRING,
        val contactNoError: String = CoreConstants.EMPTY_STRING,
        val isForeignTraveler: Boolean,
        val gstAvailable: Boolean = false,
        var gstDetail: GstInputDetail = GstInputDetail(),
        var isBookingProfileOfGuestType: Boolean = false,
        var gender: String = CoreConstants.EMPTY_STRING,
        var travellerType: String = CoreConstants.EMPTY_STRING,
        var saveTravellerDetails : Boolean = false,
        var showSaveTravellerCheckbox: Boolean = false
    ) {
        fun getCoPrimaryData(coTravellerDetail: CorpTravellerDetail): CorpTravellerDetail {
            return coTravellerDetail.copy(
                firstName = name,
                lastName = surname,
                isPrimary = isPrimaryTraveler,
                emailId = emailId,
                travellerType = travellerType,
                title = title,
                gender = gender,
                contactNo = contactNo,
                isdCode = isdCode,
                saveTravellerDetails = saveTravellerDetails
            )
        }

        fun getCoPrimaryData(): CorpTravellerDetail {
            return CorpTravellerDetail(
                title = title,
                firstName = name,
                lastName = surname,
                isdCode = isdCode,
                contactNo = contactNo,
                emailId = emailId,
                isPrimary = isPrimaryTraveler,
                gender = gender,
                travellerType = travellerType,
                saveTravellerDetails = saveTravellerDetails
            )
        }
    }

    fun getPrimaryTravelerDetails(): CorpTravellerDetail {
        return _uiState.value.getCoPrimaryData(primaryTravellerDetail)
    }


   // setting isEmailCrossVisible to false, as validation checks were failing and UI issues on Add Traveller card
    init {
        primaryTravellerEmailForGuestType =
            if (primaryTravellerDetail.isPrimary == PrimaryTraveller.GUEST) primaryTravellerDetail.emailId else ""
        updateUiState(
            isBookingProfileOfGuestType = primaryTravellerDetail.isPrimary == PrimaryTraveller.GUEST,
            gstDetail = GstInputDetail(),
            title = primaryTravellerDetail.title,
            name = primaryTravellerDetail.firstName,
            surname = primaryTravellerDetail.lastName,
            emailId = primaryTravellerDetail.emailId,
            nameError = if (primaryTravellerDetail.showFirstNameError) primaryTravellerDetail.fullNameErrorMsg
                ?: CoreConstants.EMPTY_STRING else CoreConstants.EMPTY_STRING,
            surnameError = if (primaryTravellerDetail.showLastNameError) primaryTravellerDetail.fullNameErrorMsg
                ?: CoreConstants.EMPTY_STRING else CoreConstants.EMPTY_STRING,
            isdCode = primaryTravellerDetail.isdCode,
            contactNo = primaryTravellerDetail.contactNo,
            contactNoError = primaryTravellerDetail.contactNoErrorMsg
                ?: CoreConstants.EMPTY_STRING,
            gender = primaryTravellerDetail.gender,
            travellerType = primaryTravellerDetail.travellerType,
            isPrimaryTraveler = primaryTravellerDetail.isPrimary,
            isEmailCrossVisible = false /*primaryTravellerDetail.isPrimary != PrimaryTraveller.GUEST && enableCard*/
        )
    }

    fun gstAvailable() = _uiState.value.gstAvailable

    fun updateTitle(title: String) {
        isDataModified = true
        updateUiState(title = title)
    }

    fun updateName(name: String) {
        isDataModified = true
        updateUiState(name = name, showSaveTravellerCheckbox = true)
        postUpdatedNameForCoTravelerCard()
    }

    fun updateSurname(surname: String) {
        isDataModified = true
        updateUiState(surname = surname, showSaveTravellerCheckbox = true)
        postUpdatedNameForCoTravelerCard()
    }

    fun updateEmailId(email: String) {
        isDataModified = true
        updateUiState(emailId = email)
    }

    fun updateIsdCode(isdCode: Pair<Int, String>) {
        isDataModified = true
        updateUiState(isdCode = isdCode.first)
    }

    fun updateContactNo(mobile: String) {
        isDataModified = true
        updateUiState(contactNo = mobile)
    }

    fun onSaveTravellerCheckboxClicked(selected:Boolean) {
        isDataModified = true
        updateUiState(saveTravellerDetails = selected)
    }

    fun removeEmail() {
        isDataModified = true
        eventStream.value =
            HotelEvent(HotelCorpBookingReviewFragmentEvent.MAKE_PRIMARY_TRAVELLER_VIEW_GONE, this)
        primaryTravellerDetail = CorpTravellerDetail()
        updatePrimaryTraveller()
    }
    fun openSearchActivityWithPrimaryEmail() {
        if (_uiState.value.isBookingProfileOfGuestType) {
            updateUiState(isEmailEnabled = false)
            return
        }
        eventStream.value = HotelEvent(
            HotelCorpBookingReviewActivityEvent.OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE_WITH_EMAIL,
            primaryTravellerDetail.emailId
        )
    }

    fun makeCoTravellerPrimary(
        coTravellerDetail: CorpTravellerDetail,
        isCoTravellerLoggedInUser: Boolean
    ) {
        primaryTravellerDetail.isPrimary = PrimaryTraveller.NOT_PRIMARY
        primaryTravellerDetail.saveTravellerDetails = false
        eventStream.value = HotelEvent(
            HotelCorpBookingReviewFragmentEvent.MAKE_PRIMARY_TRAVELLER_CO,
            primaryTravellerDetail
        )

        primaryTravellerDetail = coTravellerDetail

        when {
            isCoTravellerLoggedInUser -> primaryTravellerDetail.isPrimary = PrimaryTraveller.MYSELF
            else -> primaryTravellerDetail.isPrimary = PrimaryTraveller.COLLEAGUE
        }
        updateUiState(isEmailVisible = true)
        updatePrimaryTraveller()
    }

    private fun updatePrimaryTraveller() {
        updateUiState(
            isBookingProfileOfGuestType = primaryTravellerDetail.isPrimary == PrimaryTraveller.GUEST,
            gstDetail = GstInputDetail(),
            title = primaryTravellerDetail.title,
            name = primaryTravellerDetail.firstName,
            surname = primaryTravellerDetail.lastName,
            emailId = primaryTravellerDetail.emailId,
            nameError = if (primaryTravellerDetail.showFirstNameError) primaryTravellerDetail.fullNameErrorMsg
                ?: CoreConstants.EMPTY_STRING else CoreConstants.EMPTY_STRING,
            surnameError = if (primaryTravellerDetail.showLastNameError) primaryTravellerDetail.fullNameErrorMsg
                ?: CoreConstants.EMPTY_STRING else CoreConstants.EMPTY_STRING,
            isdCode = primaryTravellerDetail.isdCode,
            contactNo = primaryTravellerDetail.contactNo,
            contactNoError = primaryTravellerDetail.contactNoErrorMsg ?: CoreConstants.EMPTY_STRING,
            gender = primaryTravellerDetail.gender,
            travellerType = primaryTravellerDetail.travellerType,
            isPrimaryTraveler = primaryTravellerDetail.isPrimary,
            saveTravellerDetails = primaryTravellerDetail.saveTravellerDetails,
            showSaveTravellerCheckbox = primaryTravellerDetail.showSaveTravellerCheckbox
        )
        eventStream.postValue(
            HotelEvent(
                HotelCorpBookingReviewFragmentEvent.ON_PRIMARY_EMAIL_CHANGE,
                primaryTravellerDetail.emailId
            )
        )
    }


    fun validateCorpPrimaryTravellerData(): Boolean {
        var result = validateCorpPrimaryTravellerDetail()
        if (_uiState.value.gstAvailable) {
            result = validateGstDetail() && result
        }
        return result
    }

    private fun validateCorpPrimaryTravellerDetail(): Boolean {
        var result = isNameValid()
        result = isSurnameValid() && result
        result = isContactNoValid() && result
        return result
    }

    private fun validateGstDetail(): Boolean {
        return _uiState.value.gstDetail.run {
            var result = isNumberValid()
            result = isNameValid() && result
            result = isAddressValid() && result
            result
        }
    }

    fun isNameValid(): Boolean {
        val result: Boolean
        if (_uiState.value.name.trim().isEmpty()) {
            result = false
            updateUiState(nameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
        } else {
            result = HotelUtil.validateRegEx(
                _uiState.value.name,
                HotelConstants.FIRST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(_uiState.value.name, HotelConstants.DOT_AND_SPACE_REGEX)
            if (!result) updateUiState(nameError = ResourceProvider.instance.getString(R.string.htl_name_error)) else updateUiState(
                nameError = ""
            )
        }
        return result
    }

    fun isSurnameValid(): Boolean {
        val result: Boolean
        if (_uiState.value.surname.trim().isEmpty()) {
            result = false
            updateUiState(surnameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
        } else {
            result = HotelUtil.validateRegEx(
                _uiState.value.surname,
                HotelConstants.LAST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(
                _uiState.value.surname,
                HotelConstants.DOT_AND_SPACE_REGEX
            )
            if (!result) updateUiState(surnameError = ResourceProvider.instance.getString(R.string.htl_name_error)) else updateUiState(
                surnameError = ""
            )
        }
        return result
    }

    fun isEmailIdValid(): Boolean {
        val result: Boolean
        if (_uiState.value.emailId.isEmpty()) {
            result = false
            updateUiState(emailError = ResourceProvider.instance.getString(R.string.htl_email_empty))
        } else {
            result =LoginUtil.checkEmail(_uiState.value.emailId)
            updateUiState(emailError = if (result) "" else ResourceProvider.instance.getString(R.string.htl_email_error))
        }
        return result
    }

    fun isContactNoValid(): Boolean {
        val validation = HotelUtil.isContactNoValid(_uiState.value.isdCode, _uiState.value.contactNo)
        updateUiState(contactNoError = validation.second)
        return validation.first
    }

    private fun postUpdatedNameForCoTravelerCard() {
        var data = "${_uiState.value.name} ${_uiState.value.surname}".trim()
        _uiState.value.travellerType.let {
            if (it == CorpConstants.GUEST) {
                data += " ($it) "
            }
        }
        isDataModified = true
        eventStream.value = HotelEvent(
            HotelCorpBookingReviewFragmentEvent.SHOW_UPDATED_PRIMARY_TRAVELLER_NAME,
            data
        )
    }

    fun updatePrimaryTravellerDetails(
        primaryTravellerDetail: CorpTravellerDetail,
        isPrimaryTravellerLoggedInUser: Boolean
    ) {
        isDataModified = true
        primaryTravellerDetail.isPrimary = PrimaryTraveller.NOT_PRIMARY
        this.primaryTravellerDetail = primaryTravellerDetail
        if (isPrimaryTravellerLoggedInUser)
            primaryTravellerDetail.isPrimary = PrimaryTraveller.MYSELF
        else
            primaryTravellerDetail.isPrimary = PrimaryTraveller.COLLEAGUE
        updatePrimaryTraveller()
        updateUiState(isEmailVisible = true)
        eventStream.value = HotelEvent(
            HotelCorpBookingReviewFragmentEvent.MAKE_PRIMARY_TRAVELLER_VIEW_VISIBLE,
            primaryTravellerDetail
        )
    }

    fun getGstInput(): GSTNDetails {
        return GSTNDetails(
            gstn = _uiState.value.gstDetail.number,
            organizationName = _uiState.value.gstDetail.name,
            address1 = _uiState.value.gstDetail.address
        )
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CORP_ADD_TRAVELLER
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_ADD_TRAVELLER
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as? CorpPrimaryTravelerViewModelV2)
        return this == matchedWith
    }

    override fun cardName(): String {
        return "Corp Review Add Traveller"
    }

    private fun updateUiState(
        isUserLoggedIn: Boolean = _uiState.value.isUserLoggedIn,
        isPrimaryTraveler: PrimaryTraveller = _uiState.value.isPrimaryTraveler,
        emailId: String = _uiState.value.emailId,
        emailError: String = _uiState.value.emailError,
        isEmailVisible: Boolean = _uiState.value.isEmailVisible,
        isEmailEnabled: Boolean = _uiState.value.isEmailEnabled,
        isEmailCrossVisible: Boolean = _uiState.value.isEmailCrossVisible,
        title: String = _uiState.value.title,
        name: String = _uiState.value.name,
        surname: String = _uiState.value.surname,
        nameError: String = _uiState.value.nameError,
        surnameError: String = _uiState.value.surnameError,
        isdCode: Int = _uiState.value.isdCode,
        contactNo: String = _uiState.value.contactNo,
        contactNoError: String = _uiState.value.contactNoError,
        isForeignTraveler: Boolean = _uiState.value.isForeignTraveler,
        gstAvailable: Boolean = _uiState.value.gstAvailable,
        gstDetail: GstInputDetail = _uiState.value.gstDetail,
        isBookingProfileOfGuestType: Boolean = _uiState.value.isBookingProfileOfGuestType,
        gender: String = _uiState.value.gender,
        travellerType: String = _uiState.value.travellerType,
        saveTravellerDetails: Boolean = _uiState.value.saveTravellerDetails,
        showSaveTravellerCheckbox: Boolean = _uiState.value.showSaveTravellerCheckbox
    ) {
        val data = _uiState.value.copy(
            nameHeader = if (isForeignTravel) {
                ResourceProvider.instance.getString(R.string.htl_name_header_intl_travel)
            } else {
                ResourceProvider.instance.getString(R.string.htl_name_header)
            },
            isGcc = LoginUtils.getPreferredRegion().isGlobalEntity(),
            isUserLoggedIn = isUserLoggedIn,
            isPrimaryTraveler = isPrimaryTraveler,
            emailId = emailId,
            emailError = emailError,
            isEmailVisible = isEmailVisible,
            isEmailEnabled = isEmailEnabled,
            isEmailCrossVisible = isEmailCrossVisible,
            title = title,
            name = name,
            surname = surname,
            nameEnabled = !enableCard && _uiState.value.name.isNotNullAndEmpty(),
            surnameEnabled = !enableCard && primaryTravellerDetail.isLastNameValid(),
            nameError = nameError,
            surnameError = surnameError,
            isdCode = isdCode,
            contactNo = contactNo,
            contactNoError = contactNoError,
            isForeignTraveler = isForeignTraveler,
            gstAvailable = gstAvailable,
            gstDetail = gstDetail,
            isBookingProfileOfGuestType = isBookingProfileOfGuestType,
            gender = gender,
            travellerType = travellerType,
            saveTravellerDetails = saveTravellerDetails,
            showSaveTravellerCheckbox = showSaveTravellerCheckbox
        )
        _uiState.value = data
        if (isDataModified) eventStream.postValue(HotelEvent(HotelCorpBookingReviewFragmentEvent.UPDATE_CO_TRAVELLER_PRIMARY_DATA, data.getCoPrimaryData()))
        isDataModified = false
    }

    fun canShowSaveTravellerCheckbox(): Boolean {
        return primaryTravellerDetail.isPrimary != PrimaryTraveller.GUEST
                && primaryTravellerDetail.emailId == LoginUtils.loggedInUserEmail
    }

}