package com.mmt.hotel.bookingreview.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.CoTravellerFragmentData
import com.mmt.hotel.bookingreview.ui.HotelCoTravellerFragment
import com.mmt.hotel.bookingreview.ui.HotelCoTravellerFragment.Companion.CO_TRAVELLER_DATA
import com.mmt.hotel.bookingreview.viewmodel.CoTravellerFragmentViewModel
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap
import java.lang.RuntimeException

@Module
@InstallIn(FragmentComponent::class)
class HotelCoTravellerFragmentModule {
    @Provides
    @IntoMap
    @ViewModelKey(CoTravellerFragmentViewModel::class)
    fun provideCoTravellerViewModel(viewModel: CoTravellerFragmentViewModel): HotelViewModel = viewModel

    @Provides
    fun provideCoTravellerData(fragment: Fragment): CoTravellerFragmentData {
        if (fragment is HotelCoTravellerFragment) {
            return fragment.requireArguments().getParcelable(CO_TRAVELLER_DATA)!!
        }
        throw RuntimeException("CoTravellerFragmentData can only be used in HotelCoTravellerFragment")
    }
}