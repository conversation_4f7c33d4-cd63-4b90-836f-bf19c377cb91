package com.mmt.hotel.bookingreview.viewmodel.corp

import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.model.response.CorpApprovingManager

class HotelCorpRequestApprovalManagerItemVM(val corpApprovingManager: CorpApprovingManager, val position: Int, val size: Int) {

    fun getTitle(): String {
        return ResourceProvider.instance.getString(R.string.htl_approving_manager) + CoreConstants.SPACE + position.toString()
    }

    fun showdivider(): <PERSON><PERSON><PERSON> {
        return size != position
    }

}