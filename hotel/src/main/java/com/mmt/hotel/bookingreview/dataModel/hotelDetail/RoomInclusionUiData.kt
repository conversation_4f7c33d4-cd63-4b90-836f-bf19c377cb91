package com.mmt.hotel.bookingreview.dataModel.hotelDetail

import android.os.Parcelable
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.listingV2.model.response.hotels.BottomSheetData
import kotlinx.android.parcel.Parcelize

@Parcelize
data class RoomInclusionUiData(val title: CharSequence,
                               val subTitle: String?,
                               val bulletTextItemList : List<LinearLayoutItemData>? = null,
                               val iconType: String? = null,
                               val imageUrl : String? = null,
                               val planType: String?,
                               val isLastInclusion: Boolean = false,
                               val descriptionText: String? = null,
                               val marginStart : Int = ResourceProvider.instance.getDimensionPixelSize(R.dimen.margin_medium),
                               val trailingCtaText: String = CoreConstants.EMPTY_STRING,
                               val trailingCtaBottomSheet: BottomSheetData? = null) :
    Parcelable {

    fun showBulletTexts() = bulletTextItemList?.isNotEmpty() ?: false
}