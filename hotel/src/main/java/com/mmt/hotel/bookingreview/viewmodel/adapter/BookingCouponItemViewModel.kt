package com.mmt.hotel.bookingreview.viewmodel.adapter

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.dataModel.CouponItemUIData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.listingV2.event.ListingPersuasionPlaceHolder.Companion.PLACEHOLDER_CARD_M4
import com.mmt.hotel.listingV2.helper.ListingPlaceHolderSupportedTemplates
import com.mmt.hotel.listingV2.model.response.hotels.TemplatePersuasion
import com.mmt.uikit.util.isNotNullAndEmpty

class BookingCouponItemViewModel(var data: CouponItemUIData, private val eventStream: MutableLiveData<HotelEvent>) :BaseObservable(){

    fun updateData(data: CouponItemUIData) {
        this.data = data
        notifyChange()
    }

    @DrawableRes
    fun getImageBg(): Int {
        return if (data.isSelected) {
            if(LoginUtils.isCorporateUser ) {
                R.drawable.ic_radioon_corp
            }else {
                R.drawable.ic_radio_selected
            }
        } else {
            R.drawable.radio_off_new
        }
    }

    fun getCouponPersuasion(): TemplatePersuasion?{
        return data.hotelCoupon.couponPersuasions?.get(PLACEHOLDER_CARD_M4)
    }

    fun getUnSupportedTemplates(placeHolderId: String): List<String> {
        return ListingPlaceHolderSupportedTemplates.getUnSupportedTemplates(placeHolderId)
    }

    fun getCouponCode(): String {
        return if (data.hotelCoupon.isDisabled && data.code.isNotEmpty()) {
            data.code[0] + ResourceProvider.instance.getString(R.string.htl_DUMMY_SHORT_TEXT)
        } else {
            data.code
        }
    }

    fun getCouponAmount(): String {
        return if (data.isSelected) {
            data.amount
        } else {
            CoreConstants.EMPTY_STRING
        }
    }

    fun showDivider() = data.showDivider

    fun showCrossIcon() = data.isSelected && data.showCrossIcon

    fun getDescription(): SpannableStringBuilder {
        return if (data.isSelected) {
            getSpannableDescription(data.couponSuccessMsg.orEmpty())
        } else {
            getSpannableDescription(data.description.orEmpty())
        }
    }

    private fun getSpannableDescription(descriptionText: String): SpannableStringBuilder {
        val builder = StringBuilder()
        val resourceProvider = ResourceProvider.instance

        val tncText = resourceProvider.getString(R.string.htl_coupon_tnc)
        builder.append(descriptionText)

        if (data.tncUrl.isNotNullAndEmpty()) {
            builder.append(CoreConstants.SPACE)
            builder.append(tncText)
        }

        val spanBuilder = SpannableStringBuilder(builder.toString())
        if (data.tncUrl.isNotNullAndEmpty()) {
            setClickableSpan(builder, spanBuilder, tncText)
            spanBuilder.setSpan(
                ForegroundColorSpan(resourceProvider.getColor(R.color.htl_color_008cff)),
                builder.indexOf(tncText),
                builder.indexOf(tncText) + tncText.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        return spanBuilder
    }

    private fun setClickableSpan(
        stringBuilder: StringBuilder,
        spanBuilder: SpannableStringBuilder,
        value: String
    ) {
        spanBuilder.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    eventStream.value =
                        HotelEvent(HotelBookingReviewFragmentEvent.COUPON_TNC_CLICKED, data.tncUrl)
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                }

            },
            stringBuilder.indexOf(value),
            stringBuilder.indexOf(value) + value.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        if (stringBuilder.indexOf(value) > 0) { //There's text before tnc, we need to make it clickable too
            spanBuilder.setSpan(object : ClickableSpan() {

                override fun onClick(widget: View) {
                    onCouponClicked()
                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                }

            }, 0, stringBuilder.indexOf(value), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    fun getTextColor(): Int {
        return when {
            data.isSelected -> {
                if (LoginUtils.isCorporateUser) {
                    R.color.htl_corp_coupon_label
                } else {
                    R.color.hotel_applied_coupon_color
                }
            }
            data.hotelCoupon.isDisabled -> {
                R.color.hotel_coupon_color
            }
            data.disableBnplNotApplicableCoupon -> {
                R.color.hotel_coupon_disabled_color
            }
            else -> {
                R.color.hotel_coupon_color
            }
        }
    }

    @ColorRes
    fun getDescriptionColor(): Int {
        return when {
            data.isSelected || data.hotelCoupon.isDisabled -> {
                R.color.hotel_coupon_color
            }
            data.disableBnplNotApplicableCoupon -> {
                R.color.hotel_coupon_disabled_color
            }
            else -> {
                R.color.hotel_coupon_color
            }
        }
    }

    fun getCTAText(): String{
        return if(!data.isSelected) ResourceProvider.instance.getString(R.string.htl_APPLY)
        else ResourceProvider.instance.getString(R.string.htl_TXT_REMOVE)
    }

    fun applyCoupon() {
        onCouponClicked()
    }

    fun removeCoupon() {
        if(!data.isSelected){
            return
        }
        eventStream.postValue(HotelEvent(HotelBookingReviewFragmentEvent.COUPON_CLICKED, Pair(data.hotelCoupon, false)))
    }

    fun toggleSelectedState(){
        if(data.hotelCoupon.isDisabled || data.disableBnplNotApplicableCoupon){
            return
        }else if(data.isSelected){
            removeCoupon()
        }else{
            onCouponClicked()
        }
    }

    fun onCouponClicked() {
        if (data.isSelected || data.hotelCoupon.isDisabled || data.disableBnplNotApplicableCoupon) {
            return
        }
        eventStream.postValue(HotelEvent(HotelBookingReviewFragmentEvent.COUPON_CLICKED, Pair(data.hotelCoupon, true)))
    }

    fun getContentDesc() : String{
        val str = StringBuilder()
        str.append("${getCouponCode()}. ")
        str.append("${getDescription()}. ")
        str.append("${data.errorText ?: ""}. ")
        return str.toString()
    }


    fun getBenefitDealDescription(): String {
        return if (data.isSelected) {
            (data.couponSuccessMsg ?: data.description).orEmpty()
        } else {
            data.description.orEmpty()
        }
    }

    fun onTncClicked(){
        eventStream.value =
            HotelEvent(HotelBookingReviewFragmentEvent.COUPON_TNC_CLICKED_WITH_NAME, Pair(data.tncUrl, data.hotelCoupon.couponCode))
    }
}