package com.mmt.hotel.bookingreview.adapter

import androidx.annotation.IntDef
import com.mmt.hotel.base.adapter.AdapterItemPrefix

@Retention(AnnotationRetention.SOURCE)
@IntDef(
        flag = true,
        value = [
            HotelBookingReviewAdapterItem.PROPERTY_DETAIL,
            HotelBookingReviewAdapterItem.PRICE_DETAIL,
            HotelBookingReviewAdapterItem.COUPONS,
            HotelBookingReviewAdapterItem.PAYMENT_OPTION_CARD,
            HotelBookingReviewAdapterItem.ADDITIONAL_FEES,
            HotelBookingReviewAdapterItem.PROPERTY_RULES,
            HotelBookingReviewAdapterItem.TRAVELLER_DETAIL_INPUT,
            HotelBookingReviewAdapterItem.SPECIAL_REQUEST,
            HotelBookingReviewAdapterItem.PAYMENT_BUTTON,
            HotelBookingReviewAdapterItem.MMT_DOUBLE_BLACK,
            HotelBookingReviewAdapterItem.TERMS_CONDITION_V2,
            HotelBookingReviewAdapterItem.TRIP_DETAILS_CARD,
            HotelBookingReviewAdapterItem.CORP_ADD_TRAVELLER,
            HotelBookingReviewAdapterItem.CORP_ADD_CO_TRAVELLER,
            HotelBookingReviewAdapterItem.CORP_REASON_FOR_BOOKING,
            HotelBookingReviewAdapterItem.CORP_GST,
            HotelBookingReviewAdapterItem.CORP_PERSONAL_BOOKING_TRAVELLER_CARD,
            HotelBookingReviewAdapterItem.CORP_TRIP_TAG,
            HotelBookingReviewAdapterItem.CORP_MSME_OFFER_CARD,
            HotelBookingReviewAdapterItem.CORP_APPROVAL_BTNS,
            HotelBookingReviewAdapterItem.CORP_PRICE_DETAIL,
            HotelBookingReviewAdapterItem.CAMPAIGN_ALERT,
            HotelBookingReviewAdapterItem.INSURANCE_CARD,
            HotelBookingReviewAdapterItem.CORP_APPROVAL_WORK_FLOW_STATUS,
            HotelBookingReviewAdapterItem.PAYMENT_POLICY_CARD,
            HotelBookingReviewAdapterItem.MMT_EXCLUSIVE_CARD,
            HotelBookingReviewAdapterItem.MMT_SELECT_CARD_V2,
            HotelBookingReviewAdapterItem.GST_DETAIL,
            HotelBookingReviewAdapterItem.TRIP_MONEY_BNPL_CARD,
            HotelBookingReviewAdapterItem.BOOKING_INFO_CARD,
            HotelBookingReviewAdapterItem.PRICE_ALERT,
            HotelBookingReviewAdapterItem.ADD_ONS,
            HotelBookingReviewAdapterItem.GST_DETAILS_CARD,
            HotelBookingReviewAdapterItem.ROOM_INFO,
            HotelBookingReviewAdapterItem.CORP_SUBSCRIPTION_CARD,
            HotelBookingReviewAdapterItem.CHARITY_CARD,
            HotelBookingReviewAdapterItem.BUSINESS_FUNNEL_IDENTIFICATION,
            HotelBookingReviewAdapterItem.PAN_TCS_CARD,
            HotelBookingReviewAdapterItem.HOTEL_CLOUD_CARD,
            HotelBookingReviewAdapterItem.INTL_ROAMING_CARD
        ]
)
annotation class HotelBookingReviewAdapterItem() {
    companion object {
        const val PROPERTY_DETAIL = AdapterItemPrefix.REVIEW + 1
        const val PRICE_DETAIL = AdapterItemPrefix.REVIEW  + 2
        const val COUPONS = AdapterItemPrefix.REVIEW  + 3
        const val ADDITIONAL_FEES = AdapterItemPrefix.REVIEW  + 4
        const val PROPERTY_RULES = AdapterItemPrefix.REVIEW  + 5
        const val TRAVELLER_DETAIL_INPUT = AdapterItemPrefix.REVIEW  + 6
        const val SPECIAL_REQUEST = AdapterItemPrefix.REVIEW  + 7
        const val PAYMENT_BUTTON = AdapterItemPrefix.REVIEW  + 9
        const val MMT_DOUBLE_BLACK = AdapterItemPrefix.REVIEW  + 10
        const val TERMS_CONDITION_V2 = AdapterItemPrefix.REVIEW  + 11
        const val TRIP_DETAILS_CARD = AdapterItemPrefix.REVIEW  + 13

        const val CORP_ADD_TRAVELLER = AdapterItemPrefix.REVIEW  + 14
        const val CORP_ADD_CO_TRAVELLER = AdapterItemPrefix.REVIEW  + 15
        const val CORP_REASON_FOR_BOOKING = AdapterItemPrefix.REVIEW  + 16
        const val CORP_GST = AdapterItemPrefix.REVIEW  + 17
        const val CORP_TRIP_TAG = AdapterItemPrefix.REVIEW  + 18
        const val CORP_APPROVAL_BTNS = AdapterItemPrefix.REVIEW  + 19
        const val CORP_PRICE_DETAIL = AdapterItemPrefix.REVIEW  + 20
        const val CAMPAIGN_ALERT = AdapterItemPrefix.REVIEW  + 21
        const val INSURANCE_CARD = AdapterItemPrefix.REVIEW  + 22
        const val CORP_APPROVAL_WORK_FLOW_STATUS = AdapterItemPrefix.REVIEW  + 23
        const val CORP_MSME_OFFER_CARD = AdapterItemPrefix.REVIEW  + 24
        const val CORP_PERSONAL_BOOKING_TRAVELLER_CARD = AdapterItemPrefix.REVIEW  + 25
        const val BOOKING_INFO_CARD = AdapterItemPrefix.REVIEW  + 26
        const val PAYMENT_POLICY_CARD = AdapterItemPrefix.REVIEW  + 27

        const val TRIP_MONEY_BNPL_CARD = AdapterItemPrefix.REVIEW  + 28
        const val MMT_EXCLUSIVE_CARD = AdapterItemPrefix.REVIEW  + 29
        const val PAYMENT_OPTION_CARD = AdapterItemPrefix.REVIEW + 30
        const val MMT_SELECT_CARD_V2  = AdapterItemPrefix.REVIEW + 31
        const val GST_DETAILS_CARD = AdapterItemPrefix.REVIEW + 32
        const val GST_DETAIL = AdapterItemPrefix.REVIEW + 33

        const val PRICE_ALERT = AdapterItemPrefix.REVIEW + 34
        const val ROOM_INFO = AdapterItemPrefix.REVIEW + 35
        const val ADD_ONS = AdapterItemPrefix.REVIEW + 36
        const val RTB_CONFIRMATION_CARD = AdapterItemPrefix.REVIEW + 37
        const val CORP_SUBSCRIPTION_CARD = AdapterItemPrefix.REVIEW + 38
        const val CHARITY_CARD = AdapterItemPrefix.REVIEW  + 39
        const val HOTEL_CLOUD_CARD = AdapterItemPrefix.REVIEW  + 40
        const val BUSINESS_FUNNEL_IDENTIFICATION = AdapterItemPrefix.REVIEW  + 41
        const val PAN_TCS_CARD = AdapterItemPrefix.REVIEW  + 42
        const val INSURANCE_CARD_WIDGET = AdapterItemPrefix.REVIEW  + 43
        const val INTL_ROAMING_CARD = AdapterItemPrefix.REVIEW  + 44
        const val CORP_GUEST_HOUSE_INFO_CARD = AdapterItemPrefix.REVIEW  + 45
        const val FREE_ADD_ON_CARD = AdapterItemPrefix.REVIEW  + 46
        const val REVIEW_STICKY_CARD = AdapterItemPrefix.REVIEW  + 47
    }
}


@Retention(AnnotationRetention.SOURCE)
@IntDef(
        flag = true,
        value = [
            HotelCoTravellerAdapterItem.ADD_NEW_GUEST,
            HotelCoTravellerAdapterItem.SAVED_GUEST_HEADER,
            HotelCoTravellerAdapterItem.SAVED_GUEST
        ]
)
annotation class HotelCoTravellerAdapterItem() {
    companion object {
        const val ADD_NEW_GUEST = 1
        const val SAVED_GUEST_HEADER = 2
        const val SAVED_GUEST = 3
    }
}

@Retention(AnnotationRetention.SOURCE)
@IntDef(
        flag = true,
        value = [CorpEmployeeSearchAdapterItem.CORP_EMPLOYEE_LIST_DETAIL]
)
annotation class CorpEmployeeSearchAdapterItem {
    companion object {
        const val CORP_EMPLOYEE_LIST_DETAIL = 1
    }
}

@Retention(AnnotationRetention.SOURCE)
@IntDef(
    flag = true,
    value = [BookingReviewPriceCardAdapterItem.ITEM_BREAKUP,
        BookingReviewPriceCardAdapterItem.ITEM_CHARITY,
        BookingReviewPriceCardAdapterItem.ITEM_TOTAL_PRICE]
)
annotation class BookingReviewPriceCardAdapterItem {
    companion object {
        const val ITEM_BREAKUP = 1
        const val ITEM_CHARITY = 2
        const val ITEM_TOTAL_PRICE = 3
    }
}