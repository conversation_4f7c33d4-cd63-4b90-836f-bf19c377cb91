package com.mmt.hotel.bookingreview.helper.constants

class BookingAlertsConstants {

    companion object {
        const val PRICE_DECREASE = "PRICE_DECREASE"
        const val PRICE_INCREASE = "PRICE_INCREASE"
        const val CONFIRMATION_POLICY = "CONFIRMATION_POLICY"
        const val CHECKIN_POLICY = "CHECKIN_POLICY"
        const val CANCELLATION_POLICY = "CANCEL_POLICY"
        const val RTB = "RTB"
        const val RTB_CHANGE = "RTB_CHANGE"
        const val RTB_PRE_APPROVED = "RTB_PRE_APPROVED"
        const val FREE_CANC_CAMPAIGN = "FREE_CANC_CAMPAIGN"
        const val PRICE = "PRICE"
        const val MEALPLAN = "MEALPLAN"
        const val ROOM_CODE = "ROOM_CODE"
        const val RPCC = "RPCC"
        const val CANCELLATION = "CANCELLATION"
        const val R_MP = "R_MP"
        const val R_RC = "R_RC"
        const val R_RP = "R_RP"
        const val R_C = "R_C"
        const val R_P = "R_P"
        const val PD = "PD"
        const val PI = "PI"
    }
}

