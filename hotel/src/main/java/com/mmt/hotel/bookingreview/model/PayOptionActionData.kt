package com.mmt.hotel.bookingreview.model


import com.mmt.data.model.payment.PaymentType
import com.mmt.hotel.common.constants.HotelConstants.BNPL_DEFAULT_VARIANT

data class PayOptionActionData(val emiSelected: Boolean = false,
                               val bnplSelected: Boolean = false,
                               val paymentType: PaymentType = PaymentType.FULL_PAYMENT,
                               val rtbAutoCharge: Boolean = false,
                               val partialPayment: <PERSON>olean = false,
                               val bnplVariant: String = BNPL_DEFAULT_VARIANT
)