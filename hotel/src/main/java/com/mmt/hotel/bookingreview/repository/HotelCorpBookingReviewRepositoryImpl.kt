package com.mmt.hotel.bookingreview.repository

import com.mmt.auth.login.model.old.corporate.FetchEmployeesResponse
import com.mmt.hotel.bookingreview.helper.BookingReviewRequestHelper
import com.mmt.hotel.bookingreview.helper.HotelCorpBookingReviewHelper
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.request.PostApprovalRequestV2
import com.mmt.hotel.bookingreview.model.response.AddNewEmployeeResponse
import com.mmt.hotel.bookingreview.model.response.PostApprovalResponseV2
import com.mmt.hotel.bookingreview.model.response.UpdatedCorpPolicyResponse
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagResponseV2
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.HtlUrlConstants.CORPORATE_CCB_BASE_URL
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject


open class HotelCorpBookingReviewRepositoryImpl @Inject constructor(private val bookingReviewRequestHelper: BookingReviewRequestHelper)
    : HotelBookingReviewRepositoryImpl(bookingReviewRequestHelper), HotelCorpBookingReviewRepository {

    companion object {
        val UPDATE_CORP_POLICY_API = "$CLIENT_GATEWAY_BASE_URL/update-policy/$client/$apiVersionCode"
        val FETCH_TRIP_TAGS_WITH_GST = "$CORPORATE_CCB_BASE_URL/tripTag/v2/tripTags?lob=%s&primaryPaxEmailId=%s&searchKey=%s&cityCode=%s&vendorGstin=%s"
        val APPROVAL_POST_API = "$CLIENT_GATEWAY_BASE_URL/approvals/$client/$apiVersionCode"
        val ADD_NEW_EMPLOYEE_API = "$CORPORATE_CCB_BASE_URL/v1/employee/auth/invite"
    }

    private fun getSearchQueryUrl(searchQuery: String): String {
        return "$FETCH_EMPLOYEE_BASE_URL/$searchQuery?viewManagerDetails=true&from=0&pagesize=20&orgId=${com.mmt.auth.login.util.LoginUtils.orgId}&currency=inr&language=eng&region=in"
    }

    override fun fetchSearchQueryResults(searchQuery: String): Flow<FetchEmployeesResponse> {
        return makeGetRequest(url = getSearchQueryUrl(searchQuery), countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN)
    }

    override fun updateCorpPolicy(email: List<String>?, corpBookingReviewHelper: HotelCorpBookingReviewHelper): Flow<UpdatedCorpPolicyResponse> {
        return makePostRequest(url = UPDATE_CORP_POLICY_API,
                postData = bookingReviewRequestHelper.getUpdateCorpPolicyRequestData(corpBookingReviewHelper.getTxnKey(), email, corpBookingReviewHelper.getCountryCode()),
                countryCode = corpBookingReviewHelper.getCountryCode() ?: HotelConstants.COUNTRY_CODE_UNKNOWN)
    }

    override fun fetchTripTagsWithGSTV2(email: String, corpBookingReviewHelper: HotelCorpBookingReviewHelper): Flow<CorpTripTagResponseV2> {
        var url: String = String.format(FETCH_TRIP_TAGS_WITH_GST, HotelConstants.LOB_NAME, email, corpBookingReviewHelper.getTxnKey(), corpBookingReviewHelper.getLocationId(), corpBookingReviewHelper.getGstInfo()?.gstin)
        corpBookingReviewHelper.getGstInfo()?.gstStateCode?.let{
            url += "&${HotelConstants.VENDOR_STATE_CODE}=${it}"
        }
        if(corpBookingReviewHelper.getBookingReviewData()?.personalCorpBooking == true) {
            url += "&${HotelConstants.KEY_PERSONAL_BOOKING}=true"
        }
        return makeGetRequest(url = url, countryCode = corpBookingReviewHelper.getCountryCode() ?: HotelConstants.COUNTRY_CODE_UNKNOWN)
    }

    override fun postApprovalAPIV2(corpBookingReviewHelper: HotelCorpBookingReviewHelper, reasonForTravel: Map<String, String>?, checkDuplicateBooking : Boolean): Flow<PostApprovalResponseV2> {
        return makePostRequest(url = APPROVAL_POST_API,
                postData = bookingReviewRequestHelper.getApprovalPostRequestData(corpBookingReviewHelper, reasonForTravel, checkDuplicateBooking),
                countryCode = corpBookingReviewHelper.getCountryCode() ?: HotelConstants.COUNTRY_CODE_UNKNOWN)
    }

    override fun postApprovalAPIV2(postApprovalRequestV2: PostApprovalRequestV2): Flow<PostApprovalResponseV2> {
        return makePostRequest(url = APPROVAL_POST_API,
                postData = postApprovalRequestV2,
                countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN)
    }

    override fun addNewEmployee(travellerDetail: CorpTravellerDetail): Flow<AddNewEmployeeResponse> {
        return makePostRequest(url = ADD_NEW_EMPLOYEE_API,
                postData = bookingReviewRequestHelper.getAddEmployeeData(travellerDetail),
                countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN)
    }

}