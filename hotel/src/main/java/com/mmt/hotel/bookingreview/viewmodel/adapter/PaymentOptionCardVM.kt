package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.PayOptionActionData
import com.mmt.hotel.bookingreview.model.response.HotelBnplDetails
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

class PaymentOptionCardVM(val paymentOptions: List<PaymentCardItemVM>, val eventStream: MutableLiveData<HotelEvent>) : BaseObservable(),DiffUtilRecycleItem {
    var isDefaultOptionSelected = false  //This flag is used to change selected payment option if bnplDetails is different in availRoom and validateCoupon first time
    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.PAYMENT_OPTIONS
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as PaymentOptionCardVM).paymentOptions
        return paymentOptions == matchedWith
    }

    override fun cardName(): String {
        return "Review Payment Option"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.PAYMENT_OPTION_CARD
    }

    fun updateSelection(payOptionActionData: PayOptionActionData, bnplDetails: HotelBnplDetails?) {
        for (item in paymentOptions) {
            if (item.payOptionActionData.bnplSelected) {
                item.updateSelection(item.payOptionActionData == payOptionActionData, bnplDetails?.bnplText, bnplDetails?.bnplSubText)
            } else {
                item.updateSelection(item.payOptionActionData == payOptionActionData)
            }
        }
    }

    fun updateBnplOption(text:String?,subText:String?) {
        paymentOptions.find { it.payOptionActionData.bnplSelected }?.updateData(text,subText)
    }
}