package com.mmt.hotel.bookingreview.helper.constants

import androidx.annotation.IntDef


@Retention(AnnotationRetention.SOURCE)
@IntDef(value = [ErrorHandlerMode.TOAST,
    ErrorHandlerMode.DIALOG
])
annotation class ErrorHandlerMode {
    companion object {
        const val TOAST = 0
        const val DIALOG = 1
    }
}

@Retention(AnnotationRetention.SOURCE)
@IntDef(value = [CheckErrorDialogActions.DISMISS_POPUP,
    CheckErrorDialogActions.LOGIN,
    CheckErrorDialogActions.RETRY,
    CheckErrorDialogActions.SOLD_OUT,
    CheckErrorDialogActions.GO_TO_LISTING,
    CheckErrorDialogActions.GO_TO_SELECT_ROOM,
    CheckErrorDialogActions.GO_TO_DETAIL,
    CheckErrorDialogActions.GO_TO_OTP,
    CheckErrorDialogActions.GO_WITHOUT_WALLET,
    CheckErrorDialogActions.GO_TO_HOTEL_DETAIL,
    CheckErrorDialogActions.MAKE_CHECKOUT_REQUEST,
    CheckErrorDialogActions.SKIP_DOUBLE_BLACK_VALIDATION,
    CheckErrorDialogActions.MODIFY_DOUBLE_BLACK_NAME,
    CheckErrorDialogActions.VIEW_OPEN_REQUESTS
])
annotation class CheckErrorDialogActions {
    companion object {
        const val DISMISS_POPUP = 0
        const val LOGIN = 1
        const val RETRY = 3
        const val SOLD_OUT = 4
        const val GO_TO_LISTING = 5
        const val GO_TO_SELECT_ROOM = 6
        const val GO_TO_DETAIL = 7
        const val GO_TO_OTP = 8
        const val GO_WITHOUT_WALLET = 9
        const val GO_TO_HOTEL_DETAIL = 10
        const val MAKE_CHECKOUT_REQUEST = 11
        const val SKIP_DOUBLE_BLACK_VALIDATION = 112
        const val MODIFY_DOUBLE_BLACK_NAME = 113
        const val VIEW_OPEN_REQUESTS = 125 //TODO will ask about it
    }
}

@Retention(AnnotationRetention.SOURCE)
@IntDef(value = [CheckErrorToastActions.DO_NOTHING,
    CheckErrorToastActions.TOAST_GO_TO_LISTING,
    CheckErrorToastActions.TOAST_SOLD_OUT,
    CheckErrorToastActions.TOAST_GO_TO_SELECT_ROOM,
    CheckErrorToastActions.TOAST_GO_TO_DETAIL,
    CheckErrorToastActions.TOAST_GO_TO_OTP
])
annotation class CheckErrorToastActions {
    companion object {
        const val DO_NOTHING = 0
        const val TOAST_GO_TO_LISTING = 1
        const val TOAST_SOLD_OUT = 2
        const val TOAST_GO_TO_SELECT_ROOM = 3
        const val TOAST_GO_TO_DETAIL = 4
        const val TOAST_GO_TO_OTP = 5
    }
}

object CheckoutErrorCodes {
    const val DOUBLE_BLACK_NAME_CHANGE_ERROR_CODE = "418802"
    const val DOUBLE_BLACK_OTHER_ERROR_CODE = "418803"
    const val DOUBLE_BLACK_SOCKET_ERROR_CODE = "418203"
    const val DOUBLE_BLACK_EMPTY_RESPONSE_ERROR_CODE = "418405"
    const val DOUBLE_BLACK_VALIDATION_ERROR_CODE = "418801"
    const val RTB_MULTIPLE_REQ_ERROR_CODE = "400384"
    const val RTB_TOO_MANY_REQ_ERROR_CODE = "400385"
    const val CHECKOUT_ERROR = 25
    const val WRONG_GSTN_ERROR = "400322"
    const val SUBSCRIPTION_ERROR_CODE = "400351"
    const val OSBA_ERROR_CODE = "OSBA"
    const val GBF_ERROR_CODE = "GBF"
}