package com.mmt.hotel.bookingreview.model.response.checkout

import android.os.Parcelable
import com.mmt.hotel.common.model.response.HotelApiError
import com.mmt.hotel.detail.model.response.BgLinearGradient
import kotlinx.parcelize.Parcelize

@Parcelize
data class CheckoutResponse(
    val correlationKey: String?,
    val checkoutId: String?,
    val paymentRespMessage: String?,
    val currency: String?,
    val bookingID: String?,//Mandatory field in case of success response
    val totalAmount: String?,//Mandatory field in case of success response
    val error: HotelApiError?,
    val redirect: String?,
    val consentData: ConsentData?) : Parcelable


@Parcelize
data class ConsentData(
    val heading: String?,
    val title : String?,
    val titleColor : String?,
    val iconUrl: String?,
    val description: String?,
    val bgLinearGradient: BgLinearGradient?,
    val strokeColor : String?,
    val dataList : ArrayList<ConsentDataItem>?) : Parcelable

@Parcelize
data class ConsentDataItem(
    val title: String?,
    val value : String?
) : Parcelable


