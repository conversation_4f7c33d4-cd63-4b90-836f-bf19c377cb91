package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.response.price.HotelPriceBreakUp
import com.mmt.hotel.bookingreview.model.response.room.RoomInclusion
import com.mmt.hotel.common.model.response.persuasion.BackgroundGradient
import kotlinx.parcelize.Parcelize

@Parcelize
data class RatePlansUpgrade(
    val title: String? = null,
    val tierImage: String? = null,
    val selectedTitle: String? = null,
    val upgradedTitle: String? = null,
    val disclaimer: String? = null,
    val upgradeType: String? = null,
    val selectedRateplans: List<BlackUpgradeRatePlan>? = null,
    val upgradedRateplans: List<BlackUpgradeRatePlan>? = null
): Parcelable

@Parcelize
data class BlackUpgradeRatePlan(
    val roomName: String? = null,
    val roomDesc: String? = null,
    val inclusionsList: List<RoomInclusion>? = null,
    val bgGradient: BackgroundGradient? = null,
    val priceMap: HotelPriceBreakUp? = null
): Parcelable
