package com.mmt.hotel.bookingreview.di

import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpHtlBookingApprovalRequestSentViewModel
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap


@Module
@InstallIn(FragmentComponent::class)
abstract class HotelCorpBookingApprovalRequestSentFragmentModule {
    @Binds
    @IntoMap
    @ViewModelKey(CorpHtlBookingApprovalRequestSentViewModel::class)
    internal abstract fun provideCorpHtlBookingApprovalRequestSentViewModel(viewModel: CorpHtlBookingApprovalRequestSentViewModel): HotelViewModel
}