package com.mmt.hotel.bookingreview.viewmodel.corp

import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.model.response.CorpAutoBookRequestorConfig
import javax.inject.Inject

class HotelCorpApprovalRequestReconfirmationViewModel @Inject constructor(private val data: CorpAutoBookRequestorConfig) :
    HotelViewModel() {

    fun getTitle(): String {
        return data.title ?: EMPTY_STRING
    }

    fun subTitle(): String {
        return data.subTitle ?: EMPTY_STRING
    }

    fun getCtaText(): String {
        return data.cta?.text ?: EMPTY_STRING
    }

    fun onCtaClick() {
        updateEventStream(HotelEvent(HotelCorpBookingReviewActivityEvent.OPEN_CORP_APPROVAL_PAGE,data.workFlowId))
    }
}