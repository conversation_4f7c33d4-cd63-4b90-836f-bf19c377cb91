package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.PostApprovalTripTag
import com.mmt.hotel.bookingreview.model.TravellerDetailV2
import com.mmt.hotel.common.model.request.RequestDetails
import com.mmt.hotel.old.hotelreview.model.request.checkout.SpecialCheckoutRequest
import kotlinx.parcelize.Parcelize

@Parcelize
data class PostApprovalRequestV2(
    val reasonForTravel: Map<String, String>?,
    val workflowStatus: String,
    val specialRequest: SpecialCheckoutRequest?,
    val travellerDetailsList: List<TravellerDetailV2>?,
    val tripTag: PostApprovalTripTag?,
    val txnKey: String?,
    val myBizFlowIdentifier: String?,
    val requisitionID: String?,
    val requestDetails: RequestDetails?
) : Parcelable