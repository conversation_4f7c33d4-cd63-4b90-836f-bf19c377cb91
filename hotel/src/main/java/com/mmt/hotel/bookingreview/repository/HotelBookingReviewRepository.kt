package com.mmt.hotel.bookingreview.repository

//import com.mmt.home.home.model.UpdateProfileResponse // ToDo Ankur Ignore
import com.mmt.auth.login.model.userservice.UserDetailRefreshResponse
import com.mmt.hotel.bookingreview.model.BookingReviewData
import com.mmt.hotel.bookingreview.model.CheckoutData
import com.mmt.hotel.bookingreview.model.request.AddOnSelected
import com.mmt.hotel.bookingreview.model.request.ConsentAPIRequest
import com.mmt.hotel.bookingreview.model.request.TripMoneyBnplRequest
import com.mmt.hotel.bookingreview.model.response.ConsentAPIResponse
import com.mmt.hotel.bookingreview.model.response.PayLaterEligibilityResponse
import com.mmt.hotel.bookingreview.model.response.TotalPricingApiResponseV2
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.model.response.price.AvailResponse
import com.mmt.hotel.bookingreview.model.response.validatecoupon.ValidateApiResponseV2
import kotlinx.coroutines.flow.Flow


/*
*   Class will contains all the network call related to BookingReview
* */

interface HotelBookingReviewRepository {
    fun makeAvailRoomApiCall(bundleData: BookingReviewData, fetchUpsellInfo: Boolean, modifyBooking: Boolean = false): Flow<Pair<AvailResponse, String>>
    fun fetchCoTravellers(): Flow<UserDetailRefreshResponse>
    fun makeCheckoutRequest(checkoutData: CheckoutData): Flow<Pair<CheckoutResponse, String?>>
    fun makeValidateCouponRequest(
        bookingReviewData: BookingReviewData?,
        couponCode: String,
        isCouponApplied: Boolean,
        countryCode: String,
        expData: String?,
        txnKey: String,
        quickCheckoutApplicable: Boolean = false
    ): Flow<Pair<ValidateApiResponseV2, String>>

    fun makeTotalPricingRequest(
        data: BookingReviewData?,
        txnKey: String,
        addonSelected: List<AddOnSelected>,
        countryCode: String,
        expData: String?,
        enableTcs: Boolean
    ): Flow<Pair<TotalPricingApiResponseV2, String>>

    fun makeConsentApiRequest(data: ConsentAPIRequest, countryCode: String): Flow<ConsentAPIResponse>
    fun makeTripMoneyBnplValidationRequest(tripMoneyBnplRequest: TripMoneyBnplRequest, countryCode: String): Flow<PayLaterEligibilityResponse>
    fun getSelectedCurrency(): String
}