package com.mmt.hotel.bookingreview.ui.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.bookingreview.viewmodel.CoTravellerSavedHeaderViewModel
import com.mmt.hotel.databinding.HtlCoTravellerSavedHeaderItemBinding

class CoTravellerSavedHeaderViewHolder(layoutInflater: LayoutInflater, layoutId: Int, parent: ViewGroup)
    : HotelRecyclerViewHolder<HtlCoTravellerSavedHeaderItemBinding, CoTravellerSavedHeaderViewModel>(layoutInflater, layoutId, parent) {
    override fun bindData(data: CoTravellerSavedHeaderViewModel, position: Int) {
        dataBinding.model = data
        dataBinding.executePendingBindings()
    }
}