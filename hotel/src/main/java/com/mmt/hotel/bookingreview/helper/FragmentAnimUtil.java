package com.mmt.hotel.bookingreview.helper;

import android.animation.Animator;
import android.animation.AnimatorInflater;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.view.View;

import androidx.annotation.NonNull;

import com.mmt.hotel.R;

public class FragmentAnimUtil {
    @NonNull
    public static Animator slideUpAnimationAndDimBg(Activity activity, final boolean enter, int nextAnim, View bgView,float alpha) {
        if(!(nextAnim== R.animator.slide_up||nextAnim== R.animator.slide_down)){
            if(enter){
                nextAnim = R.animator.slide_up;
            }else{
                nextAnim = R.animator.slide_down;
            }
        }
        final Animator anim = AnimatorInflater.loadAnimator(activity, nextAnim);
        anim.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                if(bgView == null){
                    return;
                }
                doAnim(enter, bgView, 500,alpha);
            }
        });
        return anim;
    }

    private static void doAnim(Activity activity, boolean enter, int bgId) {
        View bg = activity.findViewById(bgId);
        doAnim(enter, bg,500, 0.5f);
    }

    /**
     * animate fragment from bottom and dim background bg
     * common use case any popup
     *
     * @param activity activity context
     * @param enter if entering
     * @param nextAnim animation to happen
     * @param bgId bg to dim
     * @return animator instance
     */
    @NonNull
    public static Animator slideUpAnimationAndDimBg(Activity activity, final boolean enter, int nextAnim, int bgId) {
        View bg = null;
        if(bgId != 0 && activity != null){
            bg = activity.findViewById(bgId);
        }
        return slideUpAnimationAndDimBg(activity, enter, nextAnim, bg,0.5f);
    }

    public static void doAnim(boolean enter, View bg, long duration, float alpha) {
        if(enter) {
            if(bg != null) {
                bg.setVisibility(View.VISIBLE);
                ObjectAnimator.ofFloat(bg, "alpha", 0, alpha).setDuration(duration).start();
            }
        }else{
            if(bg != null) {
                bg.setVisibility(View.GONE);
                ObjectAnimator.ofFloat(bg, "alpha", 1, alpha).setDuration(duration).start();
            }
        }
    }
}
