package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_CORP_APPROVAL_PAGE
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.model.response.CorpAutoBookRequestorConfig
import com.mmt.hotel.bookingreview.ui.HotelBookingReviewActivity
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpApprovalRequestReconfirmationViewModel
import com.mmt.hotel.databinding.CorpApprovalRequestConfirmationBinding
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.migration.OptionalInject
import dagger.hilt.android.migration.OptionalInjectCheck
import javax.inject.Inject

@AndroidEntryPoint
class HotelCorpApprovalRequestReconfirmationFragment :
    HotelBottomSheetDialogFragment<HotelCorpApprovalRequestReconfirmationViewModel, CorpApprovalRequestConfirmationBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel: HotelEventSharedViewModel? = null

    companion object {
        const val TAG = "HotelApprovalRequestReconfirmationFragment"

        @JvmStatic
        fun newInstance(bundle: Bundle): HotelCorpApprovalRequestReconfirmationFragment {
            val fragment = HotelCorpApprovalRequestReconfirmationFragment()
            fragment.arguments = bundle;
            return fragment
        }
    }

    override fun initViewModel(): HotelCorpApprovalRequestReconfirmationViewModel {
        return getViewModel(factory)
    }

    override fun initAndValidate() {
        val data = arguments?.getParcelable<CorpAutoBookRequestorConfig>(TAG)
        if(data == null) {
            dismiss()
        }
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.corp_approval_request_confirmation
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel(factory)
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            OPEN_CORP_APPROVAL_PAGE -> {
                dismiss()
                sendEventToActivity(event)
            }
            DISMISS_FRAGMENT -> {
                dismiss()
            }
        }
    }

    private fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }

}