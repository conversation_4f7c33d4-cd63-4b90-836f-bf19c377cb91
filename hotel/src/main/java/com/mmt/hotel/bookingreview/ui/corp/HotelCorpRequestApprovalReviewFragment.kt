package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.mmt.core.util.performIfActivityActive
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomUpFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent.PAYMENT_BUTTON_CLICKED
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.EVENT_HANDLE_ELEVATION
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.REQUEST_CORP_APPROVAL_API
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.SCROLL_TO_BOTTOM
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.SKIP_APPROVAL_BUTTON_CLICKED_FROM_REQUEST_APPROVAL_SCREEN
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalReviewViewModel
import com.mmt.hotel.databinding.HotelCorpRequestApprovalReviewFragmentLayoutBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelCorpRequestApprovalReviewFragment : HotelBottomUpFragment<HotelCorpRequestApprovalReviewViewModel, HotelCorpRequestApprovalReviewFragmentLayoutBinding>() {
    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel: HotelEventSharedViewModel? = null

    companion object {
        const val TAG = "HotelRequestApprovalReviewFragment"

        @JvmStatic
        fun newInstance(bundle: Bundle): HotelCorpRequestApprovalReviewFragment {
            val fragment = HotelCorpRequestApprovalReviewFragment()
            fragment.arguments = bundle;
            return fragment
        }
    }

    override val backgroundViewId: Int
        get() = R.id.view_bg_above_review_screen

    override fun onHandleBackPress() {
        performIfActivityActive(activity) { activity ->
            (activity as FragmentActivity).supportFragmentManager.popBackStackImmediate()
        }
    }

    override fun initViewModel(): HotelCorpRequestApprovalReviewViewModel {
        return getViewModel(factory)
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.hotel_corp_request_approval_review_fragment_layout
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            DISMISS_FRAGMENT -> {
                dismissFragment()
            }
            REQUEST_CORP_APPROVAL_API,
            SKIP_APPROVAL_BUTTON_CLICKED_FROM_REQUEST_APPROVAL_SCREEN,
            PAYMENT_BUTTON_CLICKED -> {
                dismissFragment()
                sendEventToActivity(event)
            }

            EVENT_HANDLE_ELEVATION -> {
                if (viewDataBinding.recyclerView.canScrollVertically(1) || viewDataBinding.recyclerView.canScrollVertically(-1)) {
                    viewModel.showElevation.set(true)
                } else {
                    viewModel.showElevation.set(false)
                }
            }
            SCROLL_TO_BOTTOM -> {
                viewDataBinding.recyclerView.smoothScrollToPosition(viewModel.adapter.itemCount)
            }
        }
    }

    private fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }

    override fun canAnimateStatusBar(): Boolean {
        return super.canAnimateStatusBar()
    }
}