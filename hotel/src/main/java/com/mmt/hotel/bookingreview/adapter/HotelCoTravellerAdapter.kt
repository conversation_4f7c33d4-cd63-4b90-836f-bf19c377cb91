package com.mmt.hotel.bookingreview.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.HotelBaseRecyclerAdapter
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.bookingreview.ui.viewholder.CoTravellerAddViewHolder
import com.mmt.hotel.bookingreview.ui.viewholder.CoTravellerSavedHeaderViewHolder
import com.mmt.hotel.bookingreview.ui.viewholder.CoTravellerSavedViewHolder

class HotelCoTravellerAdapter(val itemList: MutableList<AbstractRecyclerItem>) : HotelBaseRecyclerAdapter(itemList) {

    init {
        setHasStableIds(true)
    }

    override fun getViewHolder(viewType: Int, layoutInflater: LayoutInflater, parent: ViewGroup)
            : HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem> {
        return when (viewType) {
            HotelCoTravellerAdapterItem.ADD_NEW_GUEST -> {
                CoTravellerAddViewHolder(layoutInflater, R.layout.htl_co_traveller_add_item, parent)
            }
            HotelCoTravellerAdapterItem.SAVED_GUEST_HEADER -> {
                CoTravellerSavedHeaderViewHolder(layoutInflater, R.layout.htl_co_traveller_saved_header_item, parent)
            }
            else -> {
                CoTravellerAddViewHolder(layoutInflater, R.layout.htl_co_traveller_add_item, parent)
            }
        } as HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>
    }

    override fun getItemId(position: Int): Long {
        return itemList[position].hashCode().toLong()
    }
}