package com.mmt.hotel.bookingreview.di

import androidx.fragment.app.Fragment
import com.mmt.auth.login.ConsentClient
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.BookingReviewData
import com.mmt.hotel.compose.review.ui.fragment.HotelBookingReviewFragmentV2
import com.mmt.hotel.compose.review.viewModel.CorpReviewFragmentVM
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class HotelBookingFragmentModule {
     @Provides
    @IntoMap
    @ViewModelKey(CorpReviewFragmentVM::class)
    fun provideCorpReviewFragmentVM(viewModel: CorpReviewFragmentVM): HotelViewModel = viewModel

    @Provides
    fun getBookingReviewData(fragment : Fragment) : BookingReviewData? {
      if(fragment is HotelBookingReviewFragmentV2) {
            val data = fragment.arguments?.getParcelable(HotelBookingReviewFragmentV2.KEY_BUNDLE_PARAM)  as BookingReviewData?
            data?.promoConsentProvided = ConsentClient.getConsentFromCache()
            return data
        }
        throw RuntimeException("Only HotelBookingReviewFragment can get BookingReviewData as of now")
    }
}