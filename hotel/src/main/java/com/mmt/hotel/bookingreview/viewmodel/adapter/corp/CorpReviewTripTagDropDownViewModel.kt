package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.compose.runtime.mutableIntStateOf
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent.UPDATE_GST_BASED_ON_TRIP_TAG
import com.mmt.hotel.bookingreview.helper.CorpTravelDetailFormHelper
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagFieldV2

class CorpReviewTripTagDropDownViewModel(
    private val corpTripTagData: CorpTripTagFieldV2,
    private val eventStream: MutableLiveData<HotelEvent>
) : HotelCorpTravelDetailFormItemViewModelV2(corpTripTagData, eventStream) {

    val selectedIndex = ObservableInt(-1)

    val selectedOption = mutableIntStateOf(-1)

    init {
        checkForSelectedIndex()
    }

    fun getSelectedValue(): String {
        val selectedIndex = selectedOption.intValue
        val attributeSize = corpTripTagData.attributePossibleValues?.size
            ?: corpTripTagData.possibleValuesAndGST?.size ?: 0
        if (selectedOption.intValue == -1 || selectedIndex > (attributeSize - 1)) {
            return ResourceProvider.instance.getString(R.string.htl_SELECT)
        }
        return corpTripTagData.attributePossibleValues?.get(selectedIndex)
            ?: run {
                corpTripTagData.possibleValuesAndGST?.get(selectedIndex)?.value
                    ?: CoreConstants.EMPTY_STRING
            }
    }
    fun getAllPossibleOptions(): List<String> {
        val list = ArrayList<String>()
        if (corpTripTagData.attributePossibleValues?.isNotEmpty() == true) {
            list.addAll(corpTripTagData.attributePossibleValues ?: emptyList())
        } else if (corpTripTagData.possibleValuesAndGST?.isNotEmpty() == true) {
            corpTripTagData.possibleValuesAndGST.forEachIndexed { index, item ->
                if (item.value != null) {
                    val isSelected =
                        corpTripTagData.attributeSelectedValue?.contains(item.value) ?: false
                    list.add(item.value)
                    if (isSelected && CorpTravelDetailFormHelper.ItemTypes.DROP_DOWN.type.equals(
                            corpTripTagData.attributeType,
                            true
                        )
                    ) {
                        selectedIndex.set(index + 1)
                    }
                }
            }
        }
        return list
    }

    fun checkForSelectedIndex() {
        corpTripTagData.possibleValuesAndGST?.forEachIndexed { index, item ->
            if (item.value != null) {
                val isSelected =
                    corpTripTagData.attributeSelectedValue?.contains(item.value) ?: false
                if (isSelected && CorpTravelDetailFormHelper.ItemTypes.DROP_DOWN.type.equals(
                        corpTripTagData.attributeType,
                        true
                    )
                ) {
                    selectedOption.intValue = index
                }
            }
        }
    }

    fun handelAdapterItemOnSelectedItemV2(position: Int) {
        selectedOption.intValue = position
        showErrorObservable.set(false)
        showErrorState.value = false
        try {
            corpTripTagData.attributePossibleValues?.get(position)
                ?.let {
                    val possibleValueSet = HashSet<String>()
                    possibleValueSet.add(it)
                    corpTripTagData.attributeSelectedValue = ArrayList(possibleValueSet)
                }
            corpTripTagData.possibleValuesAndGST?.let { items ->
                items[position].let {
                    val possibleValueSet = HashSet<String>()
                    possibleValueSet.add(it.value.orEmpty())
                    corpTripTagData.attributeSelectedValue = ArrayList(possibleValueSet)
                    if (corpTripTagData.gstBasedTripTag == true) {
                        eventStream.postValue(
                            HotelEvent(
                                UPDATE_GST_BASED_ON_TRIP_TAG,
                                it.gstDetails
                            )
                        )
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}