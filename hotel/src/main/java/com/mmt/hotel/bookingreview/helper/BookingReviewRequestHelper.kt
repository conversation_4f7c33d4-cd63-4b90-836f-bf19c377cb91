package com.mmt.hotel.bookingreview.helper

import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.auth.login.model.Employee
import com.mmt.core.MMTCore
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.data.model.util.DeviceUtils
import com.mmt.hotel.bookingreview.model.BookingReviewData
import com.mmt.hotel.bookingreview.model.CheckoutData
import com.mmt.hotel.bookingreview.model.corp.AddEditEmployee
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.request.*
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.constants.CorpConstants.APPROVAL_STATUS_PENDING
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.data.RequestDetailsData
import com.mmt.hotel.common.helper.HotelCommonRequestHelper
import com.mmt.hotel.common.model.ResponseFilterFlag
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.HotelRequestConstants
import com.mmt.hotel.common.model.request.HotelRequestConstants.Companion.BOOKING_DEVICE
import com.mmt.hotel.common.model.request.RequestDetails
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.dayuse.model.request.DayUseApiSlot
import javax.inject.Inject

/*
*   Helper class for creating network request POJO for BookingReview
* */

class BookingReviewRequestHelper @Inject constructor(private val commonRequestHelper: HotelCommonRequestHelper) {

    fun getAvailRoomApiData(data: BookingReviewData, fetchUpsellInfo: Boolean, modifyBooking: Boolean): AvailRoomApiRequestV2 {
        var requestDetails = getRequestDetails(data)

        if (LoginUtils.getPreferredRegion().isGlobalEntity()) {
            val consent = if (LoginUtils.isLoggedIn) data.promoConsentProvided else false
            requestDetails = requestDetails?.copy(promoConsent = consent)
        }

        return AvailRoomApiRequestV2(
            deviceDetails = commonRequestHelper.getDeviceDetails()
                .copy(simSerialNo = DeviceUtils.getSimSubscriptionList(MMTCore.mContext)),
            searchCriteria = getSearchCriteria(data),
            requestDetails = requestDetails,
            featureFlags = ResponseFilterFlag(
                showUpsell = fetchUpsellInfo,
                quickReview = data.quickReview,
                showBnplCard = true,
                modifyBooking = modifyBooking,
                altCheaperDates = ExperimentUtil.getAlternateDatesPokusValue(data.userSearchData.hotelId),
                maskedPropertyName = data.maskedPropertyName
            ),
            expData = data.expData,
            filterCriteria = data.appliedFilters
        )
    }

    private fun getRequestDetails(data: BookingReviewData?): RequestDetails? {
        if (data == null) {
            return null
        }
        return getRequestDetail(data)
    }

    private fun getRequestDetail(data: BookingReviewData): RequestDetails {
        val requestDetailData = RequestDetailsData(
            funnel = data.userSearchData.funnelSrc,
            pageContext = HotelRequestConstants.PAGE_CONTEXT_REVIEW,
            zcpDataString = data.userSearchData.zcpDataString,
            payMode = data.payMode,
            preApprovedValidity = data.preApprovedValidity,
            requisitionID = data.userSearchData.requisitionID,
            myBizFlowIdentifier = data.userSearchData.myBizFlowIdentifier,
            journeyId = data.userSearchData.journeyId,
            workFlowId = data.userSearchData.workflowId,
            forwardFlow = data.userSearchData.forwardBookingFlow
        )
        return commonRequestHelper.getRequestDetails(requestDetailData)
    }

    private fun getSearchCriteria(data: BookingReviewData): AvailRoomSearchCriteria {
        val searchType = data.searchType
        val roomCriteriaV2 = data.roomCriteria
        val userSearchData = data.userSearchData
        val addonDetails = if(data.addOnDetail?.isEmpty() == true){
            null
        } else {
            data.addOnDetail
        }
        return AvailRoomSearchCriteria(
            hotelId = userSearchData.hotelId,
            checkIn =
                HotelUtil.convertCheckInDateFormat(
                    userSearchData.checkInDate,
                    HotelConstants.CURRENT_DATE_FORMAT, HotelConstants.NEW_DATE_FORMAT
                ),
            checkOut =
                HotelUtil.convertCheckOutDateFormat(
                    userSearchData.checkOutDate,
                    HotelConstants.CURRENT_DATE_FORMAT,
                    HotelConstants.NEW_DATE_FORMAT,
                    userSearchData.checkInDate
                ),
            countryCode = userSearchData.countryCode,
            locationId = userSearchData.locationId,
            locationType = userSearchData.locationType,
            cityCode = userSearchData.cityCode,
            currency = HotelCurrencyUtil.getSelectedCurrencyCode(),
            searchType = searchType,
            pricingKey = data.pricingKey,
            roomCriteria = roomCriteriaV2,
            travellerEmailID = getCorpPrimaryTraveller(data.corpPrimaryTraveller),
            personalCorpBooking = data.personalCorpBooking,
            slot = getSlotDetails(data),
            checkUpgrade = data.checkUpgrade,
            addOnDetail = addonDetails,
            hotelType = data.hotelType,
            guestHouseAvailable = data.guestHouseAvailable,
            userSearchType = HotelUtil.getUserSearchType(userSearchData.searchType),
            tripType = userSearchData.tripType
        )
    }

    private fun getSlotDetails(data: BookingReviewData): DayUseApiSlot? {
        val slotRequestData = data.dayUseSlotRequestData ?: return null
        return DayUseApiSlot(duration = slotRequestData.duration,  timeSlot = slotRequestData.timeSlot)
    }

    private fun getCorpPrimaryTraveller(data: List<Employee>?): List<String>? {
        if(!LoginUtils.isCorporateUser)
            return null
        val travellerList = mutableListOf<String>()
        data?.let {employeeList ->
            if(employeeList.isNotEmpty() && employeeList.first().type != "guest") {
                travellerList.add(employeeList.first().businessEmailId)
            }else{
                LoginUtils.loggedInUserEmail?.let { travellerList.add(it) }
            }
        } ?: LoginUtils.loggedInUserEmail?.let { travellerList.add(it) }
        return travellerList
    }

    fun getUpdateCorpPolicyRequestData(txnKey: String?, travellerEmailId: List<String>?, countryCode: String?): UpdateCorpPolicyRequest {
        return UpdateCorpPolicyRequest(txnKey, BOOKING_DEVICE, travellerEmailId, countryCode)
    }

    fun getValidateCouponApiData(
        bookingReviewData: BookingReviewData?,
        txnKey: String,
        couponCode: String,
        isCouponApplied: Boolean,   // coupon is clicked which will be applied, initially this coupon is not applied
        expData: String?,
        quickCheckoutApplicable: Boolean = false
    ): ValidateCouponApiRequestV2 {
        return ValidateCouponApiRequestV2(
            requestDetails = getRequestDetails(bookingReviewData),
            txnKey = txnKey, couponCode = couponCode, removeCoupon = !isCouponApplied,
            expData = expData,quickCheckoutApplicable= quickCheckoutApplicable,
            featureFlags = BookingReviewResponseFilterFlag(maskedPropertyName = bookingReviewData?.maskedPropertyName)
        )
    }

    fun getTotalPricingRequestData(bookingReviewData: BookingReviewData?,txnKey: String,
                                   addonSelected: List<AddOnSelected>, countryCode: String, expData: String?, enableTcs: Boolean = false): TotalPricingRequestV2 {
        return TotalPricingRequestV2(
            requestDetails = getRequestDetails(bookingReviewData),
            countryCode = countryCode,
            txnKey = txnKey,
            addOnSelected = addonSelected,
            expData = expData,
            enableTcs = enableTcs,
            featureFlags = BookingReviewResponseFilterFlag(maskedPropertyName = bookingReviewData?.maskedPropertyName)
        )
    }

    fun getApprovalPostRequestData(corpBookingReviewHelper: HotelCorpBookingReviewHelper, reasonForTravel: Map<String, String>?, checkDuplicateBooking : Boolean? = null): PostApprovalRequestV2 {
        return PostApprovalRequestV2(
            requestDetails = getRequestDetailsForApproval(corpBookingReviewHelper.getUserSearchData(),checkDuplicateBooking),
            reasonForTravel = reasonForTravel,
            workflowStatus = APPROVAL_STATUS_PENDING,
            specialRequest = corpBookingReviewHelper.getSpecialCheckoutRequest(),
            tripTag = corpBookingReviewHelper.getSelectedTripTagData(),
            travellerDetailsList = corpBookingReviewHelper.getTravellerDetailsList(),
            txnKey = corpBookingReviewHelper.getTxnKey(),
            requisitionID = corpBookingReviewHelper.getBookingReviewData()?.userSearchData?.requisitionID,
            myBizFlowIdentifier = corpBookingReviewHelper.getBookingReviewData()?.userSearchData?.myBizFlowIdentifier)
    }

    private fun getRequestDetailsForApproval(
        userSearchData: UserSearchData?,
        checkDuplicateBooking: Boolean?
    ): RequestDetails? {
        userSearchData?.let {
            val requestDetailData = RequestDetailsData(
                funnel = it.funnelSrc,
                pageContext = HotelRequestConstants.PAGE_CONTEXT_REVIEW,
                zcpDataString = it.zcpDataString,
                requisitionID = it.requisitionID,
                myBizFlowIdentifier = it.myBizFlowIdentifier,
                journeyId = it.journeyId,
                checkDuplicateBooking = checkDuplicateBooking
            )
            return commonRequestHelper.getRequestDetails(requestDetailData)
        }
        return null
    }

    fun  getCheckoutApiData(checkoutData: CheckoutData): CheckoutApiRequest {
        return CheckoutApiRequest(
                requestDetails = getRequestDetails(checkoutData),
                transactionKey = checkoutData.transactionKey,
                currency = checkoutData.currency,
                travellerDetails = checkoutData.travellerDetailList,
                paymentDetail = checkoutData.paymentDetail,
                skipDoubleBlack = checkoutData.skipDoubleBlack,
                authenticationDetail = checkoutData.authenticationDetail,
                specialRequest = checkoutData.specialRequest,
                workflowStatus = checkoutData.workflowStatus,
                tripTag = checkoutData.tripTag,
                tripDetailsText = checkoutData.tripDetailsText,
                skipRtbValidation  = checkoutData.skipRtbValidation,
                personalCorpBooking = checkoutData.personalCorpBooking,
                reasonForSkipApproval = checkoutData.reasonForSkipApproval,
                gstnDetail = checkoutData.gstnDetail,
                promoConsent = checkoutData.promoConsent,
                addOnDetail = checkoutData.addOnDetail,
                flexibleCheckinSlotId = checkoutData.flexibleCheckinSlotId
        )
    }

    fun getRequestDetails(data: CheckoutData): RequestDetails? {
        data.userSearchData?.let {
            val requestDetailData = RequestDetailsData(
                funnel = it.funnelSrc,
                pageContext = HotelRequestConstants.PAGE_CONTEXT_REVIEW,
                zcpDataString = it.zcpDataString,
                payMode = data.payMode,
                preApprovedValidity = data.preApprovedValidity,
                requisitionID = it.requisitionID,
                myBizFlowIdentifier = it.myBizFlowIdentifier,
                journeyId = it.journeyId,
                checkDuplicateBooking = data.checkDuplicateBooking
            )
            return commonRequestHelper.getRequestDetails(requestDetailData)
        }
        return null
    }

    fun getAddEmployeeData(travellerDetail: CorpTravellerDetail): AddNewEmployeeRequest {
        return AddNewEmployeeRequest(employee = getAddEditEmployee(travellerDetail),
                organizationId = LoginUtils.orgId ?: CoreConstants.EMPTY_STRING)
    }

    private fun getAddEditEmployee(travellerDetail: CorpTravellerDetail): AddEditEmployee {
        return AddEditEmployee(
            name = travellerDetail.getTravellerName(),
            phoneNumber = travellerDetail.contactNo,
            businessEmailId = travellerDetail.emailId
        )
    }

    fun getSelectedCurrency() = HotelCurrencyUtil.getSelectedCurrencyCode()
}