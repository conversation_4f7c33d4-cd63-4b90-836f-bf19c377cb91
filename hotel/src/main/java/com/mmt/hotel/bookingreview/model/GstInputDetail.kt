package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.hotel.R
import com.mmt.hotel.common.constants.HotelConstants.ET_ERROR_BG
import com.mmt.hotel.common.constants.HotelConstants.ET_NORMAL_BG
import com.mmt.hotel.common.util.HotelUtil.getGstNumberValidationRegex
import com.mmt.hotel.common.util.HotelUtil.validateRegEx
import kotlinx.parcelize.Parcelize

@Parcelize
data class GstInputDetail(var number: String? = null,
                          var numberBg: Int = ET_NORMAL_BG,
                          var numberErrorMsg: String? = null,
                          var name: String? = null,
                          var nameBg: Int = ET_NORMAL_BG,
                          var nameErrorMsg: String? = null,
                          var address: String? = null,
                          var addressBg: Int = ET_NORMAL_BG,
                          var addressErrorMsg: String? = null,
                          var showNameError: Boolean = false,
                          var showNumberError: Boolean = false,
                          var showAddressError: Boolean = false,) : Parcelable {
    fun isNumberValid(): Boolean {
        val result: Boolean
        if(number.isNullOrEmpty()){
            result = false
            numberErrorMsg = ResourceProvider.instance.getString(R.string.htl_gst_no_empty)
        } else {
            if (LoginUtils.getPreferredRegion().isGlobalEntity()) {
                result = StringUtil.isNotNullAndEmpty(number)
                numberErrorMsg =
                    if (result) null else ResourceProvider.instance.getString(R.string.htl_trn_no_error)
            } else {
                result = validateRegEx(number, getGstNumberValidationRegex())
                numberErrorMsg =
                    if (result) null else ResourceProvider.instance.getString(R.string.htl_gst_no_error)
            }
        }
        numberBg = if(result) ET_NORMAL_BG else ET_ERROR_BG
        showNumberError = !result
        return result
    }

    fun isNameValid(): Boolean {
        val result = !name.isNullOrEmpty()
        nameBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        nameErrorMsg = if(result) null else  ResourceProvider.instance.getString(R.string.htl_gst_name_empty)
        showNameError = !result
        return result
    }

    fun isAddressValid(): Boolean {
        val result = !address.isNullOrEmpty()
        addressBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        addressErrorMsg = if(result) null else  ResourceProvider.instance.getString(R.string.htl_gst_addr_empty)
        showAddressError = !result
        return result
    }

    fun resetGstErrors(){
        numberBg = ET_NORMAL_BG
        nameBg = ET_NORMAL_BG
        addressBg = ET_NORMAL_BG
        numberErrorMsg = null
        nameErrorMsg = null
        addressErrorMsg = null
        showNameError = false
        showNumberError = false
        showAddressError = false
    }
}