package com.mmt.hotel.bookingreview.viewmodel.adapter

import com.mmt.core.extensions.isNotNullAndEmpty
import com.mmt.hotel.bookingreview.model.response.PriceFooter
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.hotel.R
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.common.constants.PaymentModeTypes
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

open class PaymentButtonViewModel(
    val priceFooter: PriceFooter?,
    val payMode: String,
    private val requestToBookFlow: Boolean,
    private val eventStream: MutableLiveData<HotelEvent>?
) : PaymentButtonFooterViewModel() {

    private val resources = ResourceProvider.instance

   override fun getBgStartColor() = resources.getColor(R.color.htl_booking_payment_bg_start)

   override fun getBgEndColor() = resources.getColor(R.color.htl_booking_payment_bg_end)

   override fun onClick() {
        if (requestToBookFlow && !LoginUtils.isLoggedIn) {
            val loginHeader = resources.getString(R.string.htl_login_to_book)
            eventStream?.postValue(HotelEvent(HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY,loginHeader))
        }else{
            eventStream?.postValue(HotelEvent(HotelBookingReviewFragmentEvent.PAYMENT_BUTTON_CLICKED))
        }
    }

    override fun getPriceFooterData() = priceFooter

    override fun showFooter() = priceFooter != null

    override fun getCtaText(): String {
        return if (priceFooter?.ctaText.isNotNullAndEmpty()) {
            priceFooter?.ctaText ?: getButtonText()
        } else {
            getButtonText()
        }
    }

    override fun isEnabled(): Boolean {
        return true
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.PAYMENT_BUTTON
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        (item as? PaymentButtonViewModel)?.let {
            return it.payMode == this.payMode && it.getButtonText() == this.getButtonText()
        }
        return false
    }

    override fun cardName(): String {
        return "Review payment Button"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.PAYMENT_BUTTON
    }

    fun getButtonText(): String{
        if (requestToBookFlow && !LoginUtils.isLoggedIn){
           return ResourceProvider.instance.getString(R.string.htl_login_to_book)
        }else if (requestToBookFlow) {
            return ResourceProvider.instance.getString(R.string.htl_book_now_text)
        } else {
            if (payMode != PaymentModeTypes.PAS) {
                return ResourceProvider.instance.getString(R.string.htl_pah_review_button_text)
            }
            return ResourceProvider.instance.getString(R.string.htl_review_button_text)
        }
    }
}