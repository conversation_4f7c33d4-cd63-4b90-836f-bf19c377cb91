package com.mmt.hotel.bookingreview.model.corp

import android.os.Parcelable
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.bookingreview.model.response.CorpApprovalInfo
import com.mmt.hotel.bookingreview.model.response.CorpApprovingManager
import com.mmt.hotel.bookingreview.model.response.CorpAutoBookRequestorConfig
import com.mmt.hotel.corpapproval.model.response.CorpCategoryReasons
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import kotlinx.parcelize.Parcelize

@Parcelize
data class CorpApprovalRequestFragmentData(
    var employeeComment: String = EMPTY_STRING,
    val corpApprovalInfo: CorpApprovalInfo?,
    val reasonForBooking: List<CorpReasons>? = null,
    val corpAutoBookRequestorConfig: CorpAutoBookRequestorConfig? = null,
    val corpApprovingManagers: List<CorpApprovingManager>?,
    val corpSkipApprovalReasonsData: CorpSkipApprovalReasonsData? = null
) : Parcelable