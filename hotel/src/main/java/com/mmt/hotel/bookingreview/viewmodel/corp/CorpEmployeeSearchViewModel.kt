package com.mmt.hotel.bookingreview.viewmodel.corp

import android.text.Editable
import android.widget.Toast
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.viewModelScope
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.auth.login.model.Employee
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.bookingreview.dataModel.CorpAddTravellerConfig
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent.EDIT_TRAVELLER
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent.ON_BACK_PRESSED
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent.UPDATE_RECYCLER_VIEW
import com.mmt.hotel.bookingreview.event.HotelBookingPolicyFragmentEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.repository.HotelCorpBookingReviewRepository
import com.mmt.hotel.bookingreview.viewmodel.adapter.corp.CorpAddTravellerSearchListViewModel
import com.mmt.hotel.common.constants.CorpConstants.EMPLOYEE
import com.mmt.hotel.common.constants.CorpConstants.GUEST
import com.mmt.hotel.common.di.NamedConstants
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject
import javax.inject.Named
import kotlin.collections.HashSet

class CorpEmployeeSearchViewModel @Inject constructor(
    @Named(NamedConstants.CORP_EMPLOYEE_SEARCH_ACTIVITY)
    private val corpBookingReviewRepository: HotelCorpBookingReviewRepository
) : HotelToolBarViewModel() {

    val isClearVisible = ObservableBoolean(false)
    val searchedQuery = ObservableField<String>()
    val searchResultsVisibility = ObservableBoolean(false)
    val loading = ObservableBoolean(false)
    val searchResultLoadError = ObservableBoolean(true)
    val searchResultErrorText = ObservableField<String>()
    val resourceProvider = ResourceProvider.instance
    var employeesAddedList:ArrayList<CorpTravellerDetail> ?= null
    var viaPrimary: Boolean = false
    val addGuestBtnTextColor = ObservableField<Int>(R.color.color_008cff)
    var employeeConfig: CorpAddTravellerConfig? = null
    var guestConfig: CorpAddTravellerConfig ?= null

    init{
        loadInitialState()
    }

    fun loadInitialState(){
        employeeConfig = CorpAddTravellerConfig(title = ResourceProvider.instance.getString(R.string.htl_add_employee_title),
                                                description = ResourceProvider.instance.getString(R.string.htl_add_employee_description),
                                                ctaAction = ::addEmployeeFragment,
                                                ctaText = ResourceProvider.instance.getString(R.string.htl_add_employee_cta))
        guestConfig = CorpAddTravellerConfig(title = ResourceProvider.instance.getString(R.string.htl_add_guest_title),
                                            description = ResourceProvider.instance.getString(R.string.htl_add_guest_description),
                                            ctaAction = ::addGuestFragment,
                                            ctaText = ResourceProvider.instance.getString(R.string.htl_add_guest_cta))
    }
    private fun retrieveSearchResults() {
        hideErrorMessage()
        showLoader()
        viewModelScope.launch {
            corpBookingReviewRepository.fetchSearchQueryResults(searchedQuery.get().toString())
                .catch {
                    it.printStackTrace()
                    setRecentSearchVisibility(false)
                    hideLoader()
                }.collect{
                    hideLoader()
                    it.employees.let {employeeList ->
                        if( employeeList.isNullOrEmpty()) {
                            setRecentSearchVisibility(false)
                        } else {
                            var employeeList1:MutableList<Employee> = mutableListOf()
                            val alreadyTravellingEmployees: HashSet<String> = hashSetOf()
                            employeesAddedList?.forEach { traveller ->
                                alreadyTravellingEmployees.add(traveller.emailId)
                            }
                            for(employee in employeeList){
                                if(!alreadyTravellingEmployees.contains(employee.businessEmailId)){
                                    employeeList1.add(employee)
                                }
                            }
                            val searchList = responseConverter(employeeList1)
                            updateEventStream(HotelEvent(UPDATE_RECYCLER_VIEW, searchList))
                            setRecentSearchVisibility(true)
                        }
                    }
                }
        }
    }

    fun setPrimaryTravellerEmailIdToSearchText(primaryTravellerEmailId: String) {
        searchedQuery.set(primaryTravellerEmailId)
        retrieveSearchResults()
    }

    fun onBackPressed() {
        updateEventStream(HotelEvent(ON_BACK_PRESSED, this))
    }

    fun afterTextChanged(s: Editable) {
        val searchedText = s.toString()
        initErrorText(searchedText)
        isClearVisible.set(!s.isBlank())
        onSearchTextChanged(searchedText)
    }

    private fun initErrorText(searchedText: String) {
        val resourceProvider = ResourceProvider.instance
        if( searchedText.length < 2 ) {
            searchResultErrorText.set(resourceProvider.getString(R.string.htl_employee_search_alert))
        } else {
            if(com.mmt.auth.login.util.LoginUtils.isCorpUserAdmin) {
                searchResultErrorText.set(String.format(resourceProvider.getString(R.string.htl_employee_not_found_message), searchedText))
            }else {
                searchResultErrorText.set(String.format(resourceProvider.getString(R.string.htl_employee_not_found_message), searchedText))
            }
        }
    }

    private fun onSearchTextChanged(searchedText: String) {
        if (searchedText == searchedQuery.get()) {
            return
        }
        searchedQuery.set(searchedText)

        if (searchedText == "") {
            setRecentSearchVisibility(false)
        } else {
            retrieveSearchResults()
            // setRecentSearchVisibility(true)
        }
    }

    fun onClearClicked() {
        initErrorText(CoreConstants.EMPTY_STRING)
        onSearchTextChanged(CoreConstants.EMPTY_STRING)
    }

    private fun setRecentSearchVisibility(visibility: Boolean) {
        this.searchResultsVisibility.set(visibility)
        if( visibility ) {
            hideErrorMessage()
        } else {
            showErrorMessage()
        }
    }

    private fun responseConverter(listEmployee: List<Employee>): List<CorpAddTravellerSearchListViewModel> {
        val searchList: MutableList<CorpAddTravellerSearchListViewModel> = mutableListOf()
        listEmployee.let {
            for (employee in listEmployee) {
                if( employee.isNameAndEmailValid ) {
                    searchList.add(CorpAddTravellerSearchListViewModel(employee, eventStream))
                }
            }
        }
        return searchList
    }

    private fun showLoader() {
        this.loading.set(true)
    }

    private fun hideLoader() {
        this.loading.set(false)
    }

    private fun showErrorMessage() {
        this.searchResultLoadError.set(true)
    }

    private fun hideErrorMessage() {
        this.searchResultLoadError.set(false)
    }

    fun isCorpAdmin() = com.mmt.auth.login.util.LoginUtils.isCorpUserAdmin

    fun getAddEditTravellerTitleText(): String {
        return if(isCorpAdmin()) {
            resourceProvider.getString(R.string.htl_corp_review_add_traveller_title)
        }else{
            resourceProvider.getString(R.string.htl_corp_review_edit_traveller_title, GUEST)
        }
    }

    fun getAddGuestBtnText(): String = resourceProvider.getString(R.string.htl_corp_review_add_traveller_btn, GUEST.uppercase(Locale.ROOT))

    fun getAddEmployeeBtnText(): String = resourceProvider.getString(R.string.htl_corp_review_add_traveller_btn, EMPLOYEE.uppercase(Locale.ROOT))

    fun addEmployeeFragment() = updateEventStream(HotelEvent(EDIT_TRAVELLER, CorpAddEditTravellerFragmentData(travellerType = EMPLOYEE)))

    fun addGuestFragment() {
        if(viaPrimary) {
            Toast.makeText(MMTCore.mContext, resourceProvider.getString(R.string.htl_corp_review_invalid_primary_traveller), Toast.LENGTH_SHORT).show()
        }else{
            updateEventStream(HotelEvent(EDIT_TRAVELLER, CorpAddEditTravellerFragmentData(travellerType = GUEST)))
        }
    }

    override fun getTitle(): String {
        return ResourceProvider.instance.getString(R.string.htl_add_another_traveller)
    }

    override fun onHandleBackPress() {
        eventStream.postValue(HotelEvent(DISMISS_FRAGMENT))
    }
}

