package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.runtime.mutableStateOf
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.model.PayOptionActionData

class PaymentCardItemVM(
        selected: <PERSON><PERSON><PERSON>,
        title: String,
        subtitle: String?= null,
        val payOptionActionData: PayOptionActionData,
        private val eventStream: MutableLiveData<HotelEvent>
) {
    val isSelected = ObservableBoolean(selected)

    val isSelectedState = mutableStateOf(selected)

    val title = ObservableField<String>(title)
    val subtitle = ObservableField<String>(subtitle)
    fun updateSelection(selected: <PERSON><PERSON><PERSON>, text: String? = null, subText: String? = null) {
        updateData(text,subText)
        isSelected.set(selected)
        isSelectedState.value =selected
    }
    fun onClicked() {
        eventStream.value = HotelEvent(HotelBookingReviewFragmentEvent.PAYMENT_OPTION_SELECTED,payOptionActionData)
    }

    fun updateData(text: String?, subText: String?) {
        text?.let { title.set(it) }
        subText?.let { subtitle.set(it) }
    }
}