package com.mmt.hotel.bookingreview.model.response.price

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.AddOnInfo
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RTBInfo
import com.mmt.hotel.bookingreview.model.UpsellInfo
import com.mmt.hotel.bookingreview.model.response.*
import com.mmt.hotel.bookingreview.model.response.addon.AddonDataV2
import com.mmt.hotel.bookingreview.model.response.property.HotelPropertyRules
import com.mmt.hotel.bookingreview.model.response.additional.HotelAdditionalFees
import com.mmt.hotel.bookingreview.model.response.addon.subscription.SubscriptionData
import com.mmt.hotel.bookingreview.model.response.room.RoomRatePlan
import com.mmt.hotel.chatBot.dataModel.ChatBotWidgetInfo
import com.mmt.hotel.common.model.response.CancellationTimelineModel
import com.mmt.hotel.common.model.response.HotelsUserBlackInfo
import com.mmt.hotel.common.model.response.MsmeOfferCardModel
import com.mmt.hotel.common.model.response.TcsWidgetInfo
import com.mmt.hotel.common.model.response.persuasionCards.CardDataV2
import com.mmt.hotel.common.model.response.persuasionCards.CardInfo
import com.mmt.hotel.compose.review.dataModel.FlexiDetailBottomSheetData
import com.mmt.hotel.corpapproval.model.response.ApprovalDetails
import com.mmt.hotel.corpapproval.model.response.CorpApprovalSearchContext
import com.mmt.hotel.detail.model.response.GeneralInfo
import com.mmt.hotel.listingV2.model.response.hotels.HotelEmployee
import com.mmt.hotel.old.model.hotelreview.response.specialRequest.SpecialRequestForm
import kotlinx.parcelize.Parcelize

@Parcelize
data class AvailRoomResponseV2(
    val hotelInfo: HotelDetailInfo?,
    @SerializedName("blackInfo")
    val userBlackInfo: HotelsUserBlackInfo?,
    val addons: List<AddonDataV2>?,
    @SerializedName("totalpricing")
    val hotelPriceBreakUp: HotelPriceBreakUp?,
    @SerializedName("rateplanlist")
    val ratePlanList: List<RoomRatePlan>?,
    @SerializedName("specialrequests")
    val specialRequests: SpecialRequestForm?,
    val additionalFees: HotelAdditionalFees?,
    val propertyRules: HotelPropertyRules?,
    val intlRoamingInfo: IntlRoamingInfo?,
    val panInfo: HotelPanInfo?,
    val emiDetails: HotelEmiDetailsMessage?,
    val bnplDetails: HotelBnplDetails?,
    val fullPayment: HotelFullPaymentDetails?,
    val gstInfo: HotelGstInfo?,
    val tcsInfo: HotelTCSInfo?,
    val featureFlags: FeatureFlags?,
    val doubleBlackInfo: DoubleBlackInfo?,
    val txnKey: String,
    val alerts: List<BookingAlerts>?,
    @SerializedName("campaignAlert")
    val campaignAlert: BookingAlerts?,
    @SerializedName("cancellationPolicyTimeline")
    val payLaterTimeLineModel: CancellationTimelineModel?,
    val corpApprovalInfo: CorpApprovalInfo?,
    val approvingManagers: List<CorpApprovingManager>?,
    val tripDetailsCard: TripDetailsCardInfo?,
    @SerializedName("upsellOptions")
    val upsellOptions: List<UpsellInfo>?,
    val correlationKey: String? = null,
    val approvalDetails: ApprovalDetails?,
    val searchContext: CorpApprovalSearchContext?,
    @SerializedName("msmeCorpCard")
    val msmeCorpCard: MsmeOfferCardModel?,
    @SerializedName("corpData")
    val corpData: CorpData? = null,
    @SerializedName("cardData")
    val cards: List<CardDataV2>? = null,
    @SerializedName("paymentPlan")
    val paymentPlan: PaymentPlan?,
    @SerializedName("payLaterCard")
    val tripMoneyBnplData: TripMoneyBnplData?,
    @SerializedName("paxDetails")
    val corpPaxDetails: List<HotelEmployee>?,
    val instantFareInfo: InstantFareInfo? = null,
    val addOnInfo: AddOnInfo? = null,
    val rtbCard: RTBInfo? = null,
    @SerializedName("luckyUserContext")
    val xUserType: String? = null,
    val quickCheckoutApplicable: Boolean? = false,
    @SerializedName("smeSubscriptionData")
    val subscriptionCardData: SubscriptionData? = null,
    @SerializedName("scarcityInfo")
    val scarcityInfo: ScarcityInfo? = null,
    val rateplansUpgrade: RatePlansUpgrade? = null,
    @SerializedName("ackId")
    val ackId: String?,
    val flexiDetailBottomSheet: FlexiDetailBottomSheetData? = null,
    val selectedAddOnState: String? = null,
    @SerializedName("hotelCloudData")
    val hotelCloudData: HotelCloudData? = null,
    @SerializedName("tcsWidgetInfo")
    val tcsWidgetInfo: TcsWidgetInfo?,
    val trackingText: String? = null,
    @SerializedName("chatbotInfo")
    val chatBotWidgetInfo: ChatBotWidgetInfo? = null,
    @SerializedName("cardsMap")
    val cardsMap: Map<String, CardInfo>? = null,
    @SerializedName("currency")
    val currencyCode: String? = null,
    val trackingMap: Map<String, String>? = null
) : Parcelable

@Parcelize
data class ScarcityInfo(
    @SerializedName("title")
    val title: String?,
    @SerializedName("subtitle")
    val subtitle: String?,
    @SerializedName("positiveAction")
    val positiveAction: String?,
    @SerializedName("negativeAction")
    val negativeAction: String?
) : Parcelable

@Parcelize
data class CorpData(
    @SerializedName("corpCapturePersonalBookingGstn")
    val corpCapturePersonalBookingGstn: Boolean
) : Parcelable

@Parcelize
data class HotelCloudData(
    val persuasionIcon: String?,
    val persuasionText: String?,
    val title: String?,
    val sectionFeatures: List<GeneralInfo>?
) : Parcelable