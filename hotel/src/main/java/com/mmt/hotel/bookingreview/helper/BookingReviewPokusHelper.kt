package com.mmt.hotel.bookingreview.helper

import com.mmt.auth.login.util.LoginUtils

import com.mmt.hotel.base.RecyclerItemSequenceProvider
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.common.util.isHotelsLightUi
import com.mmt.hotel.common.constants.ExperimentsHotel
import javax.inject.Inject

class BookingReviewPokusHelper @Inject constructor() : RecyclerItemSequenceProvider {

    private val cardDefaultSequence = listOf(
            BookingRecyclerAdapterItemKeys.PROPERTY_DETAIL,
            BookingRecyclerAdapterItemKeys.REQUEST_CALLBACK,
            BookingRecyclerAdapterItemKeys.BUSINESS_FUNNEL_IDENTIFICATION,
            BookingRecyclerAdapterItemKeys.REVIEW_PRICE_ALERT,
            BookingRecyclerAdapterItemKeys.ROOM_INFO,
            BookingRecyclerAdapterItemKeys.ADD_ONS,
            BookingRecyclerAdapterItemKeys.BOOKING_INFO_CARD,
            BookingRecyclerAdapterItemKeys.DOUBLE_BLACK_CARD,
            BookingRecyclerAdapterItemKeys.CORP_MSME_OFFER_CARD,
            BookingRecyclerAdapterItemKeys.PROPERTY_RULES,
            BookingRecyclerAdapterItemKeys.BPG_CARD,
            BookingRecyclerAdapterItemKeys.BOOKING_PRICE_BREAKUP,
            BookingRecyclerAdapterItemKeys.GST_DETAIL_CARD,
            BookingRecyclerAdapterItemKeys.PAYMENT_POLICY_CARD,
            BookingRecyclerAdapterItemKeys.TRIPMONEY_BNPL_CARD,
            BookingRecyclerAdapterItemKeys.PAYMENT_OPTIONS,
            BookingRecyclerAdapterItemKeys.COUPONS_DETAIL,
            BookingRecyclerAdapterItemKeys.ADDITIONAL_FEES,
            BookingRecyclerAdapterItemKeys.FREE_ADD_ON_CARD,
            BookingRecyclerAdapterItemKeys.TRAVELLER_DETAIL_INPUT,
            BookingRecyclerAdapterItemKeys.INTERNATIONAL_ROAMING_CARD,
            BookingRecyclerAdapterItemKeys.BLACK_CARD,
            BookingRecyclerAdapterItemKeys.SPECIAL_REQUEST,
            BookingRecyclerAdapterItemKeys.TERMS_CONDITION_V2,
            BookingRecyclerAdapterItemKeys.TRIP_DETAILS_CARD,
            BookingRecyclerAdapterItemKeys.CAMPAIGN_ALERT,
            BookingRecyclerAdapterItemKeys.PAYMENT_BUTTON
    )

    private val lightUICardSequence = listOf(
        BookingRecyclerAdapterItemKeys.PROPERTY_DETAIL,
        BookingRecyclerAdapterItemKeys.REQUEST_CALLBACK,
        BookingRecyclerAdapterItemKeys.BUSINESS_FUNNEL_IDENTIFICATION,
        BookingRecyclerAdapterItemKeys.REVIEW_PRICE_ALERT,
        BookingRecyclerAdapterItemKeys.ROOM_INFO,
        BookingRecyclerAdapterItemKeys.ADD_ONS,
        BookingRecyclerAdapterItemKeys.BOOKING_PRICE_BREAKUP,
        BookingRecyclerAdapterItemKeys.COUPONS_DETAIL,
        BookingRecyclerAdapterItemKeys.ADDITIONAL_FEES,
        BookingRecyclerAdapterItemKeys.TRAVELLER_DETAIL_INPUT,
        BookingRecyclerAdapterItemKeys.TERMS_CONDITION_V2,
        BookingRecyclerAdapterItemKeys.CAMPAIGN_ALERT,
        BookingRecyclerAdapterItemKeys.PAYMENT_BUTTON
    )

    private val corpCardDefaultSequence = listOf(
            BookingRecyclerAdapterItemKeys.CORP_APPROVAL_WORK_FLOW_STATUS,//first card that will be shown in case of approvalFlow only
            BookingRecyclerAdapterItemKeys.CORP_APPROVAL_GUEST_HOUSE_INFO_CARD,
            BookingRecyclerAdapterItemKeys.PROPERTY_DETAIL,
            BookingRecyclerAdapterItemKeys.REVIEW_PRICE_ALERT,
            BookingRecyclerAdapterItemKeys.HOTEL_CLOUD,
            BookingRecyclerAdapterItemKeys.ROOM_INFO,
            BookingRecyclerAdapterItemKeys.ADD_ONS,
            BookingRecyclerAdapterItemKeys.CORP_SUBSCRIPTION_CARD,
            BookingRecyclerAdapterItemKeys.CORP_PRICE_DETAIL,
            BookingRecyclerAdapterItemKeys.COUPONS_DETAIL,
            BookingRecyclerAdapterItemKeys.PROPERTY_RULES,
            BookingRecyclerAdapterItemKeys.CORP_MSME_OFFER_CARD,
            BookingRecyclerAdapterItemKeys.CORP_ADD_TRAVELLER,
            BookingRecyclerAdapterItemKeys.PAN_TCS_WIDGET,
            BookingRecyclerAdapterItemKeys.CORP_ADD_CO_TRAVELLER,
            BookingRecyclerAdapterItemKeys.CORP_REASON_FOR_BOOKING,

            BookingRecyclerAdapterItemKeys.CORP_ADD_GST_INFORMATION,
            BookingRecyclerAdapterItemKeys.CORP_TRIP_TAG,
            BookingRecyclerAdapterItemKeys.CORP_GST,
            BookingRecyclerAdapterItemKeys.SPECIAL_REQUEST,
            BookingRecyclerAdapterItemKeys.TERMS_CONDITION_V2,
            BookingRecyclerAdapterItemKeys.CORP_APPROVAL_BTNS)

    override fun getCardSequence(): List<String> {
        return if (LoginUtils.isCorporateUser) {
            getCardSequenceFromPokusForB2B()
        } else if (isHotelsLightUi()) {
            lightUICardSequence
        } else {
            getCardSequenceFromPokusForB2C()
        }
    }

    fun getCardSequenceFromPokusForB2C(): List<String> {
        val cardSequencePokusValue: String? = getCardSequenceForBookingReview()
        if (cardSequencePokusValue.isNullOrEmpty()) {
            return cardDefaultSequence
        }
        val cards = cardSequencePokusValue.split(",")
        if (cards.size <= 1) {
            return cardDefaultSequence
        }
        return cards
    }

    fun getCardSequenceFromPokusForB2B(): List<String> {
        val cardSequencePokusValue: String = getCardSequenceForBookingReview()
        if (cardSequencePokusValue.isEmpty()) {
            return corpCardDefaultSequence
        }
        val cards = cardSequencePokusValue.split(",")
        if (cards.size <= 1) {
            return corpCardDefaultSequence
        }
        return cards
    }

    fun getCardSequenceForBookingReview(): String {
        return if (LoginUtils.isCorporateUser) {
            ExperimentsHotel.reviewDetailCardOrderB2B.getPokusValue()
        } else {
            ExperimentsHotel.reviewDetailCardOrderB2C.getPokusValue()
        }
    }
}