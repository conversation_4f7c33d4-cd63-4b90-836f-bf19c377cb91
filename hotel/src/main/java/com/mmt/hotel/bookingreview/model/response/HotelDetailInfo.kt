package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.common.data.HotelCategoryData
import com.mmt.hotel.detail.constants.DetailPageViewType
import com.mmt.hotel.detail.model.response.FlexibleCheckinInfo
import com.mmt.hotel.detail.model.response.RequestCallBackData
import com.mmt.hotel.listingV2.model.request.SupportDetails
import com.mmt.hotel.old.model.hotelListingResponse.Address
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelDetailInfo(val categories: List<String>?,
                           val applicableHotelCategoryData: LinkedHashMap<String, HotelCategoryData>?,
                           val hotelIcon: String?,
                           val propertyType: String?, // to be used for tracking only
                           val starRating: Int?,
                           val starRatingType: String?,
                           val highSellingAltAcco: Boolean = false,
                           val address: Address?,
                           val cityName: String?,
                           val name: String?,
                           val hotelId: String?,
                           val countryName: String?,
                           val listingType: String?,
                           val guestRoomKey: String?,
                           val guestRoomValue: String?,
                           val checkinTime: String?,
                           val checkoutTime: String?,
                           @SerializedName("mmtHotelCategory")
                           @DetailPageViewType
                           val propertyViewType: String?,
                           val dayUseInfo : DayUseInfo?,
                           val hotelTags: Map<String, HotelTagInfo>?,
                           val altAcco: Boolean = false,
                           val quickBookSubTitle: String?,
                           @SerializedName("entireProperty")
                           val entireProperty : Boolean = false,
                           val groupBookingPrice: Boolean = false,
                           val roomText: String? = null,
                           val propertyLabel: String? = null, // to be used for ui
                           val bedInfoText: String? = null,
                           val bedroomCount: Int? = null,
                           val childOccupancyMsg: String? = null,
                           val supportDetails : SupportDetails? = null,
                           @SerializedName("requestCallbackData")
                           val requestCallBackData: RequestCallBackData? = null,
                           @SerializedName("showCallToBook")
                           val showCallToBook: Boolean,
                           @SerializedName("flexibleCheckinInfo")
                           val flexibleCheckinInfo: FlexibleCheckinInfo?,
                           val supplierType:String? = null
                           ) : Parcelable