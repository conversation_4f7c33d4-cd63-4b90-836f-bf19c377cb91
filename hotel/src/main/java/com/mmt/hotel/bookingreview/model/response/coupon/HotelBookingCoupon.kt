package com.mmt.hotel.bookingreview.model.response.coupon

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.listingV2.model.response.hotels.TemplatePersuasion
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelBookingCoupon(
    @SerializedName("code")
    val couponCode: String,
    @SerializedName("couponAmount")
    val amount: Double,
    val description: String,
    @SerializedName("autoApplicable")
    val autoApplicable: Boolean = false,
    @SerializedName("disabled")
    val isDisabled: Boolean = false, //if true show it locked else show it enabled,
    @SerializedName("bnplAllowed")
    val bnplAllowed: Boolean = false,
    @SerializedName("tncUrl")
    val tncUrl: String? = null,
    @SerializedName("promoIcon")
    val promoIcon: String? = null,
    val couponPersuasions: Map<String, TemplatePersuasion>?,
    @SerializedName("title")
    val title: String? = null,
    @SerializedName("couponType")
    val couponType: String? = null,
    @SerializedName("persuasionText")
    val persuasionText: String? = null,
    @SerializedName("couponTypeText")
    val couponTypeText: String? = null,
    @SerializedName("tncText")
    val tncText: String? = null,
    @SerializedName("bankName")
    val bankName: String? = null,
    @SerializedName("noCostEmiApplicable")
    val noCostEmiApplicable: Boolean = false,
    @SerializedName("bankOffer")
    val bankOffer: Boolean = false
) : Parcelable