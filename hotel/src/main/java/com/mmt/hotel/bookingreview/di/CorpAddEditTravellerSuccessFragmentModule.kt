package com.mmt.hotel.bookingreview.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerSuccessFragmentData
import com.mmt.hotel.bookingreview.ui.corp.CorpAddEditTravellerSuccessFragment
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpAddEditTravellerSuccessViewModel
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class CorpAddEditTravellerSuccessFragmentModule {
    @Provides
    @IntoMap
    @ViewModelKey(CorpAddEditTravellerSuccessViewModel::class)
    fun provideCorpAddEditTravellerSuccessViewModel(viewModel: CorpAddEditTravellerSuccessViewModel): HotelViewModel = viewModel

    @Provides
    fun provideBundleData(fragment : Fragment) : CorpAddEditTravellerSuccessFragmentData?{
        if(fragment is CorpAddEditTravellerSuccessFragment) {
            return fragment.arguments?.getParcelable(CorpAddEditTravellerSuccessFragment.TAG)
        }
        return null
    }
}
