package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.lifecycle.MutableLiveData
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem.Companion.MMT_SELECT_CARD_V2
import com.mmt.hotel.bookingreview.dataModel.MMTSelectCardV2UiData
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.uikit.custom.RoundedTransformation

class MMTSelectV2CardViewModel(var data: MMTSelectCardV2UiData, eventStream: MutableLiveData<HotelEvent>): DiffUtilRecycleItem {

    val MMT_SELECT_V2 = "MMT Select V2 Card"
    val imageTransformation = RoundedTransformation(
        ResourceProvider.instance.getDimension(R.dimen.htl_review_card_radius_large).toInt(), 0
    )
    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.BLACK_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val newVM = (item as? MMTSelectV2CardViewModel)
        return data == newVM?.data
    }

    override fun cardName(): String {
        return MMT_SELECT_V2
    }

    override fun getItemType(): Int {
       return MMT_SELECT_CARD_V2
    }

}