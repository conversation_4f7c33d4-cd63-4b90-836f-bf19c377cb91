package com.mmt.hotel.bookingreview.viewmodel.corp

import com.mmt.hotel.bookingreview.viewmodel.adapter.CoTravellerSelectedItemViewModel
import android.widget.RadioGroup
import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_CO_TRAVELLER_FRAGMENT
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.GstInputDetail
import com.mmt.hotel.bookingreview.model.TravellerDetailV2
import com.mmt.hotel.bookingreview.model.UserInputDetail
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.constants.HotelConstants.DEFAULT_TITLE
import com.mmt.hotel.bookingreview.model.CoTravellerFragmentData
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.bookingreview.viewmodel.TravelerViewModel
import com.mmt.hotel.bookingreview.viewmodel.adapter.BookingReviewPriceViewModel
import com.mmt.hotel.common.constants.GuestType
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.listingV2.event.HotelListingClickEvents

/**
 * class to handle business logic corresponding to Corp personal booking traveller information view
 *
 * create by Varun Airon on 31/01/22
 */
class CorpPersonalBookingTravellerViewModel(val foreignTravel: Boolean = false, var eventStream: MutableLiveData<HotelEvent>) : BaseObservable(),
    TravelerViewModel {
    companion object {
        private const val TRAVELLER_TYPE_SELF = "TRAVELLER_TYPE_SELF"
        private const val TRAVELLER_TYPE_SOMEONE_ELSE = "TRAVELLER_TYPE_SOMEONE_ELSE"

        var RB_SELF_ID = R.id.rb_myself
        var RB_OTHER_ID = R.id.rb_other
    }

    var coTravellerList = mutableListOf<CoTraveller>()
    var coTravellerUiItemList = mutableListOf<LinearLayoutItemData>()

    var travellerType = TRAVELLER_TYPE_SELF

    var userInputDetail: UserInputDetail
    var gstDetail: GstInputDetail
    private val loggedInUser = LoginUtils.loggedInUser

    init {
        userInputDetail = getLoggedInUserDetails()
        gstDetail = GstInputDetail()
    }

    fun hasCoTraveller(): Boolean {
        return coTravellerUiItemList.isNotEmpty()
    }

    fun showAddCoTravellerOption(): Boolean{
        return LoginUtils.isIndiaFunnelContext()
    }

    fun setTravellerType(rg: RadioGroup, id: Int) {
        when (id) {
            RB_SELF_ID -> setSelfDetail()
            RB_OTHER_ID -> setOtherDetail()
        }
    }

    private fun setSelfDetail() {
        travellerType = TRAVELLER_TYPE_SELF
        updateUserInputDetail(getLoggedInUserDetails())
        eventStream.postValue(HotelEvent(HotelListingClickEvents.BOOKING_FOR_MYSELF,true))
    }

    private fun getLoggedInUserDetails() : UserInputDetail {
        return UserInputDetail().apply {
            loggedInUser?.corpData?.employee?.let { employee ->
                val namePair = HotelUtil.getFirstAndLastNamePair(employee.name)
                title = HotelUtil.getTitle(loggedInUser.title,loggedInUser.gender)
                name = namePair.first
                surname = namePair.second
                isdCode =
                    if (loggedInUser.primaryContactCountryCode.isNotEmpty()) loggedInUser.primaryContactCountryCode.toInt() else HotelUtil.getIsdCodeForTravellerForm()
                contactNo = employee.phoneNumber ?: CoreConstants.EMPTY_STRING
                emailId = employee.businessEmailId ?: CoreConstants.EMPTY_STRING
            }
        }
    }

    private fun setOtherDetail() {
        travellerType = TRAVELLER_TYPE_SOMEONE_ELSE
        updateUserInputDetail(getDefaultDetail())
        eventStream.postValue(HotelEvent(HotelListingClickEvents.BOOKING_FOR_MYSELF,false))
    }

    private fun getDefaultDetail(): UserInputDetail {
        return UserInputDetail().apply {
            loggedInUser?.let {
                isdCode = if (loggedInUser.primaryContactCountryCode.isNotEmpty()) loggedInUser.primaryContactCountryCode.toInt() else HotelUtil.getIsdCodeForTravellerForm()
                contactNo = loggedInUser.primaryContact ?: CoreConstants.EMPTY_STRING
                emailId = loggedInUser.emailId ?: CoreConstants.EMPTY_STRING
            }
        }
    }

    private fun updateUserInputDetail(data: UserInputDetail) {
        userInputDetail = data
        notifyChange()
    }

    fun validateTravellerData(): Boolean {
        val result = validateTravellerDetail()
        notifyChange()
        return result
    }

    private fun validateTravellerDetail(): Boolean {
        return userInputDetail.run {
            var result = isNameValid()
            result = isSurnameValid() && result
            result = isContactNoValid() && result
            result
        }
    }



    fun addGuest() {
        eventStream.value = HotelEvent(OPEN_CO_TRAVELLER_FRAGMENT,
            CoTravellerFragmentData(travellerType = travellerType, coTravellerList = coTravellerList))
    }

    fun showCoTraveller(coTravellerList: MutableList<CoTraveller>) {
        this.coTravellerList = coTravellerList
        coTravellerUiItemList = mutableListOf()
        for (coTraveller in coTravellerList) {
            coTravellerUiItemList.add(LinearLayoutItemData(R.layout.htl_co_traveller_selected_item, BR.model, CoTravellerSelectedItemViewModel(eventStream, coTraveller)))
        }
        notifyChange()
    }

    fun removeCoTraveller(coTravellerItem: CoTravellerSelectedItemViewModel) {
        val travellers = coTravellerUiItemList.toList()
        for (itemData in travellers) {
            if (itemData.data as CoTravellerSelectedItemViewModel == coTravellerItem) {
                coTravellerUiItemList.remove(itemData)
                coTravellerList.remove(coTravellerItem.coTraveller)
            }
        }
        notifyChange()
    }

    fun updateDoubleBlackName(name: String, surname: String) {
        userInputDetail.apply {
            this.name = name
            this.surname = surname
        }
        notifyChange()
    }

    fun getTravellerDataForCheckout(gstnDetails: GSTNDetails?): MutableList<TravellerDetailV2> {
        val travellerDetailList = mutableListOf<TravellerDetailV2>()
        val masterTravellerDetail = TravellerDetailV2(
            title = userInputDetail.title,
            firstName = userInputDetail.name,
            lastName = userInputDetail.surname,
            emailID = userInputDetail.emailId,
            masterPax = true,
            isdCode = userInputDetail.isdCode.toString(),
            mobileNo = userInputDetail.contactNo,
            paxType = GuestType.ADULT.name,
            panCard = if (userInputDetail.pan.isNotEmpty()) userInputDetail.pan else null,
            registerGstinNum = gstnDetails?.gstn,
            gstinCompanyName = gstnDetails?.organizationName,
            gstinCompanyAddress = gstnDetails?.address1,
            state = gstnDetails?.city,
            saveGstDetails = gstnDetails?.saveGstDetails
            //roomNo = if(isMultiRoomTravellerSection()) 1 else null
        )
        travellerDetailList.add(masterTravellerDetail)

        for (coTraveller in coTravellerList) {
            val coTravellerDetail = TravellerDetailV2(title = coTraveller.title
                ?: DEFAULT_TITLE,
                firstName = coTraveller.first_name.orEmpty(),
                lastName = coTraveller.last_name.orEmpty(),
                emailID = coTraveller.traveller_email,
                masterPax = false,
                paxType = if (coTraveller.pax_type == GuestType.CHILD.name) GuestType.CHILD.name else GuestType.ADULT.name,
                gender = coTraveller.gender,
                travellerId = coTraveller.travellerId
            )
            travellerDetailList.add(coTravellerDetail)
        }
        return travellerDetailList
    }


    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CORP_PERSONAL_BOOKING_TRAVELLER_CARD
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_ADD_TRAVELLER
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as CorpPersonalBookingTravellerViewModel).foreignTravel
        return foreignTravel == matchedWith
    }

    override fun cardName(): String {
        return "Corp Review Personal Booking"
    }
}