package com.mmt.hotel.bookingreview.dataModel

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import kotlinx.parcelize.Parcelize

@Parcelize
data class CouponItemUIData(
    val code: String,
    val description: String?,
    val amount: String,
    val hotelCoupon: HotelBookingCoupon,
    var showCrossIcon: Boolean,
    val isSelected: Boolean,
    val couponSuccessMsg: String? = null,
    var showDivider: Boolean = true,
    val disableBnplNotApplicableCoupon: Boolean = false, //if true , disable coupons due to bnpl not allowed on coupon
    val errorText: String? = null,
    val tncUrl: String? = null,
    val tncText: String? = null,
    val couponTypeText: String? = null,
    val title: String? = null,
    val promoIcon : String? = null,
    val persuasionText : String? = null,
    val isBankOffer : Boolean = false


) : Parcelable