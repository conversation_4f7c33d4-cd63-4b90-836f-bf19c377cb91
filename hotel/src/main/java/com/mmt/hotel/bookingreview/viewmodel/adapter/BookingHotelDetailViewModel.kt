package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.dataModel.alerts.PriceChangeAlertUiDataModel
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.BookingHotelDetailUiData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent.TRACK_UPSELL_SHOWN
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.listingV2.dataModel.HotelRatingUIData
import com.mmt.hotel.userReviews.featured.util.RatingUtilsV2
import com.mmt.uikit.custom.RoundedTransformation
import com.mmt.uikit.util.isNotNullAndEmpty
import com.squareup.picasso.Transformation


class BookingHotelDetailViewModel(val data: BookingHotelDetailUiData, private val eventStream: MutableLiveData<HotelEvent>)
    : BaseObservable(), DiffUtilRecycleItem {

    fun getHotelName() = data.name

    fun getAddress() = data.address

    fun getStarRating() = data.starRating

    fun getHotelRatingUIData(): HotelRatingUIData {
        return HotelRatingUIData(data.starRating, data.starRatingType, data.isAltAcco, data.countryCode)
    }

    fun getImgUrl() = data.imgUrl

    fun showEntirePropertyBlock() = HotelUtil.showBedInfo(data.stayDetailUiData.isEntireProperty)

    fun showBedInfoText() =
        HotelUtil.showBedInfo(data.stayDetailUiData.isEntireProperty) && data.stayDetailUiData.bedInfo.isNotNullAndEmpty()

    fun getCheckInDate() = data.checkInCheckOutDetails.checkInDate

    fun getCheckInTime() = data.checkInCheckOutDetails.checkInTime

    fun getCheckOutDate() = data.checkInCheckOutDetails.checkOutDate

    fun getCheckOutTime() = data.checkInCheckOutDetails.checkOutTime

    fun getGuestDetails() = data.stayDetailUiData.guestDetails

    fun getChildOccupancyMsg() = data.stayDetailUiData.childOccupancyMsg

    fun getGuestDetailsLabel() = data.stayDetailUiData.guestDetailsLabel

    fun getGuestRoomValue() = data.stayDetailUiData.guestRoomValue

    fun getRoomBedInfo(): String? {
        if (data.stayDetailUiData.bedInfo.isNullOrEmpty()) {
            return EMPTY_STRING
        }
        return data.stayDetailUiData.bedInfo
    }

    fun getNightCount() = data.checkInCheckOutDetails.noOfNights


    fun getHotelCategories() = data.hotelCategories

    fun getMMTSpecificCategories() = data.mmtSpecificCategories

    fun showMMTSpecificCategories() = data.mmtSpecificCategories.isNotEmpty()

    fun getTopPlaceholderInfo() = data.hotelTags?.get(HotelTagPlaceholder.PC_HOTEL_TOP)

    fun getBottomPlaceholderInfo() = data.hotelTags?.get(HotelTagPlaceholder.PC_BELOW_HOTEL)

    fun getContentDesc(): String {
        val str = "You are about to book property ${getHotelName()} located at ${getAddress()}. Your check in date is ${getCheckInDate()} at ${getCheckInTime()} and check out date is ${getCheckOutDate()} at ${getCheckOutTime()} for ${getNightCount()} . "
        val entirePropertyText = getGuestRoomValue() + getRoomBedInfo()
        val guestDetails = "Guest Details : ${getGuestDetails()} ${if(showEntirePropertyBlock()) entirePropertyText else ""}. "
        return str + guestDetails
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.PROPERTY_DETAIL
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as BookingHotelDetailViewModel).data
        return data == matchedWith
    }

    override fun cardName(): String {
        return "Review Property Detail"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.PROPERTY_DETAIL
    }

    fun getPicassoTransformation(): Transformation {
        return RoundedTransformation(ResourceProvider.instance.getDimensionPixelSize(R.dimen.margin_tiny), 0)
    }

    fun onCtaTextViewClick() {
        data.instantFareInfo?.cta?.deeplink?.let{ eventStream.value = HotelEvent(
            HotelBookingReviewActivityEvent.OPEN_DEEPLINK, it) }
    }

    fun getCtaTextViewTitle() = data.instantFareInfo?.cta?.title
    fun getCtaTitle() = data.instantFareInfo?.title


}