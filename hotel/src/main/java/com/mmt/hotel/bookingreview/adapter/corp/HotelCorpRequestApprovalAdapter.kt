package com.mmt.hotel.bookingreview.adapter.corp

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.HotelBaseRecyclerAdapter
import com.mmt.hotel.base.ui.viewHolder.HotelGeneralRecyclerViewHolder
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalManagerInfoVM
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalReasonsVM
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalReviewViewModel.Companion.REQUEST_APPROVAL_MANAGER_INFO_ITEM
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalReviewViewModel.Companion.REQUEST_APPROVAL_REASONS_ITEM
import com.mmt.hotel.databinding.HotelCorpRequestApprovalManagerInfoBinding
import com.mmt.hotel.databinding.HotelCorpRequestApprovalReasonsBinding

class HotelCorpRequestApprovalAdapter(private val itemList: MutableList<AbstractRecyclerItem>) : HotelBaseRecyclerAdapter(itemList) {

    override fun getViewHolder(viewType: Int, layoutInflater: LayoutInflater, parent: ViewGroup):
            HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem> {
        return when (viewType) {
            REQUEST_APPROVAL_MANAGER_INFO_ITEM -> HotelGeneralRecyclerViewHolder<HotelCorpRequestApprovalManagerInfoBinding, HotelCorpRequestApprovalManagerInfoVM>(layoutInflater, R.layout.hotel_corp_request_approval_manager_info, parent, BR.viewModel)

            REQUEST_APPROVAL_REASONS_ITEM -> HotelGeneralRecyclerViewHolder<HotelCorpRequestApprovalReasonsBinding, HotelCorpRequestApprovalReasonsVM>(layoutInflater, R.layout.hotel_corp_request_approval_reasons, parent, BR.viewModel)

            else -> {
                //do nothing
                null
            }
        } as HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>
    }
}