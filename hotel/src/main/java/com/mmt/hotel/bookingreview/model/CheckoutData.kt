package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import com.mmt.data.model.payment.PaymentType
import com.mmt.hotel.bookingreview.model.request.AddOnRequestData
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.model.request.AuthenticationDetail
import com.mmt.hotel.bookingreview.model.request.PaymentDetail
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.old.hotelreview.model.request.checkout.SpecialCheckoutRequest
import kotlinx.parcelize.Parcelize

/**
 * 1. avail api -> must recreate object
 * 2. total price, validate coupon api -> update [paymentType], [paymentDetail] (Possibility of paymde, emi, bnpl flag change)
 */
@Parcelize
data class CheckoutData(val transactionKey: String,
                        val idContext: String = if(com.mmt.auth.login.util.LoginUtils.isCorporateUser) "CORP" else "B2C",
                        val currency: String,
                        val countryCode: String,
                        val recheckRequired: Boolean = com.mmt.auth.login.util.LoginUtils.isCorporateUser,
                        var paymentType: PaymentType,
                        var travellerDetailList: List<TravellerDetailV2> = mutableListOf<TravellerDetailV2>(),
                        var paymentDetail: PaymentDetail = PaymentDetail(),
                        var skipDoubleBlack: Boolean? = null,
                        var authenticationDetail: AuthenticationDetail? = null,
                        var specialRequest: SpecialCheckoutRequest? = null,
                        var workflowStatus: String? = null,
                        var tripTag: PostApprovalTripTag? = null,
                        var skipRtbValidation:Boolean = false,
                        var tripDetailsText: String? = null,
                        var responseData: CheckoutResponse? = null,
                        var personalCorpBooking : Boolean = false,
                        var reasonForSkipApproval : String? = null,
                        var gstnDetail: DomesticGSTNDetails? =  null,
                        val userSearchData: UserSearchData?,
                        val payMode : String?,
                        val preApprovedValidity:Long?,
                        var promoConsent: Boolean? = null,
                        var addOnDetail : Map<String, AddOnRequestData>? = null,
                        var flexibleCheckinSlotId: String? = null,
                        var checkDuplicateBooking: Boolean? = null,
                        var maskedPropertyName: Boolean? = null,
) : Parcelable