package com.mmt.hotel.bookingreview.model.corp

import android.os.Parcelable
import com.mmt.hotel.R
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import kotlinx.parcelize.Parcelize

/**
 * class to hold data required to display skip approval reason to user
 *
 * create by Varun Airon on 02/02/22
 */
@Parcelize
data class CorpSkipApprovalReasonsData(
    val title: String? = null,
    val skipApprovalReasons: List<CorpReasons>? = null,
    val isRequisitionFlow: Boolean = false,
) : Parcelable