package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.detail.model.response.RuleTableInfo
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelPolicyCategory(val category: String,
                               val subCategory: String?,
                               val id: String,
                               val title: String,
                               @SerializedName("categoryDesc")
                               val ruleDesc: String?,
                               val ruleTableInfo: RuleTableInfo?,
                               val rules: List<String>?) : Parcelable