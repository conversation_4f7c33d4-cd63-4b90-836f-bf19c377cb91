package com.mmt.hotel.bookingreview.viewmodel.adapter

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import androidx.databinding.BaseObservable
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.model.webview.WebViewBundle
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.EventType
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.BookingReviewPriceCardAdapterItem.Companion.ITEM_BREAKUP
import com.mmt.hotel.bookingreview.adapter.BookingReviewPriceCardAdapterItem.Companion.ITEM_TOTAL_PRICE
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.dataModel.PriceItemUIData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.response.HotelTCSInfo
import com.mmt.hotel.common.util.*
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.uikit.util.isNotNullAndEmpty


class BookingReviewPriceViewModel(
    var data: PriceItemUIData,
    var tcsInfo: HotelTCSInfo?,
    private val eventStream: MutableLiveData<HotelEvent>,
    private val eventLambda: ((HotelEvent) -> Unit)? = null
) : BaseObservable(), DiffUtilRecycleItem {

    var backGround: ObservableInt = ObservableInt(R.drawable.htl_detail_item_bg)
    val items = ObservableArrayList<AbstractRecyclerItem>()
    var showTCSInfo = ObservableBoolean(false)

    init {
        updateBackground(data)
        getPriceBreakupItems()
    }

    fun updateData(data: PriceItemUIData) {
        val hotelTag = this.data.hotelTagInfo
        val persuasions = this.data.persuasions
        this.data = data.copy(hotelTagInfo = hotelTag, persuasions = persuasions)
        updateBackground(data)
        getPriceBreakupItems()
        notifyChange()
    }

    fun updateBackground(data: PriceItemUIData) {
        if (data.isDayUseFunnel) {
            backGround.set(R.drawable.htl_detail_item_bg)
        } else {
            backGround.set(R.drawable.border_circular_edges)
        }
        notifyChange()
    }

    fun getSpannableDescription(): SpannableStringBuilder {
        val builder = StringBuilder()
        val resourceProvider = ResourceProvider.instance
        val descriptionText = tcsInfo?.msg ?: EMPTY_STRING
        val ctaText = tcsInfo?.cta ?: EMPTY_STRING
        val ctaUrl = tcsInfo?.webUrl ?: EMPTY_STRING

        builder.append(descriptionText)

        if (ctaText.isNotNullAndEmpty()) {
            builder.append(CoreConstants.SPACE)
            builder.append(ctaText)
        }

        val spanBuilder = SpannableStringBuilder(builder.toString())
        val webViewBundle = WebViewBundle(
            webViewTitle = resourceProvider.getString(R.string.htl_learn_more_about_TCS),
            webViewUrl = ctaUrl
        )
        if (ctaText.isNotNullAndEmpty()) {
            setClickableSpan(builder, spanBuilder, webViewBundle, ctaText)

            spanBuilder.setSpan(
                StyleSpan(Typeface.BOLD),
                builder.indexOf(ctaText),
                builder.indexOf(ctaText) + ctaText.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spanBuilder.setSpan(
                ForegroundColorSpan(resourceProvider.getColor(R.color.htl_color_008cff)),
                builder.indexOf(ctaText),
                builder.indexOf(ctaText) + ctaText.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        if (spanBuilder.isNotEmpty()) {
            showTCSInfo.set(true)
        }
        return spanBuilder
    }

    private fun setClickableSpan(
        stringBuilder: StringBuilder,
        spanBuilder: SpannableStringBuilder,
        webViewBundle: WebViewBundle,
        ctaText: String
    ) {
        spanBuilder.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val tcsBottomSheetEvent = HotelEvent(
                            HotelBookingReviewFragmentEvent.OPEN_TCS_CTA_BOTTOMSHEET,
                            webViewBundle, eventType = EventType.BOTTOM_SHEET
                    )
                    eventLambda?.invoke(tcsBottomSheetEvent)?:run {
                        eventStream.value = HotelEvent(
                                HotelBookingReviewFragmentEvent.OPEN_TCS_CTA_BOTTOMSHEET,
                                webViewBundle
                        )
                    }

                }

                override fun updateDrawState(ds: TextPaint) {
                    ds.isUnderlineText = false
                }

            },
            stringBuilder.indexOf(ctaText),
            stringBuilder.indexOf(ctaText) + ctaText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
    }

    fun getBasePriceLabel(): String {
        return data.itemMap[KEY_BASE_FARE]?.label ?: EMPTY_STRING
    }

    fun getBasePriceAmount(): String {
        return data.itemMap[KEY_BASE_FARE]?.amount ?: EMPTY_STRING
    }


    fun getTotalDiscountAmount(): String {
        return data.itemMap[KEY_TOTAL_DISCOUNT]?.amount ?: EMPTY_STRING
    }
    fun getTotalPriceAfterDiscountAmount(): String {
        return data.itemMap[KEY_PRICE_AFTER_DISCOUNT]?.amount.orEmpty()
    }

    fun getTaxesAmount(): String {
        return data.itemMap[KEY_TAXES]?.amount.orEmpty()
    }

    fun getTotalPriceAmount(): String {
        return if (data.itemMap.containsKey(KEY_TOTAL_AMOUNT)) {
            data.itemMap[KEY_TOTAL_AMOUNT]?.amount.orEmpty()
        } else {
            data.itemMap[AMOUNT_YOU_PAYING_AT_HOTEL_KEY]?.amount.orEmpty()
        }
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.PRICE_DETAIL
    }


    fun topPadding(): Float {
        return if (data.isDayUseFunnel) {
            ResourceProvider.instance.getDimensionPixelSize(R.dimen.htl_detail_card_padding_v).toFloat()
        } else {
            ResourceProvider.instance.getDimensionPixelSize(R.dimen.htl_review_card_padding).toFloat()
        }
    }


    fun getTitle(): String {
        return if (data.isDayUseFunnel) {
            ResourceProvider.instance.getString(R.string.htl_price_breakup_card_title)
        } else {
            EMPTY_STRING
        }
    }

    fun getContentDesc(): String {
        val str = StringBuilder()
        str.append("Price breakup for your booking is. ")
        if(getBasePriceAmount().isNotEmpty()) str.append("Base price,${getBasePriceAmount()}. ")
        if(getTotalDiscountAmount().isNotEmpty()) str.append("Total discount,${getTotalDiscountAmount()}. ")
        if(getTotalPriceAfterDiscountAmount().isNotEmpty()) str.append("Discounted price,${getTotalPriceAfterDiscountAmount()}. ")
        if(getTaxesAmount().isNotEmpty()) str.append("Taxes and Service fees applicable, ${getTaxesAmount()}. ")
        if(getTotalPriceAmount().isNotEmpty()) str.append("Total amount to be paid, ${getTotalPriceAmount()}. ")
        return str.toString()
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.BOOKING_PRICE_BREAKUP
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as BookingReviewPriceViewModel).data
        return data == matchedWith
    }

    override fun cardName(): String {
        return "Review Price Detail"
    }

    fun getPersuasionDataModel(placeHolderId: String) = data.persuasions?.get(placeHolderId)

    private fun getPriceBreakupItems(){
        val list = mutableListOf<AbstractRecyclerItem>()
        data.itemMap[KEY_BASE_FARE]?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_BREAKUP, eventLambda = eventLambda))
        }
        data.itemMap[KEY_TOTAL_DISCOUNT]?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_BREAKUP, eventLambda = eventLambda))
        }
        data.itemMap[KEY_PRICE_AFTER_DISCOUNT]?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_BREAKUP, eventLambda = eventLambda))
        }
        data.itemMap[KEY_TAXES]?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_BREAKUP, eventLambda = eventLambda))
        }
        data.itemMap[KEY_INSURANCE]?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_BREAKUP, eventLambda = eventLambda))
        }
        data.itemMap[KEY_CHARITY]?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_BREAKUP, eventLambda = eventLambda))
        }
        data.charityAddOnUIData?.let {
            list.add(BookingReviewCharityItemViewModel(it, eventStream, eventLambda = eventLambda))
        }
        data.itemMap[AMOUNT_YOU_PAYING_NOW_KEY]?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_BREAKUP, eventLambda = eventLambda))
        }
        (data.itemMap[KEY_TOTAL_AMOUNT] ?: data.itemMap[AMOUNT_YOU_PAYING_AT_HOTEL_KEY])?.let {
            list.add(BookingReviewPriceItemViewModel(it, eventStream, ITEM_TOTAL_PRICE, eventLambda = eventLambda))
        }
        items.clear()
        items.addAll(list)
    }
}