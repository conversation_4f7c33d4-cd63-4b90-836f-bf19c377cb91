package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class TripDetailsCardInfo(val headerSubText:String?,
                               val headerText:String?,
                               val texts:List<String>?,
                               val title:String?,
                               val actionText:String?,
                               val actionUrl:String?) : Parcelable