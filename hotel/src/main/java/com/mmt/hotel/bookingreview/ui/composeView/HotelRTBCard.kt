package com.mmt.hotel.bookingreview.ui.composeView

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTag
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RTBInfo
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.widget.compose.MmtComposeTextView

object ResourcesIds{
    val title = "title"
    val subtitle = "subtitle"
    val infoText = "infoText"
    val persuasionIcon = "persuasionIcon"
    val persuasionText = "persuasionText"
}

const val DAY = "DAY"
const val NIGHT = "NIGHT"

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun HotelRTBCard(rtbCard: RTBInfo) {
    val currentBrush = getGradientBrush(rtbCard.type)

    val rtbModifier = getRtbCardModifier()

    // Card Content
    Column(
        modifier = rtbModifier
            .semantics { testTagsAsResourceId = true }
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(start = dimensionResource(R.dimen.htl_review_card_margin), end = dimensionResource(R.dimen.htl_review_card_margin))
            .background(
                brush = currentBrush,
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_large))
            )
            .padding(start = dimensionResource(id = R.dimen.margin_medium),
                top = dimensionResource(id = R.dimen.margin_medium),
                bottom = dimensionResource(id = R.dimen.margin_medium),
                end = dimensionResource(id = R.dimen.margin_small))
    ) {
        // Title
        renderTitle(rtbCard.title)

        // Render rtbInfoList as bullet points
        renderRtbInfoList(rtbCard.rtbInfoList)

        // Render persuasion row if available
        renderPersuasionRow(rtbCard.persuasionIcon, rtbCard.persuasionText, rtbCard.type)
    }
}

@Composable
fun getGradientBrush(type: String?): Brush {
    val dayBrush = createLinearGradientBrush(R.color.htl_color_ffe0cb)
    val nightBrush = createLinearGradientBrush(R.color.htl_rtb_card_night_background)
    val defaultBrush = createLinearGradientBrush(R.color.color_ffffff)

    return when (type) {
        NIGHT -> nightBrush
        DAY -> dayBrush
        else -> defaultBrush
    }
}

@Composable
fun createLinearGradientBrush(colorId: Int): Brush {
    return Brush.linearGradient(
        colors = listOf(
            colorResource(id = R.color.color_ffffff),
            colorResource(id = R.color.color_ffffff),
            colorResource(id = colorId)
        ),
        start = Offset(0f, Float.POSITIVE_INFINITY),
        end = Offset(Float.POSITIVE_INFINITY, 0f)
    )
}

@Composable
fun getRtbCardModifier(): Modifier {
    return Modifier.padding(top = dimensionResource(id = R.dimen.margin_large))
}

@Composable
fun renderTitle(title: String?) {
    MmtComposeTextView(
        modifier = Modifier.semantics { testTag = ResourcesIds.title },
        text = title,
        mmtFontStyle = latoBlack,
        fontSize = spDimensionResource(id = R.dimen.htl_text_size_large),
        color = Color.Black
    )
}

@Composable
fun renderRtbInfoList(rtbInfoList: List<String>?) {
    if (!rtbInfoList.isNullOrEmpty()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = dimensionResource(R.dimen.margin_medium),
                    top = dimensionResource(R.dimen.margin_small),
                    end = dimensionResource(R.dimen.margin_medium)
                )
        ) {
            rtbInfoList.forEach { info ->
                BulletPointItem(text = info)
            }
        }
    }
}

@Composable
fun renderPersuasionRow(persuasionIcon: String?, persuasionText: String?, type: String?) {
    if (!persuasionIcon.isNullOrBlank() && !persuasionText.isNullOrBlank()) {
        Row(
            modifier = Modifier
                .padding(
                    top = dimensionResource(id = R.dimen.margin_small),
                    end = dimensionResource(R.dimen.margin_medium)
                )
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Render persuasion icon
            LoadImage(
                modifier = Modifier
                    .padding(start = dimensionResource(id = R.dimen.margin_extra_tiny), end = 0.dp, top = 0.dp, bottom = 0.dp)
                    .semantics { testTag = ResourcesIds.persuasionIcon }
                    .size(dimensionResource(R.dimen.image_dimen_medium)),
                imageUrl = persuasionIcon
            )

            // Render persuasion text
            MmtComposeTextView(
                modifier = Modifier
                    .padding(start = dimensionResource(id = R.dimen.margin_tiny))
                    .semantics { testTag = ResourcesIds.persuasionText },
                text = persuasionText,
                mmtFontStyle = latoBold,
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
                color = if (type == DAY) colorResource(id = R.color.htl_review_persuasion_text_color) else colorResource(id = R.color.htl_rtb_persuasion_text_night)
            )
        }
    }
}

@Composable
fun BulletPointItem(text: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = dimensionResource(id = R.dimen.margin_tiny))
    ) {
        Image(
            modifier = Modifier.padding(top = 2.dp),
            painter = painterResource(R.drawable.ic_htl_ic_bullet_with_padding),
            contentDescription = null
        )

        MmtComposeTextView(
            text = text,
            color = colorResource(id = R.color.htl_description_text_black_color),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            mmtFontStyle = latoRegular,
            lineHeight = spDimensionResource(R.dimen.text_view_height_medium),
            modifier = Modifier
                .semantics { testTag = ResourcesIds.infoText }
                .weight(1f)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun RTBPreview() {
    HotelRTBCard(
        RTBInfo(
            "Booking Confirmation within 24 hours.",
            null,
            null,
            listOf("The host may take some time to confirm as this property is generally in high demand.", "The host may take some time to confirm as this property is generally in high demand."),
            "DAY",
            "https://promos.makemytrip.com/altaccoimages/RTB_dayicon.png",
            "Persuasion Text"
        )
    )
}