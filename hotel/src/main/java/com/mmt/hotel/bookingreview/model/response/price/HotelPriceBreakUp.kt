package com.mmt.hotel.bookingreview.model.response.price

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.response.PriceFooter
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.detail.model.response.CouponPersuasion
import com.mmt.hotel.listingV2.model.response.hotels.TemplatePersuasion
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelPriceBreakUp(
    val loginMessage: String?,
    val priceDisplayMsg: String?,
    val priceTaxMsg: String?,
    val originalPriceMsg: String?,
    val couponDesc: String?,
    val pricingKey: String,
    val couponAmount: String?,
    val details: List<PriceItem>?,
    val coupons: MutableList<HotelBookingCoupon>?,
    val bookingDeeplink: String?,
    val priceToolTip: String?,
    val noCouponText: String?,
    val payAtHotelText: String?,
    val groupPriceText: String?,
    val savingsText: String?,
    val bnplUnavailableMsg: String? = null,
    val payAtHotelMsg: String?,
    val couponSubtext: String?,
    @SerializedName("currency")
    val currency: String? = null,
    val pinCodeMandatory: Boolean? = null,
    val pricePersuasions: Map<String, TemplatePersuasion>? = null,
    val priceFooter: PriceFooter? = null,
    val benefitDeals: BenefitDeal? = null,
    val couponPersuasion: CouponPersuasion? = null,
    val maxCouponsToShow: Int? = null,
    val emiPlanDetail: EmiDetails? = null,
    val linkedRPDiscountMsg: String? = null,
    val linkedRPBottomSheetTitle: String? = null,
    val linkedRPOriginalPriceMsg: String? = null,
    ) : Parcelable

@Parcelize
data class EmiDetails(
    val message: String?,
    val ctaText: String?,
    val emiPlanDesc: String,
    val emiTagType: String?,
    val emiTagDetail: String?,
): Parcelable