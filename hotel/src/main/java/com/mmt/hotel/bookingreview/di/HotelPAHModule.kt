package com.mmt.hotel.bookingreview.di

import android.app.Activity
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.PahIntentData
import com.mmt.hotel.bookingreview.repository.HotelPahPayRepository
import com.mmt.hotel.bookingreview.repository.HotelPahPayRepositoryImpl
import com.mmt.hotel.bookingreview.ui.HotelPahPayActivity
import com.mmt.hotel.bookingreview.viewmodel.HotelPahActivityViewModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.multibindings.IntoMap


@Module
@InstallIn(ActivityComponent::class)
class HotelPAHModule {
    @Provides
    @IntoMap
    @ViewModelKey(HotelPahActivityViewModel::class)
    fun providePahPayModel(viewModel: HotelPahActivityViewModel): HotelViewModel {
        return viewModel
    }

    @Provides
    fun providePahPayRepository(repositoryImp: HotelPahPayRepositoryImpl): HotelPahPayRepository {
        return repositoryImp
    }

    @Provides
    fun getPahIntentData(activity: Activity): PahIntentData {
        return (activity as HotelPahPayActivity).intent.extras!!.get(
            HotelPahPayActivity.PAH_INTENT_DATA
        ) as PahIntentData
    }
}