package com.mmt.hotel.bookingreview.repository

import com.mmt.auth.login.model.old.corporate.FetchEmployeesResponse
import com.mmt.hotel.bookingreview.helper.HotelCorpBookingReviewHelper
import com.mmt.hotel.bookingreview.model.BookingReviewData
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.request.PostApprovalRequestV2
import com.mmt.hotel.bookingreview.model.response.AddNewEmployeeResponse
import com.mmt.hotel.bookingreview.model.response.PostApprovalResponseV2
import com.mmt.hotel.bookingreview.model.response.UpdatedCorpPolicyResponse
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagResponseV2
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNResponse
import com.mmt.hotel.detail.model.response.StaticDetailApiResponse
import kotlinx.coroutines.flow.Flow


/*
*   Class will contains all the network call related to CorpBookingReview
* */

interface HotelCorpBookingReviewRepository : HotelBookingReviewRepository {
    fun fetchSearchQueryResults(searchQuery: String): Flow<FetchEmployeesResponse>
    fun updateCorpPolicy(email: List<String>?, corpBookingReviewHelper: HotelCorpBookingReviewHelper): Flow<UpdatedCorpPolicyResponse>
    fun fetchTripTagsWithGSTV2(email: String, corpBookingReviewHelper: HotelCorpBookingReviewHelper): Flow<CorpTripTagResponseV2>
    fun postApprovalAPIV2(corpBookingReviewHelper: HotelCorpBookingReviewHelper, reasonForTravel: Map<String, String>?, checkDuplicateBooking : Boolean): Flow<PostApprovalResponseV2>
    fun postApprovalAPIV2(postApprovalRequestV2: PostApprovalRequestV2): Flow<PostApprovalResponseV2>
    fun addNewEmployee(travellerDetail: CorpTravellerDetail): Flow<AddNewEmployeeResponse>
}