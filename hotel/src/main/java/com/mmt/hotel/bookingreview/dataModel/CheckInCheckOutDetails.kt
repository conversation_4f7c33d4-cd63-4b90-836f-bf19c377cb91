package com.mmt.hotel.bookingreview.dataModel

data class CheckInCheckOutDetails(
    val checkInDate: String,
    val checkInTime: String?,
    val checkInDay: String,
    val checkOutDate: String,
    val checkOutTime: String?,
    val checkOutDay: String,
    val noOfNights: String,
    val checkInPolicyAlert: String? = null
) {

    fun showCheckInTime() = checkInTime?.isNotEmpty() == true && checkOutTime?.isNotEmpty() == true
}