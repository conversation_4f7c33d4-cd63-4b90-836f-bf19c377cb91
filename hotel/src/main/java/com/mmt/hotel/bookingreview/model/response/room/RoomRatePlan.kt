package com.mmt.hotel.bookingreview.model.response.room

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.BasicTagInfo
import com.mmt.hotel.bookingreview.model.response.BookingAlerts
import com.mmt.hotel.common.model.response.CancellationTimelineModel
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.selectRoom.model.response.room.ratePlan.RatePlanCard
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import java.util.Locale

@Parcelize
data class RoomRatePlan(val roomName: String?,
                        @SerializedName("code")
                        val ratePlanCode: String?,
                        val roomCode: String?,
                        val mealCode: String?,
                        @SerializedName("segmentid")
                        val segmentId: String?,
                        @SerializedName("highlightImage")
                        val highlightImage: String?,
                        @SerializedName("suppliercode")
                        val supplierCode: String?,
                        val description: String?,
                        @SerializedName("cancellationPolicy")
                        val cancellationPolicy: RoomInclusion?,
                        @SerializedName("mealplan")
                        val mealPlan: RoomMealPlan?,
                        @SerializedName("cancellationPolicyTimeline")
                        val payLaterTimeLineModel: CancellationTimelineModel?,
                        @SerializedName("inclusionsList")
                        val roomInclusions: List<RoomInclusion>?,
                        var roomTariff: List<RoomTariff>?,
                        @SerializedName("alerts")
                        val roomAlerts: List<BookingAlerts>?,
                        @SerializedName("occupancydetails")
                        val occupancyDetails: RoomOccupancyDetail,
                        @SerializedName("basePlan")
                        val basePlan: Boolean = false,
                        @SerializedName("cards")
                        val cards : @RawValue Map<String, RatePlanCard>? = null,
                        @SerializedName("tagInfo")
                        val tagInfo: BasicTagInfo? = null,
                        @SerializedName("roomCategoryText")
                        val roomCategoryText : String? = null
) : Parcelable {
    fun getSellCategoryText(): String {
        return HotelUtil.titleCaseForEnglish(roomCategoryText, R.string.htl_room)
    }
}
