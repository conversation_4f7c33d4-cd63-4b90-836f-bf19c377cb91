package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.mmt.hotel.corpapproval.model.response.CorpCategoryReasons
import kotlinx.parcelize.Parcelize

@Parcelize
data class CorpAutoBookRequestorConfig(
    val title: String?,
    val subTitle: String?,
    val cta: CorpAutoBookRequestorCta?,
    val deeplink: String?=null,
    val iconUrl: String?=null,
    val nearbyHotelDeeplink: String?=null,
    @Transient
    var workFlowId: String? = null,
    val travelReasons: List<CorpCategoryReasons>? = null,
) : Parcelable


@Parcelize
data class CorpAutoBookRequestorCta(
    val text: String?
) : Parcelable