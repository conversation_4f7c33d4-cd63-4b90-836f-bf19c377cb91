package com.mmt.hotel.bookingreview.ui.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.viewmodel.corp.ReasonSelectionItemState
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.corpApprovalV2.ui.bottomSheets.RadioButtonWithLabel
import com.mmt.hotel.corpapproval.viewModel.HotelCorpApproverActionDialogFragmentVM
import com.mmt.hotel.widget.compose.MmtComposeEditText

@Composable
fun ReasonSelectionItem(
    data: ReasonSelectionItemState,
    onSelection: (ReasonSelectionItemState) -> Unit,
    onCommentChange: (ReasonSelectionItemState, String) -> Unit
) {

    Column(
        modifier = Modifier
            .background(Color.White)
    ) {

        val onRadioButtonClick = {
            onSelection(data)
        }

        RadioButtonWithLabel(
            modifier = Modifier
                .padding(vertical = dimensionResource(id = R.dimen.margin_small_extra))
                .testTag("reason_radio_button"),
            text = data.reason.text,
            selected = data.isSelected,
            onClick = onRadioButtonClick,
            textColor = colorResource(id = R.color.htl_description_text_black_color),
        )

        if (data.isSelected && data.reason.inputType == HotelCorpApproverActionDialogFragmentVM.TEXTBOX) {
            MmtComposeEditText(
                value = data.comment,
                hint = stringResource(id = R.string.htl_corp_approval_enter_reason_text),
                textColor = colorResource(id = R.color.htl_description_text_black_color),
                textHintColor = colorResource(id = R.color.htl_description_text_black_color),
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium),
                mmtFontStyle = latoRegular,
                maxLines = 10,
                modifier = Modifier
                    .padding(
                        start = dimensionResource(id = R.dimen.margin_xHuge),
                        bottom = dimensionResource(id = R.dimen.margin_tiny)
                    )
                    .fillMaxWidth()
                    .height(dimensionResource(id = R.dimen.select_room_image_height))
                    .background(
                        colorResource(id = R.color.white),
                        RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny))
                    )
                    .border(
                        width = dimensionResource(id = R.dimen.htl_divider_height),
                        color = colorResource(id = R.color.grey_d8d8d8),
                        shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny))
                    )
                    .padding(dimensionResource(id = R.dimen.margin_large))
                    .testTag("reason_edit_text"),
                onValueChangeCallback = ({
                    onCommentChange(data, it)
                })
            )
        }

        if (data.showDivider) {
            HorizontalDivider(modifier = Modifier.padding(vertical = dimensionResource(id = R.dimen.margin_tiny)))
        }
    }

}