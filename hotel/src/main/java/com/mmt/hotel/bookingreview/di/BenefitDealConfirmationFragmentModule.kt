package com.mmt.hotel.bookingreview.di
import androidx.fragment.app.Fragment
import com.mmt.hotel.base.model.response.PopUpData
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.bookingreview.ui.BenefitDealConfirmationFragment
import com.mmt.hotel.bookingreview.viewmodel.BenefitDealConfirmationVM
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class BenefitDealConfirmationFragmentModule {
    @Provides
    @IntoMap
    @ViewModelKey(BenefitDealConfirmationVM::class)
    fun provideBenefitDealConfirmationFragmentViewModel(viewModel: BenefitDealConfirmationVM): HotelViewModel = viewModel

    @Provides
    fun bundleData(fragment: Fragment) :PopUpData {

        ((fragment as? BenefitDealConfirmationFragment)?.arguments?.getParcelable(BenefitDealConfirmationFragment.BUNDLE_KEY_DATA) as? PopUpData)?.let {
            return it
        }
        throw RuntimeException ("Only BenefitDealConfirmationFragment can use PopUpBaseObj and it should be non null")
    }

    @Provides
    fun couponData(fragment: Fragment) : HotelBookingCoupon {
        ((fragment as? BenefitDealConfirmationFragment)?.arguments?.getParcelable(
            BenefitDealConfirmationFragment.BUNDLE_KEY_COUPON_DATA
        ) as? HotelBookingCoupon)?.let {
            return it
        }
        throw RuntimeException ("Only BenefitDealConfirmationFragment can use HotelBookingCoupon and it should be non null")
    }
}