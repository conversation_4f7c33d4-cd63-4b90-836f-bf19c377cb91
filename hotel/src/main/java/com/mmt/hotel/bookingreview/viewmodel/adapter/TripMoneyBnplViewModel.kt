package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.runtime.mutableIntStateOf
import androidx.databinding.BaseObservable
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.constant.CoreConstants.SPACE
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.response.TripMoneyBnplResponse
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

class TripMoneyBnplViewModel(
    val data: TripMoneyBnplResponse?,
    private val eventStream: MutableLiveData<HotelEvent>
) : BaseObservable(), DiffUtilRecycleItem {
    /*
        showCard is used to show different states according to values i.e
        0-> show initial trip money bnpl card
        1-> show loading state card
        2-> show validation Api success card
        3-> show validation Api failure card
     */
    val showCard = ObservableInt(0) // initial state
    val showCardMutableState = mutableIntStateOf(0) // initial state

    val responseLogo = ObservableField(EMPTY_STRING)
    val responseTitleIcon = ObservableField(EMPTY_STRING)
    val responseTitle = ObservableField(EMPTY_STRING)
    val responseSubTitle = ObservableField(EMPTY_STRING)
    val responseDesc = ObservableField(EMPTY_STRING)
    val responseSuccessTitle = ObservableField(EMPTY_STRING)
    val responseSuccessLogo = ObservableField(EMPTY_STRING)
    val responseSuccessAmount = ObservableField(EMPTY_STRING)
    val showRetryButton = ObservableBoolean(false)
    val showMargin = ObservableBoolean(true)
    val ctaText = ObservableField(EMPTY_STRING)

    init {
        data?.let {
            updateResponse(it)
        } ?: kotlin.run {
            updateDataOnApiFailure()
        }
    }

    fun validate() {
        showCardMutableState.intValue = 1
        showCard.set(1) //loading state
        eventStream.postValue(HotelEvent(HotelBookingReviewFragmentEvent.TRIPMONEY_BNPL_VALIDATION_CLICKED,false))
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.TRIP_MONEY_BNPL_CARD
    }

    fun updateResponse(response: TripMoneyBnplResponse) {
        if(response.checkEligibility == true) {
            showCardMutableState.intValue = 0
            showCard.set(0) // initial state
        } else if (response.eligible == true) {
            showCardMutableState.intValue = 2
            showCard.set(2) // success state
        } else {
            showCardMutableState.intValue = 3
            showMargin.set(true)
            showCard.set(3) // failure state
        }
        updateDataFields(response)
    }

    private fun updateDataFields(response: TripMoneyBnplResponse) {
        responseLogo.set(response.tripMoneyValidationData?.logo)
        responseTitleIcon.set(response.tripMoneyValidationData?.titleIcon)
        responseTitle.set(response.tripMoneyValidationData?.title)
        responseSubTitle.set(response.tripMoneyValidationData?.subTitle)
        responseDesc.set(response.tripMoneyValidationData?.desc)
        responseSuccessTitle.set(response.tripMoneyValidationData?.tripMoneyBnplSuccessCard?.title)
        ctaText.set(response.tripMoneyValidationData?.ctaText)
        responseSuccessLogo.set(response.tripMoneyValidationData?.tripMoneyBnplSuccessCard?.logo)
        response.tripMoneyValidationData?.tripMoneyBnplSuccessCard?.amount?.let {
            val currency =
                HotelCurrencyUtil.getCurrencySymbolOrDefault(response.tripMoneyValidationData.tripMoneyBnplSuccessCard.currency)
            responseSuccessAmount.set(currency + StringUtil.getCommaSeparatedPrice(it))
        }
    }

    fun updateDataOnApiFailure() {
        showCardMutableState.intValue = 3
        showCard.set(3)
        showRetryButton.set(true)
        showMargin.set(false)
        responseTitle.set(ResourceProvider.instance.getString(R.string.htl_trip_money_error_title))
        responseSubTitle.set(ResourceProvider.instance.getString(R.string.htl_trip_money_error_subtitle))
    }

    fun getUserMobileNumber(): String {
        val loggedInUser = LoginUtils.loggedInUser
        loggedInUser?.let {
            val primaryNumber = it.primaryContact ?: EMPTY_STRING
            if (primaryNumber.isNotEmpty()) {
                return "+" + (if (it.primaryContactCountryCode.isNotEmpty()) it.primaryContactCountryCode.toInt() else LoginUtils.getDefaultMobileCodeForCurrentRegion()
                    .toInt()).toString() + SPACE + primaryNumber
            }
            return primaryNumber
        }
        return EMPTY_STRING
    }

    fun onRetryClicked() {
        showRetryButton.set(false)
        showCardMutableState.intValue = 1
        showCard.set(1) //loading state
        eventStream.postValue(HotelEvent(HotelBookingReviewFragmentEvent.TRIPMONEY_BNPL_VALIDATION_CLICKED,true))
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.TRIPMONEY_BNPL_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as TripMoneyBnplViewModel).data
        return data == matchedWith
    }

    override fun cardName(): String {
        return "Review Trip Money BNPL"
    }
}