package com.mmt.hotel.bookingreview.ui

import android.graphics.Rect
import android.os.Bundle
import android.view.View
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.mmt.core.util.DeviceUtil
import com.mmt.hotel.R
import com.mmt.hotel.databinding.FragmentHotelCoTravellerBinding
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.ui.fragment.HotelFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.adapter.HotelCoTravellerAdapter
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SHOW_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.ADD_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.DESELECT_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.SELECT_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.UPDATE_RECYCLER_VIEW
import com.mmt.hotel.bookingreview.model.CoTravellerFragmentData
import com.mmt.hotel.bookingreview.viewmodel.CoTravellerSavedViewModel
import com.mmt.hotel.bookingreview.viewmodel.CoTravellerFragmentViewModel
import com.mmt.hotel.bookingreview.viewmodel.CoTravellerSavedHeaderViewModel
import com.mmt.hotel.common.constants.HotelConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelCoTravellerFragment : HotelBottomSheetDialogFragment<CoTravellerFragmentViewModel, FragmentHotelCoTravellerBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory
    private var adapter: HotelCoTravellerAdapter = HotelCoTravellerAdapter(arrayListOf())
    var activitySharedViewModel: HotelEventSharedViewModel? = null

    companion object {
        val TAG = "HotelCoTravellerFragment"
        val CO_TRAVELLER_DATA: String = "CO_TRAVELLER_DATA"

        @JvmStatic
        fun getInstance(data: CoTravellerFragmentData): HotelCoTravellerFragment {
            val fragment = HotelCoTravellerFragment()
            val bundle = Bundle()
            bundle.putParcelable(CO_TRAVELLER_DATA, data)
            fragment.arguments = bundle;
            return fragment
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.HotelBottomSheetCornerRadiusDialogTheme)
    }

    override fun initViewModel(): CoTravellerFragmentViewModel {
        return getViewModel(factory)
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_hotel_co_traveller
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
        initRecyclerView()
    }

    private fun initRecyclerView() {
        adapter = HotelCoTravellerAdapter(arrayListOf())
        viewDataBinding.recyclerView.adapter = adapter
        if (isFragmentRecreating() && viewModel.listItems.isNotEmpty()) {
            adapter.updateList(viewModel.listItems)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (dialog as? BottomSheetDialog)?.behavior?.state = BottomSheetBehavior.STATE_EXPANDED
        viewDataBinding.btnDone.viewTreeObserver.addOnGlobalLayoutListener{
            val rect = Rect()
            viewDataBinding.btnDone.getWindowVisibleDisplayFrame(rect)
            val screenHeight = viewDataBinding.btnDone.rootView.height
            if(screenHeight - rect.bottom > HotelConstants.KEYBOARD_HT_THRESHOLD){
                viewModel.footerVisible.set(false)
            } else {
                viewModel.footerVisible.set(true)
            }
        }
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            SHOW_CO_TRAVELLER -> {
                sendEventToActivity(event)
                dismiss()
            }
            UPDATE_RECYCLER_VIEW -> {
                adapter?.updateList(event.data as List<AbstractRecyclerItem>)
            }
            ADD_CO_TRAVELLER -> {
                addCoTravellerInList(event.data as AbstractRecyclerItem)
                viewModel.onCoTravellerSelection(event.data as CoTravellerSavedViewModel)
                viewModel.trackAddCoTraveller()
            }
            SELECT_CO_TRAVELLER -> {
                viewModel.onCoTravellerSelection(event.data as CoTravellerSavedViewModel)
            }
            DESELECT_CO_TRAVELLER -> {
                viewModel.onCoTravellerDeSelection(event.data as CoTravellerSavedViewModel)
            }
            DISMISS_FRAGMENT->{
                dismiss()
            }
        }
    }

    private fun addCoTravellerInList(item: AbstractRecyclerItem) {
        if (adapter.itemList.size > 1) {
            val savedGuestVm = adapter.itemList[1] as? CoTravellerSavedHeaderViewModel?
            savedGuestVm?.let {
                it.itemList.forEach {item  ->
                    (item as? CoTravellerSavedViewModel?)?.showDivider = true
                }
                it.itemList.add(0, item)
                adapter.modifyItem(it)
            }
        } else {
            adapter.addItem(1, CoTravellerSavedHeaderViewModel(mutableListOf(item), viewModel.eventStream))
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        viewModel.listItems = adapter.itemList
        super.onSaveInstanceState(outState)
    }

    fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }
}
