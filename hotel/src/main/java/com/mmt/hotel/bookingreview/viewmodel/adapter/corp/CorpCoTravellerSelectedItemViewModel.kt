package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.compose.runtime.mutableStateOf
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent
import com.mmt.hotel.common.constants.CorpConstants.GUEST
import java.util.*

class CorpCoTravellerSelectedItemViewModel(val eventStream: MutableLiveData<HotelEvent>, val coTravellerDetail: CorpTravellerDetail,val enableCard:Boolean = true) {

    var showDivider: ObservableBoolean = ObservableBoolean(true)
    var showDividerState = mutableStateOf(true)

    fun getFullName(): String {
        if(coTravellerDetail.travellerType == GUEST) {
            return coTravellerDetail.getTravellerName() + " (${GUEST.toUpperCase(Locale.ROOT)}) "
        }
        return coTravellerDetail.getTravellerName()
    }

    fun isMakePrimaryVisible() = coTravellerDetail.travellerType != GUEST

    fun onMakePrimaryClick() {
        onRemoveClick()
        eventStream.value = HotelEvent(HotelCorpBookingReviewFragmentEvent.MAKE_CO_TRAVELLER_PRIMARY, coTravellerDetail)
    }

    fun onRemoveClick() {
        eventStream.value = HotelEvent(HotelCorpBookingReviewFragmentEvent.REMOVE_CO_TRAVELLER_CLICKED, this)
    }
}