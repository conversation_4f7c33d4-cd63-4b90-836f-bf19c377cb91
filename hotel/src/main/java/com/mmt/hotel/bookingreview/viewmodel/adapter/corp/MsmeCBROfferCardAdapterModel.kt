package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.databinding.BaseObservable
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.viewmodel.adapter.BookingReviewPriceViewModel
import com.mmt.hotel.common.model.response.MsmeOfferCardModel
import com.mmt.hotel.common.viewmodel.adapterModel.IMsmeOfferCard
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

/**
 * Adapter model to hold information for Msme Corp Booking Review offer card for <Corp Booking Review screen>
 *
 * create by <PERSON>arun <PERSON> on 16/11/21
 */
class MsmeCBROfferCardAdapterModel (var msmeCardModel : MsmeOfferCardModel)
    : DiffUtilR<PERSON>ycle<PERSON><PERSON>, IMsmeOfferCard, BaseObservable() {

    fun updateData(data: MsmeOfferCardModel) {
        this.msmeCardModel = data
        notifyChange()
    }

    override fun getMsmeOfferTitle() = msmeCardModel.titleText
    override fun getMsmeOfferDescription() = msmeCardModel.description

    override fun bottomMargin() = ResourceProvider.instance.getDimensionPixelSize(R.dimen.htl_review_card_margin)

    override fun horizontalMargin(): Int {
        return R.dimen.margin_large
    }

    override fun getCornerRadiiInDp() = 16

    override fun getItemType() = HotelBookingReviewAdapterItem.CORP_MSME_OFFER_CARD

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_MSME_OFFER_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as MsmeCBROfferCardAdapterModel).msmeCardModel
        return msmeCardModel == matchedWith
    }

    override fun cardName(): String {
        return "Corp Review MSME Card"
    }

}