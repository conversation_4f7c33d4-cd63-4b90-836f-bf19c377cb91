package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelPolicyBundleData(val txnKey: String?,
                                 val checkInTime: String?,
                                 val checkOutTime: String?,
                                 val countryCode: String?,
                                 var policyRuleID: String? = null) : Parcelable