package com.mmt.hotel.bookingreview.dataModel

import androidx.annotation.StringRes
import com.mmt.hotel.common.data.LinearLayoutItemData


data class BookingCouponUiData(val coupons: List<LinearLayoutItemData>,
                               var noCouponText: String?,
                               val isDayUseFunnel: Boolean,
                               val couponSubtext:String?,
                               val allCouponDisabled : Boolean = false,
                               val buttonText : String,
                                val totalAvailableCoupon: Int?)