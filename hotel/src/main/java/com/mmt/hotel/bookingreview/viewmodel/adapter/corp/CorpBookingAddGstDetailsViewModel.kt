package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.databinding.BaseObservable
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem.Companion.GST_DETAILS_CARD
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys.Companion.CORP_ADD_GST_INFORMATION
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.listingV2.event.HotelListingClickEvents.GST_FORM_SHOWN
import com.mmt.uikit.util.isNotNullAndEmpty

class CorpBookingAddGstDetailsViewModel(var claimText: String?, private var gstnState:String?,companyName:String?=null, companyAddress:String?=null, var eventStream: MutableLiveData<HotelEvent>): BaseObservable(), DiffUtilRecycleItem {

    var gstCardShownTracking = false

    val selectStateString = ResourceProvider.instance.getString(R.string.htl_select_state)

    var showCompanyNameAndAddressWidget = ObservableBoolean(false)

    var stateError = ObservableBoolean(false)
    var addressError = ObservableBoolean(false)
    var nameError = ObservableBoolean(false)
    var gstnError = ObservableBoolean(false)

    var gstn = ObservableField (EMPTY_STRING)
    val companyAddress = ObservableField (companyAddress)
    val companyName = ObservableField (companyName)


    var stateErrorMessage = ObservableField(EMPTY_STRING)
    var addressErrorMessage = ObservableField(EMPTY_STRING)
    var nameErrorMessage = ObservableField(EMPTY_STRING)
    var gstnErrorMessage = ObservableField(EMPTY_STRING)

    var stateBg =  ObservableInt(R.drawable.htl_gst_text_bg)
    var gstnBg = ObservableInt(R.drawable.htl_gst_text_bg)
    var addressBg = ObservableInt(R.drawable.htl_gst_text_bg)
    var nameBg = ObservableInt(R.drawable.htl_gst_text_bg)


    var adminCheckBox = ObservableBoolean(false)
    val stateList : Array<String> = createStateList()
    var currentState = initCurrentState()


    private fun initCurrentState(): String {
        return gstnState?.let { gstnState ->
            stateList.firstOrNull {
                it == gstnState
            }
        } ?: selectStateString
    }
    fun updateClaimText(claimString: String?){
        claimText = claimString
    }

    fun onStatusChanged(b: Boolean){
        adminCheckBox.set(b)
    }

    fun onGstStateReceived(gstState: String?) {
        if(gstnState == gstState || gstState.isNullOrBlank()) {
            return
        }
        gstnState = gstState
        if (currentState == selectStateString) {
            stateList.firstOrNull {
                it == gstState
            }?.let {
                currentState = it
            }
        }
    }

    private fun createStateList(): Array<String>{
        var stateList: Array<String> = arrayOf(selectStateString)
        stateList += ResourceProvider.instance.getStringArray(R.array.states)
        return stateList
    }

    fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int){
        if(s.toString().isEmpty()){
            showCompanyNameAndAddressWidget.set(false)
        }else{
            showCompanyNameAndAddressWidget.set(true)
        }
    }

    override fun cardOrder(): String {
        return CORP_ADD_GST_INFORMATION
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as CorpBookingAddGstDetailsViewModel).claimText
        return claimText == matchedWith
    }

    override fun cardName(): String {
        return "CORP ADD GST DETAILS"
    }

    override fun getItemType(): Int {
        return GST_DETAILS_CARD
    }

    fun isInputValid(): Boolean{
        var isValid = true
        val resourceProvider = ResourceProvider.instance
        clearErrorNotification()

        if(currentState.equals(selectStateString)){
            stateErrorMessage.set(resourceProvider.getString(R.string.htl_gst_state_error_msg))
            stateError.set(true)
            isValid = false
        }
        if(gstn.get().isNotNullAndEmpty()){     // if user have entered GSTN, mark address and name as mandatory
            var gstN = gstn.get()
            if(companyName.get()?.equals(EMPTY_STRING) == true) {
                nameErrorMessage.set(resourceProvider.getString(R.string.htl_company_name_error_msg))
                nameBg.set(R.drawable.htl_gst_text_error_bg)
                nameError.set(true)
                isValid = false
            }
            if(companyAddress.get()?.equals(EMPTY_STRING) == true){
                addressErrorMessage.set(resourceProvider.getString(R.string.htl_company_address_error_msg))
                addressBg.set(R.drawable.htl_gst_text_error_bg)
                addressError.set(true)
                isValid = false
            }
            if(gstN?.length != 15){
                gstnErrorMessage.set(resourceProvider.getString(R.string.htl_gstn_error_msg))
                gstnBg.set(R.drawable.htl_gst_text_error_bg)
                gstnError.set(true)
                isValid = false
            }
        }
        return isValid
    }

    private fun clearErrorNotification(){
        stateErrorMessage.set(EMPTY_STRING)
        addressErrorMessage.set(EMPTY_STRING)
        nameErrorMessage.set(EMPTY_STRING)
        gstnErrorMessage.set(EMPTY_STRING)

        stateError.set(false)
        addressError.set(false)
        gstnError.set(false)
        nameError.set(false)

        stateBg.set(R.drawable.htl_gst_text_bg)
        gstnBg.set(R.drawable.htl_gst_text_bg)
        addressBg.set(R.drawable.htl_gst_text_bg)
        nameBg.set(R.drawable.htl_gst_text_bg)
    }

    fun getGSTDetails(): GSTNDetails {
        return GSTNDetails(gstn = if(gstn.get().isNotNullAndEmpty()) gstn.get() else null,
            address1 = if(companyAddress.get().isNotNullAndEmpty()) companyAddress.get() else null,
            city  = currentState,
            organizationName = if(companyName.get().isNotNullAndEmpty()) companyName.get() else null,
            saveGstDetails = if(gstn.get().isNotNullAndEmpty()) adminCheckBox.get() else false)
    }

    fun trackEditGSTCardShown(){
        if(!gstCardShownTracking){
            gstCardShownTracking = true
            eventStream.postValue(HotelEvent(GST_FORM_SHOWN))
        }
    }

}