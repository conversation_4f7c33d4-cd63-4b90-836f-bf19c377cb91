package com.mmt.hotel.bookingreview.helper

import androidx.compose.runtime.mutableStateOf
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.executeIfCast
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.EventType
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.dataModel.CouponFragmentData
import com.mmt.hotel.bookingreview.dataModel.CouponItemUIData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.event.HotelCouponFragmentEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.bookingreview.model.response.validatecoupon.ValidateApiResponseV2
import com.mmt.hotel.bookingreview.repository.HotelBookingReviewRepository
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewTrackingHelper
import com.mmt.hotel.common.constants.CouponTypes
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.compose.review.dataModel.BookingCouponItemUIData
import com.mmt.hotel.compose.review.dataModel.LoginButtonItemUIData
import com.mmt.hotel.compose.review.dataModel.UpdateAppliedCouponEventData
import com.mmt.hotel.compose.review.itemTypes.HotelBookingCouponItemType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

class HotelCouponSheetHelper  @Inject constructor(val bookingReviewRepository: HotelBookingReviewRepository,
                                                  val trackingHelper: HotelBookingReviewTrackingHelper,
                                                  val data: CouponFragmentData?,
                                                  val eventStream : MutableLiveData<HotelEvent>? = null,
                                                  val eventLambda: ((HotelEvent) -> Unit)? = null) {

    val couponCodeWrapper = mutableStateOf(CoreConstants.EMPTY_STRING)

    var showCoupons = mutableStateOf(false)

    var isEditTextError = mutableStateOf(false)

    val errorMessage = mutableStateOf("")

    val showProgessDialog = MutableLiveData(false)

    val editTextBg = ObservableField(NORMAL_BG)

    var dataList = mutableListOf<AbstractRecyclerItem>()

    val localEventStream : MutableLiveData<HotelEvent>  = MutableLiveData()

    var couponHeaderList = mutableListOf<String>()

    var bankOffer = mutableListOf<AbstractRecyclerItem>()
    var paymentOffer = mutableListOf<AbstractRecyclerItem>()

    var selectedCouponHeader = mutableStateOf("")


    init {
        couponHeaderList.clear()
        val allOffers  = ResourceProvider.instance.getString(R.string.htl_all_offers)
        selectedCouponHeader.value = allOffers
        couponHeaderList.add(allOffers)
        val canShowCoupons = canShowCoupons()
        if (canShowCoupons) {
            showCoupons.value = canShowCoupons
            dataList =   getViewDataList(data?.coupons ?: emptyList())
        }
        if (paymentOffer.size>0 && bankOffer.size>0) {
            couponHeaderList.add(ResourceProvider.instance.getString(R.string.htl_bank_offers))
            couponHeaderList.add(ResourceProvider.instance.getString(R.string.htl_payment_offers))
        }
        localEventStream.observeForever { event->
            when(event.eventID){
                HotelCouponFragmentEvent.COUPON_CLICKED -> {
                    event.data.executeIfCast<Pair<HotelBookingCoupon, Boolean>> {
                        requestValidateCouponAPI(this.first.couponCode, this.second)
                    }
                }
                HotelBookingReviewFragmentEvent.COUPON_TNC_CLICKED -> {
                    val eventTncClicked = HotelEvent(HotelBookingReviewFragmentEvent.COUPON_TNC_CLICKED, event.data, eventType = EventType.CLICK)
                    eventLambda?.invoke(eventTncClicked)?:run{
                        eventStream?.postValue(eventTncClicked)
                    }
                }
                HotelBookingReviewFragmentEvent.COUPON_TNC_CLICKED_WITH_NAME -> {
                    val eventTncClicked = HotelEvent(HotelBookingReviewFragmentEvent.COUPON_TNC_CLICKED_WITH_NAME, event.data, eventType = EventType.CLICK)
                    eventLambda?.invoke(eventTncClicked)?:run{
                        eventStream?.postValue(eventTncClicked)
                    }
                }
            }
        }
    }

    fun onCouponTabClicked(position: Int) {
      val trackingValue =  if (position == 0) {
          "review_all_offers_clicked"

      } else if (position == 1) {
           "review_payment_offers_clicked"
        } else {
          "review_bank_offers_clicked"
        }
        val eventTncClicked = HotelEvent(HotelBookingReviewFragmentEvent.COUPON_LIST_TAB_CLICK, trackingValue, eventType = EventType.CLICK)
        eventLambda?.invoke(eventTncClicked)?:run{
            eventStream?.postValue(eventTncClicked)
        }
    }

     fun getEditTextBackground() : Int{
         return if(isEditTextError.value){
             R.color.htl_color_FFFAFA
         } else{
             R.color.htl_color_F6F6F6
         }

     }

    fun getEditTextStroke() : Int{
        return if(isEditTextError.value){
            R.color.htl_color_EC2127
        } else{
            R.color.htl_grey_d8d8d8
        }

    }

    companion object {
        var NORMAL_BG = R.drawable.htl_traveller_input_text_bg
        var ERROR_BG = R.drawable.htl_traveller_input_text_error_bg
    }

    fun getBgStartColor(): Int {
        return ResourceProvider.instance.getColor(R.color.htl_booking_payment_bg_start)
    }

    fun getBgEndColor(): Int {
        return ResourceProvider.instance.getColor(R.color.htl_booking_payment_bg_end)
    }

    private fun canShowCoupons():Boolean{
        val coupons = data?.coupons?: emptyList()
        if (coupons.size > 1) {
            return true
        }
        return coupons.size == 1 && !coupons[0].isSelected
    }


    fun getTitle(): String {
        return if (data?.shownBenefitDealInCoupons == true) {
            ResourceProvider.instance.getString(R.string.htl_coupon_and_offers)
        } else ResourceProvider.instance.getString(R.string.htl_coupon_codes)
    }

    fun getChooseACouponTitle(): String{
        return if (data?.shownBenefitDealInCoupons == true) {
            ResourceProvider.instance.getString(R.string.htl_choose_coupon_or_offer)
        } else ResourceProvider.instance.getString(R.string.htl_choose_coupon_code)
    }


    fun requestValidateCouponAPI(couponCode: String, isCouponApplied: Boolean) {
        val txnKey = data?.txnKey
        if (txnKey.isNullOrEmpty()) {
            updateProgressDialogVisibility(false)
            return
        }
        val expData = data?.expData.orEmpty()
        updateProgressDialogVisibility(true)
         val coroutineScope = CoroutineScope(Dispatchers.IO)

        coroutineScope.launch {
            bookingReviewRepository.makeValidateCouponRequest(
                bookingReviewData = data?.bookingReviewData,
                txnKey = txnKey,
                couponCode = couponCode,
                isCouponApplied = isCouponApplied,
                countryCode = data?.countryCode ?: HotelConstants.COUNTRY_CODE_UNKNOWN,
                expData = expData,
                quickCheckoutApplicable = data?.quickCheckoutApplicable ==true)
                .catch { it.printStackTrace() }
                .collect {
                    handleValidateCouponApiResponse(it, couponCode, isCouponApplied)
                }
        }
    }

    private fun handleValidateCouponApiResponse(responseData: Pair<ValidateApiResponseV2, String>, requestCouponCode: String, wasCouponApplied: Boolean) {
        val response = responseData.first
        val couponCode = response.apiResponse?.priceBreakUp?.coupons?.get(0)?.couponCode ?: requestCouponCode

        updateProgressDialogVisibility(false)
        response.apiResponse?.let {
            val appliedCouponEvent = HotelEvent(
                    HotelCouponFragmentEvent.UPDATE_APPLIED_COUPON,
                    UpdateAppliedCouponEventData(
                            responseData = responseData,
                            couponCode = couponCode, // coupon code of currently_Selected_Coupon ?: previously_Selected_Coupon
                            isCouponApplied = wasCouponApplied,
                            requestId = responseData.second
                    ))
            eventLambda?.invoke(appliedCouponEvent)?:run {
                eventStream?.postValue(appliedCouponEvent)
            }
            trackingHelper.trackClicks("Coupon_applied_successfully|"+ BookingTrackingConstants.REVIEW_COUPON_SUCCESS + couponCode)
        }
        response.error?.message?.let { msg ->
            val couponErrorEvent = HotelEvent(HotelCouponFragmentEvent.UPDATE_ERROR_COUPON, msg)
            eventLambda?.invoke(couponErrorEvent)?:run {
                eventStream?.postValue(couponErrorEvent)
            }
            errorMessage.value = msg
            isEditTextError.value = true
            editTextBg.set(ERROR_BG)
            trackingHelper.trackClicks(BookingTrackingConstants.REVIEW_COUPON_FAILURE + couponCode)
        }
    }

    private fun updateProgressDialogVisibility(showProgressDialog: Boolean) {
        showProgessDialog.postValue(showProgressDialog)
    }

    fun openLoginActivity() {
        val event = HotelEvent(HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY, eventType = EventType.ACTIVITY_RESULT)
        eventLambda?.invoke(event)?:run {
            eventStream?.postValue(event)
        }
    }

    fun onApplyButtonClick() {
        val eventDismissKeyboard = HotelEvent(HotelCouponFragmentEvent.DISMISS_KEYBOARD)
        eventLambda?.invoke(eventDismissKeyboard)?:run {
            eventStream?.postValue(eventDismissKeyboard)
        }
        val couponCode = couponCodeWrapper.value
        if (couponCode.isEmpty()) {
            isEditTextError.value =  true
            editTextBg.set(ERROR_BG)
            errorMessage.value = ResourceProvider.instance.getString(R.string.htl_coupon_code_empty_error)
            val errorEvent = HotelEvent(HotelCouponFragmentEvent.UPDATE_ERROR_COUPON, errorMessage.value)
            eventLambda?.invoke(errorEvent)?:run {
                eventStream?.postValue(errorEvent)
            }
            return
        }
        errorMessage.value = CoreConstants.EMPTY_STRING
        isEditTextError.value = false
        editTextBg.set(NORMAL_BG)
        requestValidateCouponAPI(couponCode, isCouponApplied = true)
    }

    private fun getViewDataList(coupons: List<CouponItemUIData>): MutableList<AbstractRecyclerItem> {
        val viewDataList: MutableList<AbstractRecyclerItem> = ArrayList()
        val viewModelList = mutableListOf<BookingCouponItemUIData>()
        var loginButtonAdded = false
        for ((index,coupon) in coupons.withIndex()) {
            if (!loginButtonAdded && coupon.hotelCoupon.isDisabled && !LoginUtils.isLoggedIn) {
                if (viewModelList.size > 0) {
                    val viewModel = viewModelList[viewModelList.size - 1]
                    viewModel.data.showDivider = false
                }

                val loginVM  = if(data?.isDayUseFunnel == true) LoginButtonItemUIData(
                    viewDataList.size > 0, eventStream, HotelBookingCouponItemType.LOGIN_DAY_USE, eventLambda = eventLambda
                ) else LoginButtonItemUIData(
                    viewDataList.size > 0, eventStream, HotelBookingCouponItemType.LOGIN_BOTTOM_SHEET, eventLambda = eventLambda
                )
                viewDataList.add(loginVM)
                loginButtonAdded = true
            }
            val updatedCoupon = coupon.copy()
            updatedCoupon.showDivider = index != coupons.size - 1
            val viewType =
                if (data?.isDayUseFunnel == true) HotelBookingCouponItemType.COUPON_ITEM_DAY_USE
                else if (coupon.hotelCoupon.couponType == CouponTypes.BENEFIT_DEAL.value) HotelBookingCouponItemType.BENEFIT_DEAL_AS_COUPON
                else HotelBookingCouponItemType.COUPON_ITEM_BOTTOM_SHEET

            val itemVM = BookingCouponItemUIData(updatedCoupon, localEventStream,viewType)
            if (coupon.isBankOffer) {
                bankOffer.add(itemVM)
            }else{
                paymentOffer.add(itemVM)
            }
            viewModelList.add(itemVM)
            viewDataList.add(itemVM)

        }
        return viewDataList
    }

    fun copy(eventLambda: ((HotelEvent) -> Unit)): HotelCouponSheetHelper {
        return HotelCouponSheetHelper(bookingReviewRepository = bookingReviewRepository,
                trackingHelper = trackingHelper,
                data = data,
                eventStream = eventStream,
                eventLambda = eventLambda)
    }
}

