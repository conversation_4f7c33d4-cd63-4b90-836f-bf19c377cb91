package com.mmt.hotel.bookingreview.viewmodel.corp

import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.viewModelScope
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.util.CommonMigrationHelper
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.adapter.corp.HotelCorpRequestApprovalAdapter
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.EVENT_HANDLE_ELEVATION
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.OPEN_CORP_MESSAGE_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.REQUEST_CORP_APPROVAL_API
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.SCROLL_TO_BOTTOM
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.helper.BookingReviewRequestHelper
import com.mmt.hotel.bookingreview.helper.HotelCorpBookingReviewHelper
import com.mmt.hotel.bookingreview.model.corp.CorpApprovalRequestFragmentData
import com.mmt.hotel.bookingreview.repository.HotelCorpBookingReviewRepository
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.corpapproval.event.HotelCorpApprovalEvents
import com.mmt.hotel.corpapproval.model.CorpApprovalMessageData
import com.mmt.hotel.corpapproval.viewModel.HotelCorpApproverActionDialogFragmentVM.Companion.OTHERS
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

class HotelCorpRequestApprovalReviewViewModel @Inject constructor(
    val data: CorpApprovalRequestFragmentData?
) : HotelToolBarViewModel() {

    companion object {
        const val REQUEST_APPROVAL_MANAGER_INFO_ITEM = 1
        const val REQUEST_APPROVAL_REASONS_ITEM = 2
    }

    val recycleItems = ObservableArrayList<AbstractRecyclerItem>()
    val adapter = HotelCorpRequestApprovalAdapter(arrayListOf())
    private var hotelCorpRequestApprovalReasonsVM = mutableListOf<HotelCorpRequestApprovalReasonsVM>()

    private var blockOopBooking: Boolean = isOopBookingBlock()
    private var isApprovalRequired: Boolean = isCorpApprovalRequired()
    private var blockSkipApproval: Boolean = isSkipApprovalBlock()
    var showElevation = ObservableBoolean(false)

    init {
        createRecyclerItems()
    }

    private fun createRecyclerItems() {
        data?.corpApprovingManagers?.let {
            recycleItems.add(HotelCorpRequestApprovalManagerInfoVM(it, eventStream))
        }
        data?.corpAutoBookRequestorConfig?.travelReasons?.let {
            it.forEach { categoryReason ->
                val viewModel = HotelCorpRequestApprovalReasonsVM(categoryReason, eventStream)
                hotelCorpRequestApprovalReasonsVM.add(viewModel)
                recycleItems.add(viewModel)
            }
        }
        updateEventStreamWithDelay(HotelEvent(EVENT_HANDLE_ELEVATION), 500)
    }

    fun getMyBizBtnStartColor(): Int {
        return ResourceProvider.instance.getColor(R.color.corp_bg_light)
    }

    fun getMyBizBtnEndColor(): Int {
        return ResourceProvider.instance.getColor(R.color.corp_bg_dark)
    }

    private fun isOopBookingBlock(): Boolean {
        data?.corpApprovalInfo?.blockOopBooking?.let { isBlockOopBooking ->
            return isBlockOopBooking > 0
        }
        return false
    }

    private fun isCorpApprovalRequired(): Boolean {
        data?.corpApprovalInfo?.approvalRequired?.let {
            return it
        }
        return false
    }

    private fun isSkipApprovalBlock(): Boolean {
        data?.corpApprovalInfo?.blockSkipApproval?.let { isSkipApprovalBlock ->
            return isSkipApprovalBlock > 0
        }
        return false
    }

    fun onSendClicked() {
        val reasonsForTravel = mutableMapOf<String, String>()
        var hasError = false

        hotelCorpRequestApprovalReasonsVM.forEach { reasonVM ->

            val comment = reasonVM.comments.get()
            val travelOption = reasonVM.travelOptionSelected.get()

            if (comment.isNullOrEmpty() && travelOption.isNullOrEmpty()) {
                reasonVM.showErrorObservable.set(true)
                hasError = true
            } else {
                if (!comment.isNullOrEmpty()) {
                    reasonsForTravel[reasonVM.getReasonKey()] = comment
                } else {
                    if (travelOption == OTHERS) {
                        reasonVM.showErrorObservable.set(true)
                        hasError = true
                    } else {
                        reasonsForTravel[reasonVM.getReasonKey()] = travelOption ?: EMPTY_STRING
                    }
                }
            }
        }

        if (hasError) {
            updateEventStream(HotelEvent(SCROLL_TO_BOTTOM))
        } else {
            updateEventStream(HotelEvent(REQUEST_CORP_APPROVAL_API, reasonsForTravel))
        }
    }

    fun onCancelClicked() {
        updateEventStream(HotelEvent(DISMISS_FRAGMENT))
    }

    fun isSkipApprovalBtnVisible(): Boolean { //existing condition used in CorpBookingApprovalRequestBtnsViewModel to show skip approval button
        if (blockOopBooking) {
            return false
        }
        return isApprovalRequired && !blockSkipApproval
    }

    fun onSkipApprovalBtnClick() {
        eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.SKIP_APPROVAL_BUTTON_CLICKED_FROM_REQUEST_APPROVAL_SCREEN, data?.corpSkipApprovalReasonsData))
    }

    override fun getTitle(): String {
        return data?.corpAutoBookRequestorConfig?.title ?: ResourceProvider.instance.getString(R.string.htl_booking_review_send_request_for_approval_cta_text)
    }

    override fun onHandleBackPress() {
        updateEventStream(HotelEvent(DISMISS_FRAGMENT))
    }

    fun getBottomsheetSubTitle(): String {
        return data?.corpAutoBookRequestorConfig?.subTitle ?: ResourceProvider.instance.getString(R.string.htl_booking_approval_content)
    }
}
