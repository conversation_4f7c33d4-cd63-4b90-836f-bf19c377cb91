package com.mmt.hotel.bookingreview.di

import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpTripTagViewModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
open class HotelCorpTripTagFragmentModule {

    @Provides
    @IntoMap
    @ViewModelKey(HotelCorpTripTagViewModel::class)
    fun provideTripTagReviewModel(viewModel: HotelCorpTripTagViewModel): HotelViewModel {
        return viewModel
    }
}