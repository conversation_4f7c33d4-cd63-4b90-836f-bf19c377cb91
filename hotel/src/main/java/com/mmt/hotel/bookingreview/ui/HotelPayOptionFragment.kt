package com.mmt.hotel.bookingreview.ui

import android.content.DialogInterface
import android.os.Bundle
import android.view.View
import androidx.databinding.ViewDataBinding
import com.mmt.core.util.KeyBoardUtils
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelPayFragmentEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelPayFragmentEvent.HIDE_KEYBOARD
import com.mmt.hotel.bookingreview.event.HotelPayFragmentEvent.UPDATE_FOOTER
import com.mmt.hotel.bookingreview.model.PayOptionData
import com.mmt.hotel.bookingreview.viewmodel.PayOptionFragmentViewModel
import javax.inject.Inject

abstract class HotelPayOptionFragment <V : ViewDataBinding>: HotelBottomSheetDialogFragment<PayOptionFragmentViewModel, V>(){

    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel : HotelEventSharedViewModel? = null

    companion object {
        const val TAG = "HotelPayOptionFragment"
        const val PAYMENT_OPTION_ARGS : String = "payment_option_args";
        const val PAY_FRAGMENT_DISMISSED : String = "PAY_FRAGMENT_DISMISSED";
    }
    override fun initAndValidate() {
        if(arguments?.getParcelable<PayOptionData>(PAYMENT_OPTION_ARGS) == null) {
            dismissAllowingStateLoss()
        }
    }

    override fun initViewModel() = getViewModel<PayOptionFragmentViewModel>(factory)

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel(factory)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.onLoadTrack()
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            HIDE_KEYBOARD -> KeyBoardUtils.hideKeyboard(activity)
            UPDATE_FOOTER -> viewModel.updateFooterState(event.data as Boolean)
            INITIATE_CHECKOUT -> {
                proceedPayment(event)
            }
            DISMISS_FRAGMENT -> {
                dismissAllowingStateLoss()
            }
            OPEN_LOGIN_ACTIVITY -> {
                dismissAllowingStateLoss()
                sendEventToActivity(event)
            }
            else -> {

            }
        }
    }

    private fun proceedPayment(event: HotelEvent) {
        dismissAllowingStateLoss()
        activitySharedViewModel?.updateEventStream(event)
        KeyBoardUtils.hideKeyboard(activity)
    }

    fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        sendEventToActivity(HotelEvent(PAY_FRAGMENT_DISMISSED))
    }
}