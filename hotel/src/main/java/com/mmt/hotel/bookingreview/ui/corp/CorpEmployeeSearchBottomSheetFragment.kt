package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import androidx.fragment.app.setFragmentResult
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.DeviceUtil
import com.mmt.core.util.KeyBoardUtils
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.adapter.corp.HotelCorpAddCoTravellerSearchAdapter
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingPolicyFragmentEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerSuccessFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpEmployeeSearchViewModel
import com.mmt.hotel.compose.review.ui.fragment.HotelCorpBookingReviewFragmentV2
import com.mmt.hotel.databinding.CorpEmployeeSearchBottomsheetFragmentBinding
import com.mmt.uikit.util.isNotNullAndEmpty
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject


@AndroidEntryPoint
class CorpEmployeeSearchBottomSheetFragment: HotelBottomSheetDialogFragment<CorpEmployeeSearchViewModel, CorpEmployeeSearchBottomsheetFragmentBinding>(){

    @Inject
    lateinit var factory: HotelViewModelFactory
    private lateinit var activitySharedViewModel: HotelEventSharedViewModel
    private lateinit var searchResultsRecyclerAdapter: HotelCorpAddCoTravellerSearchAdapter
    private var requestCode: String = CoreConstants.EMPTY_STRING

    companion object {
        const val TAG = "CorpEmployeeSearchBottomSheetFragment"
        const val CorpEmployeeBundleData = "CorpEmployeeBundleData"

        @JvmStatic
        fun newInstance(bundle: Bundle): CorpEmployeeSearchBottomSheetFragment {
            val fragment = CorpEmployeeSearchBottomSheetFragment()
            val args = Bundle().apply {
                putParcelable(CorpEmployeeBundleData, bundle)
            }
            fragment.arguments = args
            return fragment
        }
    }

    fun setRequestCode(requestCode: String) {
        this.requestCode = requestCode
    }

    fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel.updateEventStream(event)
    }

    override fun initViewModel(): CorpEmployeeSearchViewModel = getViewModel(factory)

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.corp_employee_search_bottomsheet_fragment
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            val height =(DeviceUtil.getDeviceHeight(activity)*.9).toInt()
            state = BottomSheetBehavior.STATE_EXPANDED
            peekHeight =height
            maxHeight =height
            val params = viewDataBinding.root.layoutParams
            params.height = height
            view.layoutParams = params
        }
    }


    override fun initFragmentView() {
        initRecyclerView()
        arguments?.getParcelable<Bundle>(CorpEmployeeBundleData)?.let {
            val primaryTravellerEmailId: String? = it.getString(HotelCorpBookingReviewActivity.PRIMARY_TRAVELLER_EMAIL_ID)
            val isPrimary: Boolean = it.getBoolean(HotelCorpBookingReviewActivity.PRIMARY_TRAVELLER, false)
            if (primaryTravellerEmailId != null) {
                viewModel.setPrimaryTravellerEmailIdToSearchText(primaryTravellerEmailId)
            }
            var employeeAlreadyAddedList = it.getParcelableArrayList<CorpTravellerDetail> (
                HotelCorpBookingReviewActivity.TRAVELLING_EMPLOYEE_LIST
            ) as ArrayList<CorpTravellerDetail>?
            if(employeeAlreadyAddedList.isNotNullAndEmpty()){
                viewModel.employeesAddedList = employeeAlreadyAddedList
            }
            viewModel.viaPrimary = isPrimary
        }
        activitySharedViewModel = getActivityViewModel()
    }


    private fun initRecyclerView() {
        viewDataBinding.searchResultList.apply {
            searchResultsRecyclerAdapter = HotelCorpAddCoTravellerSearchAdapter(arrayListOf())
            adapter = searchResultsRecyclerAdapter
        }
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            CorpEmployeeSearchActivityEvent.UPDATE_RECYCLER_VIEW -> {
                searchResultsRecyclerAdapter.updateList(event.data as List<AbstractRecyclerItem>)
                searchResultsRecyclerAdapter.notifyDataSetChanged()
            }
            HotelBookingPolicyFragmentEvent.DISMISS_FRAGMENT -> {
                dismiss()
            }
            HotelCorpBookingReviewFragmentEvent.ON_EMPLOYEE_CLICKED -> {
//                val resultIntent = Intent()
//                resultIntent.putExtra(HotelCorpBookingReviewFragment.SEARCH_RESULT, event.data as Parcelable)
                dismiss()
                val bundle = Bundle()
                bundle.putParcelable(HotelCorpBookingReviewFragmentV2.SEARCH_RESULT, event.data as Parcelable)
                setFragmentResult(requestCode,bundle)
            }
            CorpEmployeeSearchActivityEvent.ON_BACK_PRESSED -> {
                //KeyBoardUtils.hideKeyboard(this)

            }
            CorpEmployeeSearchActivityEvent.EDIT_TRAVELLER -> {
                dismiss()
                sendEventToActivity(event)
            }
            CorpEmployeeSearchActivityEvent.TRAVELLER_DETAILS_ADDED -> {
                dismiss()
                val data = event.data as CorpAddEditTravellerSuccessFragmentData
                val bundle = Bundle()
                bundle.putParcelable(HotelCorpBookingReviewFragmentV2.SEARCH_RESULT, data.travellerDetail as Parcelable)
                setFragmentResult(requestCode, bundle)
            }
            else -> {
            }
        }
    }
}