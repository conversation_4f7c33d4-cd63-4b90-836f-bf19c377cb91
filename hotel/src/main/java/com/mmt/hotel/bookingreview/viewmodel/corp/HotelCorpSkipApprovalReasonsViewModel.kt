package com.mmt.hotel.bookingreview.viewmodel.corp

import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.isNotNullAndEmpty
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.executeIfCast
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpSkipApprovalReasonsEvents
import com.mmt.hotel.bookingreview.model.corp.CorpSkipApprovalReasonsData
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.corpapproval.event.HotelCorpApprovalEvents
import com.mmt.hotel.corpapproval.event.HotelCorpApprovalEvents.ON_SELECTING_SKIP_APPROVAL_REASON
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.corpapproval.viewModel.HotelCorpApproverActionDialogFragmentVM
import javax.inject.Inject

/**
 * viewModel to handle business logic for skip approval reasons screen
 *
 * create by Varun Airon on 02/02/22
 */
class HotelCorpSkipApprovalReasonsViewModel @Inject constructor(
    private val skipApprovalReasonsData: CorpSkipApprovalReasonsData
) : HotelViewModel() {

    val itemReasonsList = ObservableArrayList<LinearLayoutItemData>()

    private val skipReasonsObservableModelList = mutableListOf<CorpSkipApprovalReasonObservableModel>()
    private val localEventStream = MutableLiveData<HotelEvent>()
    private lateinit var selectedReasonObservableModel : CorpSkipApprovalReasonObservableModel
    val comments = ObservableField(CoreConstants.EMPTY_STRING)
    var showErrorObservable = ObservableBoolean(false)

    init {
        skipApprovalReasonsData.skipApprovalReasons?.forEachIndexed { index, corpReason ->
            val observableModel = CorpSkipApprovalReasonObservableModel(
                reason = corpReason,
                eventStream = localEventStream,
                comment = comments,
                showDivider = skipApprovalReasonsData.skipApprovalReasons.last() != corpReason
            )
            if (index == 0) {
                // keep first reason as selected by default
                observableModel.isSelected.set(true)
                selectedReasonObservableModel = observableModel
            }
            skipReasonsObservableModelList.add(observableModel)
            itemReasonsList.add(LinearLayoutItemData(layoutId = R.layout.item_view_skip_approval_reason, dataVariableId = BR.model, data = observableModel))
        }
        localEventStream.observeForever {
            handleLocalEvents(it)
        }
    }

    /**
     * function to listen to local event stream and act accordingly
     * </p>
     * basically this function will listen and act to events fired from [CorpSkipApprovalReasonObservableModel]
     */
    private fun handleLocalEvents(event: HotelEvent) {
        when (event.eventID) {
            ON_SELECTING_SKIP_APPROVAL_REASON -> {
                event.data.executeIfCast<CorpSkipApprovalReasonObservableModel> {
                    handleSkipReasonSelection(this)
                }
            }
            HotelCorpApprovalEvents.EVENT_COMMENT_TEXT_CHANGE -> {
                showErrorObservable.set(false)
            }
        }
    }

    private fun handleSkipReasonSelection(selectedObservableModel: CorpSkipApprovalReasonObservableModel) {
        selectedReasonObservableModel = selectedObservableModel

        if (selectedObservableModel.reason.inputType == HotelCorpApproverActionDialogFragmentVM.TEXTBOX) {
            selectedObservableModel.showCommentBox.set(true)
        } else {
            comments.set(CoreConstants.EMPTY_STRING)
        }

        skipReasonsObservableModelList.filterNot { it == selectedObservableModel }.forEach {
            it.isSelected.set(false)
            it.showCommentBox.set(false)
        }
    }


    /**
     * function to response to on back button press
     */
    fun onClickBackButton() {
        eventStream.postValue(HotelEvent(HotelCorpSkipApprovalReasonsEvents.DISMISS_FRAGMENT))
    }

    /**
     * function to response to on tapping skip Approval button
     */
    fun onClickSkipApprovalButton() {
        if (inputSelectionHasErrors()) return

        val selectedReason =
            if (selectedReasonObservableModel.reason.inputType == HotelCorpApproverActionDialogFragmentVM.TEXTBOX) {
                comments.get()?.let {
                    CorpReasons(
                        text = it,
                        inputType = HotelCorpApproverActionDialogFragmentVM.TEXTBOX,
                        enablePersonalCorpBooking = selectedReasonObservableModel.reason.enablePersonalCorpBooking
                    )
                }
            } else {
                selectedReasonObservableModel.reason
            }

        val event = if (skipApprovalReasonsData.isRequisitionFlow) {
            HotelEvent(HotelCorpBookingReviewActivityEvent.INITIATE_ITINERARY_FLOW, selectedReason)
        } else {
            HotelEvent(
                HotelCorpSkipApprovalReasonsEvents.INITIATE_CHECKOUT_WITH_SKIP_APPROVAL_REASON,
                selectedReason
            )
        }

        eventStream.postValue(event)
    }

    private fun inputSelectionHasErrors(): Boolean {
        skipReasonsObservableModelList.forEach {
            if(it.isSelected.get() && it.reason.inputType == HotelCorpApproverActionDialogFragmentVM.TEXTBOX && it.comment.get().isNullOrEmpty()){
                showErrorObservable.set(true)
                return true
            }
        }
        return false
    }

    fun getTitle(): String {
        return skipApprovalReasonsData.title ?: ResourceProvider.instance.getString(R.string.htl_label_skip_approval_reasons_title)
    }

    fun showTextMessage(): Boolean {
        return !skipApprovalReasonsData.isRequisitionFlow
    }

    fun getActionCTAText(): String {
        return if(skipApprovalReasonsData.isRequisitionFlow){
            ResourceProvider.instance.getString(R.string.htl_TEXT_CONTINUE)
        } else {
            ResourceProvider.instance.getString(R.string.htl_corp_approval_skip_approval_cta_text)
        }
    }

}