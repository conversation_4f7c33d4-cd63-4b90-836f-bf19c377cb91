package com.mmt.hotel.bookingreview.model.response.additional

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.response.additional.HotelAdditionalFeesBreakUp
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelAdditionalFees(val amount: Double,
                               val currency: String,
                               val title: String,
                               val description: String?,
                               val subTitle: String?,
                               val disclaimer: String?,
                               val showReadAndAgree: Boolean,
                               val showWithPropertyRules: Boolean,
                               @Expose
                               @SerializedName("breakup")
                               val itemDetails: List<HotelAdditionalFeesBreakUp>?) : Parcelable
