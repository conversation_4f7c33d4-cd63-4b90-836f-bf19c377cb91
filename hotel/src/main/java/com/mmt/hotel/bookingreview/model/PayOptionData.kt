package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.response.HotelFullPaymentDetails
import com.mmt.hotel.bookingreview.model.response.PaymentPlan
import com.mmt.hotel.common.model.response.CancellationTimelineModel
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import kotlinx.parcelize.Parcelize

/**
 * Data to inflate pay option fragment in booking review screen
 */
@Parcelize
data class PayOptionData(
    val totalAmount: Double,
    val showEmi: Boolean, val emiMessage: String?,
    val enableBnpl: Boolean,
    val showBnpl: Boolean,
    val timeLineModel: CancellationTimelineModel?,
    var rtbPreApproved: Boolean = false,
    var requestToBook: Boolean = false,
    var rtbAutoCharge: Boolean = false,
    val skipReason: CorpReasons? = null,
    val paymentPlan: PaymentPlan? = null,
    val fullPaymentDetails: HotelFullPaymentDetails? = null,
    val currencySymbol: String
) : Parcelable