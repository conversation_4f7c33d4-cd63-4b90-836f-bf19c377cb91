package com.mmt.hotel.bookingreview.model.response.corptriptagv2

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CorpTripTagFieldV2(val attributeId: String,
                              val attributeName: String,
                              val attributeDescription: String?,
                              val attributePossibleValues: List<String>?,
                              var attributeSelectedValue: List<String>?,
                              val attributeType: String,
                              val possibleValuesAndGST:List<CorpTripTagValuesWithGSTV2>?,
                              val mandatoryCheck: Boolean,
                              val gstBasedTripTag:Boolean? = false) : Parcelable