package com.mmt.hotel.bookingreview.di

import com.mmt.hotel.bookingreview.model.PayOptionData
import com.mmt.hotel.bookingreview.ui.HotelGccPayOptionFragment
import dagger.BindsInstance
import dagger.Subcomponent

/**
 * Created by <PERSON><PERSON><PERSON> on 18/06/20.
 */

@Subcomponent(modules = [HotelPayOptionFragmentModule::class])
interface HotelGccPayOptionFragmentComponent {
    fun inject(fragment: HotelGccPayOptionFragment)

    @Subcomponent.Builder
    interface Builder {
        @BindsInstance
        fun payOptionData(payOptionData: PayOptionData): Builder

        fun build(): HotelGccPayOptionFragmentComponent
    }
}