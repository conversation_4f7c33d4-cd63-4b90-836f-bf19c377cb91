package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import com.mmt.hotel.common.model.request.RequestDetails
import kotlinx.parcelize.Parcelize

@Parcelize
data class TotalPricingRequestV2(
    val requestDetails: RequestDetails?,
    val countryCode: String,
    val addOnSelected: List<AddOnSelected>,
    val txnKey: String,
    val enableTcs: Boolean,
    val expData: String?,
    val featureFlags: BookingReviewResponseFilterFlag = BookingReviewResponseFilterFlag()
) : Parcelable