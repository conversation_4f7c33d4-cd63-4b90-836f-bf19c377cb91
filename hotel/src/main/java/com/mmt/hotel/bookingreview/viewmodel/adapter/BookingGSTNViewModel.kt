package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.databinding.ObservableInt
import com.mmt.profile.widget.GSTNWidget
import com.mmt.analytics.omnitureclient.Events
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.common.extensions.toDomesticGSTNDetails
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import kotlinx.coroutines.CoroutineScope

class BookingGSTNViewModel(val funnel: Int, val commonEvents: MutableMap<String, Any?>, val pageName: Events) : DiffUtilRecycleItem {

    var backGround = ObservableInt(R.drawable.htl_detail_item_bg)

    var gstnWidget: GSTNWidget?= null

    init {
        updateBackground()
    }

    fun isDayUseFunnel(): Boolean  {
        return funnel == HotelFunnel.DAYUSE.funnelValue
    }

    private fun updateBackground() {
        if (isDayUseFunnel()) {
            backGround.set(R.drawable.htl_detail_item_bg_white)
        } else {
            backGround.set(R.drawable.border_circular_edges)
        }
    }

    fun horizontalPadding(): Float {
        return if (isDayUseFunnel()) {
            ResourceProvider.instance.getDimensionPixelSize(R.dimen.htl_detail_card_padding_h).toFloat()
        } else {
            ResourceProvider.instance.getDimensionPixelSize(R.dimen.htl_review_card_padding).toFloat()
        }
    }

    fun verticalPadding(): Float {
        return if (isDayUseFunnel()) {
            ResourceProvider.instance.getDimensionPixelSize(R.dimen.margin_tiny).toFloat()
        } else {
            ResourceProvider.instance.getDimensionPixelSize(R.dimen.margin_extra_tiny).toFloat()
        }
    }

    fun isInputValid():Boolean {
        return gstnWidget?.checkValidity() ?: true
    }

    fun saveGSTNDetails(scope: CoroutineScope)  {
        // not passing callback as we  dont  want to wait user for api response
        gstnWidget?.saveData(null, scope)
    }

    fun getGSTNData() = gstnWidget?.getData()?.toDomesticGSTNDetails()

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.GST_DETAIL_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as BookingGSTNViewModel).funnel
        return funnel == matchedWith
    }

    override fun cardName(): String {
        return "GST DETAIL CARD"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.GST_DETAIL
    }
}