package com.mmt.hotel.bookingreview.model.response.addon


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import com.gommt.insurance.data.InsuranceWidgetData
import com.gommt.insurance.data.PriceData
import com.gommt.insurance.data.UiData
import com.google.gson.JsonElement
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import java.io.Serializable


@Parcelize
data class InsuranceData(
    @SerializedName("tmInsuranceAddOns")
    val insuranceItems: List<InsuranceDataItem>? = null,
    val widgetData:   @RawValue JsonElement? = null,
    @SerializedName("topHeading")
    val heading: String? = null
) : Parcelable

@Parcelize
data class InsuranceWidgetDataUi(
    val ui: @RawValue UiData? = null,
    val data: @RawValue PriceData? = null
) : Parcelable


