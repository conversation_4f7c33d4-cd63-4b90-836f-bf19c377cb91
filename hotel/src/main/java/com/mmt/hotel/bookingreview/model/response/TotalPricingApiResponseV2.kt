package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.response.TotalPricingResponseV2
import com.mmt.hotel.common.model.response.HotelApiError
import kotlinx.parcelize.Parcelize

@Parcelize
data class TotalPricingApiResponseV2(val response: TotalPricingResponseV2?,
                                     val correlationKey:String?,
                                     val error: HotelApiError?):Parcelable