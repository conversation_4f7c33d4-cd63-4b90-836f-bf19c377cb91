package com.mmt.hotel.bookingreview.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.viewmodel.CheckoutErrorFragmentViewModel
import com.mmt.hotel.bookingreview.viewmodel.HotelRTBErrorViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.model.HotelError
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class HotelErrorFragmentModule {

    @Provides
    @IntoMap
    @ViewModelKey(CheckoutErrorFragmentViewModel::class)
    fun provideCheckoutErrorViewModel(viewModel: CheckoutErrorFragmentViewModel): HotelViewModel = viewModel

    @Provides
    @IntoMap
    @ViewModelKey(HotelRTBErrorViewModel::class)
    fun provideRtbErrorViewModel(viewModel: HotelRTBErrorViewModel): HotelViewModel = viewModel

    @Provides
    fun provideError(fragment: Fragment): HotelError {
        return fragment.requireArguments().getParcelable(HotelConstants.ERROR_FRAG_ARGS)!!
    }
}