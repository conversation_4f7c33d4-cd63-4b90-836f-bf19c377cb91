package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import com.mmt.core.BuildConfig
import com.mmt.core.constant.CoreConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class OtpGenerateRequest(val email: String?,
                              val mob: String?,
                              val isdCode: String?,
                              val countryCode: String,
                              val appHashKey: String = if(BuildConfig.DEBUG) CoreConstants.AUTO_READ_DEBUG_KEY else CoreConstants.AUTO_READ_RELEASE_KEY) : Parcelable

@Parcelize
data class OtpValidationRequest(val otp: String?,
                              val key: String?,
                              val countryCode: String) : Parcelable