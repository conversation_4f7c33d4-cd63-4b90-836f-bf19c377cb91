package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import android.widget.Toast
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.isNotNullAndEmpty
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.extensions.isNotNullAndEmpty
import com.mmt.hotel.R
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.APPROVAL_CTA_SHOWN_TRACKING
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.SKIP_APPROVAL_SHOWN_TRACKING
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.corp.CorpApprovalRequestFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpSkipApprovalReasonsData
import com.mmt.hotel.bookingreview.model.response.PriceFooter
import com.mmt.hotel.bookingreview.viewmodel.adapter.PaymentButtonFooterViewModel
import com.mmt.hotel.common.constants.HotelConstants.GUEST_HOUSE_REASON_KEY
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.corpapproval.model.response.CorpCategoryReasons
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.thankyou.PIPE_WITH_SPACES


class CorpBookingApprovalRequestBtnsViewModel(
    private val priceFooter: PriceFooter?,
    private val corpApprovalRequestFragmentData: CorpApprovalRequestFragmentData,
    val decentralizedFlow: Boolean,
    val forwardFlow: Boolean,
    var isTripTagsApiInProgress: Boolean,
    private val eventStream: MutableLiveData<HotelEvent>
):  PaymentButtonFooterViewModel() {

    private var data: CorpApprovalRequestFragmentData = corpApprovalRequestFragmentData
    private var isApprovalRequired: Boolean = isCorpApprovalRequired()
    private var blockSkipApproval: Boolean = isSkipApprovalBlock()
    private var blockOopBooking: Boolean = isOopBookingBlock()

    val updateViewState = mutableStateOf(true)
    fun fireCTAShownTracking(){
        var ctaShownTracking = CoreConstants.EMPTY_STRING
        if(isSkipApprovalBtnVisible()){
            ctaShownTracking += (SKIP_APPROVAL_SHOWN_TRACKING + PIPE_WITH_SPACES)
        }
        ctaShownTracking += String.format(APPROVAL_CTA_SHOWN_TRACKING, getCtaText())
        eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.CTA_SHOWN_TRACKING, ctaShownTracking))
    }

    fun updateData(corpApprovalRequestFragmentData: CorpApprovalRequestFragmentData) {
        this.data = corpApprovalRequestFragmentData
        isApprovalRequired = isCorpApprovalRequired()
        blockSkipApproval = isSkipApprovalBlock()
        blockOopBooking = isOopBookingBlock()
        notifyChange()
        fireCTAShownTracking()
        updateViewState.value = true
    }

    fun getApprovalRequestData(): CorpApprovalRequestFragmentData {
        return data
    }

    fun resetData() {
        isApprovalRequired = false
        blockSkipApproval = false
        blockOopBooking = false
        notifyChange()
        updateViewState.value = true
    }

    fun getOopBookingBlock() = blockOopBooking

    override fun getBgStartColor() = if(getOopBookingBlock()) ResourceProvider.instance.getColor(R.color.htl_border_gray_color) else ResourceProvider.instance.getColor(R.color.corp_bg_light)

    override fun getBgEndColor() = if(getOopBookingBlock()) ResourceProvider.instance.getColor(R.color.htl_border_gray_color) else ResourceProvider.instance.getColor(R.color.corp_bg_dark)

    override fun onClick() {
        if (ExperimentUtil.isTripTagApiMandatory() && isTripTagsApiInProgress) {
            ResourceProvider.instance.showToast(
                ResourceProvider.instance.getString(R.string.htl_page_is_loading),
                Toast.LENGTH_SHORT
            )
        } else if(getOopBookingBlock()){
            val failureReasons = data.corpApprovalInfo?.failureReasons ?: listOf()
            eventStream.postValue(HotelEvent(HotelBookingReviewFragmentEvent.OPEN_OUT_OF_POLICY_BOTTOMSHEET, failureReasons))
        } else if (decentralizedFlow && (!forwardFlow || isApprovalRequired)) {
            val guestHouseReason = findGuestHouseReason()
            if (canShowTravelReasonFragment() && guestHouseReason != null) {
                eventStream.postValue(
                    HotelEvent(
                        HotelCorpBookingReviewActivityEvent.OPEN_SKIP_APPROVAL_REASONS_SCREEN,
                        CorpSkipApprovalReasonsData(
                            title = guestHouseReason.title,
                            skipApprovalReasons = guestHouseReason.options,
                            isRequisitionFlow = true
                        )
                    )
                )
            } else {
                eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.INITIATE_ITINERARY_FLOW))
            }
        } else if (isApprovalRequired) {
            eventStream.postValue(
                HotelEvent(
                    HotelCorpBookingReviewActivityEvent.REQUEST_APPROVAL_BUTTON_CLICKED,
                    data
                )
            )
        } else {
            eventStream.postValue(HotelEvent(HotelBookingReviewFragmentEvent.PAYMENT_BUTTON_CLICKED))
        }
    }

    private fun canShowTravelReasonFragment(): Boolean {
        return data.corpAutoBookRequestorConfig?.travelReasons.isNotNullAndEmpty()
    }

    private fun findGuestHouseReason(): CorpCategoryReasons? {
        val travelReasons = data.corpAutoBookRequestorConfig?.travelReasons
        travelReasons?.forEach { reason ->
            if (reason.reasonKey == GUEST_HOUSE_REASON_KEY) {
                return reason
            }
        }
        return null
    }

    override fun getPriceFooterData() = priceFooter

    override fun showFooter() = priceFooter != null && isApprovalBtnVisible()

    override fun getCtaText(): String {
        return if (priceFooter?.ctaText.isNotNullAndEmpty()) {
            priceFooter?.ctaText ?: ResourceProvider.instance.getString(R.string.htl_TEXT_CONTINUE)
        } else if(decentralizedFlow && (!forwardFlow || isApprovalRequired)){
            ResourceProvider.instance.getString(R.string.htl_add_to_itinerary)
        }else{
            ResourceProvider.instance.getString(R.string.htl_TEXT_CONTINUE)
        }
    }

    override fun isEnabled(): Boolean {
        return !blockOopBooking
    }

    fun onSkipApprovalBtnClick() {
        eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.SKIP_APPROVAL_BUTTON_CLICKED, data.corpSkipApprovalReasonsData))
    }

    private fun isCorpApprovalRequired(): Boolean {
        if( data.corpApprovalInfo?.approvalRequired == null) {
            return false
        }
        return data.corpApprovalInfo?.approvalRequired as Boolean
    }

    private fun isSkipApprovalBlock(): Boolean {
        data.corpApprovalInfo?.blockSkipApproval?.let { isSkipApprovalBlock ->
            return isSkipApprovalBlock > 0
        }
        return false
    }

    private fun isOopBookingBlock(): Boolean {
        data.corpApprovalInfo?.blockOopBooking?.let { isBlockOopBooking ->
            return isBlockOopBooking > 0
        }
        return false
    }

    fun getApprovalBtnText(): String {
        return if(decentralizedFlow && (!forwardFlow || isApprovalRequired)) {
            ResourceProvider.instance.getString(R.string.htl_add_to_itinerary)
        } else if( isApprovalRequired ) {
            ResourceProvider.instance.getString(R.string.htl_text_request_approval)
        } else {
            ResourceProvider.instance.getString(R.string.htl_TEXT_CONTINUE)
        }
    }

    fun isApprovalBtnVisible():Boolean {
        return !blockOopBooking
    }

    fun isSkipApprovalBtnVisible(): Boolean {
        if(decentralizedFlow || blockOopBooking){
            return false
        }
        return isApprovalRequired && !blockSkipApproval
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CORP_APPROVAL_BTNS
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_APPROVAL_BTNS
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as CorpBookingApprovalRequestBtnsViewModel).data
        return data == matchedWith && decentralizedFlow == item.decentralizedFlow
    }

    override fun cardName(): String {
        return "Review payment Button"
    }
}