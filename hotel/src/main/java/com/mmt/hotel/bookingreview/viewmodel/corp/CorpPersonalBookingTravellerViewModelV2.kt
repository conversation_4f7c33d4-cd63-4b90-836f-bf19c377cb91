package com.mmt.hotel.bookingreview.viewmodel.corp

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.constants.CorpReviewCards.CORP_PERSONAL_TRAVELER_INFORMATION_CARD
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.CoTravellerFragmentData
import com.mmt.hotel.bookingreview.model.TravellerDetailV2
import com.mmt.hotel.bookingreview.model.UserInputDetail
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.bookingreview.viewmodel.TravelerViewModel
import com.mmt.hotel.bookingreview.viewmodel.adapter.TravelerInformationViewModel
import com.mmt.hotel.common.constants.GuestType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.listingV2.event.HotelListingClickEvents

class CorpPersonalBookingTravellerViewModelV2(
    val foreignTravel: Boolean = false,
    val userSearchData: UserSearchData?,
    var eventStream: MutableLiveData<HotelEvent>
) : BaseObservable(), TravelerViewModel {


    data class CorpPersonalBookingTravellerUiModel(
        val isGcc : Boolean = LoginUtils.getPreferredRegion().isGlobalEntity(),
        val isUserLoggedIn: Boolean = true,
        val isSelfTraveller: Boolean = true,
        val isOtherTraveler: Boolean = false,
        val emailId: String = CoreConstants.EMPTY_STRING,
        val emailError: String = CoreConstants.EMPTY_STRING,
        val title: String = HotelUtil.getTitle(LoginUtils.loggedInUser?.title, LoginUtils.loggedInUser?.gender),
        val name: String = CoreConstants.EMPTY_STRING,
        val surname: String = CoreConstants.EMPTY_STRING,
        val nameError: String = CoreConstants.EMPTY_STRING,
        val pan: String = CoreConstants.EMPTY_STRING,
        val surnameError: String = CoreConstants.EMPTY_STRING,
        val isdCode: Int = HotelUtil.getIsdCodeForTravellerForm(),
        val contactNo: String = CoreConstants.EMPTY_STRING,
        val contactNoError: String = CoreConstants.EMPTY_STRING,
        val canAddCoTraveller: Boolean,
        val coTravellerList: MutableList<CoTraveller> = mutableListOf(),
        val hasCoTraveller: Boolean = false,
        val isForeignTraveler: Boolean,
        val gstAvailable: Boolean = false
    )

    private fun defaultTitle() = HotelUtil.getTitle(LoginUtils.loggedInUser?.title, LoginUtils.loggedInUser?.gender)

    private val _uiState: MutableState<CorpPersonalBookingTravellerUiModel> =
        mutableStateOf(CorpPersonalBookingTravellerUiModel(isForeignTraveler = foreignTravel, canAddCoTraveller = canAddCoTraveller()))
    val uiState: State<CorpPersonalBookingTravellerUiModel> = _uiState

    init {
        setTravellerType(true)
    }


    private fun setTravellerType(isSelfTraveller: Boolean) {
        if (isSelfTraveller) setSelfDetail() else setOtherDetail()
    }

    private fun getLoggedInUserDetails() : UserInputDetail {
        val user = LoginUtils.loggedInUser
        return UserInputDetail().apply {
            user?.corpData?.employee?.let { employee ->
                val namePair = HotelUtil.getFirstAndLastNamePair(employee.name)
                title = defaultTitle()
                name = namePair.first
                surname = namePair.second
                isdCode = if (user.primaryContactCountryCode.isNotEmpty()) user.primaryContactCountryCode.toInt() else HotelUtil.getIsdCodeForTravellerForm()
                contactNo = employee.phoneNumber ?: CoreConstants.EMPTY_STRING
                emailId = employee.businessEmailId ?: CoreConstants.EMPTY_STRING
            }
        }
    }

    private fun setSelfDetail() {
        val user = getLoggedInUserDetails()
        updateUiState(
            isSelfTraveller = true,
            isOtherTraveler = false,
            isdCode = user.isdCode,
            title = user.title,
            name = user.name,
            surname = user.surname,
            contactNo = user.contactNo,
            emailId = user.emailId
        )
        eventStream.postValue(HotelEvent(HotelListingClickEvents.BOOKING_FOR_MYSELF, true))
    }

    fun updateTitle(title: String) {
        updateUiState(title = title)
    }

    fun updateName(name: String) {
        updateUiState(name = name)
    }

    fun updateSurname(surname: String) {
        updateUiState(surname = surname)
    }

    fun changeTravellerType(isSelfTraveller: Boolean) {
        updateUiState(isSelfTraveller = isSelfTraveller)
        setTravellerType(isSelfTraveller)
    }

    fun updateEmailId(email: String) {
        updateUiState(emailId = email)
    }

    fun updateIsdCode(isdCode: Pair<Int, String>) {
        updateUiState(isdCode = isdCode.first)
    }

    fun updateContactNo(mobile: String) {
        updateUiState(contactNo = mobile)
    }

    private fun setOtherDetail() {
        val loggedInUser = getLoggedInUserDetails()
        _uiState.value = CorpPersonalBookingTravellerUiModel(isForeignTraveler = foreignTravel, canAddCoTraveller = canAddCoTraveller()).copy(
            isSelfTraveller = false,
            isOtherTraveler = true,
            emailId = loggedInUser.emailId
        )
        eventStream.postValue(HotelEvent(HotelListingClickEvents.BOOKING_FOR_MYSELF, false))
    }

    fun validateTravellerData(): Boolean {
        return validateTravellerDetail()
    }

    private fun validateTravellerDetail(): Boolean {
        var result = isNameValid()
        result = isSurnameValid() && result
        result = isContactNoValid() && result
        return result
    }

    fun isNameValid(): Boolean {
        val result: Boolean
        if (_uiState.value.name.trim().isEmpty()) {
            result = false
            updateUiState(nameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
        } else {
            result = HotelUtil.validateRegEx(
                _uiState.value.name,
                HotelConstants.FIRST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(_uiState.value.name, HotelConstants.DOT_AND_SPACE_REGEX)
            if (!result) updateUiState(nameError = ResourceProvider.instance.getString(R.string.htl_name_error)) else updateUiState(nameError = "")
        }
        return result
    }

    fun isSurnameValid(): Boolean {
        val result: Boolean
        if (_uiState.value.surname.trim().isEmpty()) {
            result = false
            updateUiState(surnameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
        } else {
            result = HotelUtil.validateRegEx(
                _uiState.value.surname,
                HotelConstants.LAST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(
                _uiState.value.surname,
                HotelConstants.DOT_AND_SPACE_REGEX
            )
            if (!result) updateUiState(surnameError = ResourceProvider.instance.getString(R.string.htl_name_error)) else updateUiState(surnameError = "")
        }
        return result
    }

    fun isEmailIdValid(): Boolean {
        val result: Boolean
        if (_uiState.value.emailId.isEmpty()) {
            result = false
            updateUiState(emailError = ResourceProvider.instance.getString(R.string.htl_email_empty))
        } else {
            result = LoginUtil.checkEmail(_uiState.value.emailId)
            updateUiState(emailError = if (result) "" else ResourceProvider.instance.getString(R.string.htl_email_error))
        }
        return result
    }

    fun isContactNoValid(): Boolean {
        val validation = HotelUtil.isContactNoValid(_uiState.value.isdCode, _uiState.value.contactNo)
        updateUiState(contactNoError = validation.second)
        return validation.first
    }


    fun addGuest() {
        eventStream.value = HotelEvent(
            HotelBookingReviewActivityEvent.OPEN_CO_TRAVELLER_FRAGMENT,
            CoTravellerFragmentData(
                travellerType = if (_uiState.value.isSelfTraveller) TravelerInformationViewModel.TRAVELLER_TYPE_SELF else TravelerInformationViewModel.TRAVELLER_TYPE_SOMEONE_ELSE,
                coTravellerList = _uiState.value.coTravellerList
            )
        )
    }

    fun showCoTraveller(coTravellerList: MutableList<CoTraveller>) {
        updateUiState(
            coTravellerList = coTravellerList,
            hasCoTraveller = coTravellerList.isNotEmpty()
        )
    }

    fun removeCoTraveller(coTraveller: CoTraveller) {
        val coTravelerList = _uiState.value.coTravellerList.toMutableList()
        coTravelerList.remove(coTraveller)
        updateUiState(coTravellerList = coTravelerList, hasCoTraveller = coTravelerList.isNotEmpty())
    }

    fun updateDoubleBlackName(name: String, surname: String) {
        updateUiState(
            name = name,
            surname = surname
        )
    }

    fun getTravellerDataForCheckout(gstnDetails: GSTNDetails?): MutableList<TravellerDetailV2> {
        val travellerDetailList = mutableListOf<TravellerDetailV2>()
        val masterTravellerDetail = TravellerDetailV2(
            title = _uiState.value.title,
            firstName = _uiState.value.name,
            lastName = _uiState.value.surname,
            emailID = _uiState.value.emailId,
            masterPax = true,
            isdCode = _uiState.value.isdCode.toString(),
            mobileNo = _uiState.value.contactNo,
            paxType = GuestType.ADULT.name,
            panCard = _uiState.value.pan.ifEmpty { null },
            registerGstinNum = gstnDetails?.gstn,
            gstinCompanyName = gstnDetails?.organizationName,
            gstinCompanyAddress = gstnDetails?.address1,
            state = gstnDetails?.city,
            saveGstDetails = gstnDetails?.saveGstDetails
        )
        travellerDetailList.add(masterTravellerDetail)

        for (coTraveller in _uiState.value.coTravellerList) {
            val coTravellerDetail = TravellerDetailV2(
                title = coTraveller.title
                    ?: HotelConstants.DEFAULT_TITLE,
                firstName = coTraveller.first_name.orEmpty(),
                lastName = coTraveller.last_name.orEmpty(),
                emailID = coTraveller.traveller_email,
                masterPax = false,
                paxType = if (coTraveller.pax_type == GuestType.CHILD.name) GuestType.CHILD.name else GuestType.ADULT.name,
                gender = coTraveller.gender,
                travellerId = coTraveller.travellerId
            )
            travellerDetailList.add(coTravellerDetail)
        }
        return travellerDetailList
    }

    fun canAddCoTraveller(): Boolean {
        return LoginUtils.isIndiaFunnelContext() && (userSearchData?.occupancyData?.adultCount ?: 1) > 1
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CORP_PERSONAL_BOOKING_TRAVELLER_CARD
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_ADD_TRAVELLER
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as CorpPersonalBookingTravellerViewModelV2).foreignTravel
        return foreignTravel == matchedWith
    }

    override fun cardName(): String {
        return CORP_PERSONAL_TRAVELER_INFORMATION_CARD
    }

    private fun updateUiState(
        isUserLoggedIn: Boolean = _uiState.value.isUserLoggedIn,
        isSelfTraveller: Boolean = _uiState.value.isSelfTraveller,
        isOtherTraveler: Boolean = _uiState.value.isOtherTraveler,
        emailId: String = _uiState.value.emailId,
        emailError: String = _uiState.value.emailError,
        title: String = _uiState.value.title,
        name: String = _uiState.value.name,
        surname: String = _uiState.value.surname,
        nameError: String = _uiState.value.nameError,
        surnameError: String = _uiState.value.surnameError,
        isdCode: Int = _uiState.value.isdCode,
        contactNo: String = _uiState.value.contactNo,
        contactNoError: String = _uiState.value.contactNoError,
        canAddCoTraveller: Boolean = _uiState.value.canAddCoTraveller,
        coTravellerList: MutableList<CoTraveller> = _uiState.value.coTravellerList,
        hasCoTraveller: Boolean = _uiState.value.hasCoTraveller,
        isForeignTraveler: Boolean = _uiState.value.isForeignTraveler,
        gstAvailable: Boolean = _uiState.value.gstAvailable
    ) {
        _uiState.value = _uiState.value.copy(
            isGcc = LoginUtils.getPreferredRegion().isGlobalEntity(),
            isUserLoggedIn = isUserLoggedIn,
            isSelfTraveller = isSelfTraveller,
            isOtherTraveler = isOtherTraveler,
            emailId = emailId,
            emailError = emailError,
            title = title,
            name = name,
            surname = surname,
            nameError = nameError,
            surnameError = surnameError,
            isdCode = isdCode,
            contactNo = contactNo,
            contactNoError = contactNoError,
            canAddCoTraveller = canAddCoTraveller,
            coTravellerList = coTravellerList,
            hasCoTraveller = hasCoTraveller,
            isForeignTraveler = isForeignTraveler,
            gstAvailable = gstAvailable
        )
    }
}