package com.mmt.hotel.bookingreview.helper.constants

import androidx.annotation.StringDef

@Retention(AnnotationRetention.SOURCE)
@StringDef(
        value = [
            PriceKey.BASE,
            PriceKey.WALLET,
            PriceKey.DISCOUNT,
            PriceKey.DISCOUNTED_PRICE,
            PriceKey.TOTAL,
            PriceKey.TAXES,
            PriceKey.SERVICE_FEES,
            PriceKey.SERVICE_CHARGE,
            PriceKey.MARK_UP,
            PriceKey.CDF_DISCOUNT
        ]
)
annotation class PriceKey() {
    companion object {
        const val BASE = "BASE_FARE"
        const val WALLET = "WALLET_DISCOUNT"
        const val DISCOUNT = "TOTAL_DISCOUNT"
        const val DISCOUNTED_PRICE = "PRICE_AFTER_DISCOUNT"
        const val TOTAL = "TOTAL_AMOUNT"
        const val TAXES = "TAXES"
        const val BASE_FARE_WITH_TAX = "BASE_FARE_WITH_TAX"

        /*Price breakup keys */
        const val SERVICE_FEES = "SERVICE_FEES"
        const val SERVICE_CHARGE = "SERVICE_CHARGE"
        const val MARK_UP = "MARK_UP"
        const val CDF_DISCOUNT = "CDF_DISCOUNT"
    }
}