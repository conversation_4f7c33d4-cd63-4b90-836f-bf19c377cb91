package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.mmt.hotel.R
import com.mmt.core.util.performIfActivityActive
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomUpFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.ui.HotelBookingReviewActivity
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpAddEditTravellerSuccessViewModel
import com.mmt.hotel.databinding.HtlCorpEditTravellerSuccessLayoutBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class CorpAddEditTravellerSuccessFragment :  HotelBottomUpFragment<CorpAddEditTravellerSuccessViewModel, HtlCorpEditTravellerSuccessLayoutBinding>(){
    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel : HotelEventSharedViewModel? = null

    companion object {
        const val TAG = "CorpAddEditTravellerSuccessFragment"
        @JvmStatic
        fun newInstance(bundle: Bundle): CorpAddEditTravellerSuccessFragment {
            val fragment = CorpAddEditTravellerSuccessFragment()
            fragment.arguments = bundle;
            return fragment
        }
    }

    override val backgroundViewId: Int
        get() = R.id.view_bg_above_review_screen

    override fun onHandleBackPress() {
        performIfActivityActive(activity) { activity ->
            (activity as FragmentActivity).supportFragmentManager.popBackStackImmediate()
        }
    }

    override fun initViewModel(): CorpAddEditTravellerSuccessViewModel {
        return getViewModel(factory)
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel  = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.htl_corp_edit_traveller_success_layout
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel(factory)
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            DISMISS_FRAGMENT -> {
                dismissFragment()
            }
        }
    }
}