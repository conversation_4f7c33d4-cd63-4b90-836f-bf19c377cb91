package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class PriceFooter(
    @SerializedName("ctaText")
    val ctaText: String? = null,
    @SerializedName("amountText")
    val amountText: String? = null,
    @SerializedName("text")
    val text: String? = null,
    @SerializedName("subtext")
    val subtext: String? = null
) : Parcelable
