package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.common.model.request.RoomCriteriaV2
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.dayuse.model.request.DayUseApiSlot
import kotlinx.parcelize.Parcelize

@Parcelize
data class SearchCriteria(var hotelId: String? = null,
                          var checkIn: String = EMPTY_STRING,
                          var checkOut: String = EMPTY_STRING,
                          var countryCode: String = EMPTY_STRING,
                          var locationId: String = EMPTY_STRING,
                          var locationType: String = EMPTY_STRING,
                          var cityCode: String? = EMPTY_STRING,
                          var currency: String = EMPTY_STRING,
                          var searchType: String = EMPTY_STRING,
                          var pricingKey: String? = null,
                          var roomStayCandidates: List<RoomStayCandidatesV2>? = null,
                          var roomCriteria: List<RoomCriteriaV2>? = null,
                          var comparatorHotelIds: List<String>? = null,
                          val tripType : String? = null,
                          var travellerEmailID: List<String>? = null,
                          var personalCorpBooking : Boolean = false,
                          val slot: DayUseApiSlot? = null,
                          val selectedRatePlan: SelectedRatePlan? = null,
                          val hotelType: String? = null,
                          val guestHouseAvailable: Boolean? = null,
                          val userSearchType: String? = null,
    ) : Parcelable