package com.mmt.hotel.bookingreview.model.response.addon.subscription

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class SubscriptionData(
    val subscriptionStatus: String,
    val title: String? = null,
    val icon1: String? = null,
    val icon2: String? = null,
    val heading: String,
    val subHeading: String,
    val bgImg: String? = null,
    val headingIcon: String? = null,
    val bottomDataView: BottomDataView? = null,
    val membershipStatus: MembershipStatus? = null,
) : Parcelable

@Parcelize
data class ExpandedViewData(
    val headerSection: HeaderSection,
    val subscriptionSection: SubscriptionSection,
    val benefitsSection: BenefitsSection
) : Parcelable

@Parcelize
data class MembershipStatus(
    val text: String,
    val bgColor: String? = null
) : Parcelable

@Parcelize
data class BottomDataView(
    val addedState: State? = null,
    val removedState: State? = null
) : Parcelable

@Parcelize
data class State(
    val text: String,
    val toastMsg: String,
    val icon: String? = null,
    val cta: CTA
) : Parcelable

@Parcelize
data class CTA(
    val text: String,
    val url: String? = null
) : Parcelable

@Parcelize
data class BenefitsSection(
    val heading: String,
    val benefits: List<Benefits>
) : Parcelable

@Parcelize
data class Benefits(
    val heading: String,
    val description: String,
    val icon: String? = null
) : Parcelable


@Parcelize
data class SubscriptionSection(
    val totalPrice: String?,
    @SerializedName("dsPrice")
    val discountedPrice: String?,
    val duration: String,
    val cta: CTA? = null,
    val bgColor: List<String>
) : Parcelable

@Parcelize
data class HeaderSection(
    val headerIcon: String?,
    val title: String,
    val heading: String,
    val subHeading: String
) : Parcelable

enum class SubscriptionStates {
    PREBUY,
    ACTIVE,
    EXPIRED
}