package com.mmt.hotel.bookingreview.helper

import com.mmt.core.util.SerializerUtil
import com.mmt.hotel.common.HotelSharedPrefUtil.Companion.instance
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.model.response.HotelApiError
import com.mmt.hotel.old.model.hotelconfig.ErrorInfo


object HotelCheckOutErrorUtil {
    fun getErrorInfo(error: HotelApiError): ErrorInfo? {
        val errorInfoMap = SerializerUtil.deserialize(
                instance.getString(SharedPrefKeys.KEY_HTL_CHECKOUT_ERRORS)) as HashMap<String?, ErrorInfo?>?
        return errorInfoMap?.get(error.code)?.let { errorInfo ->
            errorInfo.apply {
                if(msgToOverride.isNullOrEmpty()){
                    msgToOverride = error.message
                }
            }
        }
    }
}