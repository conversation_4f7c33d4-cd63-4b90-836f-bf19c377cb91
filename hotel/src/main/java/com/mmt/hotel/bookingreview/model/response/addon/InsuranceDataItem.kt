package com.mmt.hotel.bookingreview.model.response.addon


import com.google.gson.annotations.SerializedName
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class InsuranceDataItem(
    @SerializedName("ctaText")
    val ctaText: String? = null,
    @SerializedName("currency")
    val currency: String? = null,
    @SerializedName("description")
    val description: String? = null,
    @SerializedName("featureList")
    val featureList: List<String>? = null,
    @SerializedName("heading")
    val heading: String? = null,
    @SerializedName("icon")
    val icon: String? = null,
    @SerializedName("id")
    val id: Int = 0,
    @SerializedName("includedUnits")
    val includedUnits: Int = 0,
    @SerializedName("largeIcon")
    val largeIcon: String? = null,
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("priceTagLine")
    val priceTagLine: String? = null,
    @SerializedName("providerIcon")
    val providerIcon: String? = null,
    @SerializedName("shortTextDesc")
    val shortHeadingDesc: String? = null,
    @SerializedName("subHeading")
    val subHeading: String? = null,
    @SerializedName("sumInsured")
    val sumInsured: String? = null,
    @SerializedName("tagLine")
    val tagLine: String? = null,
    @SerializedName("tncLink")
    val tncLink: String? = null,
    @SerializedName("tncText")
    val tncText: String? = null,
    @SerializedName("totalPrice")
    val totalPrice: Double = 0.0,
    @SerializedName("type")
    val type: String? = null,
    @SerializedName("unitBasePrice")
    val unitBasePrice: Double = 0.0,
    @SerializedName("unitPrice")
    val unitPrice: Int = 0,
    @SerializedName("unitType")
    val unitType: String? = null,
    @SerializedName("unitVendorCgst")
    val unitVendorCgst: Int = 0,
    @SerializedName("unitVendorIgst")
    val unitVendorIgst: Double = 0.0,
    @SerializedName("unitVendorSgst")
    val unitVendorSgst: Int = 0,
    @SerializedName("vendorNo")
    val vendorNo: String? = null,
    @SerializedName("vendorLogo")
    val vendorLogo: String? = null,
    @SerializedName("postAttachMessage")
    val selectedHeading : String? = null,
    var isSelected: Boolean = false  //local field. this is use to send selected state
) : Parcelable