package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data  class CorpApprovingManager(val id: String?,
                                 val businessEmailCommId: String?,
                                 val mmtUuid: String?,
                                 val name: String?,
                                 val designation: String?,
                                 val businessEmailId: String?,
                                 val employeeId: String?,
                                 val phoneNumber: String?,
                                 val phoneNumberCommId: String?) :Parcelable