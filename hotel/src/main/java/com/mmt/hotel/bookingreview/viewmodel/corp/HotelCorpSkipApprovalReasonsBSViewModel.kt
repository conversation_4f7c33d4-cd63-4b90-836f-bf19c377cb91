package com.mmt.hotel.bookingreview.viewmodel.corp

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.model.corp.CorpSkipApprovalReasonsData
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.corpapproval.viewModel.HotelCorpApproverActionDialogFragmentVM

class HotelCorpSkipApprovalReasonsBSViewModel(
    private val skipApprovalReasonsData: CorpSkipApprovalReasonsData,
    private val eventStream: MutableLiveData<HotelEvent>
) {

    val uiState: MutableState<HotelCorpSkipApprovalReasonsBSUiState> =
        mutableStateOf(HotelCorpSkipApprovalReasonsBSUiState())

    init {
        createUIStateWithData(skipApprovalReasonsData)
    }

    private fun createUIStateWithData(skipApprovalReasonsData: CorpSkipApprovalReasonsData) {
        val uiState = HotelCorpSkipApprovalReasonsBSUiState(
            title = skipApprovalReasonsData.title ?: ResourceProvider.instance.getString(R.string.htl_label_skip_approval_reasons_title),
            skipApprovalReasons = skipApprovalReasonsData.skipApprovalReasons?.mapIndexed { index, it ->
                ReasonSelectionItemState(
                    reason = it,
                    comment = EMPTY_STRING,
                    showDivider = it != skipApprovalReasonsData.skipApprovalReasons.last(),
                    isSelected = index == 0,
                )
            } ?: emptyList(),
            showMandatoryError = false,
            specialMessage = ResourceProvider.instance.getString(R.string.htl_label_skip_approval_reasons_message),
            showSpecialMessage = !skipApprovalReasonsData.isRequisitionFlow,
            actionCTAText = if (skipApprovalReasonsData.isRequisitionFlow) ResourceProvider.instance.getString(
                R.string.htl_TEXT_CONTINUE
            ) else ResourceProvider.instance.getString(R.string.htl_corp_approval_skip_approval_cta_text)
        )
        updateUiState(uiState)
    }

    fun updateUiState(uiState: HotelCorpSkipApprovalReasonsBSUiState) {
        this.uiState.value = uiState
    }

    fun getUiState(): HotelCorpSkipApprovalReasonsBSUiState {
        return uiState.value
    }

    fun handleItemSelection(corpReason: ReasonSelectionItemState) {
        val updatedSkipApprovalReasons = uiState.value.skipApprovalReasons.map {
            if (it.reason == corpReason.reason) {
                it.copy(isSelected = true)
            } else {
                it.copy(isSelected = false)
            }
        }
        updateUiState(uiState.value.copy(skipApprovalReasons = updatedSkipApprovalReasons, showMandatoryError = false))
    }

    fun handleCommentChange(corpReason: ReasonSelectionItemState, comment: String) {
        val updatedSkipApprovalReasons = uiState.value.skipApprovalReasons.map {
            if (it.reason == corpReason.reason) {
                it.copy(comment = comment)
            } else {
                it
            }
        }
        updateUiState(uiState.value.copy(skipApprovalReasons = updatedSkipApprovalReasons, showMandatoryError = false))
    }

    fun handleSkipApprovalCTA() {
        if (inputSelectionHasErrors()) return

        val selectedReason = uiState.value.skipApprovalReasons.find { it.isSelected }?.reason?.let { reason ->
            if (reason.inputType == HotelCorpApproverActionDialogFragmentVM.TEXTBOX) {
                reason.copy(text = uiState.value.skipApprovalReasons.find { it.isSelected }?.comment.orEmpty())
            } else {
                reason
            }
        }

        val event = HotelEvent(
            if (skipApprovalReasonsData.isRequisitionFlow) {
                HotelCorpBookingReviewActivityEvent.INITIATE_ITINERARY_FLOW
            } else {
                HotelCorpBookingReviewActivityEvent.INITIATE_CHECKOUT_WITH_SKIP_REASON
            },
            selectedReason
        )

        eventStream.postValue(event)
    }

    fun inputSelectionHasErrors(): Boolean {
        uiState.value.skipApprovalReasons.forEach {
            if (it.isSelected && it.reason.inputType == HotelCorpApproverActionDialogFragmentVM.TEXTBOX && it.comment.isEmpty()) {
                updateUiState(uiState.value.copy(showMandatoryError = true))
                return true
            }
        }
        return false
    }
}

data class HotelCorpSkipApprovalReasonsBSUiState(
    val title: String = ResourceProvider.instance.getString(R.string.htl_label_skip_approval_reasons_title),
    val skipApprovalReasons: List<ReasonSelectionItemState> = emptyList(),
    val showMandatoryError: Boolean = false,
    val specialMessage: String = ResourceProvider.instance.getString(R.string.htl_label_skip_approval_reasons_message),
    val showSpecialMessage: Boolean = true,
    val actionCTAText: String = ResourceProvider.instance.getString(R.string.htl_corp_approval_skip_approval_cta_text)
)

data class ReasonSelectionItemState(
    val reason: CorpReasons,
    val comment: String,
    val showDivider: Boolean,
    val isSelected: Boolean
)