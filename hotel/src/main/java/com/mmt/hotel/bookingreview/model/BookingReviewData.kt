package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.base.model.tracking.HotelBaseTrackingData
import com.mmt.hotel.common.data.HotelUserRatingData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomCriteriaV2
import com.mmt.hotel.common.model.tracking.LocusTrackingData
import com.mmt.auth.login.model.Employee
import com.mmt.hotel.bookingreview.model.request.AddOnRequestData
import com.mmt.hotel.dayuse.model.request.SlotAvailRequestData
import com.mmt.hotel.detail.model.response.FlexiCheckinTimeSlotInfo
import com.mmt.hotel.filterV2.model.response.FilterV2
import kotlinx.parcelize.Parcelize

/**
 * This class will contains all the data, that needs to transfer from SelectRoom to BookingReview
 * */

@Parcelize
data class BookingReviewData(
    val userSearchData: UserSearchData,
    val payMode: String,
    val expData: String,
    val searchType: String,
    val userAppliedCoupon: String?,
    var roomCriteria: List<RoomCriteriaV2>,
    val userRatingData: HotelUserRatingData? = null,
    var flexibleCheckinSelectedTimeSlotInfo: FlexiCheckinTimeSlotInfo? = null,
    val pricingKey: String? = EMPTY_STRING,
    val locusTrackingData: LocusTrackingData? = null,
    var corpPrimaryTraveller: List<Employee>? = null,
    val preApprovedValidity: Long? = null,
    val hotelBaseTrackingData: HotelBaseTrackingData,
    val quickReview: Boolean = false,
    var scrollToTravellerCard: Boolean = false,
    val personalCorpBooking: Boolean = false,
    var duration: Int = 0,
    var dayUseSlotRequestData: SlotAvailRequestData? = null,
    var promoConsentProvided: Boolean = false,
    var checkUpgrade: Boolean = false,
    val addOnDetail: Map<String, AddOnRequestData>? = null,
    val isDom: Boolean, // added for tracking only don't use for other purpose
    val hotelType: String? = EMPTY_STRING,
    val guestHouseAvailable: Boolean? = null,
    val maskedPropertyName: Boolean? = null,
    val appliedFilters: List<FilterV2>?=null,
    val searchContext:String? = null
) : Parcelable