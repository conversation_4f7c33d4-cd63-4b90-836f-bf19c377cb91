package com.mmt.hotel.bookingreview.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.response.CorpAutoBookRequestorConfig
import com.mmt.hotel.bookingreview.ui.corp.HotelCorpApprovalRequestReconfirmationFragment
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpApprovalRequestReconfirmationViewModel
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap
import javax.inject.Qualifier

/**
 * This class is created to provide hilt dependency in @HotelCorpApprovalRequestReconfirmationFragment in case of QuickReviewActivity
 * will delete this when HotelBookingReview will be migrated to hilt
 */

@Module
@InstallIn(FragmentComponent::class)
open class HotelCorpApprovalRequestReconfirmationFragmentModule {
    @Provides
    @IntoMap
    @ViewModelKey(HotelCorpApprovalRequestReconfirmationViewModel::class)
    fun provideApprovalRequestReconfirmationModel(@QuickReviewRequestorConfig data: CorpAutoBookRequestorConfig): HotelViewModel {
        return HotelCorpApprovalRequestReconfirmationViewModel(data)
    }

    @Provides
    @QuickReviewRequestorConfig
    fun provideCorpAutoBookRequestorConfig(fragment: Fragment): CorpAutoBookRequestorConfig {
        if(fragment is HotelCorpApprovalRequestReconfirmationFragment) {
            return fragment.arguments?.getParcelable(HotelCorpApprovalRequestReconfirmationFragment.TAG) ?: CorpAutoBookRequestorConfig(title = null,subTitle = null,cta = null)
        }
        return CorpAutoBookRequestorConfig(title = null,subTitle = null,cta = null)
    }
}

@Qualifier
@Retention(AnnotationRetention.RUNTIME)
annotation class QuickReviewRequestorConfig
