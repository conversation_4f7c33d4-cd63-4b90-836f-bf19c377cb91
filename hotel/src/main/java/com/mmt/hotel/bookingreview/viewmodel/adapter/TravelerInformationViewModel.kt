package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.dataModel.TravellerUiData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.helper.constants.PanCardState
import com.mmt.hotel.bookingreview.model.CoTravellerFragmentData
import com.mmt.hotel.bookingreview.model.TravellerDetailV2
import com.mmt.hotel.bookingreview.viewmodel.TravelerViewModel
import com.mmt.hotel.common.constants.ExperimentsHotel
import com.mmt.hotel.common.constants.GuestType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.NATIONALITY_FIELD
import com.mmt.hotel.common.helper.HTLLocalCacheManager
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.showNationality
import com.mmt.hotel.compose.review.uiModel.MultiTravelerInformationUiModel
import com.mmt.hotel.compose.review.uiModel.TravelerInformationUiModel
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.listingV2.event.HotelListingClickEvents

class TravelerInformationViewModel(
    val travellerUiData: TravellerUiData,
    val userSearchData: UserSearchData?,
    val eventStream: MutableLiveData<HotelEvent>,
    val flowEvent: ((HotelEvent) -> Unit)?
) : BaseObservable(), TravelerViewModel {

    var shouldReValidate = false

    private val _uiState: MutableState<TravelerInformationUiModel> = mutableStateOf(TravelerInformationUiModel.getDefaultState(travellerUiData, userSearchData?.occupancyData?.adultCount ?: 1))
    val uiState: State<TravelerInformationUiModel>
        get() = _uiState

    companion object {
        const val TRAVELLER_TYPE_SELF = "TRAVELLER_TYPE_SELF"
        const val TRAVELLER_TYPE_SOMEONE_ELSE = "TRAVELLER_TYPE_SOMEONE_ELSE"
    }


    init {
        if (_uiState.value.isMultiRoomTravelerSection) {
            createMultiRoomTravellerSection()
        }
        if(HTLLocalCacheManager.getTravellerInfo() != null){
            setDetailsFromCache()
        }else if(_uiState.value.isUserLoggedIn){
            setSelfDetail()
        }
    }

    private fun setDetailsFromCache() {

        HTLLocalCacheManager.getTravellerInfo()?.let {
            updateUiStateAndSaveInCache(
                isSelfTraveller = it.isSelfTraveller,
                isOtherTraveler = it.isOtherTraveler,
                showCountrySelection = it.showCountrySelection,
                countryEditable = it.countryEditable,
                title = it.title,
                name = it.name,
                surName = it.surName,
                isdCode = it.isdCode,
                contactNo = it.contactNo,
                emailId = it.emailId,
                nationality = it.nationality,
                saveDetailsInCache = false
            )
            eventStream.postValue(HotelEvent(HotelListingClickEvents.BOOKING_FOR_MYSELF, it.isSelfTraveller))
        }
    }

    private fun setSelfDetail() {
        // if "self" already selected, do not proceed further. That will reset the UserInputDetail and
        // any validation calculated (eg. on continue cta click) will be lost.
        if (_uiState.value.isSelfTraveller) return
        val loggedInUser = com.mmt.auth.login.util.LoginUtils.loggedInUser
        updateUiStateAndSaveInCache(
            isSelfTraveller = true,
            isOtherTraveler = false,
            showCountrySelection = showNationality(),
            countryEditable = loggedInUser?.nationality.isNullOrBlank(),
            title = HotelUtil.getTitle(loggedInUser?.title, loggedInUser?.gender),
            name = loggedInUser?.firstName ?: CoreConstants.EMPTY_STRING,
            surName = loggedInUser?.lastName ?: CoreConstants.EMPTY_STRING,
            isdCode = if (!loggedInUser?.primaryContactCountryCode.isNullOrBlank()) loggedInUser?.primaryContactCountryCode?.toInt() ?: HotelUtil.getIsdCodeForTravellerForm() else HotelUtil.getIsdCodeForTravellerForm(),
            contactNo = loggedInUser?.primaryContact ?: CoreConstants.EMPTY_STRING,
            emailId = loggedInUser?.emailId ?: CoreConstants.EMPTY_STRING,
            nationality = loggedInUser?.nationality ?: CoreConstants.EMPTY_STRING
        )
        if(_uiState.value.contactNo.isEmpty()) {
            flowEvent?.let { it(HotelEvent(HotelBookingReviewActivityEvent.HANDLE_TRAVELLER_INFO_INVALID_CONTACT_NUMBER)) }
        } else {
            flowEvent?.let { it(HotelEvent(HotelBookingReviewActivityEvent.HANDLE_TRAVELLER_INFO_CONTACT_NUMBER_CHANGED, _uiState.value.contactNo)) }
        }
        flowEvent?.let { it(HotelEvent(HotelListingClickEvents.BOOKING_FOR_MYSELF, true)) }
    }

    fun setOtherDetail() {
        if (_uiState.value.isOtherTraveler) return

        _uiState.value = TravelerInformationUiModel.getDefaultState(travellerUiData, userSearchData?.occupancyData?.adultCount ?: 1).copy(
            emailId = LoginUtils.loggedInUserEmail ?: CoreConstants.EMPTY_STRING,
            showCountrySelection = false,
            isSelfTraveller = false,
            isOtherTraveler = true,
            countryEditable = true,
        )
        flowEvent?.let { it(HotelEvent(HotelBookingReviewActivityEvent.HANDLE_TRAVELLER_INFO_INVALID_CONTACT_NUMBER)) }
        flowEvent?.let { it(HotelEvent(HotelListingClickEvents.BOOKING_FOR_MYSELF, false)) }
    }

    private fun createMultiRoomTravellerSection() {
        val items = ResourceProvider.instance.getStringArray(R.array.TRAVELLER_TITLE)
        val multiRoomUserInputDetail = mutableListOf<MultiTravelerInformationUiModel>()
        for (i in 1 until travellerUiData.roomCount) {
            multiRoomUserInputDetail.add(MultiTravelerInformationUiModel(title = items[0]))
        }
    }

    fun updateMultiRoomTravellerTitle(title: String, position: Int) {
        val multiRoomUserInputDetail = _uiState.value.multiRoomTravelerList.toMutableList()
        multiRoomUserInputDetail[position] = multiRoomUserInputDetail[position].copy(title = title)
        updateUiStateAndSaveInCache(multiRoomTravelerList = multiRoomUserInputDetail)
    }

    fun updateMultiRoomTravellerName(name: String, position: Int) {
        val multiRoomUserInputDetail = _uiState.value.multiRoomTravelerList.toMutableList()
        multiRoomUserInputDetail[position] = multiRoomUserInputDetail[position].copy(name = name)
        updateUiStateAndSaveInCache(multiRoomTravelerList = multiRoomUserInputDetail)
    }

    fun updateMultiRoomTravellerSurname(surname: String, position: Int) {
        val multiRoomUserInputDetail = _uiState.value.multiRoomTravelerList.toMutableList()
        multiRoomUserInputDetail[position] = multiRoomUserInputDetail[position].copy(surName = surname)
        updateUiStateAndSaveInCache(multiRoomTravelerList = multiRoomUserInputDetail)
    }

    fun isMultiRoomTravellerNameValid(position: Int): Boolean {
        val result: Boolean
        val name = _uiState.value.multiRoomTravelerList[position].name
        val multiRoomUserInputDetail = _uiState.value.multiRoomTravelerList.toMutableList()
        if (name.trim().isEmpty()) {
            result = false
            multiRoomUserInputDetail[position] = multiRoomUserInputDetail[position].copy(nameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
            updateUiStateAndSaveInCache(multiRoomTravelerList = multiRoomUserInputDetail)
        } else {
            result = HotelUtil.validateRegEx(
                name,
                HotelConstants.FIRST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(name, HotelConstants.DOT_AND_SPACE_REGEX)
            if (!result) {
                multiRoomUserInputDetail[position] = multiRoomUserInputDetail[position].copy(nameError = ResourceProvider.instance.getString(R.string.htl_name_error))
                updateUiStateAndSaveInCache(multiRoomTravelerList = multiRoomUserInputDetail)
            }
        }
        return result
    }

    fun isMultiRoomTravellerSurnameValid(position: Int): Boolean {
        val result: Boolean
        val surname = _uiState.value.multiRoomTravelerList[position].surName
        val multiRoomUserInputDetail = _uiState.value.multiRoomTravelerList.toMutableList()
        if (surname.trim().isEmpty()) {
            result = false
            multiRoomUserInputDetail[position] = multiRoomUserInputDetail[position].copy(surNameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
            updateUiStateAndSaveInCache(multiRoomTravelerList = multiRoomUserInputDetail)
        } else {
            result = HotelUtil.validateRegEx(
                surname,
                HotelConstants.LAST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(surname, HotelConstants.DOT_AND_SPACE_REGEX)
            if (!result) {
                multiRoomUserInputDetail[position] = multiRoomUserInputDetail[position].copy(surNameError = ResourceProvider.instance.getString(R.string.htl_name_error))
                updateUiStateAndSaveInCache(multiRoomTravelerList = multiRoomUserInputDetail)
            }
        }
        return result
    }

    fun startLogin() {
        if(userSearchData?.funnelSrc == HotelFunnel.DAYUSE.funnelValue) {
            flowEvent?.let { it(HotelEvent(HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY, null)) }
        } else {
            eventStream.value = HotelEvent(HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY, null)
        }
    }

    fun updateSelectedCountry(nationality: String) {
        updateUiStateAndSaveInCache(nationality = nationality, nationalityError = "")
    }

    fun openCountriesListScreen() {
        if (_uiState.value.countryEditable) eventStream.postValue(HotelEvent(HotelBookingReviewActivityEvent.OPEN_COUNTRY_SELECTION_SCREEN,true))
    }

    fun updateTitle(title: String) {
        updateUiStateAndSaveInCache(title = title)
    }

    fun updateName(name: String) {
        updateUiStateAndSaveInCache(name = name)
    }

    fun updateSurname(surname: String) {
        updateUiStateAndSaveInCache(surName = surname)
    }

    fun changeTravellerType(isSelfTraveller: Boolean) {
        if (isSelfTraveller) {
            setSelfDetail()
        } else {
            setOtherDetail()
        }
    }

    fun updateEmailId(email: String) {
        updateUiStateAndSaveInCache(emailId = email)
    }

    fun updateIsdCode(isdCode: Pair<Int, String>) {
        updateUiStateAndSaveInCache(isdCode = isdCode.first)
        eventStream.postValue(HotelEvent(HotelBookingReviewActivityEvent.HANDLE_TRAVELLER_INFO_COUNTRY_CODE_CHANGED, isdCode))
    }

    fun updateContactNo(mobile: String) {
        updateUiStateAndSaveInCache(contactNo = mobile)
        if(isContactNoValid()){
            eventStream.postValue(HotelEvent(HotelBookingReviewActivityEvent.HANDLE_TRAVELLER_INFO_CONTACT_NUMBER_CHANGED, mobile))
        } else {
            eventStream.postValue(HotelEvent(HotelBookingReviewActivityEvent.HANDLE_TRAVELLER_INFO_INVALID_CONTACT_NUMBER))
        }
    }

    fun updateGstStatus(checked: Boolean) {
        updateUiStateAndSaveInCache(gstSelected = checked)
    }

    fun updateGstRegisteredNo(number: String) {
        updateUiStateAndSaveInCache(registrationNo = number)
    }

    fun updateGstRegisteredCompany(company: String) {
        updateUiStateAndSaveInCache(registeredCompany = company)
    }

    fun updateGstRegisteredCompanyAddress(address: String) {
        updateUiStateAndSaveInCache(registeredCompanyAddress = address)
    }

    fun showCoTraveller(cotravellers: List<CoTraveller>) {
        updateUiStateAndSaveInCache(coTravelerList = cotravellers, hasCoTraveler = cotravellers.isNotEmpty())
    }

    fun addGuest() {
        eventStream.value = HotelEvent(
            HotelBookingReviewActivityEvent.OPEN_CO_TRAVELLER_FRAGMENT,
            CoTravellerFragmentData(travellerType = if (_uiState.value.isSelfTraveller) TRAVELLER_TYPE_SELF else TRAVELLER_TYPE_SOMEONE_ELSE, coTravellerList = _uiState.value.coTravelerList)
        )
    }

    fun removeGuest(coTravellerItem: CoTraveller) {
        val travellers = _uiState.value.coTravelerList.toMutableList()
        val iterator = travellers.iterator()

        while (iterator.hasNext()) {
            val itemData = iterator.next()
            if (itemData == coTravellerItem) {
                iterator.remove()
            }
        }
        updateUiStateAndSaveInCache(coTravelerList = travellers, hasCoTraveler = travellers.isNotEmpty())
    }

    fun getMultiTravelerInformationHeader(position: Int): String {
        var nameHeader = CoreConstants.EMPTY_STRING
        nameHeader += ResourceProvider.instance.getString(
            R.string.htl_multi_room_traveller_header_item,
            position
        )
        if (travellerUiData.foreignTravel) {
            nameHeader += ResourceProvider.instance.getString(R.string.htl_name_header_intl_travel)
        } else {
            nameHeader += ResourceProvider.instance.getString(R.string.htl_name_header)
        }
        return nameHeader
    }



    fun revalidate() {
        if (shouldReValidate) {
            validateTravellerData()
            shouldReValidate = false
        }
    }

    fun validateTravellerData(): Boolean {
        var result = validateTravellerDetail()
        if (_uiState.value.gstSelected && !travellerUiData.isDayUseFunnel) {
            result = validateGstDetail() && result
        }
        if(_uiState.value.isMultiRoomTravelerSection) {
                for (userDetails in _uiState.value.multiRoomTravelerList){
                    userDetails.run {
                        result = isNameValid() && result
                        result = isSurnameValid() && result
                    }
                }
        }
        return result
    }

    private fun validateTravellerDetail(): Boolean {
        var result = isNameValid()
        result = isSurnameValid() && result
        result = isContactNoValid() && result
        result = isCountrySelected() && result
        result = isEmailIdValid() && result
        result = isPanValid() && result
        return result
    }

    private fun validateGstDetail(): Boolean {
        var result = isNumberValid()
        result = isCompanyNameValid() && result
        result = isAddressValid() && result
        return result
    }

    fun isNameValid(): Boolean {
        val result: Boolean
        if (_uiState.value.name.trim().isEmpty()) {
            result = false
            updateUiStateAndSaveInCache(nameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
        } else {
            result = HotelUtil.validateRegEx(
                _uiState.value.name,
                HotelConstants.FIRST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(_uiState.value.name, HotelConstants.DOT_AND_SPACE_REGEX)
            if (!result) updateUiStateAndSaveInCache(nameError = ResourceProvider.instance.getString(R.string.htl_name_error)) else updateUiStateAndSaveInCache(nameError = "")
        }
        return result
    }

    fun isSurnameValid(): Boolean {
        val result: Boolean
        if (_uiState.value.surName.trim().isEmpty()) {
            result = false
            updateUiStateAndSaveInCache(surNameError = ResourceProvider.instance.getString(R.string.htl_name_empty))
        } else {
            result = HotelUtil.validateRegEx(
                _uiState.value.surName,
                HotelConstants.LAST_NAME_REGEX
            ) && !HotelUtil.validateRegEx(
                _uiState.value.surName,
                HotelConstants.DOT_AND_SPACE_REGEX
            )
            if (!result) updateUiStateAndSaveInCache(surNameError = ResourceProvider.instance.getString(R.string.htl_name_error)) else updateUiStateAndSaveInCache(surNameError = "")
        }
        return result
    }

    fun isContactNoValid(validateOnly : Boolean = false): Boolean {
        val validation = HotelUtil.isContactNoValid(_uiState.value.isdCode, _uiState.value.contactNo)
        if(!validateOnly){
            // Do not update UI if only validation case
            updateUiStateAndSaveInCache(contactNoErrorMsg = validation.second, saveDetailsInCache = false)
        }
        return validation.first
    }

    fun isCountrySelected(): Boolean {
        if (!_uiState.value.showCountrySelection || ExperimentsHotel.nationality.getPokusValue() == NATIONALITY_FIELD.OPTIONAL.value)
            return true
        val result: Boolean = _uiState.value.nationality.isNotBlank()
        updateUiStateAndSaveInCache(nationalityError = if (result) "" else ResourceProvider.instance.getString(R.string.country_selection_error_message))
        return result
    }

    fun isEmailIdValid(validateOnly : Boolean = false): Boolean {
        val result: Boolean
        if (_uiState.value.emailId.isEmpty()) {
            result = false
            if(!validateOnly){
                // Do not update UI if only validation case
                updateUiStateAndSaveInCache(emailError = ResourceProvider.instance.getString(R.string.htl_email_empty), saveDetailsInCache = false)
            }
        } else {
            result = LoginUtil.checkEmail(_uiState.value.emailId)
            if(!validateOnly){
                // Do not update UI if only validation case
                updateUiStateAndSaveInCache(emailError = if (result) "" else ResourceProvider.instance.getString(R.string.htl_email_error), saveDetailsInCache = false)
            }
        }
        return result
    }

    fun isPanValid(): Boolean {
        return when (travellerUiData.panState) {
            PanCardState.PAN_CARD_NOT_SHOWN, PanCardState.PAN_CARD_NO_VALIDATION -> true
            PanCardState.PAN_CARD_VALIDATION -> {
                val result: Boolean
                if (_uiState.value.pan.isEmpty()) {
                    result = false
                    updateUiStateAndSaveInCache(panError = ResourceProvider.instance.getString(R.string.htl_pan_empty))
                } else {
                    result = HotelUtil.validateRegEx(_uiState.value.pan, HotelConstants.PAN_REGEX)
                    updateUiStateAndSaveInCache(panError = if (result) "" else ResourceProvider.instance.getString(R.string.htl_pan_error))
                }
                return result
            }
        }
    }

    fun isNumberValid(): Boolean {
        val result: Boolean
        if(_uiState.value.registrationNo.isBlank()){
            result = false
            updateUiStateAndSaveInCache(registeredNoError = ResourceProvider.instance.getString(R.string.htl_gst_no_empty))
        } else {
            if (LoginUtils.getPreferredRegion().isGlobalEntity()) {
                result = StringUtil.isNotNullAndEmpty(_uiState.value.registrationNo)
                updateUiStateAndSaveInCache(
                    registeredNoError = if (result) "" else ResourceProvider.instance.getString(
                        R.string.htl_trn_no_error
                    )
                )
            } else {
                result = HotelUtil.validateRegEx(
                    _uiState.value.registrationNo,
                    HotelUtil.getGstNumberValidationRegex()
                )
                updateUiStateAndSaveInCache(
                    registeredNoError = if (result) "" else ResourceProvider.instance.getString(
                        R.string.htl_gst_no_error
                    )
                )
            }
        }
        return result
    }

    fun isCompanyNameValid(): Boolean {
        val result = _uiState.value.registeredCompany.isNotBlank()
        updateUiStateAndSaveInCache(registeredCompanyError = if(result) "" else  ResourceProvider.instance.getString(R.string.htl_gst_name_empty))
        return result
    }

    fun isAddressValid(): Boolean {
        val result = _uiState.value.registeredCompanyAddress.isNotEmpty()
        updateUiStateAndSaveInCache(registeredCompanyAddressError = if(result) "" else  ResourceProvider.instance.getString(R.string.htl_gst_addr_empty))
        return result
    }

    private fun updateUiStateAndSaveInCache(
        header: String = _uiState.value.header,
        isUserLoggedIn: Boolean = _uiState.value.isUserLoggedIn,
        title: String = _uiState.value.title,
        name: String = _uiState.value.name,
        surName: String = _uiState.value.surName,
        nameError: String = _uiState.value.nameError,
        surNameError: String = _uiState.value.surNameError,
        emailId: String = _uiState.value.emailId,
        emailError: String = _uiState.value.emailError,
        nationality: String = _uiState.value.nationality,
        nationalityError: String = _uiState.value.nationalityError,
        showCountrySelection: Boolean = _uiState.value.showCountrySelection,
        countryEditable: Boolean = _uiState.value.countryEditable,
        isdCode: Int = _uiState.value.isdCode,
        contactNo: String = _uiState.value.contactNo,
        contactNoErrorMsg: String = _uiState.value.contactNoErrorMsg,
        isSelfTraveller: Boolean = _uiState.value.isSelfTraveller,
        isOtherTraveler: Boolean = _uiState.value.isOtherTraveler,
        showPanCard: Boolean = _uiState.value.showPanCard,
        pan: String = _uiState.value.pan,
        panError: String = _uiState.value.panError,
        showPanTooltip: Boolean = _uiState.value.showPanTooltip,
        panRequiredMsg: String = _uiState.value.panRequiredMsg,
        gstHeader: String = _uiState.value.gstHeader,
        gstSelected: Boolean = _uiState.value.gstSelected,
        hotelGstAvailable: Boolean = _uiState.value.hotelGstAvailable,
        registrationNo: String = _uiState.value.registrationNo,
        registeredCompany: String = _uiState.value.registeredCompany,
        registeredCompanyAddress: String = _uiState.value.registeredCompanyAddress,
        registeredNoError: String = _uiState.value.registeredNoError,
        registeredCompanyError: String = _uiState.value.registeredCompanyError,
        registeredCompanyAddressError: String = _uiState.value.registeredCompanyAddressError,
        isMultiRoomTravelerSection: Boolean = _uiState.value.isMultiRoomTravelerSection,
        multiRoomTravelerList: List<MultiTravelerInformationUiModel> = _uiState.value.multiRoomTravelerList,
        canAddCoTraveler: Boolean = _uiState.value.canAddCoTraveler,
        hasCoTraveler: Boolean = _uiState.value.hasCoTraveler,
        coTravelerList: List<CoTraveller> = _uiState.value.coTravelerList,
        isDayUseFunnel: Boolean = _uiState.value.isDayUseFunnel,
        isForeignTravel: Boolean = _uiState.value.isForeignTravel,
        gstMissingMsg: String = _uiState.value.gstMissingMsg,
        saveDetailsInCache : Boolean = true
    ) {
        _uiState.value = _uiState.value.copy(
            header = header,
            isUserLoggedIn = isUserLoggedIn,
            title = title,
            name = name,
            surName = surName,
            nameError = nameError,
            surNameError = surNameError,
            emailId = emailId,
            emailError = emailError,
            nationality = nationality,
            nationalityError = nationalityError,
            showCountrySelection = showCountrySelection,
            countryEditable = countryEditable,
            isdCode = isdCode,
            contactNo = contactNo,
            contactNoErrorMsg = contactNoErrorMsg,
            isSelfTraveller = isSelfTraveller,
            isOtherTraveler = isOtherTraveler,
            showPanCard = showPanCard,
            pan = pan,
            panError = panError,
            showPanTooltip = showPanTooltip,
            panRequiredMsg = panRequiredMsg,
            gstHeader = gstHeader,
            gstSelected = gstSelected,
            hotelGstAvailable = hotelGstAvailable,
            registrationNo = registrationNo,
            registeredCompany = registeredCompany,
            registeredCompanyAddress = registeredCompanyAddress,
            registeredNoError = registeredNoError,
            registeredCompanyError = registeredCompanyError,
            registeredCompanyAddressError = registeredCompanyAddressError,
            isMultiRoomTravelerSection = isMultiRoomTravelerSection,
            multiRoomTravelerList = multiRoomTravelerList,
            canAddCoTraveler = canAddCoTraveler,
            hasCoTraveler = hasCoTraveler,
            coTravelerList = coTravelerList,
            isDayUseFunnel = isDayUseFunnel,
            isForeignTravel = isForeignTravel,
            gstMissingMsg = gstMissingMsg
        )

        if(saveDetailsInCache) saveInCache()
    }

    private fun saveInCache() {

        // validation on cached data
        val isContactNumValid = isContactNoValid(true)
        val isEmailValid = isEmailIdValid(true)
        val cachedData = _uiState.value.copy(
            emailId = if(isEmailValid) _uiState.value.emailId else CoreConstants.EMPTY_STRING,
            emailError = if(isEmailValid) _uiState.value.emailError else CoreConstants.EMPTY_STRING,
            contactNo = if(isContactNumValid) _uiState.value.contactNo else CoreConstants.EMPTY_STRING,
            contactNoErrorMsg = if(isContactNumValid) _uiState.value.contactNoErrorMsg else CoreConstants.EMPTY_STRING
        )

        // save traveller info in local cache
        HTLLocalCacheManager.saveTravellerInfo(cachedData)
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.TRAVELLER_DETAIL_INPUT
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as TravelerInformationViewModel).travellerUiData
        return travellerUiData == matchedWith
    }

    override fun cardName(): String {
        return "Review Traveller Detail"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.TRAVELLER_DETAIL_INPUT
    }

    fun updateDoubleBlackName(name: String, surname: String) {
        updateUiStateAndSaveInCache(name = name, surName = surname)
    }

    fun getTravellerDataForCheckout(): MutableList<TravellerDetailV2> {
        val travellerDetailList = mutableListOf<TravellerDetailV2>()
        val masterTravellerDetail = TravellerDetailV2(
            firstName = _uiState.value.name,
            lastName = _uiState.value.surName,
            masterPax = true,
            paxType = GuestType.ADULT.name,
            nationality = if (_uiState.value.nationality.isNotBlank()) _uiState.value.nationality else null,
            title = _uiState.value.title,
            emailID = _uiState.value.emailId,
            isdCode = _uiState.value.isdCode.toString(),
            mobileNo = _uiState.value.contactNo,
            panCard = _uiState.value.pan.ifEmpty { null },
            registerGstinNum = _uiState.value.registrationNo,
            gstinCompanyName = _uiState.value.registeredCompany,
            gstinCompanyAddress = _uiState.value.registeredCompanyAddress,
            roomNo = if(_uiState.value.isMultiRoomTravelerSection) 1 else null,
        )
        travellerDetailList.add(masterTravellerDetail)

        //add travellerDetails if leadPassengerMandatoryPerRoom is true and roomCount >1
        for (i in 0 until _uiState.value.multiRoomTravelerList.size){
            val userDetails = _uiState.value.multiRoomTravelerList[i]
            val travellerDetail = TravellerDetailV2(
                firstName = userDetails.name,
                lastName =  userDetails.surName,
                masterPax = false,
                paxType =   GuestType.ADULT.name,
                title = userDetails.title,
                roomNo =    i+2
            )
            travellerDetailList.add(travellerDetail)
        }
        //add travellerDetails if leadPassengerMandatoryPerRoom is true and roomCount >1

        for (coTraveller in _uiState.value.coTravelerList) {
            val coTravellerDetail = TravellerDetailV2(
                firstName = coTraveller.first_name.orEmpty(),
                lastName = coTraveller.last_name.orEmpty(),
                masterPax = false,
                paxType = if (coTraveller.pax_type == GuestType.CHILD.name) GuestType.CHILD.name else GuestType.ADULT.name,
                title = coTraveller.title
                    ?: HotelConstants.DEFAULT_TITLE,
                emailID = coTraveller.traveller_email,
                gender = coTraveller.gender,
                travellerId = coTraveller.travellerId
            )
            travellerDetailList.add(coTravellerDetail)
        }
        return travellerDetailList
    }

    fun updateProfileIfMissing(): Boolean{
        if(!_uiState.value.isSelfTraveller){
            return false
        }
        var needUpdation = false
        val loggedInUser = com.mmt.auth.login.util.LoginUtils.loggedInUser
        loggedInUser?.run {
            if(title.isNullOrEmpty()){
                needUpdation = true
                title = _uiState.value.title
            }
            if(firstName.isNullOrEmpty()){
                needUpdation = true
                firstName = _uiState.value.name
            }
            if(lastName.isNullOrEmpty()){
                needUpdation = true
                lastName = _uiState.value.surName
            }
        }
        return needUpdation
    }

    fun getTravellerTrackingData(): String? {
        var trackingData = ""
        trackingData += if (_uiState.value.isSelfTraveller) {
            "review_myself_select"
        } else {
                "review_someoneelse_select"
            }

        if(trackingData.isNotEmpty()){
            trackingData += CoreConstants.PIPE_SEPARATOR
        }
        trackingData += when (_uiState.value.gstSelected) {
            true -> "review_gst_select"
            false -> CoreConstants.EMPTY_STRING
        }
        if(trackingData.isNotEmpty()){
            trackingData += CoreConstants.PIPE_SEPARATOR
        }
        trackingData += when(travellerUiData.panState ){
            PanCardState.PAN_CARD_NOT_SHOWN -> {
                CoreConstants.EMPTY_STRING
            }
            PanCardState.PAN_CARD_VALIDATION -> {
                "pancard_shown"+"|"+"pancard_validation_req"
            }
            PanCardState.PAN_CARD_NO_VALIDATION -> {
                "pancard_shown"+"|"
                if(_uiState.value.pan.isNotEmpty()){
                    "pancard_true"
                } else {
                    "pancard_false"
                }
            }
        }
        return trackingData
    }

}