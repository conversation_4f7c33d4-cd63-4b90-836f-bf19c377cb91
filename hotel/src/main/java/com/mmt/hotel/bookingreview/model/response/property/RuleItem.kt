package com.mmt.hotel.bookingreview.model.response.property

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class RuleItem(
                    @SerializedName("title", alternate = ["subTitle"])
                    val title: String,
                    val headerTitle: String?=null,
                    val iconType: String?,
                    val iconUrl: String?,
                    var showIcon: Boolean = true,
                    var isFromBookingPage: Boolean = false) : Parcelable

@Parcelize
data class NavigationRuleItem(
    val id: String?,
    val categoryName: String,
    val categoryDesc: String
    ) : Parcelable