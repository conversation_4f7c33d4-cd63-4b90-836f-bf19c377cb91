package com.mmt.hotel.bookingreview.tracking

import com.gommt.logger.LogUtils
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper.OEPK_V_50
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.constant.CoreConstants.PIPE_SEPARATOR
import com.mmt.core.constant.CoreConstants.PIPE_SEPARATOR_WITH_SPACE
import com.mmt.core.constant.CoreConstants.UNDERSCORE
import com.mmt.core.extensions.addTrackText
import com.mmt.core.extensions.removeSpecialChars
import com.mmt.core.util.CoreUtil
import com.mmt.data.model.payment.PaymentType
import com.mmt.hotel.analytics.HotelOmnitureKeyConstants
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.BNPL_AVAILABLE
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.BNPL_LOADER_SHOWN
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.BNPL_NOT_AVAILABLE
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.BNPL_SELECTED
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.BPG_CARD_SHOWN
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.CANCELLATION_TYPE_PREFIX
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.FULL_PAYMENT_SELECTED
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.PART_PAYMENT_SELECTED
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.PART_PAYMENT_SHOWN
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.REVIEW_CHARITY_V2_SHOWN
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.SCROLL_BUTTON_CLICKED
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.SCROLL_BUTTON_SHOWN
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.TRIP_MONEY_BNPL_CACHE_VALIDATION_SUCCESS
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.TRIP_MONEY_BNPL_CARD_SHOWN
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.TRIP_MONEY_BNPL_CTA_CLIKCED
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.TRIP_MONEY_BNPL_RETRY_CLICKED
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.TRIP_MONEY_BNPL_VALIDATION_FAILURE
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.TRIP_MONEY_BNPL_VALIDATION_SUCCESS
import com.mmt.hotel.analytics.pdt.TrackingConstants
import com.mmt.hotel.analytics.pdt.TrackingConstants.UPSELL
import com.mmt.hotel.analytics.pdt.TrackingConstants.UPSELL_INTERACT
import com.mmt.hotel.analytics.pdt.TrackingConstants.UPSELL_SHOWN
import com.mmt.hotel.base.events.TrackEvent
import com.mmt.hotel.base.tracking.HotelBaseOmnitureTrackingHelper
import com.mmt.hotel.bookingreview.constants.BookingReviewCardIds.BPG_CARD_ID
import com.mmt.hotel.bookingreview.factory.ItemTypes
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.helper.HotelBookingReviewHelper
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.PRICE_DECREASE
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.PRICE_INCREASE
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.CHECKOUT_ERROR
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.PRICE_DOWN_REVIEW
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.PRICE_UP_REVIEW
import com.mmt.hotel.bookingreview.helper.constants.BookingTrackingConstants.Companion.REVIEW_DONATE_SELECT
import com.mmt.hotel.bookingreview.model.ReviewToThankyouTrackingData
import com.mmt.hotel.bookingreview.model.response.HotelTagInfo
import com.mmt.hotel.bookingreview.model.response.PaymentPlan
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelExpKeys
import com.mmt.hotel.common.constants.ScreenName
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.FlexiCancelAddonUtil
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.showTncConsent
import com.mmt.hotel.compose.review.helper.ReviewListDataHelper
import com.mmt.hotel.detail.event.constants.HotelDetailTrackingConstants
import com.mmt.hotel.userReviews.featured.events.HotelReviewEvents.PRICE_BOTTOM_PERSUASION_SHOWN
import com.mmt.hotel.userReviews.featured.events.HotelReviewEvents.TOP_SCARCITY_PERSUASION_SHOWN
import com.mmt.uikit.util.isNotNullAndEmpty
import javax.inject.Inject

class HotelBookingReviewTrackingHelper @Inject constructor(private val dataWrapper: BookingReviewDataWrapper,
                                                           private val bookingHelper: HotelBookingReviewHelper) : HotelBaseOmnitureTrackingHelper(){

    companion object {
        const val DOM_REVIEW_LOGIN_CLICKED = "mob:domestic:Hotels:review:page:login:clicked"
        const val INTL_REVIEW_LOGIN_CLICKED = "mob:intl:Hotels:review:page:login:clicked"
        const val MYBIZ_ASSURED_TRACKING = "myBizAssured"
        const val COUPON_PREAPPLIED_TRACKING = "preapply_review"
    }

    var isBNPLFooterShown = false

    fun getCommonEvents(): MutableMap<String, Any?> {
        var eventMap = mutableMapOf<String, Any?>()
        val userSearchData = dataWrapper.userSearchData ?: return eventMap
        val trackingData = dataWrapper.bookingReviewData?.hotelBaseTrackingData?:return eventMap
        eventMap = super.getCommonEvents(userSearchData, trackingData)

        if (bookingHelper.getPaymentType() == PaymentType.PAH) {
            eventMap[OmnitureTrackingHelper.OEPK_v52] = bookingHelper.getPayMode()
        }
        eventMap[OmnitureTrackingHelper.M_C8] = bookingHelper.getPayMode()

        val ratePlanProperty = getSupplierCode() +
                CoreConstants.PIPE_SEPARATOR + if (checkIfConfirmedLaterTariff()) HotelConstants.CONFIRMED_LATER_TARIFF else HotelConstants.CONFIRMED_INSTANT_TARIFF

        eventMap[OmnitureTrackingHelper.OEPK_V_40] = userSearchData.hotelName + "-" + userSearchData.locationName + CoreConstants.PIPE_SEPARATOR + ratePlanProperty
        eventMap[OmnitureTrackingHelper.OEPK_REVENUE_Products] = ";" + userSearchData.hotelId

        eventMap[OmnitureTrackingHelper.OEPK_V_42] = bookingHelper.getStarRating()
        eventMap[OmnitureTrackingHelper.OEPK_C_57] = bookingHelper.getPropertyType()
        eventMap[OmnitureTrackingHelper.OEPK_c23] = trackingData.previousPage.orEmpty()
        return eventMap
    }

    fun trackClicks(event: String) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.OEPK_C_54] = event
        dataWrapper.userSearchData?.let {
            track(eventMap, it)
        }
    }

    fun trackClicks(event: String, prop : String) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[prop] = event
        dataWrapper.userSearchData?.let {
            track(eventMap, it)
        }
    }

    fun trackMMTShownAndClick(trackText: String) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.OEPK_c1] = trackText
        dataWrapper.userSearchData?.let {
            track(eventMap, it)
        }
    }

    fun trackMyBizPlusAutoApplied() {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.PROP_54] = "myBizplus_added"
        dataWrapper.userSearchData?.let {
            track(eventMap, it)
        }
    }

    fun trackError(msg: String) {
        val userSearchData = dataWrapper.userSearchData!!
        val eventMap: MutableMap<String, Any?> = getCommonErrorEvents(userSearchData)
        eventMap[OmnitureTrackingHelper.OEPK_V_22] = msg
        track(eventMap, dataWrapper.userSearchData!!)
    }
    /* Base Methods end */

    /**
     * Called on avail api success - booking fragment load
     */
    fun pageLoadTrack(bookingRecyclerDataHelper: ReviewListDataHelper) {
        val userSearchData = dataWrapper.userSearchData!!
        val trackingData = dataWrapper.bookingReviewData?.hotelBaseTrackingData
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        addCommonPageLoadEvents(eventMap, userSearchData)
        eventMap[OmnitureTrackingHelper.TRACK_TID] = CoreUtil.getUniqueDeviceId()
        appendPriceData(eventMap)

        trackingData?.let { data ->
            if(data.cmpId.isNotNullAndEmpty()){
                eventMap[OmnitureTrackingHelper.m_v81] = data.cmpId
            }
            eventMap[OmnitureTrackingHelper.OEPK_m17] = data.position
        }

        eventMap[OmnitureTrackingHelper.OEPK_c7] = bookingHelper.getASPBucket()

        bookingHelper.getApplicableCoupon()?.let{
            eventMap[OmnitureTrackingHelper.OEPK_c1] = COUPON_PREAPPLIED_TRACKING + PIPE_SEPARATOR_WITH_SPACE + it
        }

        val mC54String = StringBuilder("")

        mC54String.append("pancard_").append(bookingHelper.getPanInfo()?.panCardRequired
                ?: false).append("|")

        mC54String.append("|DDA_").append(bookingHelper.getCdfDiscountAmount().toString())

        getPriceAlertMsg()?.let {
            mC54String.append(CoreConstants.PIPE_SEPARATOR).append(it)
        }
        getCancellationAlertMsg()?.let {
            mC54String.append(CoreConstants.PIPE_SEPARATOR).append(it)
        }

        dataWrapper.bookingReviewData?.hotelBaseTrackingData?.let {
            if( it.isHotelMyBizAssured ) {
                mC54String.append(CoreConstants.PIPE_SEPARATOR).append(MYBIZ_ASSURED_TRACKING)
            }
        }

        if (bookingHelper.isMMTValueStayHotel()) {
            mC54String.append(CoreConstants.PIPE_SEPARATOR).append(TrackingConstants.MMT_VALUE_STAYS_TAG)
        }
        val insuranceAddOn = bookingHelper.getAddOn(HotelBookingReviewHelper.INSURANCE_ADD_ON_TYPE)
        eventMap[OmnitureTrackingHelper.OEPK_C_54] = mC54String.toString()

        val evar37String = StringBuilder()

        evar37String.append(PIPE_SEPARATOR)
            .append(HotelExpKeys.TRANSFER_FEE_TEXT)
            .append(UNDERSCORE)
            .append(HotelUtil.TRUE.uppercase())

        eventMap[OmnitureTrackingHelper.OEPK_V_37] = evar37String.toString()

        var prop1Text = EMPTY_STRING
        var prop50Text = BookingReviewTrackingConstants.REVIEW_FOOTER_SHOWN

        // track insurance data
        if (!insuranceAddOn?.insuranceData?.insuranceItems.isNullOrEmpty()) {
            val insuranceText = StringBuilder(BookingTrackingConstants.INSURANCE_SHOWN)
            insuranceText.append(UNDERSCORE +insuranceAddOn?.insuranceData?.insuranceItems?.size)
            insuranceAddOn?.insuranceData?.insuranceItems?.forEach {
                insuranceText.append(UNDERSCORE +it.id)
            }
            prop1Text = insuranceText.toString()
        }

        bookingHelper.getFBPOfferType()?.let{
            prop1Text = prop1Text.addTrackText(it)
        }

        if (bookingHelper.isRequestToBookFlow()){
            prop1Text = prop1Text.addTrackText(TrackingConstants.RTB)
        }

        dataWrapper.availResponse?.let {

            // track cancellation timeline data
            it.payLaterTimeLineModel?.let {
                prop1Text = prop1Text.addTrackText(it.getCancellationPolicyTrackingText())
            }

            // track payment plan data
            it.paymentPlan?.let {
                prop1Text = prop1Text.addTrackText(getPaymentPlanTrackText(it, PART_PAYMENT_SHOWN))
            }

            it.addons?.find { addOn -> addOn.id == HotelBookingReviewHelper.CHARITY_ADD_ON_ID_V2}?.let {
                prop1Text = prop1Text.addTrackText(REVIEW_CHARITY_V2_SHOWN)
            }

            it.cards?.forEach {
                val trackText = getCardShownTrackText(it.cardInfo.cardId.orEmpty())
                trackText?.let {
                    prop1Text = prop1Text.addTrackText(it)
                }
            }

            if(it.bnplDetails?.bnplApplicable != null){
                if(it.bnplDetails?.bnplApplicable) {
                    prop1Text = prop1Text.addTrackText(BNPL_AVAILABLE)
                }else{
                    prop1Text = prop1Text.addTrackText(BNPL_NOT_AVAILABLE)
                }
            }else{
                prop1Text = prop1Text.addTrackText(BNPL_NOT_AVAILABLE)
            }

            if(it.ratePlanList?.isNotEmpty() == true){
                val cancellationTypesStr = it.ratePlanList.mapNotNull { rp -> rp.cancellationPolicy?.type }.joinToString("_")
                prop1Text = prop1Text.addTrackText(CANCELLATION_TYPE_PREFIX + cancellationTypesStr)
            }

            val priceAlert = it.alerts?.filter { it.type == PRICE_INCREASE || it.type == PRICE_DECREASE}?.firstOrNull()
            if(priceAlert != null){
                val priceReasonMap = priceAlert.initReasonsMap()
                prop1Text = prop1Text.addTrackText(priceAlert.type)
                var shortReasons = emptyList<String?>()
                priceAlert?.reasons?.forEach { key ->
                    shortReasons += if (priceReasonMap.containsKey(key)) {
                        priceReasonMap[key]
                    } else {
                        key
                    }
                }
                prop1Text = prop1Text.addTrackText(shortReasons.joinToString())
            }

            it.tcsInfo?.let {
                prop1Text = prop1Text.addTrackText("tcs_education_review_loaded")
            }

            if(showTncConsent()) {
                prop1Text = prop1Text.addTrackText("compliance_card_displayed")
            }

            it.chatBotWidgetInfo?.let { chatBotInfo ->
                prop1Text = prop1Text.addTrackText(String.format(HotelDetailTrackingConstants.CHAT_BOT_LOADED, chatBotInfo.chatBotType?:"", 2))
            }

            // Top Persuasion
            it.hotelInfo?.hotelTags.let { pers ->
                val topPersuasion = pers?.get(HotelTagPlaceholder.PC_HOTEL_TOP)
                if (topPersuasion?.type == HotelTagInfo.TYPE_SCARCITY) {
                    prop50Text = prop50Text.addTrackText(TOP_SCARCITY_PERSUASION_SHOWN)
                }
            }

            // Bottom Persuasion
            it.hotelPriceBreakUp.let { priceBreakup ->
                priceBreakup?.pricePersuasions?.get(HotelTagPlaceholder.PLACEHOLDER_PRICE_BOTTOM)
                    ?.let { bottomPersuasion ->
                        prop50Text = prop50Text.addTrackText(PRICE_BOTTOM_PERSUASION_SHOWN)
                    }
            }
        }

        eventMap[OmnitureTrackingHelper.OEPK_C_1] = prop1Text

        if(dataWrapper.userBlackInfo?.tierName != null && dataWrapper.userBlackInfo?.cardId?.contains("mmt_select") == true) {
            var userTier = CoreConstants.EMPTY_STRING
            if(dataWrapper.userBlackInfo?.tierName == null){
                userTier = "new"
            }else{
                userTier = dataWrapper.userBlackInfo?.tierName ?: EMPTY_STRING
            }
            prop50Text = prop50Text.addTrackText("MMTS_Review_card_${userTier}_user_shown")
        }

        var prop71Text = getLoadedCardsTrackingData(bookingRecyclerDataHelper.getListOfAvailableItemPositionMap())
        if(bookingHelper.tripMoneyBnplCardShown()) {
            prop71Text += "| ${TRIP_MONEY_BNPL_CARD_SHOWN}"
        }

        dataWrapper.availResponse?.hotelInfo?.flexibleCheckinInfo?.let {
            prop50Text = prop50Text.addTrackText(HotelDetailTrackingConstants.FLEXIBLE_CHECKIN_HOTEL)
        }

        eventMap[OmnitureTrackingHelper.OEPK_C_50] = prop50Text
        eventMap[OmnitureTrackingHelper.OEPK_C_71] = prop71Text
        eventMap[OmnitureTrackingHelper.OEPK_m93] = dataWrapper.expData
        eventMap[OmnitureTrackingHelper.OEPK_C_8] = getProp8TrackingText()

        dataWrapper.availResponse?.trackingMap?.forEach {
            if (eventMap.containsKey(it.key).not()) {
                eventMap[it.key] = it.value
            } else {
                eventMap[it.key] = eventMap[it.key].toString() + "|" + it.value
            }
        }
        HotelMigratorHelper.instance.updateSearchEvents(ScreenName.BOOKING_REVIEW, dataWrapper.userSearchData?.locationName, dataWrapper.userSearchData?.hotelId)
        trackPriceChange(eventMap)
        track(eventMap, userSearchData)
        checkForPageNameTrackingError()
    }

    private fun checkForPageNameTrackingError() {
        val userSearchData = dataWrapper.userSearchData
        val bookingReviewData = dataWrapper.bookingReviewData
        val correlationKey = dataWrapper.correlationKey

        if(userSearchData == null || bookingReviewData == null) {
            return
        }
        val trackingData = bookingReviewData.hotelBaseTrackingData.previousPage
        val countryCode = userSearchData.countryCode
        val isDom = bookingReviewData.isDom
        val isCountryCodeDom = HotelUtil.isDom(countryCode)
        if (isCountryCodeDom != isDom || userSearchData.locationName.isEmpty()) {
            LogUtils.error("HotelBookingReviewTrackingHelper", "${userSearchData}   previous page: $trackingData  correlationKey: $correlationKey")
        }
    }

    private fun getProp8TrackingText(): String {
        val pro8StringBuilder = StringBuilder()
        val trackingText = dataWrapper.availResponse?.trackingText
        if (trackingText.isNotNullAndEmpty()) {
            pro8StringBuilder.append(trackingText)
        }
        if (pro8StringBuilder.isNotEmpty()) {
            pro8StringBuilder.append(PIPE_SEPARATOR)
        }
        pro8StringBuilder.append("rating_" + (dataWrapper.bookingReviewData?.userRatingData?.userRating
                ?: 0.0).toString())
        if (pro8StringBuilder.isNotEmpty()) {
            pro8StringBuilder.append(PIPE_SEPARATOR)
        }
        pro8StringBuilder.append(FlexiCancelAddonUtil.getFlexiCancelTrackingText(dataWrapper.payLaterTimeLineModel, isFlexiCancelAvailable()))
        return pro8StringBuilder.toString()
    }

    private fun isFlexiCancelAvailable(): Boolean {
        return dataWrapper.upsellOptions?.any { it.addOnType == HotelConstants.ADD_ON_FLEXI_CANCEL } ?: false
    }



    private fun getCardShownTrackText(cardId: String): String? {
        return when(cardId) {
            BPG_CARD_ID -> BPG_CARD_SHOWN
            else -> null
        }
    }

    private fun trackPriceChange(eventMap: MutableMap<String, Any?>) {
        val rootLevelAlerts = bookingHelper.getRootLevelAlerts() ?: return
        var msg: String? = null
        for (alerts in rootLevelAlerts) {
            if (BookingAlertsConstants.PRICE_DECREASE.equals(alerts.type, true)) {
                msg = PRICE_UP_REVIEW
            } else if (BookingAlertsConstants.PRICE_INCREASE.equals(alerts.type, true)) {
                msg = PRICE_DOWN_REVIEW
            }
        }
        msg?.let {
            eventMap[OmnitureTrackingHelper.OEPK_V_22] = it + dataWrapper.userSearchData?.hotelId + "_supplier_" + getSupplierCode()
        }
    }

    fun trackSoldOutError(eventName: String) {
        trackError(eventName + dataWrapper.userSearchData?.hotelId + "_norm_supplier_" + getSupplierCode())
    }

    fun trackLoginClick() {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.OEPK_EVENT_118] = 1
        eventMap[OmnitureTrackingHelper.OEPK_C_54] = if (HotelUtil.isDom(dataWrapper.userSearchData!!.countryCode)) {
            DOM_REVIEW_LOGIN_CLICKED
        } else {
            INTL_REVIEW_LOGIN_CLICKED
        }
        track(eventMap, dataWrapper.userSearchData!!)
    }

    fun trackLoginOnSlide() {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.OEPK_C_1] = "login_option_clicked"
        track(eventMap, dataWrapper.userSearchData!!)
    }

    fun trackContinueClickV2(
        bookingRecyclerDataHelper: ReviewListDataHelper,
        isFromReviewCTAClick: Boolean = false,
        visibleCardIds: List<String>
    ) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        appendPriceData(eventMap)
        eventMap[OmnitureTrackingHelper.OEPK_event104] = 1
        if (isFromReviewCTAClick) {
            if (isBNPLFooterShown) {
                eventMap[OmnitureTrackingHelper.OEPK_C_50] = BookingReviewTrackingConstants.REVIEW_BNPL_FOOTER_CTA_CLICKED
            } else {
                eventMap[OmnitureTrackingHelper.OEPK_C_50] = BookingReviewTrackingConstants.REVIEW_FOOTER_CTA_CLICKED
            }
        }
        eventMap[OmnitureTrackingHelper.OEPK_C_54] = getPro54TrackingDataV2(bookingRecyclerDataHelper)
        eventMap[HotelOmnitureKeyConstants.PROP65] = visibleCardIds
        HotelMigratorHelper.instance.updateSearchEvents(ScreenName.PAYMENT, dataWrapper.userSearchData?.locationName, dataWrapper.userSearchData?.hotelId)
        track(eventMap, dataWrapper.userSearchData!!)
    }

    private fun getSupplierCode(): String {
        return dataWrapper.availResponse?.ratePlanList?.firstOrNull()?.supplierCode ?:  EMPTY_STRING
    }

    private fun getPro54TrackingDataV2(bookingRecyclerDataHelper: ReviewListDataHelper): String {
        val itemPositionMap = bookingRecyclerDataHelper.getListOfAvailableItemPositionMap()
        var pro54TrackingData = bookingRecyclerDataHelper.getTravellerTrackingData().orEmpty()
        pro54TrackingData += getVisibleItemsTrackingData(itemPositionMap)
        val specialRequests = getSpecialRequestsTrackingData()
        if (specialRequests.isNotNullAndEmpty()) {
            pro54TrackingData += specialRequests
        }
        if (bookingHelper.isCharityAddonIncludedInPrice()) {
            pro54TrackingData += "$REVIEW_DONATE_SELECT|"
        }
        return pro54TrackingData
    }

    private fun getLoadedCardsTrackingData(itemPositionMap: Map<String, Int>): String {
        return itemPositionMap.keys.toList().toString()
    }
    private fun getVisibleItemsTrackingData(itemPositionMap: Map<String, Int>) :String{
        var trackingData = EMPTY_STRING
        val lastCompletelyVisibleItem = bookingHelper.getLastVisibleItemPosition()

        // add international roaming card
        if (checkIfItemIsShown(itemPositionMap,lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.INTERNATIONAL_ROAMING_CARD)) {
            trackingData += "intl_roamingcard|"
        }

        // add propertyRuleShown
        if (checkIfItemIsShown(itemPositionMap,lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.PROPERTY_RULES)) {
            trackingData += "review_mustread_shown|"
        }

        // add additional charges
        if (checkIfItemIsShown(itemPositionMap,lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.ADDITIONAL_FEES)) {
            trackingData += "review_compcharges_shown|"
        }

        // add special request
        if (checkIfItemIsShown(itemPositionMap,lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.SPECIAL_REQUEST)) {
            trackingData += "review_request_shown|"
        }

        // add charity
        if (checkIfItemIsShown(itemPositionMap,lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.BOOKING_PRICE_BREAKUP)) {
            trackingData += "review_donate_shown|"
        }

        // add coupon
        if (checkIfItemIsShown(itemPositionMap,lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.COUPONS_DETAIL)) {
            trackingData += "review_coupon_shown|"
        }

        // add double black card
        if (checkIfItemIsShown(itemPositionMap,lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.DOUBLE_BLACK_CARD)) {
            trackingData += "review_doubleblackcard_shown|"
        }

        // add hotelCategories tag
        if (checkIfItemIsShown(itemPositionMap, lastCompletelyVisibleItem, BookingRecyclerAdapterItemKeys.PROPERTY_DETAIL)) {
            val appendedCategories = getHotelCategoriesList()
            if (appendedCategories.isNotEmpty()) {
                trackingData += appendedCategories
            }
        }
        return trackingData
    }

    private fun getHotelCategoriesList(): String {
        var appendedCategories = EMPTY_STRING
        val hotelCategories = bookingHelper.getHotelCategories()
        if (hotelCategories.isEmpty()) {
            return appendedCategories
        }
        for (category in hotelCategories) {
            appendedCategories += category.removeSpecialChars() + "|"
        }
        return appendedCategories
    }

    private fun checkIfItemIsShown(itemPositionMap: Map<String, Int>, lastCompletelyVisibleItem: Int, key: String): Boolean {
        val propertyRulesPosition = itemPositionMap[key]
                ?: Integer.MAX_VALUE
        if (propertyRulesPosition <= lastCompletelyVisibleItem) {
            return true
        }
        return false
    }

    private fun checkIfConfirmedLaterTariff(): Boolean {
        val rootLevelAlerts = bookingHelper.getRootLevelAlerts() ?: return false
        for (alerts in rootLevelAlerts) {
            if (BookingAlertsConstants.CONFIRMATION_POLICY.equals(alerts.type, true)) {
                return true
            }
        }
        return false
    }

    private fun getSpecialRequestsTrackingData(): String? {
        val specialRequestForm = bookingHelper.getSpecialRequest()
        if (specialRequestForm == null || specialRequestForm.categories?.isNullOrEmpty() == true) {
            return null
        }
        val categories = specialRequestForm.categories
        var specialRequests = EMPTY_STRING
        for(category in categories){
            if(!category.isSelected){
                continue
            }
            if(ItemTypes.INPUTBOX.equals(category.type,true)){
                specialRequests+= "request_custom|"
            }else{
                specialRequests+="request_${category.name.removeSpecialChars()}|"
            }
        }
        return specialRequests
    }

    fun trackPayOptionLoad(data: String) {
        trackClicks(data)
    }

    fun trackPayOptionContinue(data: String) {
        trackClicks(data)
    }

    fun trackHaveCouponCodeClick() {
        trackClicks(BookingTrackingConstants.HAVE_COUPON_CODE)
    }
    fun trackCorpAddTravellerError() {
        trackClicks("review_corp_add_traveller_error")
    }

    fun trackCorpPersonalBookingAddTravellerError() {
        trackClicks("review_corp_personal_booking_add_traveller_error")
    }

    fun trackTripTagFormError() {
        trackClicks("review_corp_trip_tag_form_error")
    }

    fun trackAddTravellerClick() {
        trackClicks("review_addguest_click")
    }

    fun trackAddCoTraveller() {
        trackClicks("review_newguest_add")
    }

    fun trackShowCoTraveller() {
        trackClicks("Additioinal_guest_added")
    }

    fun trackSkipDoubleBlackValidation(){
        trackClicks("review_doubleblack_continue")
    }

    fun trackModifyDoubleBlackName(){
        trackClicks("review_doubleblack_modify")
    }

    fun trackCheckoutError(errorCode: String) {
        val userSearchData = dataWrapper.userSearchData!!
        val eventMap: MutableMap<String, Any?> = getCommonErrorEvents(userSearchData)
        eventMap[OmnitureTrackingHelper.OEPK_V_22] = CHECKOUT_ERROR + errorCode
        eventMap[OmnitureTrackingHelper.OEPK_c23] = dataWrapper.bookingReviewData?.hotelBaseTrackingData?.previousPage.orEmpty()
        track(eventMap, userSearchData)
    }

    fun trackCheckoutUnknownError(){
        trackError("checkout failed")
    }

    private fun appendPriceData(eventMap: MutableMap<String, Any?>) {
        val event401 = bookingHelper.getDiscountAmount().toString()
        val event402 = bookingHelper.getSericeChargeAndCommissionAmount().toString()
        val event403 = bookingHelper.getWalletAmount().toString()
        val event404 = bookingHelper.getMmtServiceFee().toString()
        val rateInfo = (event401 + CoreConstants.PIPE_SEPARATOR + event402 + CoreConstants.PIPE_SEPARATOR + event403
                + CoreConstants.PIPE_SEPARATOR + event404 + CoreConstants.PIPE_SEPARATOR + bookingHelper.getTotalAmount().toString())
        eventMap[OmnitureTrackingHelper.OEPK_C_59] = rateInfo
        eventMap[OmnitureTrackingHelper.OEPK_event401] = event401
        eventMap[OmnitureTrackingHelper.OEPK_event402] = event402
        eventMap[OmnitureTrackingHelper.OEPK_event403] = event403
        eventMap[OmnitureTrackingHelper.OEPK_event404] = event404
    }

    private fun getPriceAlertMsg(): String? {
        val rootLevelAlerts = bookingHelper.getRootLevelAlerts() ?: return null
        var alertMessage : String? = null
        for (alerts in rootLevelAlerts) {
            if (BookingAlertsConstants.PRICE_DECREASE.equals(alerts.type, true)) {
                alertMessage = "hotel_price_increase"
            } else if (BookingAlertsConstants.PRICE_INCREASE.equals(alerts.type, true)) {
                alertMessage = "hotel_price_decrease"
            }
        }
        return alertMessage
    }

    private fun getCancellationAlertMsg(): String? {
        dataWrapper.availResponse?.ratePlanList?.let {
            for (ratePlan in it){
                ratePlan.roomAlerts?.let {
                    for(alert in it){
                        if (BookingAlertsConstants.CANCELLATION_POLICY.equals(alert.type, true)){
                            return "cancel_policy_change"
                        }
                    }
                }
            }
        }
        return null
    }


    /**
     * Create tracking objects to pass to thankYou screen in HotelLobInfo object
     */
    fun getThankYouTrackingData(): ReviewToThankyouTrackingData {
        return bookingHelper.getThankYouTrackingData()
    }

    override fun getScreenEvent(userSearchData: UserSearchData): String {
        return HotelUtil.getPageNameEvent(HotelConstants.PAGE_NAME_REVIEW, userSearchData.countryCode, userSearchData.funnelSrc).value
    }

    fun trackRTBEvents(event:String) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.OEPK_c1] = event
        track(eventMap, dataWrapper.userSearchData!!)
    }

    fun trackRTBWithBNPL(event:String) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.OEPK_c1] = event
        eventMap[OmnitureTrackingHelper.OEPK_C_47] = BNPL_SELECTED
        track(eventMap, dataWrapper.userSearchData!!)
    }

    fun trackRTBWithFullPayment(event:String) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[OmnitureTrackingHelper.OEPK_c1] = event
        eventMap[OmnitureTrackingHelper.OEPK_C_47] = FULL_PAYMENT_SELECTED
        track(eventMap, dataWrapper.userSearchData!!)
    }

    fun trackUpsellClicked(addOnType: String?= null) {
        if (!dataWrapper.isUpsellClickTracked) {
            val isFlexiCancel = addOnType == HotelConstants.ADD_ON_FLEXI_CANCEL
            val eventValue = if (isFlexiCancel) {
                val ratePlanType = if (isFreeCancellationTariff()) {
                    "FC"
                } else {
                    "NR"
                }
                UPSELL_INTERACT + "_FLX" + "_$ratePlanType"
            } else {
                UPSELL_INTERACT
            }
            trackEvent(eventValue, OmnitureTrackingHelper.OEPK_c1)
            dataWrapper.isUpsellClickTracked = true
        }
    }

    fun isFreeCancellationTariff(): Boolean {
        return dataWrapper.payLaterTimeLineModel?.timelinesV2?.isNotEmpty() == true
    }

    fun trackUpsellShown() {
        if (!dataWrapper.isUpsellShownTracked) {
            trackShownEvent(OmnitureTrackingHelper.OEPK_c1, UPSELL_SHOWN)
            dataWrapper.isUpsellShownTracked = true
        }
    }


    fun trackSelectedUpsell() {
        var eventName = EMPTY_STRING
        for ((index, upsellInfo) in bookingHelper.getUpsellOptions().withIndex()) {
            if (upsellInfo.isSelected) {
                eventName = "$UPSELL$UNDERSCORE${index+1}"
                break
            }
        }
        if (eventName.isEmpty()) return
        trackEvent(eventName, OmnitureTrackingHelper.OEPK_c1)
    }

    fun trackEvent(eventName: String, eventVariable: String) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventMap[eventVariable] = eventName
        track(eventMap, dataWrapper.userSearchData!!)
    }

    fun trackTravellerScrollButtonShown() {
        trackShownEvent(OmnitureTrackingHelper.OEPK_c1,SCROLL_BUTTON_SHOWN)
    }

    fun trackTravellerScrollButtonClicked() {
        trackEvent(SCROLL_BUTTON_CLICKED, OmnitureTrackingHelper.OEPK_c1)
    }

    fun trackCouponApplied(event: String) {
        trackEvent(event, OmnitureTrackingHelper.OEPK_c1)
    }

    fun trackTripMoneyBnplCtaClicked() {
        trackEvent(TRIP_MONEY_BNPL_CTA_CLIKCED,
            OmnitureTrackingHelper.OEPK_C_71)
    }

    fun trackTripMoneyValidationSuccess() {
        trackEvent(TRIP_MONEY_BNPL_VALIDATION_SUCCESS,
            OmnitureTrackingHelper.OEPK_C_71)
    }

    fun trackFullScreenBnpl0Loader(){
        val evar15String = BNPL_LOADER_SHOWN
        var eventMap = mutableMapOf<String, Any?>()
        eventMap[OmnitureTrackingHelper.OEPK_V_15] = evar15String
        dataWrapper.userSearchData?.let {
            track(eventMap, it)
        }
        trackEvent(BNPL_LOADER_SHOWN, OmnitureTrackingHelper.OEPK_C_1 )
    }

    fun trackTripMoneyValidationFailure() {
        trackEvent(TRIP_MONEY_BNPL_VALIDATION_FAILURE,
            OmnitureTrackingHelper.OEPK_C_71)
    }

    fun trackTripMoneyRetryCtaClicked() {
        trackEvent(TRIP_MONEY_BNPL_RETRY_CLICKED,
            OmnitureTrackingHelper.OEPK_C_71)
    }

    fun trackTripMoneyBnplCardShown() {
        trackEvent(TRIP_MONEY_BNPL_CARD_SHOWN,
            OmnitureTrackingHelper.OEPK_C_71)
    }

    fun trackTripMoneyCacheValidationSuccess() {
        trackEvent(TRIP_MONEY_BNPL_CACHE_VALIDATION_SUCCESS,
            OmnitureTrackingHelper.OEPK_C_71)
    }

    fun trackFullPaymentSelected() {
        trackEvent(FULL_PAYMENT_SELECTED, OmnitureTrackingHelper.OEPK_C_47)
    }

    fun trackPartPaymentSelected(paymentPlan: PaymentPlan) {
        trackEvent(getPaymentPlanTrackText(paymentPlan, PART_PAYMENT_SELECTED), OmnitureTrackingHelper.OEPK_c1)
    }

    private fun getPaymentPlanTrackText(paymentPlan: PaymentPlan, text: String): String {
        val stagesCount = (paymentPlan.paymentPolicy?.size ?: 0) + if (paymentPlan.text.isNotEmpty()) {
            1
        } else {
            0
        }
        return text + UNDERSCORE + stagesCount + UNDERSCORE + paymentPlan.text
    }

    fun trackShownEvent(key: String, value: String) {
        appendEventToPageExitWithCount(TrackEvent(key, value))
    }

    private fun trackXUserTypeEvent() {
        val xUserType = bookingHelper.getXUserType() ?: return
        appendPageExitEvent(TrackEvent(OmnitureTrackingHelper.OEPK_C_1, xUserType))
    }

    fun trackUpsellClick(eventProp1: String?, eventProp54: String?) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents()
        eventProp54?.let {
            eventMap[OmnitureTrackingHelper.OEPK_C_54] = it
        }
        eventProp1?.let {
            eventMap[OmnitureTrackingHelper.OEPK_C_1] = it
        }
        dataWrapper.userSearchData?.let {
            track(eventMap, it)
        }
    }


    fun trackPageExitEvent(items: List<String>? = null) {
        dataWrapper.userSearchData?.let {
            trackXUserTypeEvent()
            appendPageExitEvent(
                TrackEvent(
                    OmnitureTrackingHelper.OPEK_event313,
                    (bookingHelper.getPriceAfterDiscount() + bookingHelper.getTaxAmount()).toString()
                )
            )
            appendPageExitEvent(
                TrackEvent(
                    HotelOmnitureKeyConstants.PROP65,
                    items.toString()
                )
            )
            trackPageExitEvents(it)
        }
    }

    fun trackLongStayPersuasionShown(eventValue: String) {
        trackEvent(eventValue, OEPK_V_50)
    }

    fun trackEventsInProp44(eventValue: String) {
        trackEvent(eventValue, OmnitureTrackingHelper.OPEN_C_44)
    }
}