package com.mmt.hotel.bookingreview.viewmodel


import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RTBInfo
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

class HotelRTBConfirmationViewModel(var rtbCard: RTBInfo, eventStream: MutableLiveData<HotelEvent>):  DiffUtilRecycleItem {

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.RTB_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as HotelRTBConfirmationViewModel).rtbCard
        return rtbCard == matchedWith
    }

    override fun cardName(): String {
        return "RTB Confirmation Card"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.RTB_CONFIRMATION_CARD
    }
}