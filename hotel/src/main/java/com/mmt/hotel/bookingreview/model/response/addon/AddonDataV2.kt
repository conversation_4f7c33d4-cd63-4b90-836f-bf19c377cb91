package com.mmt.hotel.bookingreview.model.response.addon

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.bookingreview.model.response.addon.subscription.ExpandedViewData
import com.mmt.hotel.bookingreview.model.response.addon.subscription.SubscriptionData
import kotlinx.parcelize.Parcelize

@Parcelize
data class AddonDataV2(@SerializedName("addOnType")
                       val type: String = EMPTY_STRING,
                       val id: String = EMPTY_STRING,
                       @SerializedName("insuranceData")
                       val insuranceData: InsuranceData? = null,
                       val price: Double? = 0.0,
                       val alternateCurrencyPrice: Double? = 0.0,
                       val title: String? = null,
                       val tncUrl: String? = null,
                       val bucketId:String = EMPTY_STRING,
                       val validFrom:String? = null,
                       val descriptions: List<AddonDescription>? = null,
                       val autoSelect: Boolean = true,
                       val subscriptionCardData: SubscriptionData? = null,
                       val expandedViewData: ExpandedViewData? = null,
                       val description:String? = null,
                       val imageUrl:String? = null,
                       val bgColor:String? = null,
                       val ctaText:String? = null,
                       @SerializedName("items")
                       val addOnItems:List<AddonV2Item>? = null,
) : Parcelable