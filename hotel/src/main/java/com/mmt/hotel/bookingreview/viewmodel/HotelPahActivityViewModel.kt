package com.mmt.hotel.bookingreview.viewmodel

import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.viewModelScope
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.Utils.isNetworkAvailable
import com.mmt.hotel.R
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.DISMISS_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.DISMISS_WITH_ERROR
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.OPEN_THANK_YOU
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.SHOW_TOAST_MESSAGE
import com.mmt.hotel.bookingreview.helper.constants.OtpState
import com.mmt.hotel.bookingreview.model.PahIntentData
import com.mmt.hotel.bookingreview.model.request.AuthenticationDetail
import com.mmt.hotel.bookingreview.model.request.OtpDetail
import com.mmt.hotel.bookingreview.model.request.OtpValidationRequest
import com.mmt.hotel.bookingreview.repository.HotelPahPayRepository
import com.mmt.hotel.bookingreview.tracking.HotelPahPayTrackingHelper
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.util.HotelMigratorHelper
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import java.text.DecimalFormat
import javax.inject.Inject


class HotelPahActivityViewModel @Inject constructor(val pahData: PahIntentData,
                                                    private val repository: HotelPahPayRepository,
                                                    private val trackingHelper: HotelPahPayTrackingHelper) : HotelToolBarViewModel() {

    var showOtpUI = ObservableBoolean()
    var showProgressbar = ObservableBoolean(false)
    var otpBtnText = ObservableField(ResourceProvider.instance.getString(R.string.htl_otp_send_text))
    var otp = ObservableField(CoreConstants.EMPTY_STRING)
    var key: String? = null

    var otpStatus = ObservableField(OtpState.NOT_REQUESTED)
    var otpRequestCount = 0

    val decimalFormat = DecimalFormat("00")
    var timerText = ObservableField(CoreConstants.EMPTY_STRING)
    var errorText :ObservableField<SpannableStringBuilder?> = ObservableField()

    var countDownTimer: CountDownTimer? = null

    companion object{
        const val RESEND_SPAN_LENGTH = 6
        const val MAX_OTP_REQUEST_COUNT = 5
        const val TIMER_VALUE = 120000L
        const val TIMER_INTERVAL = 1000L
    }

    init {
        if(!pahData.forceShow
                && LoginUtils.isLoggedIn
                && LoginUtils.isMobileNumberVerified(getContactNo())){
            showOtpUI.set(false)
            makeCheckoutRequest(null)
        } else {
            showOtpUI.set(true)
        }
    }

    override fun getTitle(): String {
        return ResourceProvider.instance.getString(R.string.htl_pah_pay_title)
    }

    override fun onHandleBackPress() {
        updateEventStream(DISMISS_ACTIVITY,null)
    }

    override fun showCrossIcon(): Boolean {
        return false
    }

    fun getButtonBgStartColor(): Int{
        return ResourceProvider.instance.getColor(R.color.htl_booking_payment_bg_start)
    }

    fun getButtonBgEndColor(): Int{
        return ResourceProvider.instance.getColor(R.color.htl_booking_payment_bg_end)
    }

    fun getAmountText(): String{
        return ResourceProvider.instance.getString(R.string.htl_text_cost, pahData.currencySymbol, pahData.totalAmount)
    }

    fun getIsdCode(): String? {
        return pahData.checkoutData.travellerDetailList[0].isdCode
    }

    fun getContactNo(): String? {
        return pahData.checkoutData.travellerDetailList[0].mobileNo
    }

    fun getResendText(text: String): SpannableStringBuilder {
        val sb = SpannableStringBuilder()
        sb.append(text)
        val clickableSpan = object : ClickableSpan() {
            override fun onClick(widget: View) {
                otpStatus.set(OtpState.GENERATING)
                makeOtpGenerationRequest()
                trackingHelper.trackResendOtp()
            }
        }
        sb.setSpan(clickableSpan, sb.length - RESEND_SPAN_LENGTH, sb.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        sb.setSpan( ForegroundColorSpan(ResourceProvider.instance.getColor(R.color.header_color)), sb.length - RESEND_SPAN_LENGTH, sb.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return sb
    }

    fun handleOtpBroadcast(otpFromSms: String?) {
        if (isNetworkAvailable(MMTCore.mContext)) {
            if(otpStatus.get() == OtpState.GENERATED) {
                otp.set(otpFromSms.orEmpty())
            }
            cancelCountDownTimer()
        } else {
            updateEventStream(SHOW_TOAST_MESSAGE, ResourceProvider.instance.getString(R.string.htl_NETWORK_ERROR_MSG))
        }
    }

    fun onButtonClick(){
        when(otpStatus.get()){
            OtpState.NOT_REQUESTED, OtpState.NOT_GENERATED ->{
                otpStatus.set(OtpState.GENERATING)
                makeOtpGenerationRequest()
                trackingHelper.trackSendOtp()
            }
            OtpState.GENERATED, OtpState.INVALID -> {
                otpStatus.set(OtpState.VALIDATING)
                validateAndContinueBooking()
            }
            else ->{}
        }
    }

    fun makeOtpGenerationRequest(){
        errorText.set(null)
        otpRequestCount ++
        showProgressbar.set(true)
        otpStatus.set(OtpState.GENERATING)
        viewModelScope.launch {
            repository.makeOtpGenerationRequest(pahData)
                .catch {
                    if (otpRequestCount < MAX_OTP_REQUEST_COUNT) {
                        otpStatus.set(OtpState.NOT_GENERATED)
                        errorText.set(getResendText(ResourceProvider.instance.getString(R.string.htl_OTP_GENERATION_FAIL)))
                        otpBtnText.set(ResourceProvider.instance.getString(R.string.htl_otp_resend_text))
                        showProgressbar.set(false)
                    } else {
                        updateEventStream(DISMISS_WITH_ERROR, null)
                    }
                    trackingHelper.trackOtpGenerationFail()
                }
                .collect {
                    otpStatus.set(OtpState.GENERATED)
                    key = it.key
                    otpBtnText.set(ResourceProvider.instance.getString(R.string.htl_pah_review_button_text))
                    showProgressbar.set(false)
                    startCountDownTimer()
                }
        }
    }

    private fun validateAndContinueBooking(){
        if(otp.get().isNullOrEmpty()){
            updateEventStream(SHOW_TOAST_MESSAGE, ResourceProvider.instance.getString(R.string.htl_otp_empty_error_msg))
            return
        }
        showProgressbar.set(true)
        otpStatus.set(OtpState.VALIDATING)
        viewModelScope.launch {
            val otpValidationRequest = OtpValidationRequest(
                otp = otp.get(),
                key = key,
                countryCode = pahData.userSearchData.countryCode
            )
            repository.makeOtpValidationRequest(otpValidationRequest)
                .catch {
                    otpStatus.set(OtpState.INVALID)
                    showProgressbar.set(false)
                    trackingHelper.trackOtpSomethingWentWrong()
                }
                .collect {
                    otpStatus.set(OtpState.VALIDATED)
                    if (it.otpIsValid) {
                        makeCheckoutRequest(
                            OtpDetail(
                                OTP = otpValidationRequest.otp.orEmpty(),
                                key = key.orEmpty()
                            )
                        )
                    } else {
                        otp.set(CoreConstants.EMPTY_STRING)
                        otpStatus.set(OtpState.INVALID)
                        showProgressbar.set(false)
                        updateEventStream(
                            SHOW_TOAST_MESSAGE,
                            ResourceProvider.instance.getString(R.string.htl_OTP_VALIDATION_ERROR)
                        )
                        trackingHelper.trackInvalidOtp()
                    }
                }
        }
        trackingHelper.trackValidateOtp()
    }

    private fun makeCheckoutRequest(otpDetail: OtpDetail?){
        viewModelScope.launch {
            otpDetail?.let {
                pahData.checkoutData.authenticationDetail = AuthenticationDetail(otpDetail = it)
            }
            repository.makeCheckoutRequest(pahData.checkoutData)
                .catch {
                    updateEventStream(DISMISS_WITH_ERROR, null)
                }
                .collect { checkoutResponse ->
                    checkoutResponse.error?.let {
                        updateEventStream(DISMISS_WITH_ERROR, checkoutResponse)
                    } ?: kotlin.run {
                        updateEventStream(OPEN_THANK_YOU, checkoutResponse)
                    }
                }
        }
    }

    private fun startCountDownTimer() {
        countDownTimer = object : CountDownTimer(TIMER_VALUE, TIMER_INTERVAL) {
            override fun onFinish() {
                timerText.set(CoreConstants.EMPTY_STRING)
                if (otpRequestCount < MAX_OTP_REQUEST_COUNT) {
                    errorText.set(getResendText(ResourceProvider.instance.getString(R.string.htl_OTP_FETCH_FAIL)))
                    otpBtnText.set(ResourceProvider.instance.getString(R.string.htl_pah_review_button_text))
                } else {
                    updateEventStream(DISMISS_WITH_ERROR, null)
                }
                trackingHelper.trackSmsAutoFetchFail()
            }

            override fun onTick(millisUntilFinished: Long) {
                val minRemaining = decimalFormat.format(millisUntilFinished / (60 * 1000) % 60)
                val secRemaining = decimalFormat.format(millisUntilFinished / 1000 % 60)
                timerText.set("$minRemaining:$secRemaining")
            }
        }
        countDownTimer?.start()
    }

    fun cancelCountDownTimer(){
        countDownTimer?.cancel()
        timerText.set(CoreConstants.EMPTY_STRING)
    }

    fun trackPageLoad(){
        trackingHelper.pageLoadTrack()
    }

    override fun onCleared() {
        super.onCleared()
        cancelCountDownTimer()
    }
}