package com.mmt.hotel.bookingreview.ui.viewholder.corp

import android.view.LayoutInflater
import android.view.ViewGroup
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.bookingreview.viewmodel.adapter.corp.CorpAddTravellerSearchListViewModel
import com.mmt.hotel.databinding.CorpSearchEmployeeListBinding

class CorpSearchEmployeeVH(layoutInflater: LayoutInflater, layoutId: Int, parent: ViewGroup)
    : HotelRecyclerViewHolder<CorpSearchEmployeeListBinding, CorpAddTravellerSearchListViewModel>(layoutInflater, layoutId, parent) {
    override fun bindData(data: CorpAddTravellerSearchListViewModel, position: Int) {
        dataBinding.viewModel = data
        dataBinding.executePendingBindings()
    }
}
