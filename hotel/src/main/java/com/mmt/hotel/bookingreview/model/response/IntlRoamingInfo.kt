package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class IntlRoamingInfo(
    val title: String?,
    val iconUrl: String?,
    val planBenefits: ArrayList<PlanBenefit>?,
    val tncText: String?,
    val tncUrl: String?,
    val defaultConsentText: String?,
    val consentTextWithContact: String?,
    val planValidityInfo: String?,
    val shareConsentCtaText: String?,
    val removeConsentCtaText: String?
): Parcelable

@Parcelize
class PlanBenefit(
    val imageUrl: String?,
    val planInfoText: String?
): Parcelable