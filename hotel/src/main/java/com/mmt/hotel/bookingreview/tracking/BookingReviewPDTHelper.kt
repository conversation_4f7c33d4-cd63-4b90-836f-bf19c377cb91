package com.mmt.hotel.bookingreview.tracking

import com.mmt.analytics.AnalyticsSDK
import com.mmt.analytics.EventsType
import com.mmt.auth.login.model.userservice.MobileNumber
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.hotel.analytics.EventNames
import com.mmt.hotel.analytics.HotelCustomErrorCode
import com.mmt.hotel.analytics.pdt.HotelPdtTrackingHelperV2
import com.mmt.hotel.analytics.pdt.TrackingConstants
import com.mmt.hotel.analytics.pdt.events.HotelErrorGenericEvent
import com.mmt.hotel.analytics.pdt.events.HotelReviewEntryEvent
import com.mmt.hotel.analytics.pdt.events.HotelReviewPageExitEvent
import com.mmt.hotel.analytics.pdt.model.RoomSelectionModel
import com.mmt.hotel.analytics.pdt.model.TariffModel
import com.mmt.hotel.bookingreview.helper.HotelBookingReviewHelper
import com.mmt.hotel.bookingreview.helper.HotelBookingReviewHelper.Companion.INSURANCE_ADD_ON_TYPE
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants
import com.mmt.hotel.bookingreview.model.AddOnTrackingModel
import com.mmt.hotel.bookingreview.model.response.addon.AddonDataV2
import com.mmt.hotel.thankyou.SPACE
import com.gommt.logger.LogUtils
import com.mmt.auth.login.util.LoginUtils
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject


class BookingReviewPDTHelper @Inject constructor(val bookingReviewHelper: HotelBookingReviewHelper) :
    HotelPdtTrackingHelperV2() {

    companion object {
        const val TAG = "BookingReviewPDTHelper"
        const val HOTEL_REVIEW_PAGE = "Review"
        const val PAYMENTS_PAGE = "payments"
    }

    var sessionStartTime = System.currentTimeMillis()
    private val trackingMap = mutableMapOf<String, Any?>()

    suspend fun trackPageEnter() = withContext(Dispatchers.IO) {
        try {
            val bookingReviewData = bookingReviewHelper.getBookingReviewData() ?: return@withContext
            val userSearchData = bookingReviewData.userSearchData
            val event = HotelReviewEntryEvent(
                HOTEL_REVIEW_PAGE,
                EventsType.PDT_EVENT.id,
                sessionStartTime,
                CoreConstants.EMPTY_STRING,
                HOTEL_REVIEW_PAGE,
                bookingReviewHelper.getBookingReviewData()?.hotelBaseTrackingData?.prevFunnelStepPdt.orEmpty(),
                bookingReviewHelper.getBookingReviewData()?.hotelBaseTrackingData?.prevPageNamePdt.orEmpty()
            )
            event.hotelId = userSearchData.hotelId
            if (trackingMap.isEmpty()) {
                prepareTrackingMap()
            }
            putAllBasicHotelInfo(event.hotelGenericEvent, trackingMap)
            putCorrelationKey(event, trackingMap[TrackingConstants.KEY_CORRELATION] as String?)
            AnalyticsSDK.instance.trackEvent(event)
        } catch (e: Exception) {
            LogUtils.error(
                AnalyticsSDK.PDT_TRACKER,
                e
            )
        }
    }

    /*
    *   This method uses checkIfPriceAlertExists method for checking whether price related alert is present in api response or not
    *   if present, only then it will trackError
    * */
    suspend fun trackPriceChangeEvent() = withContext(Dispatchers.IO) {
        try {
            val errorMessagePair = checkIfPriceAlertExists() ?: return@withContext
            trackBookingPageErrors(
                bookingReviewHelper.getCorrelationKey(),
                errorMessagePair,
                bookingReviewHelper.getRatePlansAsString()
            )
        } catch (e: Exception) {
            LogUtils.error(
                AnalyticsSDK.PDT_TRACKER,
                e
            )
        }
    }

    /*
    *   Method to check if price alert is present or not and return Pair<String,String>
    *   based on availability otherwise null
    * */
    private fun checkIfPriceAlertExists(): Pair<String, String>? {
        val rootLevelAlerts = bookingReviewHelper.getRootLevelAlerts() ?: return null
        var amount = 0.0
        var isPriceIncreaseAlert = false
        var reason: String = EMPTY_STRING
        for (alerts in rootLevelAlerts) {
            val priceReasonMap = alerts.initReasonsMap()
            val shortReasons = emptyList<String?>().toMutableList()
            if (BookingAlertsConstants.PRICE_DECREASE.equals(alerts.type, true)) {
                alerts.reasons?.forEach{ key ->
                    shortReasons += if(priceReasonMap.containsKey(key)){
                        priceReasonMap[key]
                    }else{
                        key
                    }
                }
                amount = alerts.amount ?: 0.0
            } else if (BookingAlertsConstants.PRICE_INCREASE.equals(alerts.type, true)) {
                isPriceIncreaseAlert = true
                amount = alerts.amount ?: 0.0
                alerts.reasons?.forEach{ key ->
                    shortReasons += if(priceReasonMap.containsKey(key)){
                        priceReasonMap[key]
                    }else{
                        key
                    }
                }
            }
            reason = shortReasons.joinToString()
        }
        if (amount == 0.0) {
            return null
        }
        return if (isPriceIncreaseAlert) {
            Pair(
                HotelCustomErrorCode.PRICE_CHANGE_INCREASE.code+SPACE+reason,
                HotelCustomErrorCode.PRICE_CHANGE_INCREASE.message
            )
        } else {
            Pair(
                HotelCustomErrorCode.PRICE_CHANGE_DECREASE.code+SPACE+reason,
                HotelCustomErrorCode.PRICE_CHANGE_DECREASE.message
            )
        }
    }

    suspend fun trackBookingPageErrors(
        correlationKey: String?, errorCodeErrorMessagePair: Pair<String?, String?>,
        additionalInfo: String? = CoreConstants.EMPTY_STRING
    ) = withContext(Dispatchers.IO) {
        try {
            val bookingReviewData = bookingReviewHelper.getBookingReviewData() ?: return@withContext
            val userSearchData = bookingReviewData.userSearchData
            val event = HotelErrorGenericEvent(
                EventNames.ERROR_EVENT, HOTEL_REVIEW_PAGE, EventsType.PDT_EVENT.id,
                CoreConstants.EMPTY_STRING,
                HOTEL_REVIEW_PAGE,
                CoreConstants.EMPTY_STRING,
                CoreConstants.EMPTY_STRING
            )
            event.apply {
                hotelId = userSearchData.hotelId
                countryCode = userSearchData.countryCode
                initLocusTrackingData(bookingReviewData.locusTrackingData)
                this.correlationKey = correlationKey
                bindEventParams(
                    event = event.hotelGenericEvent,
                    userSearchData = userSearchData,
                    locusTrackingData = bookingReviewData.locusTrackingData,
                    roomCriteria = bookingReviewData.roomCriteria
                )
                extraContent = additionalInfo
                errorCode = errorCodeErrorMessagePair.first
                errorMessage = errorCodeErrorMessagePair.second
            }
            AnalyticsSDK.instance.trackEvent(event)
        } catch (e: java.lang.Exception) {
            LogUtils.error(
                AnalyticsSDK.PDT_TRACKER,
                e
            )
        }
    }

    fun trackPageExit(navigation:String) {
        try {
            val bookingReviewData = bookingReviewHelper.getBookingReviewData() ?: return
            val userSearchData = bookingReviewData.userSearchData
            val correlationKey = bookingReviewHelper.getCorrelationKey() ?: return
            val transactionKey = bookingReviewHelper.getTxnKey() ?: return
            val event = HotelReviewPageExitEvent(
                HOTEL_REVIEW_PAGE,
                EventsType.PDT_EVENT.id,
                sessionStartTime,
                CoreConstants.EMPTY_STRING,
                HOTEL_REVIEW_PAGE,
                bookingReviewHelper.getBookingReviewData()?.hotelBaseTrackingData?.prevFunnelStepPdt.orEmpty(),
                bookingReviewHelper.getBookingReviewData()?.hotelBaseTrackingData?.prevPageNamePdt.orEmpty()
            )
            event.bookingTransactionKey = transactionKey
            event.correlationKey = correlationKey
            event.hotelId = userSearchData.hotelId
            val checkOutData = bookingReviewHelper.getPaymentCheckoutData()
            checkOutData?.let {
                val paymentDetail = it.paymentDetail
                event.isBnplSelected = paymentDetail.isBNPL
            }

            val appliedCoupon = bookingReviewHelper.getAppliedCoupon()
            appliedCoupon?.let { coupon ->
                event.couponCode = coupon.couponCode
                event.couponAmt = coupon.amount.toFloat()
            }

            event.couponPreApplied = bookingReviewHelper.getAutoAppliedCouponCode()?.couponCode ?: ""


            event.selectedAddOnList = getAddOnData()
            event.payMode = bookingReviewHelper.getPayMode()
            event.navigation = navigation
            val charityAddOn = bookingReviewHelper.getCharityAddonData()
            charityAddOn?.let {
                val charityAmount = it.price ?: 0.0
                if (charityAmount > 0.0) {
                    event.charityAmt = charityAmount.toFloat()
                }
            }
            event.isCharityOpted = bookingReviewHelper.isCharityAddonIncludedInPrice()
            // put dayUseSlotDuration again in map as user can change it
            trackingMap[TrackingConstants.KEY_TOTAL_PRICE_TO_PAY] =
                bookingReviewHelper.getTotalAmount().toFloat()
            trackingMap[TrackingConstants.KEY_DAY_USE_SLOT_DURATION] = bookingReviewHelper.getDayUseSlotDuration()
            putAllBasicHotelInfo(event.hotelGenericEvent, trackingMap)

            val roomsDetailList: List<RoomSelectionModel> = getRoomsDetailList()
            event.roomSelectionModel = roomsDetailList
            event.isBookingForMySelf = bookingReviewHelper.isBookingForMySelf()
            AnalyticsSDK.instance.trackEvent(event)
        } catch (e: Exception) {
            LogUtils.error(
                AnalyticsSDK.PDT_TRACKER,
                e
            )
        }
    }

    private fun getAddOnData(): MutableList<AddOnTrackingModel>? {
        var addOnTrackingList : MutableList<AddOnTrackingModel>?=null
        val addOnList = bookingReviewHelper.getAddOnList()
        if (!addOnList.isNullOrEmpty()) {
            addOnTrackingList = mutableListOf<AddOnTrackingModel>()
            addOnList.forEach {
                if (it.type == HotelBookingReviewHelper.INSURANCE_ADD_ON_TYPE) {
                    getInsuranceTrackingList(it)?.let {
                        addOnTrackingList.addAll(it)
                    }
                } else {
                    val selectedAddOn = bookingReviewHelper.getSelectedAddOn(it.id)
                    val includedUnits = selectedAddOn?.unitSelected?.getTotalCount()
                    addOnTrackingList.add(
                            AddOnTrackingModel(addonType = it.type,
                                    currency = LoginUtils.getFunnelContextCurrencyCodeV1(),
                                    heading = it.title,
                                    id = it.id,
                                    includedUnits = includedUnits,
                                    selected = includedUnits != null && includedUnits > 0,
                                    unitPrice = it.price?.toInt(),
                                    unitType = "per person"
                            )
                    )
                }

            }
        }
        return addOnTrackingList
    }

    private fun getInsuranceTrackingList(insuranceAddOn: AddonDataV2): List<AddOnTrackingModel>? {
        return insuranceAddOn.insuranceData?.insuranceItems?.map {
            AddOnTrackingModel(addonType = INSURANCE_ADD_ON_TYPE,
                    currency = it.currency,
                    heading = it.heading,
                    id = it.id.toString(),
                    includedUnits = it.includedUnits,
                    selected = it.isSelected,
                    unitPrice = it.unitPrice,
                    unitType = it.unitType
            )
        }
    }

    private fun getRoomsDetailList(): List<RoomSelectionModel> {
        val roomSelectionModel: MutableList<RoomSelectionModel> = ArrayList()
        val roomCriteriaList =
            bookingReviewHelper.getRatePlanListFromAvail() ?: return roomSelectionModel
        for (roomCriteria in roomCriteriaList) {
            val model = RoomSelectionModel()
            model.roomCode = roomCriteria.roomCode
            val tariffModelList: MutableList<TariffModel> = ArrayList()
            val tariff = TariffModel()
            if (roomCriteria.mealCode.isNotNullAndEmpty()) {
                tariff.mealCode = roomCriteria.mealCode
            }
            tariff.payMode = bookingReviewHelper.getPayMode()
            tariff.ratePlanCode = roomCriteria.ratePlanCode
            tariffModelList.add(tariff)
            model.tariffList = tariffModelList
            roomSelectionModel.add(model)
        }
        return roomSelectionModel
    }

    private fun prepareTrackingMap() {
        val bookingReviewData = bookingReviewHelper.getBookingReviewData() ?: return
        trackingMap[TrackingConstants.KEY_LOCUS_TRACKING_DATA] = bookingReviewData.locusTrackingData
        trackingMap[TrackingConstants.KEY_USER_SEARCH_DATA] = bookingReviewData.userSearchData
        trackingMap[TrackingConstants.KEY_PROPERTY_TYPE] = bookingReviewHelper.getPropertyType()
        trackingMap[TrackingConstants.KEY_TOTAL_PRICE_TO_PAY] =
            bookingReviewHelper.getTotalAmount().toFloat()
        trackingMap[TrackingConstants.KEY_ROOM_CRITERIA] = bookingReviewData.roomCriteria
        trackingMap[TrackingConstants.KEY_STAR_RATING] = bookingReviewHelper.getStarRating()
        trackingMap[TrackingConstants.KEY_CORRELATION] = bookingReviewHelper.getCorrelationKey()
        trackingMap[TrackingConstants.KEY_DAY_USE_CHECK_IN_TIME] = bookingReviewHelper.getDayUseCheckInTime()
        trackingMap[TrackingConstants.KEY_DAY_USE_SLOT_DURATION] = bookingReviewHelper.getDayUseSlotDuration()
    }
}