package com.mmt.hotel.bookingreview.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.response.RatePlansUpgrade
import com.mmt.hotel.bookingreview.ui.HotelRatePlanUpgradeFragment
import com.mmt.hotel.bookingreview.viewmodel.RatePlanUpgradeFragmentViewModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class RatePlanUpgradeFragmentModule {

    @Provides
    @IntoMap
    @ViewModelKey(RatePlanUpgradeFragmentViewModel::class)
    fun provideRatePlanUpgradeViewModel(viewModel: RatePlanUpgradeFragmentViewModel): HotelViewModel = viewModel

    @Provides
    fun provideRatePlanUpgradeData(fragment: Fragment): RatePlansUpgrade {
        var data: RatePlansUpgrade? = null
        if (fragment is HotelRatePlanUpgradeFragment) {
            data = fragment.arguments?.getParcelable(HotelRatePlanUpgradeFragment.RATE_PLAN_UPGRADE_DATA) as? RatePlansUpgrade?
        }

        if (data != null) {
            return data
        } else throw RuntimeException ("Only HotelRatePlanUpgradeFragment can use RatePlansUpgrade")

    }
}