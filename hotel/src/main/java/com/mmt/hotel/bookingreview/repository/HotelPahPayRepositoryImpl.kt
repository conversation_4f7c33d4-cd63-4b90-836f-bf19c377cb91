package com.mmt.hotel.bookingreview.repository


import com.mmt.hotel.base.repository.HotelBaseRepository
import com.mmt.hotel.bookingreview.helper.BookingReviewRequestHelper
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.model.response.otp.OtpResponse
import com.mmt.hotel.bookingreview.model.CheckoutData
import com.mmt.hotel.bookingreview.model.PahIntentData
import com.mmt.hotel.bookingreview.model.request.CheckoutApiRequest
import com.mmt.hotel.bookingreview.model.request.OtpGenerateRequest
import com.mmt.hotel.bookingreview.model.request.OtpValidationRequest
import com.mmt.hotel.common.constants.HotelConstants
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject


class HotelPahPayRepositoryImpl @Inject constructor(private val bookingReviewRequestHelper: BookingReviewRequestHelper) : HotelBaseRepository(), HotelPahPayRepository {

    companion object {
        val OTP_BASE_URL = "$CLIENT_BACKEND_BASE_URL/clientbackend/entity/api/otp"
        val OTP_GENERATE_API = "$OTP_BASE_URL/generate"
        val OTP_VALIDATE_API = "$OTP_BASE_URL/validate"
        val CHECKOUT_API = "$CLIENT_GATEWAY_BASE_URL/payment-checkout/$client/$apiVersionCode"
    }

    override fun makeOtpGenerationRequest(pahData: PahIntentData): Flow<OtpResponse> {
        return makePostRequest(url = OTP_GENERATE_API,
                postData = getOtpGenerateRequest(pahData),
                countryCode = pahData.userSearchData.countryCode)
    }

    override fun makeOtpValidationRequest(otpValidationRequest: OtpValidationRequest): Flow<OtpResponse> {
        return makePostRequest(url = OTP_VALIDATE_API,
                postData = otpValidationRequest,
                countryCode = otpValidationRequest.countryCode)
    }

    /**
     * update server with booking data before moving to payment screen
     */
    override fun makeCheckoutRequest(checkoutData: CheckoutData): Flow<CheckoutResponse> {
        val checkoutApiRequest = getCheckoutRequest(checkoutData)
        return makePostRequest(url = CHECKOUT_API,
            postData = checkoutApiRequest,
            countryCode = checkoutData.countryCode,
            queryParams = getMandatoryQueryParams(checkoutApiRequest.requestDetails))
    }

    private fun getCheckoutRequest(checkoutData: CheckoutData): CheckoutApiRequest {
        return CheckoutApiRequest(
            transactionKey = checkoutData.transactionKey,
            requestDetails = bookingReviewRequestHelper.getRequestDetails(checkoutData),
            travellerDetails = checkoutData.travellerDetailList,
            paymentDetail = checkoutData.paymentDetail,
            authenticationDetail = checkoutData.authenticationDetail,
            specialRequest = checkoutData.specialRequest,
            workflowStatus = checkoutData.workflowStatus,
            currency = checkoutData.currency
        )
    }

    private fun getOtpGenerateRequest(pahData: PahIntentData): OtpGenerateRequest {
        pahData.checkoutData.travellerDetailList[0].run {
            return OtpGenerateRequest(
                    email = this.emailID,
                    mob = this.mobileNo,
                    isdCode = this.isdCode,
                    countryCode = pahData.userSearchData.countryCode
            )
        }
    }
}