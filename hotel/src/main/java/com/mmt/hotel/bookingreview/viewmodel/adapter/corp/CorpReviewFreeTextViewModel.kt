package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.CollectionUtil
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagFieldV2

class CorpReviewFreeTextViewModel(private val corpTripTagData: CorpTripTagFieldV2): HotelCorpTravelDetailFormItemViewModelV2(corpTripTagData, null) {

    fun getFreeText(): String {
        if(CollectionUtil.isNotNullAndEmpty(corpTripTagData.attributeSelectedValue)) {
            return corpTripTagData.attributeSelectedValue?.get(0) ?: CoreConstants.EMPTY_STRING
        }
        return CoreConstants.EMPTY_STRING
    }

    fun setFreeText(freeText: String) {
        val list: ArrayList<String>?
        freeText.trim()
        if (freeText.isEmpty()) {
            list = null
        } else {
            list = ArrayList()
            list.add(freeText)
        }
        corpTripTagData.attributeSelectedValue = list
        showErrorObservable.set(false)
    }

    fun removeError() {
        showErrorState.value = false
    }
}