package com.mmt.hotel.bookingreview.dataModel

import android.os.Parcelable
import com.mmt.hotel.old.model.hotelreview.response.specialRequest.Category
import kotlinx.parcelize.Parcelize

@Parcelize
data class SpecialRequestUiItemData(val title: String,
                                    val category: Category, // this node is added so that it can be passed directly in payment checkout
                                    var isSelected: Boolean = true) : Parcelable