package com.mmt.hotel.bookingreview.model.response.room

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class RoomInfo(val name: String, val size: String?,
                    val images: List<String>?,
                    val sleepingArrangements: List<RoomSleepingArrangement>?,
                    val totalRooms: Int = 0,
                    val maxGuestCount: Int = 0,
                    val maxChildCount: Int = 0,
                    val maxAdultCount: Int = 0

) : Parcelable