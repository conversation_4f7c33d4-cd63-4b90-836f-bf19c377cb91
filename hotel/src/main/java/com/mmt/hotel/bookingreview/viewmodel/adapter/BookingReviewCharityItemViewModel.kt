package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.EventType
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.BookingReviewPriceCardAdapterItem
import com.mmt.hotel.bookingreview.dataModel.CharityAddOnUIData
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent


class BookingReviewCharityItemViewModel(
    var data: CharityAddOnUIData,
    private val eventStream: MutableLiveData<HotelEvent>,
    private val eventLambda: ((HotelEvent) -> Unit)? = null
) : AbstractRecyclerItem {

    fun getCharityAmount(): String {
        return if (data.canShowAmount) {
            if (data.isIncludedInPrice) {
                data.includedAmount
            } else {
                data.excludedAmount
            }
        } else {
            CoreConstants.EMPTY_STRING
        }
    }

    fun getSelectionColor(): Int {
        return if (LoginUtils.isCorporateUser) {
            R.color.htl_mybiz_text_color
        } else {
            R.color.traveller_cta_color
        }
    }

    fun onCharityCheckBoxClicked() {
        val charityAddonEvent  = HotelEvent(
                HotelBookingReviewFragmentEvent.MAKE_CHARITY_ADDON_CALL,
                !data.isIncludedInPrice
        )
        eventLambda?.invoke(charityAddonEvent) ?: run {
            eventStream.postValue(charityAddonEvent)
        }
    }

    fun onInfoIconClick() {
        val bottomSheetEvent = HotelEvent(
            HotelBookingReviewFragmentEvent.SHOW_CHARITY_ADDON_BOTTOMSHEET,
            data, eventType = EventType.BOTTOM_SHEET
        )
        eventLambda?.invoke(bottomSheetEvent) ?: run {
            eventStream.postValue(bottomSheetEvent)
        }
    }

    fun onTAndCIconClick(link: String) {
        val webViewEvent = HotelEvent(HotelBookingReviewFragmentEvent.OPEN_WEB_VIEW, link, eventType = EventType.CLICK)
        eventLambda?.invoke(webViewEvent) ?: run {
            eventStream.postValue(webViewEvent)
        }
    }
    override fun getItemType(): Int {
        return BookingReviewPriceCardAdapterItem.ITEM_CHARITY
    }

}