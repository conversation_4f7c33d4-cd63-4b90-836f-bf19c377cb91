package com.mmt.hotel.bookingreview.viewmodel

import androidx.lifecycle.MutableLiveData
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.model.response.addon.InsuranceDataItem
import com.mmt.hotel.compose.review.dataModel.InsuranceDataItemInterface

/**
 * Created by sunil.jain on 01/06/21.
 */
class InsuranceExtraItemVM(
    private val insuranceItemList: List<InsuranceDataItem>,
    private val eventStream: MutableLiveData<HotelEvent>,
    val extraItemCount: Int
) : InsuranceDataItemInterface {
    val text = ResourceProvider.instance.getString(
        R.string.htl_insurance_extra_string,
        extraItemCount
    )

    fun onClicked() {
        eventStream.value = HotelEvent(
            HotelBookingReviewFragmentEvent.INSURANCE_EXTRA_ITEM_CLICKED,
            insuranceItemList
        )
    }
}