package com.mmt.hotel.bookingreview.viewmodel

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.hotel.bookingreview.adapter.HotelCoTravellerAdapterItem
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.ADD_CO_TRAVELLER
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.model.GuestInputDetail
import com.mmt.hotel.common.constants.GuestType

class CoTravellerAddViewModel(var eventStream: MutableLiveData<HotelEvent>, val dataWrapper: BookingReviewDataWrapper) : BaseObservable(), AbstractRecyclerItem {

    var guestInputDetail: GuestInputDetail

    init {
        guestInputDetail = GuestInputDetail()
    }

    override fun getItemType(): Int {
        return HotelCoTravellerAdapterItem.ADD_NEW_GUEST
    }

    fun saveGuest() {
        if (validateGuestInput()) {
            eventStream.value = HotelEvent(ADD_CO_TRAVELLER, getCoTravellerSavedViewModel())
            guestInputDetail.clear()
        }
    }

    fun getCoTravellerSavedViewModel(): CoTravellerSavedViewModel {
        val coTraveller = CoTraveller().apply {
            title = guestInputDetail.title
            first_name = guestInputDetail.name
            last_name = guestInputDetail.surname
            pax_type = if (guestInputDetail.child) GuestType.CHILD.name else GuestType.ADULT.name
        }
        dataWrapper.coTravellerList.add(0, coTraveller)
        val savedCoTravellerViewModel = CoTravellerSavedViewModel(eventStream, coTraveller)
        savedCoTravellerViewModel.updateCheckState(true)
        return savedCoTravellerViewModel
    }

    fun validateGuestInput(): Boolean {
        return guestInputDetail.run {
            var result = isNameValid()
            result = isSurnameValid() && result
            result
        }
    }
}