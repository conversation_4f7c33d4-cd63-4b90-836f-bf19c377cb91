package com.mmt.hotel.bookingreview.model.request

import androidx.annotation.StringDef

@Retention(AnnotationRetention.SOURCE)
@StringDef(
        value = [
            HotelBookingReviewRequestConstants.MMT_AUTH,
            HotelBookingReviewRequestConstants.CHANNEL,
            HotelBookingReviewRequestConstants.CVV_OPTIONAL
        ]
)
annotation class HotelBookingReviewRequestConstants() {
    companion object {
        const val MMT_AUTH = "mmtAuth"
        const val CHANNEL = "channel=Native&product=HotelsNew"
        const val CVV_OPTIONAL = "mandateOptionalCVV"
    }
}