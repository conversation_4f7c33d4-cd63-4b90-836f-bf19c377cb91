package com.mmt.hotel.bookingreview.viewmodel

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelCoTravellerAdapterItem
import com.mmt.hotel.bookingreview.adapter.HotelCoTravellerSubAdapter


class CoTravellerSavedHeaderViewModel(val itemList: MutableList<AbstractRecyclerItem>, var eventStream: MutableLiveData<HotelEvent>) : BaseObservable(), AbstractRecyclerItem {

    val adapter = HotelCoTravellerSubAdapter()

    override fun getItemType(): Int {
        return HotelCoTravellerAdapterItem.SAVED_GUEST_HEADER
    }
}