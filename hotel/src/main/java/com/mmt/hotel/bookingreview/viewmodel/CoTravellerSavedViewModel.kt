package com.mmt.hotel.bookingreview.viewmodel

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.hotel.bookingreview.adapter.HotelCoTravellerAdapterItem
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.DESELECT_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.SELECT_CO_TRAVELLER
import com.mmt.hotel.bookingreview.model.GuestInputDetail
import com.mmt.hotel.common.constants.GuestType
import com.mmt.hotel.common.constants.HotelConstants.DEFAULT_TITLE

class CoTravellerSavedViewModel(var eventStream: MutableLiveData<HotelEvent>,
                                var coTraveller: CoTraveller,
                                var showDivider: Boolean = false
) : BaseObservable(), AbstractRecyclerItem {
    var editState = false
    var checked = false
    lateinit var guestInputDetail: GuestInputDetail

    init {
        resetInputDetail()
    }

    override fun getItemType(): Int {
        return HotelCoTravellerAdapterItem.SAVED_GUEST
    }

    fun updateGuest() {
        if (validateGuestInput()) {
            coTraveller.apply {
                title = guestInputDetail.title
                first_name = guestInputDetail.name
                last_name = guestInputDetail.surname
                pax_type = if (guestInputDetail.child) GuestType.CHILD.name else GuestType.ADULT.name
            }
            editState = false
            notifyChange()
        }
    }

    fun validateGuestInput(): Boolean {
        return guestInputDetail.run {
            var result = isNameValid()
            result = isSurnameValid() && result
            result
        }
    }

    fun getCoTravellerCompleteName(): String {
        return coTraveller.first_name + " " + coTraveller.last_name
    }

    fun onEditClick() {
        editState = true
        notifyChange()
        guestInputDetail.notifyChange()
    }

    fun cancelEdit() {
        resetInputDetail()
        editState = false
        notifyChange()
    }

    fun onCheckClick() {
        eventStream.value = when (checked) {
            true -> HotelEvent(SELECT_CO_TRAVELLER, this)
            false -> HotelEvent(DESELECT_CO_TRAVELLER, this)
        }
    }

    fun updateCheckState(state: Boolean){
        checked = state;
        notifyChange()
    }

    fun resetInputDetail(){
        guestInputDetail = GuestInputDetail().also {
            it.title = coTraveller.title ?: DEFAULT_TITLE
            it.name = coTraveller.first_name.orEmpty()
            it.surname = coTraveller.last_name.orEmpty()
            it.child = coTraveller.pax_type?.equals(GuestType.CHILD.name) ?: false
        }
        guestInputDetail.notifyChange()
    }
}