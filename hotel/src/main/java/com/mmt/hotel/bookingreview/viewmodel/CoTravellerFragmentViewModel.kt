package com.mmt.hotel.bookingreview.viewmodel

import androidx.databinding.ObservableBoolean
import com.mmt.hotel.R
import com.mmt.core.util.ResourceProvider
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SHOW_CO_TRAVELLER
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelCoTravellerFragmentEvent.UPDATE_RECYCLER_VIEW
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.model.CoTravellerFragmentData
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewTrackingHelper
import javax.inject.Inject

class CoTravellerFragmentViewModel @Inject constructor(val data: CoTravellerFragmentData,
                                                       private val dataWrapper: BookingReviewDataWrapper,
                                                       private val trackingHelper: HotelBookingReviewTrackingHelper) : HotelToolBarViewModel() {

    private val selectedAdultCoTravellerList = mutableListOf<CoTravellerSavedViewModel>()
    private val selectedChildCoTravellerList = mutableListOf<CoTravellerSavedViewModel>()

    var maxAdultCoTraveller: Int
    var maxChildCoTraveller: Int
    var footerVisible = ObservableBoolean(true)
    var listItems : List<AbstractRecyclerItem> = emptyList()

    init {
        maxAdultCoTraveller = dataWrapper.userSearchData?.occupancyData?.adultCount ?: 0
        maxChildCoTraveller = dataWrapper.userSearchData?.occupancyData?.childAges?.size ?: 0
        val leadPassengerPerRoom = dataWrapper.availResponse?.featureFlags?.leadPassengerMandatoryPerRoom ?: false
        val totalRooms = dataWrapper.userSearchData?.occupancyData?.roomCount ?: 0
        if(leadPassengerPerRoom && maxAdultCoTraveller >= totalRooms) {
            maxAdultCoTraveller -= totalRooms //to restrict Add guests limit in case of Expedia multi room bookings
        } else if(!leadPassengerPerRoom && maxAdultCoTraveller > 0 ) {
            maxAdultCoTraveller -= 1
        }
        createRecyclerViewItems()
    }

    fun createRecyclerViewItems() {
        val items = mutableListOf<AbstractRecyclerItem>()
        val savedList = mutableListOf<AbstractRecyclerItem>()
        dataWrapper.coTravellerList.forEachIndexed { index, coTraveller ->
            val item = CoTravellerSavedViewModel(eventStream, coTraveller, index != 0)
            if(data.coTravellerList.contains(coTraveller)){
                item.updateCheckState(true)
                onCoTravellerSelection(item)
            }
            savedList.add(item)
        }

        items.add(CoTravellerAddViewModel(eventStream, dataWrapper))
        if (savedList.isNotEmpty()) {
            items.add(CoTravellerSavedHeaderViewModel(savedList, eventStream))
        }
        updateEventStream(HotelEvent(UPDATE_RECYCLER_VIEW, items))
    }

    fun onCoTravellerSelection(coTravellerSavedViewModel: CoTravellerSavedViewModel) {
        val isChild = coTravellerSavedViewModel.guestInputDetail.child
        if (isChild) {
            addInList(selectedChildCoTravellerList, coTravellerSavedViewModel, maxChildCoTraveller)
        } else {
            addInList(selectedAdultCoTravellerList, coTravellerSavedViewModel, maxAdultCoTraveller)
        }
    }

    fun onCoTravellerDeSelection(coTravellerSavedViewModel: CoTravellerSavedViewModel) {
        val isChild = coTravellerSavedViewModel.guestInputDetail.child
        if (isChild) {
            selectedChildCoTravellerList.remove(coTravellerSavedViewModel)
        } else {
            selectedAdultCoTravellerList.remove(coTravellerSavedViewModel)
        }
    }

    fun addInList(list: MutableList<CoTravellerSavedViewModel>, item: CoTravellerSavedViewModel, maxLimit: Int) {
        if (maxLimit == 0) {
            item.updateCheckState(false)
            return
        }
        if (list.size < maxLimit) {
            list.add(item)
        } else {
            val index = maxLimit - 1
            list.get(index).updateCheckState(false)
            list.set(index, item)
        }
    }

    fun onDoneClick() {
        val selectedCoTravellerList = mutableListOf<CoTraveller>()
        for (item in selectedAdultCoTravellerList) {
            selectedCoTravellerList.add(item.coTraveller)
        }
        for (item in selectedChildCoTravellerList) {
            selectedCoTravellerList.add(item.coTraveller)
        }
        updateEventStream(HotelEvent(SHOW_CO_TRAVELLER, selectedCoTravellerList))
    }

    override fun getTitle(): String {
        return ResourceProvider.instance.getString(R.string.htl_select_guest)
    }

    override fun onHandleBackPress() {
        updateEventStream(HotelEvent(DISMISS_FRAGMENT))
    }

    override fun showCrossIcon(): Boolean {
        return true
    }

    fun trackAddCoTraveller(){
        trackingHelper.trackAddCoTraveller()
    }

    fun getBgStartColor(): Int {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
            ResourceProvider.instance.getColor(R.color.corp_bg_light)
        } else {
            ResourceProvider.instance.getColor(R.color.htl_booking_payment_bg_start)
        }
    }

    fun getBgEndColor(): Int {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
            ResourceProvider.instance.getColor(R.color.corp_bg_dark)
        } else {
            ResourceProvider.instance.getColor(R.color.htl_booking_payment_bg_end)
        }
    }

}