package com.mmt.hotel.bookingreview.viewmodel

import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelErrorFragmentViewModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.helper.constants.CheckErrorDialogActions
import com.mmt.hotel.common.constants.HotelConstants.MY_TRIPS_REDIRECT_URL
import com.mmt.hotel.common.model.HotelError
import javax.inject.Inject

class HotelRTBErrorViewModel @Inject constructor(hotelError: HotelError,
                                                 private val dataWrapper: BookingReviewDataWrapper) : HotelErrorFragmentViewModel(hotelError) {

    override fun performAction(@CheckErrorDialogActions action: Int) {
        when (action) {
            CheckErrorDialogActions.VIEW_OPEN_REQUESTS -> {
                updateEventStream(HotelEvent(HotelBookingReviewActivityEvent.OPEN_MYTRIPS_DEEPLINK, MY_TRIPS_REDIRECT_URL))
            }
            CheckErrorDialogActions.MAKE_CHECKOUT_REQUEST -> {
                dataWrapper.checkoutData.skipRtbValidation = true
                updateEventStream(HotelEvent(HotelBookingReviewActivityEvent.INITIATE_CHECKOUT, null))
            }
            else -> {
                updateEventStream(HotelEvent(DISMISS_ERROR_FRAG, null))
            }
        }
    }

}