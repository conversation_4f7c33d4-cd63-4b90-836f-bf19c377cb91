package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.runtime.mutableStateOf
import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.uikit.custom.RoundedTransformation
import com.mmt.uikit.util.isNotNullAndEmpty
import com.squareup.picasso.Transformation


class BRRoomInfoViewModel(val roomDetails: List<RoomDetailViewModel>, private val eventStream: MutableLiveData<HotelEvent>)
    : BaseObservable(), DiffUtilRecycleItem {

    val rooms = mutableStateOf(roomDetails)
    fun getContentDesc(): String {
        val strRoomDetails = StringBuilder("Your booking includes,")
        roomDetails.forEach {
            it.data.let { room ->
                if(room.roomName.isNotNullAndEmpty()) strRoomDetails.append("${room.roomName}, ")
                if(room.roomOccupancy.isNotNullAndEmpty()) strRoomDetails.append("occupancy ${room.roomOccupancy}, ")
                room.inclusionItems.forEach{ inclusion ->
                    (inclusion.data as? RoomInclusionsViewModel)?.data?.let { data ->
                        strRoomDetails.append("${data.title},")
                        strRoomDetails.append("${data.subTitle ?: ""},")
                    }
                }
            }
        }
        return strRoomDetails.toString()
    }

    fun updateRoomItems(roomDetails: List<RoomDetailViewModel>) {
        this.rooms.value = emptyList()
        this.rooms.value = roomDetails
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.ROOM_INFO
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
         (item as BRRoomInfoViewModel).roomDetails.forEachIndexed {index, match2 ->
             val match1 = roomDetails.getOrNull(index)
             if(match1 != match2){
                 return false
             }
        }
        return true
    }

    override fun cardName(): String {
        return "Review Room Info"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.ROOM_INFO
    }

    fun getPicassoTransformation(): Transformation {
        return RoundedTransformation(ResourceProvider.instance.getDimensionPixelSize(R.dimen.margin_tiny), 0)
    }

}