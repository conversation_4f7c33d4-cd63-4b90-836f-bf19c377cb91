package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.compose.runtime.mutableStateOf
import androidx.databinding.ObservableBoolean

class CorpTravelDetailOptionViewModelV2(val value: Any, val parentViewModel: HotelCorpTravelDetailFormItemViewModelV2, val isSelected: Boolean) {

    val itemSelected = ObservableBoolean(isSelected)
    val itemSelectedState = mutableStateOf(isSelected)


    fun onStatusChanged(isChecked: Boolean) {
        parentViewModel.onValueSelected(value, isChecked)
    }

}