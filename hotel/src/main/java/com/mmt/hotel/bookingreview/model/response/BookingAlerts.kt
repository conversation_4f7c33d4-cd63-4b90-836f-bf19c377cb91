package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.CANCELLATION
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.MEALPLAN
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.PD
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.PI
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.PRICE
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.PRICE_DECREASE
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.PRICE_INCREASE
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.ROOM_CODE
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.RPCC
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.R_C
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.R_MP
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.R_P
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.R_RC
import com.mmt.hotel.bookingreview.helper.constants.BookingAlertsConstants.Companion.R_RP
import kotlinx.parcelize.Parcelize

@Parcelize
data class BookingAlerts(val type: String,
                         val text: String?,
                         val subText: String?,
                         val amount: Double?,
                         val currency: String?,
                         val reasons: List<String>?) : Parcelable {



    fun initReasonsMap(): HashMap<String, String> {
        val priceReasonMap = HashMap<String, String>()
        priceReasonMap[PRICE_INCREASE] = PI
        priceReasonMap[PRICE_DECREASE] = PD
        priceReasonMap[PRICE] = R_P
        priceReasonMap[CANCELLATION] = R_C
        priceReasonMap[RPCC] = R_RP
        priceReasonMap[ROOM_CODE] = R_RC
        priceReasonMap[MEALPLAN] = R_MP
        return priceReasonMap
    }

}