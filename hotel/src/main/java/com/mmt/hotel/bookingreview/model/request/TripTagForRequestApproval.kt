package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class TripTagForRequestApproval(val attributeId: String?,
                                     val attributeName: String?,
                                     val attributeSelectedValue: List<String>?,
                                     val mandatoryCheck: Boolean?): Parcelable