package com.mmt.hotel.bookingreview.ui

import android.app.ActionBar.LayoutParams
import android.os.Bundle
import android.view.View
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.model.webview.WebViewBundle
import com.mmt.core.util.DeviceUtil
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.viewmodel.HotelWebViewBottomSheetVM
import com.mmt.hotel.databinding.HtlWebViewBottomsheetBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelWebViewBottomSheet :
    HotelBottomSheetDialogFragment<HotelWebViewBottomSheetVM, HtlWebViewBottomsheetBinding>() {

    @Inject
    lateinit var viewModelFactory: HotelViewModelFactory

    var webViewBundle: WebViewBundle? = null

    override fun initViewModel() = ViewModelProvider(this)[HotelWebViewBottomSheetVM::class.java]

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.HotelBottomSheetCornerRadiusDialogTheme)
        arguments?.getParcelable<WebViewBundle>(HotelWebViewBottomSheet.WEBVIEW_DATA)?.let {
            webViewBundle = it
        }
    }

    private fun setWebViewHeight(view: View) {
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            val height = (DeviceUtil.getDeviceHeight(activity) * .7).toInt()
            state = BottomSheetBehavior.STATE_EXPANDED
            this.isDraggable = false
            peekHeight = height
            maxHeight = height
            val params = viewDataBinding.bottomsheetLayout.layoutParams
            params.height = height
            view.layoutParams = params
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setWebViewHeight(view)
        with(viewDataBinding) {
            bWebView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView, url: String) {
                    header.text = view.title
                }
            }
            bWebView.loadUrl(webViewBundle?.webViewUrl ?: EMPTY_STRING)
        }
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.htl_web_view_bottomsheet
    }

    override fun initFragmentView() {
        // Nothing to do here.
    }


    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            HotelBookingReviewFragmentEvent.DISMISS_TCS_BOTTOMSHEET -> {
                dismiss()
            }
        }
    }


    companion object {
        const val TAG = "HotelWebViewBottomSheet"
        const val WEBVIEW_DATA = "WEBVIEW_DATA"

        @JvmStatic
        fun newInstance(webViewBundle: WebViewBundle): HotelWebViewBottomSheet {
            val fragment = HotelWebViewBottomSheet().apply {
                arguments = Bundle().apply {
                    putParcelable(WEBVIEW_DATA, webViewBundle)
                }
            }
            return fragment
        }
    }


}