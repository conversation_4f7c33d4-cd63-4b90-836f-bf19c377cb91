package com.mmt.hotel.bookingreview.viewmodel.corp

import android.database.Observable
import androidx.databinding.ObservableField
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.R
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.uikit.util.isNotNullAndEmpty
import javax.inject.Inject

class CorpHtlBookingApprovalRequestSentViewModel @Inject constructor() : HotelViewModel() {

    private var requisitionId = ObservableField(CoreConstants.EMPTY_STRING)
    var ctaText = ObservableField(ResourceProvider.instance.getString(R.string.htl_btn_back_to_mybiz_home))

    fun setRequisitionId(requisitionId: String? = CoreConstants.EMPTY_STRING){
        this.requisitionId.set(requisitionId?:CoreConstants.EMPTY_STRING)
        if(this.requisitionId.get().isNotNullAndEmpty()) {
            ctaText.set(ResourceProvider.instance.getString(R.string.htl_move_to_requisition_details_page))
        }
    }

    fun getMyBizBtnStartColor(): Int {
        return ResourceProvider.instance.getColor(R.color.corp_bg_light)
    }

    fun onClick() {
        if(requisitionId.get().isNotNullAndEmpty()){
            eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.ADD_TO_ITINIRARY_FLOW, requisitionId.get()))
        }else {
            eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.BACK_TO_HOME_BIZ))
        }
    }

    fun getMyBizBtnEndColor(): Int {
        return ResourceProvider.instance.getColor(R.color.corp_bg_dark)
    }
}