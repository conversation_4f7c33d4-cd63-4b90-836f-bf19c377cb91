package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.response.price.HotelPriceBreakUp
import com.mmt.hotel.bookingreview.model.response.price.PriceItem
import com.mmt.hotel.bookingreview.model.response.room.RoomRatePlan
import com.mmt.hotel.common.model.response.CancellationTimelineModel
import com.mmt.hotel.common.model.response.ResponseError
import com.mmt.hotel.compose.review.dataModel.FlexiDetailBottomSheetData
import kotlinx.parcelize.Parcelize

@Parcelize
data class TotalPricingResponseV2(
    @SerializedName("totalPricing")
    val hotelPriceBreakUp: HotelPriceBreakUp?,
    @SerializedName("rateplanlist")
    val ratePlanList: List<RoomRatePlan>?, //Room level data which can be changed due to addOn application
    @SerializedName("cancellationPolicyTimeline")
    val payLaterTimeLineModel: CancellationTimelineModel?,
    val emiDetails: HotelEmiDetailsMessage?,
    val bnplDetails: HotelBnplDetails?,
    val fullPayment: HotelFullPaymentDetails,
    val addon: List<PriceItem>?,
    val corpApprovalInfo: CorpApprovalInfo?,
    val error: ResponseError,
    @SerializedName("paymentPlan")
    val paymentPlan: PaymentPlan?,
    val flexiDetailBottomSheet: FlexiDetailBottomSheetData? = null,
    val currency: String? = null
) : Parcelable