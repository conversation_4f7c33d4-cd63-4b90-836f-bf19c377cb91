package com.mmt.hotel.bookingreview.model.response.gstn

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Used in B2B
 */
@Parcelize
data class GSTNDetails(var gstn: String? = null,
                       val ordId: Int? = null,
                       var address1: String? = null,
                       val address2: String? = null,
                       val pinCode: String? = null,
                       val city: String? = null,
                       var organizationName: String? = null,
                       val displayMessage: String? = null,
                       val gstClaimable: Boolean? = null,
                       val gstFilterParameter: String? = null,
                       val headquarter: Boolean? = null,
                       val saveGstDetails: Boolean? = null) : Parcelable