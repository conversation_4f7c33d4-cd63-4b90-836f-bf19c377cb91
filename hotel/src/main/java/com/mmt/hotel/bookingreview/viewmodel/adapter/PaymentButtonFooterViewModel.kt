package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.databinding.BaseObservable
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.response.PriceFooter
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

abstract class PaymentButtonFooterViewModel : BaseObservable(), DiffUtilRecycleItem {

    abstract fun getPriceFooterData() : PriceFooter?

    abstract fun showFooter() : Boolean

    abstract fun onClick()

    abstract fun getBgStartColor() : Int

    abstract fun getBgEndColor() : Int

    abstract fun getCtaText() : String

    abstract fun isEnabled() : Boolean

}