package com.mmt.hotel.bookingreview.viewmodel

import android.content.Intent
import android.widget.Toast
import androidx.lifecycle.viewModelScope
import com.gommt.travelplex.bridge.ChatPageData
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.auth.login.ConsentClient
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ProcessLifecycleHelper.getString
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.util.CommonMigrationHelper
import com.mmt.hotel.R
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.ALT_ACCO_RTB_MULTIBOOK_BLOCK
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.ALT_ACCO_RTB_MULTIBOOK_WARNING
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_PAYMENT_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_THANKYOU_PAGE
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.helper.HotelBookingPaymentHelper
import com.mmt.hotel.bookingreview.helper.HotelBookingReviewHelper
import com.mmt.hotel.bookingreview.helper.HotelInventoryUnblockUtil
import com.mmt.hotel.bookingreview.helper.constants.BookingReviewConstants.BNPL_AT_0
import com.mmt.hotel.bookingreview.helper.constants.CheckErrorDialogActions
import com.mmt.hotel.bookingreview.helper.constants.CheckoutErrorCodes
import com.mmt.hotel.bookingreview.helper.constants.CheckoutErrorCodes.RTB_MULTIPLE_REQ_ERROR_CODE
import com.mmt.hotel.bookingreview.helper.constants.CheckoutErrorCodes.RTB_TOO_MANY_REQ_ERROR_CODE
import com.mmt.hotel.bookingreview.model.HotelLobInfo
import com.mmt.hotel.bookingreview.model.PahIntentData
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.response.PostApprovalResponseV2
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.repository.HotelBookingReviewRepository
import com.mmt.hotel.bookingreview.tracking.BookingReviewPDTHelper
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewPdtV2Tracker
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewTrackingHelper
import com.mmt.hotel.bookingreview.ui.HotelBookingReviewActivity.Companion.SELECTED_RATE_PLAN_CODE
import com.mmt.hotel.bookingreview.ui.HotelBookingReviewActivity.Companion.SELECTED_ROOM_CODE
import com.mmt.hotel.bookingreview.ui.corp.CorpReviewConsentBottomSheetFragment
import com.mmt.hotel.chatBot.dataModel.ChatBotActionData
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.THANKYOU_PAGE
import com.mmt.hotel.common.model.HotelError
import com.mmt.hotel.common.model.response.HotelApiError
import com.mmt.hotel.common.request_callback.event.RequestCallbackEvent
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.detail.event.constants.HotelDetailTrackingConstants
import com.mmt.hotel.landingV3.helper.SearchContextConverter
import com.mmt.travelplex.TravelPlexDataHelper
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import javax.inject.Inject


open class HotelBookingReviewActivityViewModel @Inject constructor(private val repository: HotelBookingReviewRepository,
                                                                   private val dataWrapper: BookingReviewDataWrapper,
                                                                   private val bookingReviewHelper: HotelBookingReviewHelper,
                                                                   private val trackingHelper: HotelBookingReviewTrackingHelper,
                                                                   private val pdtTracker:BookingReviewPDTHelper,
                                                                   private val pdtV2Tracker:HotelBookingReviewPdtV2Tracker,
                                                                   private val bookingPaymentHelper: HotelBookingPaymentHelper
) : HotelViewModel() {

    companion object {
        const val RESULT_CODE_SOLD_OUT_ON_REVIEW = 2
        const val NO_RESULT_CODE = -1
        const val SUBSRCIPTION_ERROR_CODE = "PPAX_SUB_UNAUTH"
    }

    val travelPlexDataHelper by lazy {
        TravelPlexDataHelper(SearchContextConverter())
    }

    var checkDuplicateBooking = true

    // used to make payment checkout call on user consent of duplicate booking
    var skipReason : CorpReasons? = null
    // used to make approval call on user consent of duplicate booking
    var reasonsForTravel = mutableMapOf<String, String>()

    fun getCorpPrimaryTraveller(): CorpTravellerDetail?{
        return bookingReviewHelper.getCorpPrimaryTraveller()
    }

    fun getExtraHotelLobInfo(): HotelLobInfo{
        return bookingPaymentHelper.getHotelLobInfo(dataWrapper, bookingReviewHelper.getThankYouTrackingData())
    }

    fun getChatBotData() : ChatPageData? {
        val bookingReviewData = bookingReviewHelper.getBookingReviewData()?:return  null
        val searchContext = bookingReviewData.searchContext ?: return null
        val userSearchData = bookingReviewData.userSearchData
        val baseTrackingData = bookingReviewData.hotelBaseTrackingData
        val lobMetaData = bookingReviewHelper.getBookingReviewDataWrapper().chatBotInfo?.lobMetaData.orEmpty()
        return travelPlexDataHelper.getChatPageData(lobMetaData, searchContext, userSearchData, baseTrackingData)
    }

    fun proceedCheckout(skipReason : CorpReasons? = null, canShowBNPLPreTxnScreen: Boolean = true) {
        this.skipReason = skipReason
        val checkOutData = dataWrapper.getPaymentCheckoutData() ?: run {
            handleSomethingWrong()
            return
        }

        if(ExperimentUtil.getBNPLPreTxnDuration() != 0 && canShowBNPLPreTxnScreen && checkOutData.paymentDetail?.bnplVariant == BNPL_AT_0){
            return
        }

        if (LoginUtils.getPreferredRegion().isGlobalEntity()) {
            checkOutData.promoConsent = if (LoginUtils.isLoggedIn) {
                dataWrapper.consentCardData?.selected?: (dataWrapper.bookingReviewData?.promoConsentProvided?: false)
            } else false

            if (dataWrapper.consentCardData?.selected == true) {
                ConsentClient.updateConsent(true)
            }
        }

        skipReason?.let {
            // update skip reason if user has selected any.
            // Will be passed here only from Corp booking review on requesting skipping manager's approval with a valid reason to skip
            checkOutData.personalCorpBooking = it.enablePersonalCorpBooking == true
            checkOutData.reasonForSkipApproval = it.text
        }

        checkOutData.checkDuplicateBooking = this.checkDuplicateBooking


        viewModelScope.launch {
            repository.makeCheckoutRequest(checkOutData)
                .onStart { dataWrapper.checkoutData.skipDoubleBlack = null }
                .catch {
                    it.printStackTrace()
                    trackingHelper.trackCheckoutUnknownError()
                    handleSomethingWrong()
                }.collect { handleCheckoutResponse(it.first,it.second) }
        }
    }

    fun handleCheckoutResponse(responseData: CheckoutResponse, requestId: String?=null) {
        if(responseData.consentData != null){
            updateEventStream(
                HotelEvent(HotelBookingReviewActivityEvent.OPEN_CONSENT_DIALOG, Pair(responseData.consentData, CorpReviewConsentBottomSheetFragment.SOURCE_CHECKOUT))
            )
            return
        }
        val checkOutData = dataWrapper.getPaymentCheckoutData() ?: return
        checkOutData.responseData = responseData
        CommonMigrationHelper.instance.refreshUserData()
        responseData.error?.let {
            handleCheckoutError(it,requestId)
        } ?: run {
            val paymentIntentData = bookingPaymentHelper.getPaymentIntentData(bookingReviewHelper, dataWrapper, trackingHelper.getThankYouTrackingData())
            if(dataWrapper.bnplDetails?.bnplVariant == BNPL_AT_0 && (responseData.redirect?.equals(THANKYOU_PAGE, ignoreCase = true) == true)){
                updateEventStream(HotelEvent(OPEN_THANKYOU_PAGE, responseData))
            }else if (paymentIntentData != null) {
                updateEventStream(HotelEvent(OPEN_PAYMENT_ACTIVITY, paymentIntentData))
            } else {
                handleSomethingWrong()
            }
        }
        requestId?.let {
            bookingReviewHelper.updateRequestId(HotelPdtV2Constants.BackendApis.checkout,
                it
            )
        }
    }


    /**
     * object creation for PAH intent bundle
     */
    fun getPahIntentData(): PahIntentData? {
        val intentData = bookingPaymentHelper.getPahIntentData(bookingReviewHelper, dataWrapper, trackingHelper.getThankYouTrackingData())
        return intentData ?: kotlin.run {
            handleSomethingWrong()
            null
        }
    }

    /**
     * handle checkoutapi error cases
     */
    open fun handleCheckoutError(error: HotelApiError, requestId: String?) {
        val event  = bookingPaymentHelper.handleCheckoutError(error, dataWrapper)
        event?.let {
            updateEventStream(it)
        }?: kotlin.run {
            handleSomethingWrong()
        }
        trackCheckoutError(error,requestId)
    }


    protected fun trackCheckoutError(error: HotelApiError, requestId: String?) {
        error.code?.let { trackingHelper.trackCheckoutError(it) }
        when (error.code) {
            RTB_TOO_MANY_REQ_ERROR_CODE -> {
                trackingHelper.trackRTBEvents(ALT_ACCO_RTB_MULTIBOOK_BLOCK)
            }
            RTB_MULTIPLE_REQ_ERROR_CODE -> {
                trackingHelper.trackRTBEvents(ALT_ACCO_RTB_MULTIBOOK_WARNING)
            }
        }
        requestId?.let {
            bookingReviewHelper.updateRequestId(HotelPdtV2Constants.BackendApis.checkout, it)
            pdtV2Tracker.trackError(HotelPdtV2Constants.BackendApis.checkout, error, it)
        }
    }

    fun trackEvents(eventName: String) {
        trackingHelper.trackClicks(eventName)
    }

    fun trackEvents(eventName: String, prop: String) {
        trackingHelper.trackClicks(eventName, prop)
    }

    fun trackMMTShownAndClick(eventName: String) {
        trackingHelper.trackMMTShownAndClick(eventName)
    }
    fun trackSoldOutErrorEvents(eventName: String){
        trackingHelper.trackSoldOutError(eventName)
    }

    fun trackFlexiCheckinDropDownReset() {
        trackingHelper.trackClicks(HotelDetailTrackingConstants.FLEXI_CHECKIN_DROPDOWN_RESET, OmnitureTrackingHelper.OEPK_C_71)
    }

    fun trackEventsInProp44(eventName: String){
        trackingHelper.trackEventsInProp44(eventName)
    }

    fun handleApprovalsError(errorResponse: PostApprovalResponseV2?){
        when(errorResponse?.responseCode){
            HotelBookingReviewActivityViewModel.SUBSRCIPTION_ERROR_CODE -> {
                ResourceProvider.instance.showToast(errorResponse?.errorTitle ?: getString(R.string.htl_SOMETHING_WENT_WRONG), Toast.LENGTH_LONG)
            }
            else -> {
                when(errorResponse?.error?.code) {
                    CheckoutErrorCodes.OSBA_ERROR_CODE, CheckoutErrorCodes.GBF_ERROR_CODE-> {
                        val hotelError = HotelError(
                            title = errorResponse.error.errorTitle.orEmpty(),
                            msg = errorResponse.error.message,
                            positiveBtnText = ResourceProvider.instance.getString(R.string.htl_OK),
                            negativeBtnText = null,
                            positiveAction = CheckErrorDialogActions.DISMISS_POPUP,
                            negativeAction = null,
                            multipleOpenRequest = false
                        )
                        updateEventStream(HotelEvent(HotelBookingReviewActivityEvent.OPEN_ERROR_FRAGMENT, hotelError))
                    }
                    else -> {
                        handleSomethingWrong()
                    }
                }
            }
        }
    }

    open fun handleSomethingWrong() {
        ResourceProvider.instance.showToast(R.string.htl_SOMETHING_WENT_WRONG, Toast.LENGTH_LONG)
        updateEventStream(HotelEvent(HotelBookingReviewActivityEvent.BACK_TO_PREVIOUS_ACTIVITY, NO_RESULT_CODE))
    }

    fun isLuxeProperty() = dataWrapper.isLuxeProperty()

    fun updatePrevPageTrackingInfo() {
        dataWrapper.bookingReviewData?.hotelBaseTrackingData?.prevPageNamePdt = BookingReviewPDTHelper.PAYMENTS_PAGE
        dataWrapper.bookingReviewData?.hotelBaseTrackingData?.prevFunnelStepPdt = BookingReviewPDTHelper.PAYMENTS_PAGE
    }

    fun getCountryCode(): String {
        return dataWrapper.userSearchData?.countryCode ?: HotelConstants.COUNTRY_CODE_UNKNOWN
    }

    fun isEntireProperty() = dataWrapper.isEntireProperty()

    fun getUpsellIntent(): Intent? {
        return bookingReviewHelper.getSelectedUpsell()?.let { selectedUpsell ->
            Intent().also {
                it.putExtra(SELECTED_RATE_PLAN_CODE,
                    selectedUpsell.roomCriteria?.first()?.ratePlanCode
                )
                it.putExtra(SELECTED_ROOM_CODE, selectedUpsell.roomCriteria?.first()?.roomCode)
            }
        }
    }

    fun trackSelectedUpsell() {
        trackingHelper.trackSelectedUpsell()
    }

    fun trackB2BCTAShown(trackString: String?){
        trackingHelper.trackShownEvent(OmnitureTrackingHelper.OEPK_C_54, trackString?:CoreConstants.EMPTY_STRING)
    }

    fun trackCommonPageExitEvents(navigation: String?=null) {
        viewModelScope.launch {
            trackingHelper.trackPageExitEvent()
            pdtTracker.trackPageExit(navigation.orEmpty())
            pdtV2Tracker.trackPageExit(navigation.orEmpty())
        }
    }

    fun trackBookingForMySelf(){
        dataWrapper.isBookingForMySelf = true
    }

    fun trackBookingForOthers(){
        dataWrapper.isBookingForMySelf = false
    }

    fun setUnblockInventory(){
        dataWrapper.availResponse?.hotelInfo?.let {
            if(it.altAcco && !it.hotelId.isNullOrEmpty()){
                HotelInventoryUnblockUtil.saveUnblockInventory(it.hotelId)
            }
        }
    }

    fun isBnplAt0Selected() :Boolean{
        return dataWrapper.getPaymentCheckoutData()?.paymentDetail?.bnplVariant == BNPL_AT_0
    }

    fun setQcMetaData(qcMetaData: Any?) {
         dataWrapper.qcMetaData =qcMetaData
    }
    fun isRequestToBookFlow() : Boolean {
        return bookingReviewHelper.isRequestToBookFlow()
    }

    fun canShowCallToBook() : Boolean {
        var canShow = dataWrapper.availResponse?.hotelInfo?.showCallToBook ?: false
        canShow = canShow && (HotelSharedPrefUtil.instance.callToBookShownTimeStamp() == -1L || getHourDifference(HotelSharedPrefUtil.instance.callToBookShownTimeStamp()) >= 24)
        return canShow
    }

    private fun getHourDifference(timeInMillis: Long): Long {
        val currentTimeMillis = System.currentTimeMillis()
        val timeDifferenceInMillis = currentTimeMillis - timeInMillis
        val hourInMillis = 1000 * 60 * 60 // 1 hour in milliseconds
        return timeDifferenceInMillis / hourInMillis
    }

    fun showCallToBookBottomSheet() {
        val data = dataWrapper.availResponse?.hotelInfo?.requestCallBackData ?: return
        data.let {
            HotelSharedPrefUtil.instance.updateCallToBookShownTimeStamp()
            eventStream.value = HotelEvent(RequestCallbackEvent.REQUEST_CALLBACK, data)
        }
    }
}