package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Parcelize
data class AddOnSelected(
    val addOnType: String,
    val bucketId: String,
    val id: String,
    val title: String? = null,
    val validFrom: String? = null,
    var unitSelected: AddOnUnitSelected,
    var insuranceDataSelected: @RawValue ArrayList<SelectedInsuranceItem>? = null,
    val items : ArrayList<AddOnSelectedItem>? = null
) : Parcelable {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        return this === other || (other is AddOnSelected
                && addOnType == other.addOnType
                && id == other.id
                )
    }

    override fun hashCode(): Int {
        var result = addOnType.hashCode()
        result = 31 * result + id.hashCode()
        return result
    }
}

@Parcelize
data class AddOnSelectedItem(val id:String): Parcelable

