package com.mmt.hotel.bookingreview.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.HotelBaseRecyclerAdapter
import com.mmt.hotel.base.ui.viewHolder.HotelGeneralRecyclerViewHolder
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.bookingreview.viewmodel.InsuranceItemVM
import com.mmt.hotel.databinding.HtlInsuranceAdapterItemBinding

/**
 * Created by sunil.jain on 03/06/21.
 */
class HotelInsuranceAdapter(private val itemList: MutableList<AbstractRecyclerItem>) :
    HotelBaseRecyclerAdapter(itemList) {

    companion object {
        const val TYPE_INSURANCE_ITEM = 1
    }
    override fun getViewHolder(viewType: Int, layoutInflater: LayoutInflater, parent: ViewGroup)
            : HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem> {
        return HotelGeneralRecyclerViewHolder<HtlInsuranceAdapterItemBinding, InsuranceItemVM>(
            layoutInflater,
            R.layout.htl_insurance_adapter_item,
            parent
        )
                as HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>
    }

    override fun getItemId(position: Int): Long {
        return itemList[position].hashCode().toLong()
    }

}