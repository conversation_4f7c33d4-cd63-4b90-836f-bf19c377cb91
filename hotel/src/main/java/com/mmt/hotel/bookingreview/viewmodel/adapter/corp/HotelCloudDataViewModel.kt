package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.response.price.HotelCloudData
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem


class HotelCloudDataViewModel(val data: HotelCloudData) : DiffUtilRecycleItem {

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.HOTEL_CLOUD
    }

    override fun isSame(item: DiffUtilRecycleItem): Bo<PERSON>an {
        val matchedWith = (item as HotelCloudDataViewModel).data
        return data == matchedWith
    }

    override fun cardName(): String {
        return "Hotel Cloud Data"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.HOTEL_CLOUD_CARD
    }

}