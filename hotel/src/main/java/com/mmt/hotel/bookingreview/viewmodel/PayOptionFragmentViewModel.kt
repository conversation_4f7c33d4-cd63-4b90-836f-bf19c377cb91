package com.mmt.hotel.bookingreview.viewmodel

import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.data.model.payment.PaymentType
import com.mmt.hotel.R
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.ALT_ACCO_RTB_PAYMENT_COMPLETE
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.ALT_ACCO_RTB_PAYMENT_RESERVECC
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.ALT_ACCO_RTB_PAYMENT_ZPN
import com.mmt.hotel.analytics.pdt.BookingReviewTrackingConstants.BNPL_SELECTED
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelPayFragmentEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.helper.constants.BookingReviewConstants.BNPL_AT_0
import com.mmt.hotel.bookingreview.model.PayOptionActionData
import com.mmt.hotel.bookingreview.model.PayOptionData
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewTrackingHelper
import com.mmt.hotel.bookingreview.viewmodel.adapter.PartPaymentOptionViewModel
import com.mmt.hotel.bookingreview.viewmodel.adapter.PaymentPolicyCardViewModel
import com.mmt.hotel.common.util.*
import com.mmt.hotel.common.util.HotelUtil.getPriceWithCurrencySymbol
import java.util.*
import javax.inject.Inject

class PayOptionFragmentViewModel @Inject constructor(val data: PayOptionData,
                                                     private val dataWrapper: BookingReviewDataWrapper,
                                                     private val trackingHelper: HotelBookingReviewTrackingHelper) : HotelViewModel() {

    var selectedPayOption: ObservableField<PayOption>
    var enablePayNow = ObservableBoolean()
    var continueBtnText = ObservableField(ResourceProvider.instance.getString(R.string.htl_pay_button_full_text))
    var showButton = ObservableBoolean(true)
    var showPaymentSlider = ObservableBoolean(false)
    var initialText = ObservableField(EMPTY_STRING)
    var textDuringSliding = ObservableField(EMPTY_STRING)

    enum class PayOption(val value: String) {
        BNPL_WITHOUT_SAVED_CARDS("BNPL_WITHOUT_SAVED_CARDS"),
        FULL_WITHOUT_SAVED_CARDS("FULL_WITHOUT_SAVED_CARDS"),
        REQUEST_TO_BOOK_WITHOUT_BNPL("REQUEST_TO_BOOK_WITHOUT_BNPL"),
        EMI("EMI"),
        PART_PAYMENT("PART_PAYMENT")
    }

    init {
        selectedPayOption = ObservableField()
        when {
            isBnplAvailable() -> {
                if (ExperimentUtil.showInPaymentOptions() && !isShowBNPLDefault()) {
                    onPayFullClicked()
                } else {
                    onBnplClicked()
                }
            }
            isPartPaymentAvailable() -> onPartPaymentClicked()
            else -> onPayFullClicked()
        }
    }

    fun isBnplAvailable(): Boolean {
        return data.showBnpl
    }

    fun bnplAmount(): String?{
        dataWrapper.bnplDetails?.finalPrice?.toDoubleOrNull()?.let {
            return getPriceWithCurrencySymbol(it,data.currencySymbol)
        }
        return null
    }

    fun fullPaymentAmount(): String?{
        data.fullPaymentDetails?.finalPrice?.toDoubleOrNull()?.let {
            return  getPriceWithCurrencySymbol(it,data.currencySymbol)
        }
        return data.fullPaymentDetails?.finalPrice
    }



    fun isBnplApplicable(): Boolean{
        return data.enableBnpl
    }

    fun isEmiAvailabele(): Boolean {
        return data.showEmi
    }

    fun isRequestToBookFlow(): Boolean {
        return data.requestToBook && !isPreApproved()
    }

    fun setBnplTransparency(): Float{
        return if(isBnplApplicable()){
            1.0f
        }else{
            0.5f
        }
    }

    fun showHeader(): Boolean {
        return isRequestToBookFlow() || isPartPaymentAvailable()
    }

    fun showFullPaymentRadioButton(): Boolean {
        return isBnplAvailable() || isEmiAvailabele() || isRequestToBookFlow() || isPartPaymentAvailable()
    }

    fun isRtbFlowWithoutBnpl():Boolean {
        return isRequestToBookFlow() && !isBnplApplicable() && data.rtbAutoCharge
    }

    fun isPartPaymentAvailable(): Boolean {
        return data.paymentPlan != null
    }

    fun isPreApproved(): Boolean {
        return data.rtbPreApproved
    }

    fun onEmiClicked() {
        selectedPayOption.set(PayOption.EMI)
        continueBtnText.set(ResourceProvider.instance.getString(R.string.htl_pay_button_emi_text))
        updateFooterState(true)
    }

    fun onPayFullClicked() {
        /* Update new state */
        selectedPayOption.set(PayOption.FULL_WITHOUT_SAVED_CARDS)
        showButton.set(true)
        showPaymentSlider.set(false)
        updateFooterState(true)

        if (isEmiAvailabele()) {
            continueBtnText.set(ResourceProvider.instance.getString(R.string.htl_pay_button_full_text))
        } else {
            continueBtnText.set(ResourceProvider.instance.getString(R.string.htl_pay_button_text))
        }

    }

    fun onBnplClicked() {
        if(isBnplApplicable()) {
            selectedPayOption.set(PayOption.BNPL_WITHOUT_SAVED_CARDS)
            updateFooterState(true)
            if (dataWrapper.bnplDetails?.bnplVariant == BNPL_AT_0) {
                showButton.set(false)
                showPaymentSlider.set(true)
                if (!LoginUtils.isLoggedIn) {
                    initialText.set(ResourceProvider.instance.getString(R.string.htl_log_in_text_for_booking))
                    textDuringSliding.set(ResourceProvider.instance.getString(R.string.htl_log_in_text_for_booking))
                } else {
                    initialText.set(ResourceProvider.instance.getString(R.string.htl_pay_slider_initial_text_booking))
                    textDuringSliding.set(ResourceProvider.instance.getString(R.string.htl_pay_slider_done_text))
                }
            } else {
                continueBtnText.set(ResourceProvider.instance.getString(R.string.htl_pay_button_bnpl_text))
            }
        }
    }

    fun onRtbWithoutBnplClicked() {
        selectedPayOption.set(PayOption.REQUEST_TO_BOOK_WITHOUT_BNPL)
        updateFooterState(true)
        continueBtnText.set(ResourceProvider.instance.getString(R.string.htl_pay_button_bnpl_text))
    }

    fun onPartPaymentClicked() {
        showButton.set(true)
        showPaymentSlider.set(false)
        selectedPayOption.set(PayOption.PART_PAYMENT)
        updateFooterState(true)
        continueBtnText.set(ResourceProvider.instance.getString(R.string.htl_pay_button_text))
        data.paymentPlan?.let {
            trackingHelper.trackPartPaymentSelected(it)
        }
    }

    fun updateFooterState(value: Boolean) {
        enablePayNow.set(value)
    }

    fun continuePayment() {
        val payOptionActionData: PayOptionActionData = when (selectedPayOption.get()) {
            PayOption.BNPL_WITHOUT_SAVED_CARDS -> {
                if(isRequestToBookFlow()) {
                    trackingHelper.trackRTBWithBNPL(ALT_ACCO_RTB_PAYMENT_ZPN)
                }else{
                    trackingHelper.trackEvent(BNPL_SELECTED,  OmnitureTrackingHelper.OEPK_C_47)
                }
                PayOptionActionData(bnplSelected = true, paymentType = PaymentType.DELAYED_PAYMENT, bnplVariant = getBnplVariant())
            }
            PayOption.FULL_WITHOUT_SAVED_CARDS -> {
                if(isRequestToBookFlow()) {
                    trackingHelper.trackRTBWithFullPayment(ALT_ACCO_RTB_PAYMENT_COMPLETE)
                }else{
                    trackingHelper.trackFullPaymentSelected()
                }
                PayOptionActionData()
            }
            PayOption.EMI -> {
                PayOptionActionData(emiSelected = true)
            }
            PayOption.REQUEST_TO_BOOK_WITHOUT_BNPL -> {
                trackingHelper.trackRTBEvents(ALT_ACCO_RTB_PAYMENT_RESERVECC)
                PayOptionActionData(rtbAutoCharge = true , paymentType = PaymentType.DELAYED_PAYMENT)
            }
            PayOption.PART_PAYMENT -> {
                PayOptionActionData(paymentType = PaymentType.PART_PAYMENT, partialPayment = true)
            }
            else -> {
                PayOptionActionData()
            }
        }
        dataWrapper.getPaymentCheckoutData()?.apply {
            paymentDetail.emi = payOptionActionData.emiSelected
            paymentDetail.isBNPL = payOptionActionData.bnplSelected
            paymentType = payOptionActionData.paymentType
            paymentDetail.rtbAutoCharge = payOptionActionData.rtbAutoCharge
            paymentDetail.partialPayment = payOptionActionData.partialPayment
            paymentDetail.bnplVariant = payOptionActionData.bnplVariant
        }
        onContinueTrack()
        eventStream.value = HotelEvent(INITIATE_CHECKOUT, data.skipReason)
    }

    fun clickOnEmptyArea(){
        eventStream.value = HotelEvent(DISMISS_FRAGMENT, CoreConstants.EMPTY_STRING)
    }

    fun getBnplSubText(): String {
        return dataWrapper.bnplDetails?.bnplSubText.orEmpty()
    }

    fun getBnplText() : String {
        return dataWrapper.bnplDetails?.bnplText.orEmpty()
    }

    fun getBnplVariant(): String{
        return dataWrapper.bnplDetails?.bnplVariant.orEmpty()
    }

    fun getRtbWithoutBnplSubText() : String {
        return data.timeLineModel?.let {
            String.format(Locale.ENGLISH, ResourceProvider.instance.getString(R.string.htl_request_to_book_no_upfront_payment,
                data.currencySymbol,
                    StringUtil.getCommaSeparatedRoundOfPrice(data.totalAmount)))
        } ?: run {
            CoreConstants.EMPTY_STRING
        }
    }

    fun getFullPayText(): String {
        data.fullPaymentDetails?.fullPaymentText?.let {
            return it
        }
        return ResourceProvider.instance.getString(R.string.htl_pay_full_now, data.currencySymbol, StringUtil.getCommaSeparatedRoundOfPrice(data.totalAmount));
    }

    fun getFullPaySubText(): String {
        data.fullPaymentDetails?.fullPaymentSubText?.let {
            return it
        }
        return if(isRequestToBookFlow()) {
            data.timeLineModel?.let {
                String.format(Locale.ENGLISH, ResourceProvider.instance.getString(R.string.htl_request_to_book_full_pay_sub_text))
            } ?: run {
                CoreConstants.EMPTY_STRING
            }
        } else {
            return if (data.showBnpl) {
                data.timeLineModel?.cancellationDate?.let {
                String.format(Locale.ENGLISH, ResourceProvider.instance.getString(R.string.htl_pay_dialog_full_pay_sub_text, it))
                } ?: run {
                CoreConstants.EMPTY_STRING
                }
            } else {
            CoreConstants.EMPTY_STRING
            }
        }
    }

    fun getEmiText(): String {
        val amountString = ResourceProvider.instance.getString(R.string.htl_text_cost, data.currencySymbol, StringUtil.getCommaSeparatedPrice(data.totalAmount))
        return String.format(Locale.ENGLISH, ResourceProvider.instance.getString(R.string.htl_review_payment_easy), amountString)
    }

    fun getEmiSubText(): String {
        return data.emiMessage ?: CoreConstants.EMPTY_STRING
    }

    fun isEmiSubTextVisible(): Boolean {
        return StringUtil.isNotNullAndEmpty(data.emiMessage)
    }

    fun onLoadTrack(){
        val bnplString =
            if (data.enableBnpl) "bnpl_shown_enabled | " else if (data.showBnpl) "bnpl_shown_disabled | " else "bnpl_not_shown | "
        val emiString = if (data.showEmi) "emi_shown | " else "emi_not_shown "
        val event = "payment_options | " + bnplString + emiString
        trackingHelper.trackPayOptionLoad(event)
    }

    fun onContinueTrack(){
        dataWrapper.getPaymentCheckoutData()?.let {
            val emiString = if(it.paymentDetail.emi) "emi_" else ""
            val bnplString = if(it.paymentDetail.isBNPL) "bnpl_" else ""
            val event = "payment_options_continue_with_" + emiString + bnplString
            trackingHelper.trackPayOptionContinue(event)
        }
    }

    fun getPaymentPolicyCardViewModel(): PaymentPolicyCardViewModel? {
        return data.paymentPlan?.let {
            PartPaymentOptionViewModel(it,currencySymbol = data.currencySymbol)
        }
    }

    fun performActionOnSlidingDone(){
        if(LoginUtils.isLoggedIn){
            continuePayment()
        }else{
            eventStream.value = HotelEvent(OPEN_LOGIN_ACTIVITY, ResourceProvider.instance.getString(R.string.htl_bnpl0_slider_log_in_header))
            trackingHelper.trackLoginOnSlide()
        }
    }
}