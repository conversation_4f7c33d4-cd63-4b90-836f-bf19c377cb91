package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.ui.text.font.FontWeight
import androidx.databinding.ObservableField
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem.Companion.PAYMENT_POLICY_CARD
import com.mmt.hotel.bookingreview.dataModel.PaymentPolicyItemUiData
import com.mmt.hotel.bookingreview.dataModel.PaymentPolicyUiData
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.response.PaymentPlan
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.data.HotelTextConfigData
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.compose.MMTFontStyle
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.thankyou.SPACE
import com.mmt.uikit.fonts.FontConstants
import com.mmt.uikit.fonts.latoFont

open class PaymentPolicyCardViewModel(
    private var paymentPlan: PaymentPlan,
    private val showOnlyPolicies: Boolean = false,
    private val showAmountInDouble: Boolean = false,
    val marginBottom: Int = R.dimen.margin_0dp,
    private val showAsSubAmount: Boolean = false,
    val currencySymbol: String
): DiffUtilRecycleItem {

    val data = ObservableField<PaymentPolicyUiData>()

    init {
        data.set(createPaymentPolicyUiData(paymentPlan))
    }

    fun updateData(paymentPlan: PaymentPlan) {
        if (this.paymentPlan == paymentPlan) return
        this.paymentPlan = paymentPlan
        data.set(createPaymentPolicyUiData(paymentPlan))
    }

    private fun createPaymentPolicyUiData(paymentPlan: PaymentPlan): PaymentPolicyUiData {
        return PaymentPolicyUiData(
            title = ResourceProvider.instance.getString(R.string.htl_payment_policy_title),
            policyList = createPaymentPolicyList(paymentPlan), alertText = paymentPlan.penaltyText.orEmpty()
        )
    }

    protected open fun createPaymentPolicyList(paymentPlan: PaymentPlan): List<LinearLayoutItemData> {
        val itemList = mutableListOf<LinearLayoutItemData>()

        // add top policy
        if (!showOnlyPolicies) {
            itemList.add(
                LinearLayoutItemData(
                    R.layout.htl_payment_policy_card_item, BR.model,
                    PaymentPolicyItemUiData(
                        showIcon = true,
                        amount = HotelUtil.getCommaSeparatedPriceWithCurrency(paymentPlan.amount),
                        amountTextConfig = HotelTextConfigData(
                            textSizeResId = R.dimen.htl_text_size_small,
                            fontResId = FontConstants.LATO_BOLD,
                            textColor = R.color.grey_hotel,
                            fontResComposeId= MMTFontStyle(
                                fontFamily = latoFont,
                                fontWeight = FontWeight.Bold
                            )

                        ),
                        title = paymentPlan.text,
                        titleTextConfig = HotelTextConfigData(
                            textSizeResId = R.dimen.htl_text_size_small,
                            fontResId = FontConstants.LATO_BOLD,
                            textColor = R.color.grey_hotel,
                            fontResComposeId= MMTFontStyle(
                                fontFamily = latoFont,
                                fontWeight = FontWeight.Bold
                            )
                        )
                    )
                )
            )
        }

        val amountConfig = getAmountTextConfig()

        // add payment policies
        paymentPlan.paymentPolicy?.map {
            val amount = if (showAmountInDouble) {
                currencySymbol + SPACE + it.amount
            }else{
                HotelUtil.getCommaSeparatedPriceWithCurrency(it.amount)
            }

            val item = LinearLayoutItemData(R.layout.htl_payment_policy_card_item, BR.model,
                PaymentPolicyItemUiData(
                    index = it.sequence,
                    amount = amount,
                    amountTextConfig = amountConfig,
                    showDivider = itemList.size > 0,
                    title = it.text,
                    titleTextConfig = HotelTextConfigData(
                        textSizeResId = R.dimen.htl_text_size_small,
                        fontResId = FontConstants.LATO_REGULAR,
                        textColor = R.color.grey_rs,
                                fontResComposeId= MMTFontStyle(
                                fontFamily = latoFont,
                        fontWeight = FontWeight.Normal
                    )
                    )
                )
            )
            itemList.add(item)
        }
        return itemList
    }

    private fun getAmountTextConfig(): HotelTextConfigData {
        return if (showAsSubAmount) {
            HotelTextConfigData(
                textSizeResId = R.dimen.detail_page_text_size_tiny,
                fontResId = FontConstants.LATO_REGULAR,
                textColor = R.color.grey_hotel,
                fontResComposeId= MMTFontStyle(
                    fontFamily = latoFont,
                    fontWeight = FontWeight.Normal
                )
            )
        } else {
            HotelTextConfigData(
                textSizeResId = R.dimen.htl_text_size_small,
                fontResId = FontConstants.LATO_REGULAR,
                textColor = R.color.grey_hotel,
                fontResComposeId= MMTFontStyle(
                    fontFamily = latoFont,
                    fontWeight = FontWeight.Normal
                )
            )
        }
    }

    override fun getItemType(): Int {
        return PAYMENT_POLICY_CARD
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.PAYMENT_POLICY_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as PaymentPolicyCardViewModel).data
        return data == matchedWith
    }

    override fun cardName(): String {
        return "Review Payment Policy"
    }

}