package com.mmt.hotel.bookingreview.viewmodel

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem.Companion.PAN_TCS_CARD
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.gommt.pan.domain.request.PanRequest

class PanTcsWidgetViewModel(private val eventStream: MutableLiveData<HotelEvent>, private val panData: PanRequest): DiffUtilRecycleItem {

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.PAN_TCS_WIDGET
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as PanTcsWidgetViewModel).panData
        return panData == matchedWith
    }

    override fun cardName(): String {
        return "PAN TCS Widget"
    }

    override fun getItemType(): Int {
        return PAN_TCS_CARD
    }

}