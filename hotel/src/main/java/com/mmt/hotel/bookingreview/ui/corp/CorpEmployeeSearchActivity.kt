package com.mmt.hotel.bookingreview.ui.corp

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import androidx.recyclerview.widget.LinearLayoutManager
import com.mmt.core.util.KeyBoardUtils
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.activity.HotelActivity
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.adapter.corp.HotelCorpAddCoTravellerSearchAdapter
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent.ON_EMPLOYEE_CLICKED
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerSuccessFragmentData
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.ui.corp.HotelCorpBookingReviewActivity.Companion.PRIMARY_TRAVELLER
import com.mmt.hotel.bookingreview.ui.corp.HotelCorpBookingReviewActivity.Companion.PRIMARY_TRAVELLER_EMAIL_ID
import com.mmt.hotel.bookingreview.ui.corp.HotelCorpBookingReviewActivity.Companion.TRAVELLING_EMPLOYEE_LIST
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpEmployeeSearchViewModel
import com.mmt.hotel.compose.review.ui.fragment.HotelCorpBookingReviewFragmentV2.Companion
import com.mmt.hotel.compose.review.ui.fragment.HotelCorpBookingReviewFragmentV2.Companion.SEARCH_RESULT
import com.mmt.hotel.databinding.CorpEmployeeSearchActivityBinding
import com.mmt.uikit.util.isNotNullAndEmpty
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class CorpEmployeeSearchActivity : HotelActivity<CorpEmployeeSearchViewModel, CorpEmployeeSearchActivityBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory
    private lateinit var searchResultsRecyclerAdapter: HotelCorpAddCoTravellerSearchAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewDataBinding.viewModel = viewModel
        val primaryTravellerEmailId: String? = intent.getStringExtra(PRIMARY_TRAVELLER_EMAIL_ID)
        val isPrimary: Boolean = intent.getBooleanExtra(PRIMARY_TRAVELLER, false)
        var bundle = intent.extras
        if (primaryTravellerEmailId != null) {
            viewModel.setPrimaryTravellerEmailIdToSearchText(primaryTravellerEmailId)
        }
        var employeeAlreadyAddedList = bundle?.getParcelableArrayList<CorpTravellerDetail> (
            TRAVELLING_EMPLOYEE_LIST) as ArrayList<CorpTravellerDetail>?
        if(employeeAlreadyAddedList.isNotNullAndEmpty()){
            viewModel.employeesAddedList = employeeAlreadyAddedList
        }
        viewModel.viaPrimary = isPrimary
        initRecyclerView()
    }

    companion object {
        const val TAG = "CorpEmployeeSearchActivity"
    }

    private fun initRecyclerView() {
        viewDataBinding.searchResultList.apply {
            layoutManager = LinearLayoutManager(this@CorpEmployeeSearchActivity)
            searchResultsRecyclerAdapter = HotelCorpAddCoTravellerSearchAdapter(arrayListOf())
            adapter = searchResultsRecyclerAdapter
        }
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            CorpEmployeeSearchActivityEvent.UPDATE_RECYCLER_VIEW -> {
                searchResultsRecyclerAdapter.updateList(event.data as List<AbstractRecyclerItem>)
            }
            ON_EMPLOYEE_CLICKED -> {
                val resultIntent = Intent()
                resultIntent.putExtra(SEARCH_RESULT, event.data as Parcelable)
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }
            CorpEmployeeSearchActivityEvent.ON_BACK_PRESSED -> {
                KeyBoardUtils.hideKeyboard(this)
                onHandleBackPress()
            }
            CorpEmployeeSearchActivityEvent.EDIT_TRAVELLER -> {
                KeyBoardUtils.hideKeyboard(this)
                openEditTravellerFragment(event.data as CorpAddEditTravellerFragmentData?)
            }
            CorpEmployeeSearchActivityEvent.TRAVELLER_DETAILS_ADDED -> {
                val resultIntent = Intent()
                val data = event.data as CorpAddEditTravellerSuccessFragmentData
                resultIntent.putExtra(SEARCH_RESULT, data.travellerDetail as Parcelable)
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }
            else -> {
            }
        }
    }

    override fun createViewModel(): CorpEmployeeSearchViewModel {
        return getViewModel(factory)
    }

    override fun createEventSharedViewModel(): HotelEventSharedViewModel {
        return getViewModel()
    }

    override fun getLayoutId() = R.layout.corp_employee_search_activity

    private fun openEditTravellerFragment(data: CorpAddEditTravellerFragmentData?) {
        val bundle = Bundle()
        bundle.putParcelable(CorpAddEditTravellerFragment.TAG, data)
        val fragInstance = CorpAddEditTravellerFragment.newInstance(bundle)
        supportFragmentManager.beginTransaction()
                .replace(R.id.child_frag_container, fragInstance, CorpAddEditTravellerFragment.TAG)
                .addToBackStack(CorpAddEditTravellerFragment.TAG)
                .commitAllowingStateLoss()
        supportFragmentManager.executePendingTransactions()
    }
}