package com.mmt.hotel.bookingreview.helper

import com.gommt.insurance.InsuranceConnector
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.data.model.payment.PaymentType
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.bookingreview.helper.constants.BookingReviewConstants.MAX_ENABLED_COUPONS_ON_BOOKING_REVIEW
import com.mmt.hotel.bookingreview.model.*
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.request.AddOnRequestData
import com.mmt.hotel.bookingreview.model.request.AddOnSelected
import com.mmt.hotel.bookingreview.model.response.*
import com.mmt.hotel.bookingreview.model.response.addon.AddonDataV2
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagResponseV2
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.bookingreview.model.response.price.AvailRoomResponseV2
import com.mmt.hotel.bookingreview.model.response.price.BenefitDeal
import com.mmt.hotel.bookingreview.model.response.price.HotelPriceBreakUp
import com.mmt.hotel.bookingreview.model.response.validatecoupon.ValidateApiResponseV2
import com.mmt.hotel.chatBot.dataModel.ChatBotWidgetInfo
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.PaymentModeTypes
import com.mmt.hotel.common.model.PromoConsent
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.response.CancellationTimelineModel
import com.mmt.hotel.common.model.response.HotelsUserBlackInfo
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.compose.review.dataModel.FlexiDetailBottomSheetData
import com.mmt.hotel.dayuse.model.request.SlotAvailRequestData
import com.mmt.hotel.detail.constants.DetailPageViewType
import com.mmt.hotel.detail.model.response.FlexiCheckinTimeSlotInfo
import com.mmt.hotel.old.model.hotelreview.response.specialRequest.SpecialRequestForm
import com.mmt.payments.qc.model.RequestInfo
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject

/**
 *  class for holding data of BookingReviewRequest
 */

@ActivityRetainedScoped
class BookingReviewDataWrapper @Inject constructor() {

    var paymentType: PaymentType? = null
    var expData: String? = null

    var corpApprovalInfo: CorpApprovalInfo? = null

    var corpApprovingManagers: List<CorpApprovingManager>? = null

    var userSearchData: UserSearchData? = null

    var propertyType = HotelConstants.PROPERTY_TYPE_HOTEL

    var rootLevelAlerts: List<BookingAlerts>? = null

    var campaignAlert : BookingAlerts? = null

    var emiDetails: HotelEmiDetailsMessage? = null

    var bnplDetails: HotelBnplDetails? = null

    var fullPaymentDetails: HotelFullPaymentDetails?= null


    // acknowledgementId will get in validate api response and pass it to payment widget and will get the paymode accordingly
    var ackId: String? = null

    var flexiDetailBottomSheet: FlexiDetailBottomSheetData? = null


    // requestInfo param which we are sending to quickpayment widget to show the paymode card
    var requestInfo: RequestInfo? = null

    // metaData for quick payment widget
    var qcMetaData: Any? = null

    // to show the quick payment widget we get this flag in avail response
    var quickCheckoutApplicable: Boolean? = true


    var bookingReviewData: BookingReviewData? = null

    var corpTripTagResponseV2: CorpTripTagResponseV2? = null

    var corpPolicyResponse: UpdatedCorpPolicyResponse? = null

    var corpPrimaryTraveller: CorpTravellerDetail?= null

    var corpCoTravellerList: List<CorpTravellerDetail>?= null

    var gstDetails: GSTNDetails? = null

    var gstB2BEnteredDetails: GSTNDetails ?= null

    var domesticGSTNDetails : DomesticGSTNDetails? = null

    /* For checkout preparation */
    lateinit var checkoutData: CheckoutData

    var priceBreakUp: HotelPriceBreakUp? = null

    var addons: List<AddonDataV2>? = null

    var coupons: MutableList<HotelBookingCoupon>? = null

    var benefitDeal: BenefitDeal? = null

    var appliedCoupon: HotelBookingCoupon? = null

    var appliedCouponSuccessMessage: String? = null

    var payLaterTimeLineModel: CancellationTimelineModel? = null

    var txnKey: String = CoreConstants.EMPTY_STRING

    var correlationKey: String = CoreConstants.EMPTY_STRING

    var specialRequestForm: SpecialRequestForm? = null

    var coTravellerList = mutableListOf<CoTraveller>()

    var alerts: List<BookingAlerts>? = null

    private var lastVisibleItemPosition = 0

    var userBlackInfo: HotelsUserBlackInfo? = null

    var chatBotInfo: ChatBotWidgetInfo? = null

    val selectedAddOns : MutableList<AddOnSelected> = ArrayList()

    //0th index will contain base rate plan and so it is not shown as upsell in UI
    var upsellOptions: List<UpsellInfo> = emptyList()

    var isUpsellClickTracked = false
    var isUpsellShownTracked = false
    var pageLoadTracked = false
    var  insuranceConnector : InsuranceConnector? = null


    var addOnDetail : Map<String, AddOnRequestData> ? = null


    /**
     * [dayUseInfo] is used  when we get any tariff related to hourly booking in hotel funnel
     */
    var dayUseInfo: DayUseInfo? = null

    /**
     * [isDayUseFunnel] is used  when we enters from dayUse Funnel
     */
    var isDayUseFunnel : Boolean = false

    var roomCount : Int = 1

    var paymentPlan: PaymentPlan? = null

    var currentSelectedSlot: SlotAvailRequestData? = null

    var isBookingForMySelf: Boolean = false

    var consentCardData: PromoConsent? = null

    val requestIds = hashMapOf<HotelPdtV2Constants.BackendApis,String>() //This map will contain <apiName,request id> of backend apis

    var maxCouponsToShow: Int = MAX_ENABLED_COUPONS_ON_BOOKING_REVIEW

    var selectedAddOnState = "UNSELECTED"

    var currencyCode : String? = null

    fun getDayUseCheckInTime(): String {
        return currentSelectedSlot?.timeSlot?.toString().orEmpty()
    }

    fun getDayUseSlotDuration(): String {
        return currentSelectedSlot?.duration?.toString().orEmpty()
    }

    fun isB2CPinCodeValidationMandatory(): Boolean {
        return this.priceBreakUp?.pinCodeMandatory ?: true
    }

    fun clearData() {
        expData = null
        corpApprovalInfo = null
        corpApprovingManagers = null
        userSearchData = null
        propertyType = HotelConstants.PROPERTY_TYPE_HOTEL
        rootLevelAlerts = null
        campaignAlert = null
        emiDetails = null
        bnplDetails = null
        fullPaymentDetails = null
        bookingReviewData = null
        corpTripTagResponseV2 = null
        corpPrimaryTraveller= null
        corpCoTravellerList= null
        gstDetails = null
        gstB2BEnteredDetails = null
        domesticGSTNDetails = null
        priceBreakUp = null
        addons = null
        coupons = null
        benefitDeal = null
        appliedCoupon = null
        payLaterTimeLineModel = null
        txnKey = CoreConstants.EMPTY_STRING
        correlationKey = CoreConstants.EMPTY_STRING
        specialRequestForm = null
        coTravellerList = mutableListOf<CoTraveller>()
        alerts = null
        lastVisibleItemPosition = 0
        userBlackInfo = null
        selectedAddOns.clear()
        upsellOptions = emptyList()
        isUpsellClickTracked = false
        isUpsellShownTracked = false
        dayUseInfo = null
        roomCount = 1
        this.chatBotInfo = null
    }

    fun getPaymentCheckoutData(): CheckoutData? {
        return if (::checkoutData.isInitialized) {
            checkoutData
        } else {
            null
        }
    }

    // update only onSuccess
    fun updateFromTotalPricingResponse(
        totalPricingApiResponseV2: TotalPricingApiResponseV2?,
        requestId: String
    ) {
        totalPricingApiResponseV2?.response?.let {
            this.priceBreakUp = it.hotelPriceBreakUp
            this.payLaterTimeLineModel =  it.payLaterTimeLineModel
            this.emiDetails = it.emiDetails
            this.bnplDetails = it.bnplDetails
            this.fullPaymentDetails = it.fullPayment
            //this.correlationKey = totalPricingApiResponseV2.correlationKey.orEmpty()
            this.paymentPlan = it.paymentPlan
            this.flexiDetailBottomSheet = it.flexiDetailBottomSheet
        }
        requestIds.put(HotelPdtV2Constants.BackendApis.totalPricing,requestId)
    }

    // update only onSuccess
    var availResponse: AvailRoomResponseV2? = null
        set(availResponse) {
            field = availResponse
            this.chatBotInfo = availResponse?.chatBotWidgetInfo
            this.userBlackInfo = availResponse?.userBlackInfo
            this.priceBreakUp = availResponse?.hotelPriceBreakUp
            this.txnKey = availResponse?.txnKey ?: EMPTY_STRING
            this.addons = availResponse?.addons ?: emptyList()
            this.specialRequestForm = availResponse?.specialRequests
            this.propertyType = availResponse?.propertyRules?.propertyType
                    ?: HotelConstants.PROPERTY_TYPE_HOTEL
            this.rootLevelAlerts = availResponse?.alerts
            this.campaignAlert = availResponse?.campaignAlert
            this.emiDetails = availResponse?.emiDetails
            this.alerts = availResponse?.alerts
            this.bnplDetails = availResponse?.bnplDetails
            this.fullPaymentDetails = availResponse?.fullPayment
            this.quickCheckoutApplicable = availResponse?.quickCheckoutApplicable

            this.payLaterTimeLineModel = availResponse?.payLaterTimeLineModel
            this.corpApprovingManagers = availResponse?.approvingManagers
            this.corpApprovalInfo = availResponse?.corpApprovalInfo
            this.dayUseInfo = availResponse?.hotelInfo?.dayUseInfo
            this.roomCount = availResponse?.ratePlanList?.sumOf { it.occupancyDetails.numOfRooms } ?: 1
            this.coupons = availResponse?.hotelPriceBreakUp?.coupons?.toMutableList() ?: mutableListOf()
            this.benefitDeal = availResponse?.hotelPriceBreakUp?.benefitDeals
            this.ackId = availResponse?.ackId
            this.selectedAddOnState = availResponse?.selectedAddOnState ?: ""
            this.currencyCode = availResponse?.currencyCode
            //Sorting coupons to put enabled coupons first
            this.coupons?.sortBy {
                if(!it.isDisabled){
                    0
                } else {
                    1
                }
            }
            availResponse?.upsellOptions?.let {
                updateUpsellInfo(availResponse)
            }
            this.paymentPlan = availResponse?.paymentPlan
            this.flexiDetailBottomSheet = availResponse?.flexiDetailBottomSheet
            updateUserSearchData()
            setPaymentType(availResponse?.featureFlags?.payMode)
            this.maxCouponsToShow = availResponse?.hotelPriceBreakUp?.maxCouponsToShow
                ?: MAX_ENABLED_COUPONS_ON_BOOKING_REVIEW

        }

    fun setPaymentType(payMode: String?) {
        paymentType = when (payMode) {
            PaymentModeTypes.PAH1, PaymentModeTypes.PAH_WITHOUT_CC, PaymentModeTypes.PAH2, PaymentModeTypes.PAH_WITH_CC -> PaymentType.PAH
            else -> PaymentType.FULL_PAYMENT
        }
    }

    // update only onSuccess
    fun updateFromValidateResponse(validateApiResponseV2: ValidateApiResponseV2?, requestId: String) {
        validateApiResponseV2?.apiResponse?.let { response ->
            this.priceBreakUp = response.priceBreakUp
            this.payLaterTimeLineModel =  response.payLaterTimeLineModel
            this.emiDetails = response.emiDetails
            this.bnplDetails = response.bnplDetails
            this.fullPaymentDetails = response.fullPayment
            this.ackId = response.ackId
            //this.correlationKey = validateApiResponseV2.correlationKey.orEmpty()
            val coupons = response.priceBreakUp?.coupons
            if (coupons?.isNotEmpty() == true) {
                this.appliedCoupon = coupons[0]
                this.appliedCouponSuccessMessage = response.statusMessage
            }else{
                this.appliedCoupon = null
                this.appliedCouponSuccessMessage = null
            }
            this.paymentPlan = response.paymentPlan
            this.flexiDetailBottomSheet = response.flexiDetailBottomSheet
        }
        this.requestIds.put(HotelPdtV2Constants.BackendApis.validateCoupon,requestId)
    }

    /**
     * function for updating userSearchData from api response
     * if we open this page from deeplink , some values are missing/maybe wrong
     */
    private fun updateUserSearchData() {
        val hotelInfo = availResponse?.hotelInfo?:return
        val userSearchData = bookingReviewData?.userSearchData
        userSearchData?.let {
            it.hotelName = hotelInfo.name.orEmpty()
            it.locationName = hotelInfo.cityName.orEmpty()
            it.country = hotelInfo.countryName.orEmpty()
            if (hotelInfo.checkinTime?.isNotEmpty() == true) {
                it.checkInTime = hotelInfo.checkinTime
            }
            if (hotelInfo.checkoutTime?.isNotEmpty() == true) {
                it.checkOutTime = hotelInfo.checkoutTime
            }
        }
        this.userSearchData = userSearchData
        val trackingData = bookingReviewData?.hotelBaseTrackingData?:return
        trackingData.starRating = hotelInfo.starRating
        trackingData.propertyType = hotelInfo.propertyType
        trackingData.headerImgUrl = hotelInfo.hotelIcon
        trackingData.supplierType = hotelInfo.supplierType
    }


    fun updateLastVisibleItemPosition(position: Int) {
        if (this.lastVisibleItemPosition < position) {
            lastVisibleItemPosition = position
        }
    }

    fun getLastVisibleItemPosition() = lastVisibleItemPosition

    fun isLuxeProperty():Boolean{
        return DetailPageViewType.LUXE.equals(availResponse?.hotelInfo?.propertyViewType,true)
    }

    fun isEntireProperty() : Boolean {
        return availResponse?.hotelInfo?.entireProperty ?: false
    }

    fun updateSelectedAddOns(selectedAddOns: List<AddOnSelected>) {
        this.selectedAddOns.clear()
        this.selectedAddOns.addAll(selectedAddOns)
    }

    fun addSelectedAddOns(selectedAddOns: List<AddOnSelected>) {
        this.selectedAddOns.addAll(selectedAddOns)
    }

    // For Base Upsell Details Adding EMPTY_STRING in Fields below as they are not required and Deprecated
    private fun updateUpsellInfo(availResponse: AvailRoomResponseV2) {
        val upsellList = mutableListOf<UpsellInfo>()
        bookingReviewData?.roomCriteria?.let { baseOptionRoomCriteria ->
            upsellList.add(
                UpsellInfo(
                    displayText = EMPTY_STRING,
                    roomCriteria = baseOptionRoomCriteria
                )
            )
        }
        availResponse.upsellOptions?.map {
            upsellList.add(it)
        }
        this.upsellOptions = upsellList
    }

    fun updateBookingReviewData(data: BookingReviewData) {
        bookingReviewData?.let {
            it.roomCriteria = data.roomCriteria
        }
    }

    fun updateFlexiCheckinTimeSlotInfo(slotInfo: FlexiCheckinTimeSlotInfo?) {
        bookingReviewData?.let{
            it.flexibleCheckinSelectedTimeSlotInfo = slotInfo
        }
    }

    fun updateDomesticGSTNDetails(data: DomesticGSTNDetails?) {
        domesticGSTNDetails = data
    }
}