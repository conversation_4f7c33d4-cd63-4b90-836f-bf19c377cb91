package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import com.mmt.data.model.payment.PaymentRequestVO
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.tracking.LocusTrackingData
import com.mmt.hotel.common.model.tracking.PriceTrackingData
import kotlinx.parcelize.Parcelize

/**
 * Passed to open PAH without CC activity
 */
@Parcelize
data class PahIntentData(
    val totalAmount: String,
    val userSearchData: UserSearchData,
    val checkoutData: CheckoutData,
    val extraLobInfo: HotelLobInfo,
    var forceShow: Boolean = false,
    val currencySymbol: String
) : Parcelable

/**
 * Used to create intent data for payment activity
 */
data class PaymentIntentData(val paymentRequest: PaymentRequestVO,
                             val extraLobInfo: HotelLobInfo)

/**
 * This object gets passed to thankyou as it is without modification from payment
 */
@Parcelize
data class HotelLobInfo(val funnelSrc: Int,
                        val transactionKey: String,
                        val countryCode: String,
                        val thankyouTrackingData: ReviewToThankyouTrackingData) : Parcelable{
    fun isInvalid(): Boolean {
        return transactionKey == null && thankyouTrackingData == null
    }
}
/**
 * @param roomSearchType is room search Type passed from review page.
 * possible values can :-
 * const val EXACT_MATCH = "E"
 * const val OCCUPANCY_LESS = "O"
 * const val RECOMMENDED = "R"
 * */
@Parcelize
data class ReviewToThankyouTrackingData(val locusTrackingData: LocusTrackingData?,
                                        val priceTrackingData: PriceTrackingData?,
                                        val roomSearchType: String?,
                                        val userSearchData: UserSearchData?,
                                        val isUpsellSelectedOnReview: Boolean = false,
                                        val xUserType: String? = null
) : Parcelable