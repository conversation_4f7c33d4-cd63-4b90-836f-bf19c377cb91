package com.mmt.hotel.bookingreview.dataModel.hotelDetail

import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import androidx.lifecycle.MutableLiveData
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.EventType
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.selectRoom.event.SelectRoomEvent

class RoomInclusionModel(
    val data: RoomInclusionUiData,
    val eventStream: MutableLiveData<HotelEvent>
) {

    fun createClickableSpannable(): SpannableString {
        if (data.trailingCtaText.isEmpty() || data.trailingCtaBottomSheet == null) return SpannableString(
            data.subTitle ?: ""
        )
        val fullText = (data.subTitle ?: "") + " " + data.trailingCtaText
        val spannableString = SpannableString(fullText)
        val startIndex = (data.subTitle ?: "").length
        val endIndex = fullText.length
        spannableString.setSpan(
            StyleSpan(Typeface.BOLD),
            startIndex,
            endIndex,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            ForegroundColorSpan(ResourceProvider.instance.getColor(R.color.color_008cff)),
            startIndex,
            endIndex,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannableString
    }

    fun onClickTrailingCta() {
        if (data.trailingCtaBottomSheet != null) eventStream.postValue(
            HotelEvent(
                SelectRoomEvent.SHOW_INCLUSIONS_BOTTOMSHEET,
                data.trailingCtaBottomSheet,
                EventType.BOTTOM_SHEET
            )
        )
    }

}