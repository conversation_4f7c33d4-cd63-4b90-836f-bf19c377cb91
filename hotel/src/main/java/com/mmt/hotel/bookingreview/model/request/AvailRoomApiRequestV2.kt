package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.request.AvailRoomSearchCriteria
import com.mmt.hotel.common.model.ResponseFilterFlag
import com.mmt.hotel.common.model.request.DeviceDetails
import com.mmt.hotel.common.model.request.RequestDetails
import com.mmt.hotel.filterV2.model.response.FilterV2
import kotlinx.parcelize.Parcelize

@Parcelize
data class AvailRoomApiRequestV2(val deviceDetails: DeviceDetails?,
                                 val searchCriteria: AvailRoomSearchCriteria?,
                                 val requestDetails: RequestDetails?,
                                 val featureFlags: ResponseFilterFlag,
                                 val expData: String?,
                                 val filterCriteria: List<FilterV2>?) : Parcelable