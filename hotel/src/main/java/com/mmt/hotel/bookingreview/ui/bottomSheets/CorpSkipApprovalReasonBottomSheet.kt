package com.mmt.hotel.bookingreview.ui.bottomSheets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.ui.widgets.ReasonSelectionItem
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpSkipApprovalReasonsBSViewModel
import com.mmt.hotel.bookingreview.viewmodel.corp.ReasonSelectionItemState
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.utils.parseHtmlString
import com.mmt.hotel.widget.compose.MMTAnnotatedTextView
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun CorpSkipApprovalBottomSheetUI(
    modifier: Modifier = Modifier,
    vm: HotelCorpSkipApprovalReasonsBSViewModel,
    onDismiss: () -> Unit
) {
    val uiState = vm.getUiState()
    Column(
        modifier = modifier
            .semantics { testTagsAsResourceId = true }
            .padding(dimensionResource(id = R.dimen.margin_xHuge))
            .wrapContentHeight()
    ) {
        // Title
        MmtComposeTextView(
            modifier = Modifier
                .padding(bottom = dimensionResource(id = R.dimen.margin_large))
                .testTag("title_text"),
            text = uiState.title,
            color = colorResource(
                id = R.color.htl_grey
            ), fontSize = spDimensionResource(id = R.dimen.htl_text_size_extra_large),
            mmtFontStyle = latoBlack
        )

        Column(
            modifier = Modifier
                .weight(0.5f, false)
                .verticalScroll(
                    state = rememberScrollState(),
                    enabled = true
                )
        ) {
            val handleCommentChange = { reason: ReasonSelectionItemState, comment: String ->
                vm.handleCommentChange(reason, comment)
            }

            val handleItemSelection: (ReasonSelectionItemState) -> Unit = { reason ->
                vm.handleItemSelection(reason)
            }

            // Skip Approval Reasons
            uiState.skipApprovalReasons.forEach { reason ->
                ReasonSelectionItem(
                    data = reason,
                    onSelection = handleItemSelection,
                    onCommentChange = handleCommentChange
                )
            }

            // Error Text
            if (uiState.showMandatoryError) {
                MmtComposeTextView(
                    modifier = Modifier
                        .padding(
                            top = dimensionResource(id = R.dimen.margin_large),
                            bottom = dimensionResource(id = R.dimen.margin_large)
                        )
                        .testTag("error_text"),
                    text = stringResource(id = R.string.htl_mandatory_information),
                    color = colorResource(id = R.color.color_error_desc_error_color),
                    mmtFontStyle = latoBold
                )
            }

            // Message
            if (uiState.showSpecialMessage) {
                MMTAnnotatedTextView(
                    modifier = Modifier
                        .background(
                            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_extra_small)),
                            color = colorResource(id = R.color.htl_skip_approval_reasons_message_bg_color)
                        )
                        .padding(dimensionResource(id = R.dimen.margin_large))
                        .padding(bottom = dimensionResource(id = R.dimen.margin_small))
                        .testTag("special_message"),
                    text = stringResource(id = R.string.htl_label_skip_approval_reasons_message).parseHtmlString(),
                    color = colorResource(id = R.color.darkGray),
                )
            }
        }

        // Action Buttons
        Row(
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_xLarge)),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val onBackClick = {
                onDismiss()
            }

            MmtComposeTextView(
                modifier = Modifier
                    .padding(horizontal = dimensionResource(id = R.dimen.margin_small))
                    .mmtClickable(onClick = onBackClick)
                    .testTag("back_button"),
                text = stringResource(id = R.string.htl_HOL_QUERY_CARD_BACK).uppercase(),
                mmtFontStyle = latoBlack,
                color = colorResource(id = R.color.htl_corp_approval_back_text_color),
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium)
            )

            Spacer(modifier = Modifier.weight(1f))

            val onConfirmClick: () -> Unit = {
                if (vm.inputSelectionHasErrors().not()) {
                    onDismiss()
                }
                vm.handleSkipApprovalCTA()
            }

            MmtComposeTextView(
                modifier = Modifier
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                colorResource(id = R.color.htl_color_f95776),
                                colorResource(id = R.color.htl_color_f0772c)
                            )
                        ),
                        shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_50))
                    )
                    .mmtClickable(onClick = onConfirmClick)
                    .padding(
                        horizontal = dimensionResource(id = R.dimen.margin_xLarge),
                        vertical = dimensionResource(id = R.dimen.margin_small_extra)
                    )
                    .testTag("confirm_button"),
                text = uiState.actionCTAText,
                mmtFontStyle = latoBlack,
                color = colorResource(id = R.color.white),
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium)
            )
        }
    }
}