package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.corp.CorpPolicyApiError
import com.mmt.hotel.corpapproval.model.response.CorpCategoryReasons
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import kotlinx.parcelize.Parcelize

@Parcelize
data class UpdatedCorpPolicyResponse(val approvingManagers: List<CorpApprovingManager>?,
                                     val corpApprovalInfo: CorpApprovalInfo?,
                                     val reasonForBooking: List<CorpReasons>?,
                                     val skipApprovalReasons: List<CorpReasons>?,
                                     val error: CorpPolicyApiError? = null,
                                     @SerializedName("corpAutobookRequestorConfig")
                                     val corpAutoBookRequestorConfig:CorpAutoBookRequestorConfig?=null) : Parcelable