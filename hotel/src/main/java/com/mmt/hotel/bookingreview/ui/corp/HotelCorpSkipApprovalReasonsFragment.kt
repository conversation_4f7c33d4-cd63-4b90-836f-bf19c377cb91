package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import androidx.core.text.HtmlCompat
import com.mmt.core.util.executeIfCast
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.INITIATE_CHECKOUT_WITH_SKIP_REASON
import com.mmt.hotel.bookingreview.event.HotelCorpSkipApprovalReasonsEvents.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.event.HotelCorpSkipApprovalReasonsEvents.INITIATE_CHECKOUT_WITH_SKIP_APPROVAL_REASON
import com.mmt.hotel.bookingreview.model.corp.CorpSkipApprovalReasonsData
import com.mmt.hotel.bookingreview.ui.HotelBookingReviewActivity
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpSkipApprovalReasonsViewModel
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.databinding.LayoutFragmentHotelCorpSkipApprovalReasonsBinding
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.migration.OptionalInject
import dagger.hilt.android.migration.OptionalInjectCheck
import javax.inject.Inject

/**
 * Fragment to list down skip approval reason
 *
 * create by Varun Airon on 02/02/22
 */
@AndroidEntryPoint
class HotelCorpSkipApprovalReasonsFragment : HotelBottomSheetDialogFragment<HotelCorpSkipApprovalReasonsViewModel, LayoutFragmentHotelCorpSkipApprovalReasonsBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel: HotelEventSharedViewModel? = null

    companion object {
        const val TAG = "HotelCorpSkipApprovalReasonsFragment"
        const val BUNDLE_KEY_SKIP_APPROVAL_DATA = "BUNDLE_KEY_SKIP_APPROVAL_DATA"

        @JvmStatic
        fun instance(skipApprovalReasonsData: CorpSkipApprovalReasonsData): HotelCorpSkipApprovalReasonsFragment {
            return HotelCorpSkipApprovalReasonsFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(BUNDLE_KEY_SKIP_APPROVAL_DATA, skipApprovalReasonsData)
                }
            }
        }
    }

    override fun initViewModel() = getViewModel<HotelCorpSkipApprovalReasonsViewModel>(factory)

    override fun initAndValidate() {
        if(arguments?.getParcelable<CorpSkipApprovalReasonsData>(BUNDLE_KEY_SKIP_APPROVAL_DATA) == null) {
            dismiss()
        }
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel =  viewModel
        viewDataBinding.tvMessage.text = HtmlCompat.fromHtml(getString(R.string.htl_label_skip_approval_reasons_message), HtmlCompat.FROM_HTML_MODE_LEGACY)
    }

    override fun getLayoutId() = R.layout.layout_fragment_hotel_corp_skip_approval_reasons

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            DISMISS_FRAGMENT -> {
                dismissFragment()
            }

            INITIATE_CHECKOUT_WITH_SKIP_APPROVAL_REASON -> {
                event.data.executeIfCast<CorpReasons> {
                    dismissFragment(skipReason = this)
                }
            }

            else -> {
                sendEventToActivity(event)
            }
        }
    }

    private fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }

    /**
     * function to dismiss fragment and to pass the corpReason back to caller to initiate checkout along with the given reason
     */
    private fun dismissFragment(skipReason : CorpReasons? = null) {
        skipReason?.let {
            activitySharedViewModel?.updateEventStream(HotelEvent(INITIATE_CHECKOUT_WITH_SKIP_REASON, it))
        }
        dismiss()
    }
}