package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.constants.ExperimentsHotel
import com.mmt.hotel.common.constants.HotelConstants.DEFAULT_TITLE
import com.mmt.hotel.common.constants.HotelConstants.DOT_AND_SPACE_REGEX
import com.mmt.hotel.common.constants.HotelConstants.ET_ERROR_BG
import com.mmt.hotel.common.constants.HotelConstants.ET_NORMAL_BG
import com.mmt.hotel.common.constants.HotelConstants.FIRST_NAME_REGEX
import com.mmt.hotel.common.constants.HotelConstants.IND_PHONE_REGEX
import com.mmt.hotel.common.constants.HotelConstants.LAST_NAME_REGEX
import com.mmt.hotel.common.constants.HotelConstants.PAN_REGEX
import com.mmt.hotel.common.constants.HotelConstants.PHONE_REGEX
import com.mmt.hotel.common.constants.NATIONALITY_FIELD
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.HotelUtil.validateRegEx
import kotlinx.parcelize.Parcelize

@Parcelize
data class UserInputDetail(var title: String = DEFAULT_TITLE,
                           var name: String = EMPTY_STRING,
                           var nameBg: Int = ET_NORMAL_BG,
                           var nameErrorMsg: String? = null,
                           var surname: String = EMPTY_STRING,
                           var surnameBg: Int = ET_NORMAL_BG,
                           var isdCode: Int = HotelUtil.getIsdCodeForTravellerForm(),
                           var contactNo: String = EMPTY_STRING,
                           var contactNoBg: Int = ET_NORMAL_BG,
                           var contactNoErrorMsg: String? = null,
                           var emailId: String = EMPTY_STRING,
                           var emailIdBg: Int = ET_NORMAL_BG,
                           var emailIdErrorMsg: String? = null,
                           var pan: String = EMPTY_STRING,
                           var panBg: Int = ET_NORMAL_BG,
                           var panErrorMsg: String? = null,
                           var showNameError: Boolean = false,
                           var showSurnameError: Boolean = false,
                           var showContactError: Boolean = false,
                           var showEmailError: Boolean = false,
                           var showCountrySelectionError: Boolean = false,
                           var countryErrorMsg: String? = null,
                           var nationality: String? = null,
                           var showPanError: Boolean = false) : Parcelable {
    fun isNameValid(): Boolean {
        val result: Boolean
        if (name.trim().isEmpty()) {
            result = false
            nameErrorMsg = ResourceProvider.instance.getString(R.string.htl_name_empty)
        } else {
            result = validateRegEx(name, FIRST_NAME_REGEX) && !validateRegEx(name, DOT_AND_SPACE_REGEX)
            nameErrorMsg = if (showSurnameError) {
                nameErrorMsg
            } else if (result)
                null
            else ResourceProvider.instance.getString(R.string.htl_name_error)
        }
        nameBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        showNameError = !result
        return result
    }

    fun isSurnameValid(): Boolean {
        val result: Boolean
        if (surname.trim().isEmpty()) {
            result = false
            nameErrorMsg = ResourceProvider.instance.getString(R.string.htl_name_empty)
        } else {
            result = validateRegEx(surname, LAST_NAME_REGEX) && !validateRegEx(surname, DOT_AND_SPACE_REGEX)
            nameErrorMsg = if (showNameError) {
                nameErrorMsg
            } else if (result)
                null
            else ResourceProvider.instance.getString(R.string.htl_name_error)
        }
        surnameBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        showSurnameError = !result
        return result
    }

    fun isContactNoValid(): Boolean {
        val result: Boolean
        if (contactNo.isEmpty()) {
            result = false
            contactNoErrorMsg = ResourceProvider.instance.getString(R.string.htl_contact_empty)
        } else {
            if(isdCode == FunnelContext.INDIA.mobileCode.toInt()) {
                if(contactNo.length != 10){
                    result = false
                    contactNoErrorMsg = ResourceProvider.instance.getString(R.string.htl_india_contact_length_error)
                } else {
                    result = validateRegEx(contactNo, IND_PHONE_REGEX)
                    contactNoErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.htl_contact_error)
                }
            } else {
                if (contactNo.length < 5 && contactNo.length > 13) {
                    result = false
                    contactNoErrorMsg = ResourceProvider.instance.getString(R.string.htl_contact_length_error)
                } else {
                    result = validateRegEx(contactNo, PHONE_REGEX)
                    contactNoErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.htl_contact_error)
                }
            }
        }
        contactNoBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        showContactError = !result
        return result
    }

    fun isCountrySelected(showCountrySelection: Boolean): Boolean {
        if (!showCountrySelection || ExperimentsHotel.nationality.getPokusValue() == NATIONALITY_FIELD.OPTIONAL.value)
            return true
        val result: Boolean = !(nationality.isNullOrBlank())
        showCountrySelectionError = !result
        countryErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.country_selection_error_message)
        return result
    }

    fun isEmailIdValid(): Boolean {
        val result: Boolean
        if (emailId.isEmpty()) {
            result = false
            emailIdErrorMsg = ResourceProvider.instance.getString(R.string.htl_email_empty)
        } else {
            result = LoginUtil.checkEmail(emailId)
            emailIdErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.htl_email_error)
        }
        emailIdBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        showEmailError = !result
        return result
    }

    fun isPanValid(): Boolean {
        val result: Boolean
        if (pan.isEmpty()) {
            result = false
            panErrorMsg = ResourceProvider.instance.getString(R.string.htl_pan_empty)
        } else {
            result = validateRegEx(pan, PAN_REGEX)
            panErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.htl_pan_error)
        }
        panBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        showPanError = !result
        return result
    }
}