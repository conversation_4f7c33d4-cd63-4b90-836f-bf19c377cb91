package com.mmt.hotel.bookingreview.model.response.property

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelPropertyRules(@SerializedName("title")
                              val title: String,
                              val propertyType:String,
                              val noRulesAvailable:Boolean = false,
                              val myTripsDeeplink:String? = null,
                              val navigationRules: List<NavigationRuleItem>? = null,
                              val rules: List<RuleItem>? = null) : Parcelable