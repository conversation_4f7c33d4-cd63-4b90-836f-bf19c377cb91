package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent

class LoginButtonItemViewModel(
    val showDivider: Boolean,
    private val eventStream: MutableLiveData<HotelEvent>
) : BaseObservable() {
    val background = ResourceProvider.instance.getDrawable(R.drawable.list_item_bg_6)
    fun onClick() {
        eventStream.value = HotelEvent(HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY)
    }
}