package com.mmt.hotel.bookingreview.viewmodel

import android.os.Bundle
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.gommt.logger.LogUtils
import com.mmt.core.country.models.Currency
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelInsuranceAdapter
import com.mmt.hotel.bookingreview.event.HotelBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingReviewConstants
import com.mmt.hotel.bookingreview.model.response.addon.InsuranceDataItem
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.compose.review.dataModel.InsuranceDataItemInterface

/**
 * Created by sunil.jain on 31/05/21.
 */
class InsuranceItemVM(
    val data: InsuranceDataItem,
    val eventStream: MutableLiveData<HotelEvent>,
    val totalGuests: Int,
    val index: Int, //this should be used only for tracking
    val source: String
) : AbstractRecyclerItem {
    val selected = ObservableBoolean(data.isSelected)
    val priceText = ObservableField<String>()
    val resourceProvider = ResourceProvider.instance
    val guestText = ObservableField<String>()
    init {
        setPriceText()
    }

    private fun setPriceText() {
        val currency = try {
            Currency.valueOf(data.currency ?: HotelConstants.CURRENCY_CODE_INDIA)
        } catch (e: IllegalArgumentException) {
            LogUtils.error(TAG, e)
            Currency.INR
        }
        if (selected.get()) {
            priceText.set(currency.symbol + data.totalPrice.toInt())
            guestText.set(resourceProvider.getString(R.string.htl_for_this_trip))
        } else {
            priceText.set(currency.symbol + data.unitPrice)
            guestText.set(data.unitType)
        }
    }

    fun onItemClick() {
        selected.set(!selected.get())
        setPriceText()
        data.isSelected = selected.get()
        val bundle = Bundle().apply {
            putParcelable(BookingReviewConstants.KEY_INSURANCE_ITEM, data)
            putInt(BookingReviewConstants.KEY_POSITION, index + 1)
            putString(BookingReviewConstants.KEY_SOURCE,source)
        }
        eventStream.value = HotelEvent(HotelBookingReviewFragmentEvent.INSURANCE_CLICKED, bundle)
    }

    fun onViewBenefitsClicked() {
        val bundle = Bundle().apply {
            putString(BookingReviewConstants.KEY_URL, data.tncLink)
        }
        eventStream.value =
            HotelEvent(HotelBookingReviewFragmentEvent.INSURANCE_BENEFITS_CLICKED, bundle)
    }

    override fun getItemType(): Int {
        return HotelInsuranceAdapter.TYPE_INSURANCE_ITEM
    }

    companion object {
        const val TAG = "InsuranceItemVM"
    }
}