package com.mmt.hotel.bookingreview.model.response.room

import android.os.Parcelable
import androidx.compose.ui.text.AnnotatedString
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.listingV2.model.response.hotels.BottomSheetData
import kotlinx.parcelize.Parcelize

@Parcelize
data class RoomInclusion(
        @SerializedName("iconUrl")
        val imageUrl: String?,
        @SerializedName("text", alternate = ["value"])
        val text: String? = null,
        val type: String? = null,
        val subText: String? = null,
        val segmentIdentifier: String? = null,
        val iconType: String? = null,
        val code: String? = null,
        val onOffer: Boolean = false,
        val bookable: Boolean = false,
        val category: String? = null,
        val tagList: List<String>? = null,
        val description: String? = null,
        @SerializedName("bulletTexts")
        val bulletTexts: List<String>? = null,
        @SerializedName("descriptionText")
        val descriptionText: String? = null,
        val trailingCtaText: String? = null,
        val trailingCtaBottomSheet: BottomSheetData? = null
) : Parcelable


data class RoomInclusionAnnotatedUiData(
        val imageUrl: String?,
        val title: AnnotatedString,
        val iconType: String? = null,
        val trailingCtaText: String? = null,
        val trailingCtaBottomSheet: BottomSheetData? = null,
        val segmentIdentifier: String? = null)