package com.mmt.hotel.bookingreview.viewmodel

import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.model.response.PopUpData
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.bookingreview.event.HotelCouponFragmentEvent
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import javax.inject.Inject

class BenefitDealConfirmationVM @Inject constructor(val data: PopUpData, val coupon: HotelBookingCoupon) : HotelToolBarViewModel() {

    override fun getTitle(): String {
        return data.title.orEmpty()
    }

    override fun onHandleBackPress() {
        updateEventStream(HotelEvent(HotelCouponFragmentEvent.DISMISS_FRAGMENT))
    }

    fun onCtaClick(){
        updateEventStream(HotelEvent(HotelCouponFragmentEvent.BENEFIT_DEAL_CONFIRMATION_APPROVED, coupon))
    }
}