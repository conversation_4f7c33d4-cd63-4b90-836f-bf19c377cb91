package com.mmt.hotel.bookingreview.ui

import android.os.Bundle
import com.mmt.hotel.R
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomUpErrorFragment
import com.mmt.hotel.base.viewModel.HotelErrorFragmentViewModel.Companion.DISMISS_ERROR_FRAG
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.bookingreview.di.HotelErrorFragmentModule
import com.mmt.hotel.common.model.HotelError
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_DETAIL
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_LIST
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_PREVIOUS_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.CONTINUE_FROM_BOOKING_FRAG
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.DOUBLE_BLACK_NAME_UPDATE
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_FORCE_SHOW_PAH_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.REFRESH_ACTIVITY
import com.mmt.hotel.bookingreview.viewmodel.CheckoutErrorFragmentViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.databinding.HotelCheckoutErrorFragmentBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelCheckoutErrorFragment : HotelBottomUpErrorFragment<CheckoutErrorFragmentViewModel, HotelCheckoutErrorFragmentBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel: HotelEventSharedViewModel? = null

    companion object {
        val TAG = "HotelCheckoutErrorFragment"

        @JvmStatic
        fun getInstance(error: HotelError): HotelCheckoutErrorFragment {
            val fragment = HotelCheckoutErrorFragment()
            val bundle = Bundle()
            bundle.putParcelable(HotelConstants.ERROR_FRAG_ARGS, error);
            fragment.setArguments(bundle);
            return fragment
        }
    }

    override val backgroundViewId: Int
        get() = R.id.view_bg_above_review_screen

    override fun initViewModel() = getViewModel<CheckoutErrorFragmentViewModel>(factory)

    override fun getLayoutId(): Int {
        return R.layout.hotel_checkout_error_fragment
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel(factory)
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            BACK_TO_LIST, BACK_TO_DETAIL, BACK_TO_PREVIOUS_ACTIVITY, DOUBLE_BLACK_NAME_UPDATE,
            CONTINUE_FROM_BOOKING_FRAG, INITIATE_CHECKOUT, OPEN_FORCE_SHOW_PAH_ACTIVITY, REFRESH_ACTIVITY -> {
                activitySharedViewModel?.updateEventStream(event)
            }
            DISMISS_ERROR_FRAG -> {
            }
        }
        super.handleEvents(event)
    }
}