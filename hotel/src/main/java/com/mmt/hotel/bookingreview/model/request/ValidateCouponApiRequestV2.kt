package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import com.mmt.hotel.common.model.ResponseFilterFlag
import com.mmt.hotel.common.model.request.RequestDetails
import kotlinx.parcelize.Parcelize

@Parcelize
data class ValidateCouponApiRequestV2(
    val requestDetails: RequestDetails?,
    val txnKey: String,
    val couponCode: String,
    val removeCoupon: Boolean,
    val expData: String?,
    val quickCheckoutApplicable: Boolean? = false,
    val featureFlags: BookingReviewResponseFilterFlag = BookingReviewResponseFilterFlag()
) : Parcelable