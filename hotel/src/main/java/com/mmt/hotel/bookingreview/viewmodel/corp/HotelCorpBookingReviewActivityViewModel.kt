package com.mmt.hotel.bookingreview.viewmodel.corp

import androidx.lifecycle.viewModelScope
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.util.CommonMigrationHelper
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.helper.BookingReviewRequestHelper
import com.mmt.hotel.bookingreview.helper.HotelBookingPaymentHelper
import com.mmt.hotel.bookingreview.helper.HotelBookingReviewHelper
import com.mmt.hotel.bookingreview.helper.HotelCorpBookingReviewHelper
import com.mmt.hotel.bookingreview.model.response.PostApprovalResponseV2
import com.mmt.hotel.bookingreview.model.response.checkout.ConsentData
import com.mmt.hotel.bookingreview.repository.HotelCorpBookingReviewRepository
import com.mmt.hotel.bookingreview.tracking.BookingReviewPDTHelper
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewPdtV2Tracker
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewTrackingHelper
import com.mmt.hotel.bookingreview.ui.corp.CorpReviewConsentBottomSheetFragment
import com.mmt.hotel.bookingreview.viewmodel.HotelBookingReviewActivityViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.corpapproval.event.HotelCorpApprovalEvents
import com.mmt.hotel.corpapproval.model.CorpApprovalMessageData
import com.mmt.network.HttpUtils
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

class HotelCorpBookingReviewActivityViewModel @Inject constructor(
    private val repository: HotelCorpBookingReviewRepository,
    private val corpBookingReviewHelper: HotelCorpBookingReviewHelper,
    private val bookingReviewRequestHelper: BookingReviewRequestHelper,
    dataWrapper: BookingReviewDataWrapper,
    bookingReviewHelper: HotelBookingReviewHelper,
    trackingHelper: HotelBookingReviewTrackingHelper,
    pdtTracker: BookingReviewPDTHelper,
    pdtV2Tracker: HotelBookingReviewPdtV2Tracker,
    bookingPaymentHelper: HotelBookingPaymentHelper
) : HotelBookingReviewActivityViewModel(
    repository,
    dataWrapper,
    bookingReviewHelper,
    trackingHelper,
    pdtTracker,
    pdtV2Tracker,
    bookingPaymentHelper
) {

    fun approvalPostRequest() {
        viewModelScope.launch {
            val request = bookingReviewRequestHelper.getApprovalPostRequestData(corpBookingReviewHelper, reasonsForTravel, checkDuplicateBooking)
            repository.postApprovalAPIV2(request)
                .catch {
                    updateEventStream(HotelEvent(HotelCorpBookingReviewActivityEvent.APPROVAL_API_ERROR))
                    it.printStackTrace()
                }
                .collect {
                    if(it.statusCode != HttpUtils.HTTP_OK_200){
                        handleError(it)
                    }else if(it.consentData != null){
                        handleConsent(it.consentData)
                    }else if(it.workflowId.isNullOrEmpty()){
                        handleError(it)
                    }else{
                        handleSuccess(it)
                    }
                }
        }
    }

    private fun handleError(response: PostApprovalResponseV2) {
        if(response.error?.code == HotelConstants.AUTOBOOK_COUPON_ERROR_CODE) {
            val data = CorpApprovalMessageData(title = ResourceProvider.instance.getString(R.string.htl_corp_approval_coupon_error_title),
                text = ResourceProvider.instance.getString(R.string.htl_corp_approval_coupon_error_subtitle),
                positiveActionText = ResourceProvider.instance.getString(R.string.htl_OK_GOT_IT),
                positiveAction = HotelCorpApprovalEvents.EVENT_CANCEL)
            updateEventStream(HotelEvent(HotelCorpBookingReviewActivityEvent.OPEN_CORP_MESSAGE_FRAGMENT,data))
        } else {
            updateEventStream(HotelEvent(HotelCorpBookingReviewActivityEvent.APPROVAL_API_ERROR, response))
        }
    }

    private fun handleConsent(consentData: ConsentData) {
        updateEventStream(HotelEvent(HotelBookingReviewActivityEvent.OPEN_CONSENT_DIALOG, Pair(consentData, CorpReviewConsentBottomSheetFragment.SOURCE_APPROVAL)))
    }

    private fun handleSuccess(response: PostApprovalResponseV2) {
        CommonMigrationHelper.instance.refreshUserData()
        response.corpAutoBookRequestorConfig?.let{ data ->
            data.workFlowId = response.workflowId
            updateEventStream(HotelEvent(HotelRequestApprovalReviewEvent.APPROVAL_REQUEST_CONFIRMATION,data))
        }?: kotlin.run{
            updateEventStream(HotelEvent(HotelCorpBookingReviewActivityEvent.OPEN_CORP_HOTEL_BOOKING_APPROVAL_REQUEST_SENT_FRAGMENT, corpBookingReviewHelper.getBookingReviewData()?.userSearchData?.requisitionID))
        }
    }

}