package com.mmt.hotel.bookingreview.viewmodel

import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.core.util.getCurrencySymbol
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.bookingreview.adapter.RatePlanUpgradeAdapter
import com.mmt.hotel.bookingreview.dataModel.hotelDetail.RoomInclusionUiData
import com.mmt.hotel.bookingreview.model.response.BlackUpgradeRatePlan
import com.mmt.hotel.bookingreview.model.response.room.RoomInclusion
import com.mmt.hotel.bookingreview.viewmodel.adapter.RoomInclusionsViewModel
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.KEY_PRICE_AFTER_DISCOUNT

class RatePlanUpgradeItemVM (
    val data: BlackUpgradeRatePlan
): AbstractRecyclerItem {

    fun getPriceString(): String {
        val currencySymbol = HotelCurrencyUtil.getCurrencySymbolOrDefault(data.priceMap?.currency)
        val priceItem = data.priceMap?.details?.find { it.key == KEY_PRICE_AFTER_DISCOUNT }
        return if (priceItem != null) {
            ResourceProvider.instance.getString(
                R.string.htl_text_cost,
                currencySymbol,
                StringUtil.getCommaSeparatedPrice(priceItem.amount)
            )
        } else {
            CoreConstants.EMPTY_STRING
        }
    }

    fun getOriginalPriceMessage(): String {
        return data.priceMap?.originalPriceMsg ?: CoreConstants.EMPTY_STRING
    }

    fun getInclusionItems(): List<LinearLayoutItemData> {
        val items = mutableListOf<LinearLayoutItemData>()
        val inclusions = data.inclusionsList ?: emptyList<RoomInclusion>()
        for (inclusion in inclusions) {
            if (inclusion.text.isNullOrEmpty()) {
                continue
            }
            val uiData = RoomInclusionUiData(
                title =    inclusion.text,
                subTitle = inclusion.subText,
                iconType = inclusion.iconType,
                planType = inclusion.type,
                imageUrl = inclusion.imageUrl,
                marginStart = ResourceProvider.instance.getDimensionPixelSize(R.dimen.margin_tiny),
                trailingCtaText = inclusion.trailingCtaText.orEmpty(),
                trailingCtaBottomSheet = inclusion.trailingCtaBottomSheet
            )
            items.add(
                LinearLayoutItemData(
                    R.layout.htl_booking_inclusion_item,
                    BR.model,
                    RoomInclusionsViewModel(uiData)
                )
            )
        }
        return items
    }

    override fun getItemType(): Int {
        return RatePlanUpgradeAdapter.RATE_PLAN_UPGRADE_ITEM
    }
}