package com.mmt.hotel.bookingreview.viewmodel

import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelErrorFragmentViewModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_DETAIL
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_LIST
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.BACK_TO_PREVIOUS_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.CONTINUE_FROM_BOOKING_FRAG
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.DOUBLE_BLACK_NAME_UPDATE
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.INITIATE_CHECKOUT
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_FORCE_SHOW_PAH_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.OPEN_LOGIN_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.REFRESH_ACTIVITY
import com.mmt.hotel.bookingreview.helper.BookingReviewDataWrapper
import com.mmt.hotel.bookingreview.helper.constants.CheckErrorDialogActions
import com.mmt.hotel.common.model.HotelError
import javax.inject.Inject


class CheckoutErrorFragmentViewModel @Inject constructor(hotelError: HotelError,
                                                         private val dataWrapper: BookingReviewDataWrapper) : HotelErrorFragmentViewModel(hotelError) {

    override fun performAction(@CheckErrorDialogActions action: Int) {
        when (action) {
            CheckErrorDialogActions.GO_TO_DETAIL, CheckErrorDialogActions.GO_TO_HOTEL_DETAIL -> {
                updateEventStream(HotelEvent(BACK_TO_DETAIL, null))
            }
            CheckErrorDialogActions.GO_TO_LISTING -> {
                updateEventStream(HotelEvent(BACK_TO_LIST, null))
            }
            CheckErrorDialogActions.GO_TO_SELECT_ROOM -> {
                updateEventStream(HotelEvent(BACK_TO_PREVIOUS_ACTIVITY, HotelBookingReviewActivityViewModel.NO_RESULT_CODE))
            }
            CheckErrorDialogActions.LOGIN -> {
                updateEventStream(HotelEvent(OPEN_LOGIN_ACTIVITY, null))
            }
            CheckErrorDialogActions.RETRY -> {
                updateEventStream(HotelEvent(REFRESH_ACTIVITY, null))
            }
            CheckErrorDialogActions.SOLD_OUT -> {
                updateEventStream(HotelEvent(BACK_TO_PREVIOUS_ACTIVITY, HotelBookingReviewActivityViewModel.RESULT_CODE_SOLD_OUT_ON_REVIEW))
            }
            CheckErrorDialogActions.GO_TO_OTP -> {
                updateEventStream(HotelEvent(OPEN_FORCE_SHOW_PAH_ACTIVITY, null))
            }
            CheckErrorDialogActions.MAKE_CHECKOUT_REQUEST -> {
                updateEventStream(HotelEvent(CONTINUE_FROM_BOOKING_FRAG, null))
            }
            CheckErrorDialogActions.SKIP_DOUBLE_BLACK_VALIDATION -> {
                dataWrapper.checkoutData.skipDoubleBlack = true
                updateEventStream(HotelEvent(INITIATE_CHECKOUT, null))
            }
            CheckErrorDialogActions.MODIFY_DOUBLE_BLACK_NAME -> {
                updateEventStream(HotelEvent(DOUBLE_BLACK_NAME_UPDATE, null))
            }
            else -> {
                updateEventStream(HotelEvent(DISMISS_ERROR_FRAG, null))
            }
        }
    }

}