package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagFieldV2

class CorpReviewTripTagMultiSelectViewModel(private val corpTripTagData: CorpTripTagFieldV2): HotelCorpTravelDetailFormItemViewModelV2(corpTripTagData, null) {

    fun getAllPossibleValueVMList(): List<CorpTravelDetailOptionViewModelV2> {
        val vmList = ArrayList<CorpTravelDetailOptionViewModelV2>()
        corpTripTagData.attributePossibleValues?.forEach { value ->
            val isSelected = corpTripTagData.attributeSelectedValue?.contains(value) ?: false
            val model = CorpTravelDetailOptionViewModelV2(value, this, isSelected)
            optionsViewModels[value] = model
            vmList.add(model)
        }
        corpTripTagData.possibleValuesAndGST?.forEach { item ->
            if( item.value != null ) {
                val isSelected = corpTripTagData.attributeSelectedValue?.contains(item.value!!) ?: false
                val model = CorpTravelDetailOptionViewModelV2(item.value!!, this, isSelected)
                optionsViewModels[item.value!!] = model
                vmList.add(model)
            }
        }
        return vmList
    }
}