package com.mmt.hotel.bookingreview.ui.corp


import android.os.Bundle
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.ADD_TO_ITINIRARY_FLOW
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent.BACK_TO_HOME_BIZ
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpHtlBookingApprovalRequestSentViewModel
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.databinding.FragmentCorpHtlBookingApprovalRequestSentBinding
import dagger.hilt.android.AndroidEntryPoint
import dagger.hilt.android.migration.OptionalInject
import dagger.hilt.android.migration.OptionalInjectCheck
import javax.inject.Inject

@AndroidEntryPoint
class CorpHtlBookingApprovalRequestSentFragment : HotelFragment<CorpHtlBookingApprovalRequestSentViewModel, FragmentCorpHtlBookingApprovalRequestSentBinding>() {
    @Inject
    lateinit var factory: HotelViewModelFactory
    var activitySharedViewModel: HotelEventSharedViewModel? = null
    var requisitionId: String? = null

    companion object {
        const val TAG = "CorpHtlBookingApprovalRequestSentFragment"
        @JvmStatic
        fun newInstance(requisitionId: String?): CorpHtlBookingApprovalRequestSentFragment {
            val fragment = CorpHtlBookingApprovalRequestSentFragment().apply {
                this.requisitionId = requisitionId
            }
            return fragment
        }
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel.setRequisitionId(requisitionId)
    }

    override fun initViewModel(): CorpHtlBookingApprovalRequestSentViewModel {
        return getViewModel(factory)
    }

    override fun setDataBinding() {
        viewDataBinding.model = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_corp_htl_booking_approval_request_sent
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel(factory)
        setLightStatusBar()
    }

    override fun handleEvents(event: HotelEvent) {
        when(event.eventID) {
            BACK_TO_HOME_BIZ -> {
                sendEventToActivity(event)
            }
            ADD_TO_ITINIRARY_FLOW -> {
                sendEventToActivity(event)
            }
        }
    }

    override fun onHandleBackPress() {
        viewModel.onClick()
    }

    override fun shouldInterceptBackPress() = true

    private fun sendEventToActivity(event: HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }
}