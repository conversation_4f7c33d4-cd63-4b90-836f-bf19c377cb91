package com.mmt.hotel.bookingreview.dataModel.hotelDetail

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.response.room.RoomInclusion
import com.mmt.hotel.bookingreview.viewmodel.adapter.RoomInclusionsViewModel
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.model.response.CancellationTimelineModel
import kotlinx.parcelize.Parcelize

@Parcelize
data class RoomDetailUiDataModel(
    val hotelId: String,
    val roomCode: String,
    val ratePlanCode: String,
    val roomName: String,
    val isMultiRoomUi: Boolean,
    val roomOccupancy: String?,
    var cancellationTimeLine: CancellationTimelineModel?,
    var cancellationPolicy: RoomInclusion?,
    var inclusionItems: List<LinearLayoutItemData>,
    val cancellationPolicyAlert: String?,
    val isEntireProperty: Boolean,
    val inclusionItemsV2: List<RoomInclusionsViewModel>? = null,
    val highlightImage: String? =null,
    val tag: BasicTagInfo? = null
) : Parcelable