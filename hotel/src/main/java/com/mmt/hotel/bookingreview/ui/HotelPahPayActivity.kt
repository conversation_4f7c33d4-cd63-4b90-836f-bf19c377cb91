package com.mmt.hotel.bookingreview.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.widget.Toast
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.gommt.logger.LogUtils
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.tasks.Task
import com.mmt.core.constant.BaseLoginConstants
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.GsonUtils
import com.mmt.core.util.LOBS
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.payment.PaymentResponseVO
import com.mmt.data.model.payment.PaymentStatus
import com.mmt.data.model.util.GenericUtils
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.activity.HotelActivity
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.DISMISS_ACTIVITY
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.DISMISS_WITH_ERROR
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.OPEN_THANK_YOU
import com.mmt.hotel.bookingreview.event.HotelPahPayActivityEvent.SHOW_TOAST_MESSAGE
import com.mmt.hotel.bookingreview.helper.constants.CheckoutErrorCodes
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.viewmodel.HotelPahActivityViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.databinding.ActivityHotelPahPayBinding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelPahPayActivity : HotelActivity<HotelPahActivityViewModel, ActivityHotelPahPayBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory

    companion object {
        const val PAH_INTENT_DATA = "PAH_INTENT_DATA"
        const val TAG = "HotelPahPayActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewDataBinding.viewModel = viewModel
        viewModel.trackPageLoad()
        val client = SmsRetriever.getClient(this)
        val task: Task<Void> = client.startSmsRetriever()
        task.addOnSuccessListener { LogUtils.debug(TAG, "Success") }
        task.addOnFailureListener { e -> LogUtils.error(TAG, e) }
        registerOtpReceiver()
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_hotel_pah_pay
    }

    override fun createViewModel(): HotelPahActivityViewModel {
        return getViewModel(factory)
    }

    override fun createEventSharedViewModel(): HotelEventSharedViewModel {
        return getViewModel()
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            SHOW_TOAST_MESSAGE -> {
                ResourceProvider.instance.showToast(event.data as String, Toast.LENGTH_SHORT)
            }
            DISMISS_ACTIVITY -> {
                finish()
            }
            OPEN_THANK_YOU -> {
                openThankYouPage(event.data as CheckoutResponse)
            }
            DISMISS_WITH_ERROR -> {
                returnToReviewWithError(event.data as CheckoutResponse?)
            }
        }
    }

    private fun openThankYouPage(checkoutResponse: CheckoutResponse) {
        checkoutResponse.bookingID?.let {
            val responseVO = PaymentResponseVO(checkoutResponse.bookingID, "", 0.0f,
                    PaymentStatus.PAYMENT_SUCCESS, "")
            val intent = HotelScreenIntentUtil.getThankyouIntent().apply {
                putExtra(CoreConstants.PAYMENT_RESPONSE, GsonUtils.getInstance().serializeToJson(responseVO))
                putExtra(CoreConstants.EXTRA_INFO_FROM_LOB, GsonUtils.getInstance().serializeToJson(viewModel.pahData.extraLobInfo))
            }
            GenericUtils.startActivityInternal(this, intent)
        }?:run{
            returnToReviewWithError(null)
        }
    }

    private fun returnToReviewWithError(checkoutResponse: CheckoutResponse?) {
        val intent = Intent().apply {
            putExtra(HotelConstants.CHECKOUT_RESPONSE, checkoutResponse)
        }
        setResult(CheckoutErrorCodes.CHECKOUT_ERROR, intent)
        finish()
    }

    private fun registerOtpReceiver(){
        val otpCodeIntentFilter = IntentFilter()
        otpCodeIntentFilter.addAction(BaseLoginConstants.ACTION_MOBILE_VERIFY_OPT_CODE)
        LocalBroadcastManager.getInstance(this).registerReceiver(otpBroadCastReceiver, otpCodeIntentFilter)
    }

    private val otpBroadCastReceiver: BroadcastReceiver = object: BroadcastReceiver(){
        override fun onReceive(context: Context?, intent: Intent?) {
            viewModel.handleOtpBroadcast(intent?.getStringExtra(BaseLoginConstants.OTP_KEY))
        }
    }

    override fun onDestroy() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(otpBroadCastReceiver)
        super.onDestroy()
    }
}