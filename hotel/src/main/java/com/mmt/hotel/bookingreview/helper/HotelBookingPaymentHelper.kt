package com.mmt.hotel.bookingreview.helper

import android.widget.Toast
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.GsonUtils
import com.mmt.data.model.payment.BookingInfo
import com.mmt.data.model.payment.PaymentRequestVO
import com.mmt.data.model.payment.UserVO
import com.mmt.data.model.payment.utils.PaymentConstants
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelErrorFragmentViewModel
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.SUBSCRIPTION_REVIEW_ERROR
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent.WRONG_GSTN_ENTERED
import com.mmt.hotel.bookingreview.helper.constants.CheckErrorDialogActions
import com.mmt.hotel.bookingreview.helper.constants.CheckErrorToastActions
import com.mmt.hotel.bookingreview.helper.constants.CheckoutErrorCodes
import com.mmt.hotel.bookingreview.helper.constants.ErrorHandlerMode
import com.mmt.hotel.bookingreview.model.*
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.viewmodel.HotelBookingReviewActivityViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.PaymentModeTypes
import com.mmt.hotel.common.model.HotelError
import com.mmt.hotel.common.model.request.HotelRequestConstants
import com.mmt.hotel.common.model.response.HotelApiError
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.showEmiPopup
import com.mmt.hotel.compose.review.dataModel.FlexiDetailBottomSheetData
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.old.model.hotelconfig.ErrorInfo
import javax.inject.Inject

/*
    common helper class to handle common function used in bookingReviewViewModel and QuickReviewViewModel
 */
class HotelBookingPaymentHelper @Inject constructor() {

    fun continuePahWithoutCC(bookingReviewHelper: HotelBookingReviewHelper): Boolean {
        if (bookingReviewHelper.getPayMode() == PaymentModeTypes.PAH1
                || bookingReviewHelper.getPayMode() == PaymentModeTypes.PAH_WITHOUT_CC
        ) {
            return true
        }
        return false
    }

    fun canShowFlexiDetailBottomSheet(bookingReviewHelper: HotelBookingReviewHelper): Boolean {
        return bookingReviewHelper.getFlexiDetailBottomSheetData() != null
    }

    fun getFlexiDetailBottomSheetData(bookingReviewHelper: HotelBookingReviewHelper): FlexiDetailBottomSheetData? {
        return bookingReviewHelper.getFlexiDetailBottomSheetData()
    }

    fun continuePahWithCC(bookingReviewHelper: HotelBookingReviewHelper): Boolean {
        if (bookingReviewHelper.getPayMode() == PaymentModeTypes.PAH2
                || bookingReviewHelper.getPayMode() == PaymentModeTypes.PAH_WITH_CC
        ) {
            return true
        }
        return false
    }

    /**
     * cond 1 - card available & wallet not applied
     * cond 2 - emi available
     * cond 3 - bnpl available
     * cond 4 - paymentPlan available
     */
    fun continueWithPayFragment(bookingReviewHelper: HotelBookingReviewHelper): Boolean {
        if ((bookingReviewHelper.isEmiAvailable() && showEmiPopup())
                || (bookingReviewHelper.isRequestToBook() && bookingReviewHelper.rtbAutoCharge())
                || bookingReviewHelper.isBnplAvailable()
                || (bookingReviewHelper.getPaymentPlan() != null)
        ) {
            return true
        }
        return false
    }

    fun getPayOptionData(
            bookingReviewHelper: HotelBookingReviewHelper,
            dataWrapper: BookingReviewDataWrapper, skipReason: CorpReasons? = null
    ): PayOptionData {
        return PayOptionData(
            currencySymbol = bookingReviewHelper.getCurrencySymbol(),
                totalAmount = bookingReviewHelper.getTotalAmount(),
                showEmi = bookingReviewHelper.isEmiAvailable(),
                emiMessage = bookingReviewHelper.getEmiMessage(),
                enableBnpl = bookingReviewHelper.isBnplApplicable(),   // when bnplApplicable flag is true
                showBnpl = bookingReviewHelper.isBnplAvailable(),  // when bnplDetails node is not null
                timeLineModel = dataWrapper.payLaterTimeLineModel,
                rtbPreApproved = bookingReviewHelper.isRtbPreApproved(),
                requestToBook = bookingReviewHelper.isRequestToBook(),
                rtbAutoCharge = bookingReviewHelper.rtbAutoCharge(),
                skipReason = skipReason,
                paymentPlan = bookingReviewHelper.getPaymentPlan(),
                fullPaymentDetails = bookingReviewHelper.getFullPaymentDetails()
        )
    }

    fun getHotelLobInfo(
            dataWrapper: BookingReviewDataWrapper,
            reviewToThankYouTrackingData: ReviewToThankyouTrackingData
    ): HotelLobInfo {
        return HotelLobInfo(
                funnelSrc = dataWrapper.userSearchData?.funnelSrc
                        ?: HotelFunnel.HOTEL.funnelValue,
                transactionKey = dataWrapper.txnKey,
                countryCode = dataWrapper.userSearchData?.countryCode
                        ?: HotelConstants.COUNTRY_CODE_UNKNOWN,
                thankyouTrackingData = reviewToThankYouTrackingData
        )
    }

    /**
     * bookingInfo for payment intent bundle
     */
    fun getBookingInfo(
            responseData: CheckoutResponse,
            dataWrapper: BookingReviewDataWrapper
    ): BookingInfo {
        val product = when (dataWrapper.userSearchData?.countryCode) {
            HotelConstants.COUNTRY_CODE_INDIA -> HotelConstants.PAYMENT_DOM_HOTEL_PRODUCT
            else -> HotelConstants.PAYMENT_INTL_HOTEL_PRODUCT
        }

        return BookingInfo(
                responseData.checkoutId,
                HotelRequestConstants.CHANNEL,
                product,
                null,
                responseData.bookingID,
                responseData.totalAmount?.toFloat() ?: 0.0f,
                responseData.totalAmount?.toFloat() ?: 0.0f,
                "INR",
                null,
                0.0f
        )
    }

    /**
     * user detail for payment intent bundle
     */
    fun getUserVO(checkoutData: CheckoutData): UserVO {
        return UserVO().apply {
            com.mmt.auth.login.util.LoginUtils.loggedInUser?.let {
                loggedInEmail = it.emailId
                setIsUserLoggedIn(true)
            } ?: run {
                setIsUserLoggedIn(false)
            }
            travellerEmail = checkoutData.travellerDetailList[0].emailID
            mobile = checkoutData.travellerDetailList[0].mobileNo
        }
    }

    /**
     *  Required to open top lob fragment in payment screen
     */
    fun getPaymentIntentExtra(dataWrapper: BookingReviewDataWrapper): HashMap<String, String> {
        val paymentMap = HashMap<String, String>()
        val userSearchData = dataWrapper.userSearchData
        paymentMap[PaymentRequestVO.FRAGMENT_DATA] = GsonUtils.getInstance().serializeToJson(userSearchData)
        return paymentMap
    }

    /**
     * object creation for PAH intent bundle
     */
    fun getPahIntentData(bookingReviewHelper: HotelBookingReviewHelper, dataWrapper: BookingReviewDataWrapper,
                         reviewToThankYouTrackingData: ReviewToThankyouTrackingData): PahIntentData? {
        val checkOutData = dataWrapper.getPaymentCheckoutData() ?: run {
            return null
        }
        return PahIntentData(
            currencySymbol = bookingReviewHelper.getCurrencySymbol(),
            totalAmount = bookingReviewHelper.getTotalAmount().toString(),
            userSearchData = dataWrapper.userSearchData!!,
            checkoutData = checkOutData,
            extraLobInfo = getHotelLobInfo(dataWrapper, reviewToThankYouTrackingData)
        )
    }

    /**
     * object creation for payment intent bundle
     */
    fun getPaymentIntentData(bookingReviewHelper: HotelBookingReviewHelper, dataWrapper: BookingReviewDataWrapper,
            reviewToThankYouTrackingData: ReviewToThankyouTrackingData): PaymentIntentData? {
        val checkOutData = dataWrapper.getPaymentCheckoutData() ?: run {
            return null
        }
        return PaymentIntentData(extraLobInfo = getHotelLobInfo(dataWrapper, reviewToThankYouTrackingData),
                paymentRequest = PaymentRequestVO().apply {
                    paymentType = checkOutData.paymentType
                    fragmentId = PaymentConstants.PaymentLobFragment.HOTEL_PAYMENT_FRAGMENT
                    thankYouActionUrl = HotelScreenIntentUtil.getThankyouIntentAction()
                    bookingInfo = getBookingInfo(checkOutData.responseData!!, dataWrapper)
                    userVO = getUserVO(checkOutData)
                    isWalletBonus = bookingReviewHelper.isWalletApplied()
                    bonusAmount = bookingReviewHelper.getWalletAmount().toFloat()
                    extra = getPaymentIntentExtra(dataWrapper)
                    qcMetaData = dataWrapper.qcMetaData
                }
        )
    }

    /**
     * handle checkoutapi error cases
     */
    fun handleCheckoutError(error: HotelApiError, dataWrapper: BookingReviewDataWrapper): HotelEvent? {
        return when (error.code) {
            CheckoutErrorCodes.WRONG_GSTN_ERROR -> {
                HotelEvent(WRONG_GSTN_ENTERED)
            }
            CheckoutErrorCodes.SUBSCRIPTION_ERROR_CODE -> {
                HotelEvent(SUBSCRIPTION_REVIEW_ERROR, error.errorTitle)
            }
            CheckoutErrorCodes.DOUBLE_BLACK_OTHER_ERROR_CODE,
            CheckoutErrorCodes.DOUBLE_BLACK_SOCKET_ERROR_CODE,
            CheckoutErrorCodes.DOUBLE_BLACK_VALIDATION_ERROR_CODE,
            CheckoutErrorCodes.DOUBLE_BLACK_EMPTY_RESPONSE_ERROR_CODE -> {
                val hotelError = HotelError(
                        title = String.format(ResourceProvider.instance.getString(R.string.htl_double_black_error_title),dataWrapper.availResponse?.doubleBlackInfo?.registeredFirstName),
                        msg = ResourceProvider.instance.getString(R.string.htl_DOUBLE_BLACK_ERROR_MESSAGE),
                        positiveBtnText = ResourceProvider.instance.getString(R.string.htl_CONTINUE_TO_BOOK),
                        negativeBtnText = null,
                        positiveAction = CheckErrorDialogActions.SKIP_DOUBLE_BLACK_VALIDATION,
                        negativeAction = null
                )
                createErrorDialogEvent(hotelError)
            }
            //SavedCardPaymentActivity.PAYMENT_FAILED.toString() -> { ToDo Ankur
            "104" -> {
                val hotelError = HotelError(
                        title = ResourceProvider.instance.getString(R.string.htl_IDS_STR_PAYMENT_FAILURE),
                        msg = ResourceProvider.instance.getString(R.string.htl_PROBLEM_CONNECTING_WITH_BANK),
                        positiveBtnText = ResourceProvider.instance.getString(R.string.htl_retry_payment),
                        negativeBtnText = null,
                        positiveAction = CheckErrorDialogActions.MAKE_CHECKOUT_REQUEST,
                        negativeAction = null
                )
                createErrorDialogEvent(hotelError)
            }
            //handle Request To Book error case
            CheckoutErrorCodes.RTB_MULTIPLE_REQ_ERROR_CODE -> {
                val hotelError = HotelError(
                        title = error.message.orEmpty(),
                        msg = ResourceProvider.instance.getString(R.string.htl_rtb_multiple_open_req_text),
                        positiveBtnText = ResourceProvider.instance.getString(R.string.htl_pay_to_request),
                        negativeBtnText = ResourceProvider.instance.getString(R.string.htl_cancel),
                        positiveAction = CheckErrorDialogActions.MAKE_CHECKOUT_REQUEST,
                        negativeAction = HotelErrorFragmentViewModel.DISMISS_POPUP,
                        multipleOpenRequest = true
                )
                createRtbErrorDialogEvent(hotelError)
            }
            CheckoutErrorCodes.RTB_TOO_MANY_REQ_ERROR_CODE -> {
                val hotelError = HotelError(
                        title = error.message.orEmpty(),
                        msg = ResourceProvider.instance.getString(R.string.htl_rtb_too_many_req_text),
                        positiveBtnText = ResourceProvider.instance.getString(R.string.htl_view_open_requests),
                        negativeBtnText = ResourceProvider.instance.getString(R.string.htl_cancel),
                        positiveAction = CheckErrorDialogActions.VIEW_OPEN_REQUESTS,
                        negativeAction = HotelErrorFragmentViewModel.DISMISS_POPUP,
                        multipleOpenRequest = false
                )
                createRtbErrorDialogEvent(hotelError)
            }
            CheckoutErrorCodes.GBF_ERROR_CODE,CheckoutErrorCodes.OSBA_ERROR_CODE -> {
                val hotelError = HotelError(
                    title = error.errorTitle.orEmpty(),
                    msg = error.message,
                    positiveBtnText = ResourceProvider.instance.getString(R.string.htl_OK),
                    negativeBtnText = null,
                    positiveAction = CheckErrorDialogActions.DISMISS_POPUP,
                    negativeAction = null,
                    multipleOpenRequest = false
                )
                createErrorDialogEvent(hotelError)
            }
            else -> handleOtherErrors(error)
        }
    }

    private fun handleOtherErrors(error: HotelApiError): HotelEvent? {
        val errorInfo = HotelCheckOutErrorUtil.getErrorInfo(error) ?: return null
        return when (errorInfo.handlerType) {
            ErrorHandlerMode.DIALOG -> {
                val hotelError = HotelError(title = errorInfo.title, msg = errorInfo.msgToOverride, positiveBtnText = errorInfo.positiveBtnText,
                        negativeBtnText = errorInfo.negativeBtnText, positiveAction = errorInfo.positiveAction, negativeAction = errorInfo.negativeAction)
                createErrorDialogEvent(hotelError)
            }
            ErrorHandlerMode.TOAST -> createErrorToastEvent(errorInfo)
            else -> null
        }
    }

    private fun createErrorDialogEvent(errorInfo: HotelError): HotelEvent {
        return HotelEvent(HotelBookingReviewActivityEvent.OPEN_CHECKOUT_ERROR_FRAGMENT, errorInfo)
    }
    private fun createRtbErrorDialogEvent(errorInfo: HotelError): HotelEvent {
        return HotelEvent(HotelBookingReviewActivityEvent.OPEN_RTB_ERROR_FRAGMENT, errorInfo)
    }

    private fun createErrorToastEvent(errorInfo: ErrorInfo): HotelEvent? {
        ResourceProvider.instance.showToast(errorInfo.msgToOverride, Toast.LENGTH_LONG)
        return when (errorInfo.toastAction) {
            CheckErrorToastActions.DO_NOTHING -> HotelEvent(HotelBookingReviewActivityEvent.HIDE_PROGRESS_DIALOG, null)
            CheckErrorToastActions.TOAST_GO_TO_DETAIL -> HotelEvent(HotelBookingReviewActivityEvent.BACK_TO_DETAIL, null)
            CheckErrorToastActions.TOAST_GO_TO_LISTING -> HotelEvent(HotelBookingReviewActivityEvent.BACK_TO_LIST, null)
            CheckErrorToastActions.TOAST_GO_TO_SELECT_ROOM -> HotelEvent(HotelBookingReviewActivityEvent.BACK_TO_PREVIOUS_ACTIVITY, HotelBookingReviewActivityViewModel.NO_RESULT_CODE)
            CheckErrorToastActions.TOAST_SOLD_OUT -> HotelEvent(HotelBookingReviewActivityEvent.BACK_TO_PREVIOUS_ACTIVITY, HotelBookingReviewActivityViewModel.RESULT_CODE_SOLD_OUT_ON_REVIEW)
            else -> null
        }
    }
}