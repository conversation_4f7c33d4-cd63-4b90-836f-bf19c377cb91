package com.mmt.hotel.bookingreview.helper

object HotelInventoryUnblockUtil {

    var hotelList:ArrayList<String>? = null

    fun fetchUnblockInventory():List<String>? {
        return hotelList
    }

    fun saveUnblockInventory(hotelId: String){
        if(hotelList == null) hotelList = arrayListOf()
        hotelList?.let {
            if(!it.contains(hotelId)){
                it.add(hotelId)
            }
        }
    }

    fun clearUnblockInventory(){
        hotelList = null
    }
}