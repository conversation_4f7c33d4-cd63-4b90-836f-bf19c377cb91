package com.mmt.hotel.bookingreview.viewmodel.corp

import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.corpapproval.event.HotelCorpApprovalEvents
import com.mmt.hotel.corpapproval.model.response.CorpReasons

/**
 * model class to hold skip reason data and to handle behaviour on selecting the reason
 *
 * create by <PERSON><PERSON><PERSON> on 02/02/22
 */
class CorpSkipApprovalReasonObservableModel(
    val reason: CorpReasons,
    val eventStream: MutableLiveData<HotelEvent>,
    val comment: ObservableField<String>,
    val showDivider: Boolean = false
) {
    val showCommentBox = ObservableBoolean(false)

    val isSelected = ObservableBoolean(false)

    val hintTextColor = ObservableInt(R.color.view_disabled_grey)

    fun onOptionClicked() {
        if (isSelected.get()) {
            return
        }
        isSelected.set(true)
        eventStream.postValue(
            HotelEvent(
                HotelCorpApprovalEvents.ON_SELECTING_SKIP_APPROVAL_REASON,
                this
            )
        )
    }

    fun onEditBoxTextChange() {
        eventStream.postValue(HotelEvent(HotelCorpApprovalEvents.EVENT_COMMENT_TEXT_CHANGE))
    }
}