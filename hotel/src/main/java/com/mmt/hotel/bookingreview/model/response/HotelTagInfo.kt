package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.common.model.response.persuasion.PersuasionTimer
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelTagInfo(val icon: String?,
                        @SerializedName("title", alternate = ["text"])
                        val title: String?,
                        val background: String?,
                        val type: String,
                        val titleColor: String? = null,
                        val iconColor: String? = null,
                        val stickyText: String? = null,
                        val isSticky: Boolean = false,
                        val timer: PersuasionTimer? = null
) : Parcelable {

    companion object {
        const val TYPE_SCARCITY = "SCARCITY"
    }
}