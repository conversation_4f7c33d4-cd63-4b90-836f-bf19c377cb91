package com.mmt.hotel.bookingreview.helper.constants

import androidx.annotation.StringDef

class BookingRecyclerAdapterItemKeys {

    @Retention(AnnotationRetention.SOURCE)
    @StringDef(
        value = [
            PROPERTY_DETAIL,
            REQUEST_CALLBACK,
            BOOKING_PRICE_BREAKUP,
            COUPONS_DETAIL,
            PAYMENT_OPTIONS,
            AD<PERSON><PERSON>ONAL_FEES,
            PROPERTY_RULES,
            TRAVELLER_DETAIL_INPUT,
            SPECIAL_REQUEST,
            DOUBLE_BLACK_CARD,
            PAYMENT_BUTTON,
            BLACK_CARD,
            TRIP_DETAILS_CARD,
            TRIPMONEY_BNPL_CARD,
            CORP_ADD_TRAVELLER,
            CORP_ADD_CO_TRAVELLER,
            CORP_REASON_FOR_BOOKING,
            CORP_GST,
            CORP_TRIP_TAG,
            CORP_APPROVAL_BTNS,
            CORP_PRICE_DETAIL,
            INSURANCE,
            CORP_APPROVAL_WORK_FLOW_STATUS,
            BPG_CARD,
            BOOKING_INFO_CARD,
            GST_DETAIL_CARD,
            CORP_ADD_GST_INFORMATION
        ]
    )
    annotation class BookingRecyclerAdapterItemKeys
    companion object {
        const val PROPERTY_DETAIL = "pd"
        const val ADD_ONS = "ao"
        const val ROOM_INFO = "ri"
        const val REVIEW_PRICE_ALERT = "rpa"
        const val BOOKING_PRICE_BREAKUP = "bpb"
        const val COUPONS_DETAIL = "cd"
        const val PAYMENT_OPTIONS = "po"
        const val ADDITIONAL_FEES = "af"
        const val PROPERTY_RULES = "pr"
        const val TRAVELLER_DETAIL_INPUT = "td"
        const val PAN_TCS_WIDGET = "ptw"
        const val SPECIAL_REQUEST = "sr"
        const val TERMS_CONDITION_V2 = "tcv2"
        const val CAMPAIGN_ALERT = "ca"
        const val DOUBLE_BLACK_CARD = "dbc"
        const val PAYMENT_BUTTON = "pb"
        const val BLACK_CARD = "bc"
        const val TRIP_DETAILS_CARD = "tdc"
        const val PAYMENT_POLICY_CARD = "ppc"
        const val TRIPMONEY_BNPL_CARD = "tmbc"
        const val MMT_EXCLUSIVE_CARD = "mexc"
        const val REQUEST_CALLBACK = "rcb"

        const val CORP_ADD_TRAVELLER = "cad"
        const val CORP_ADD_CO_TRAVELLER = "cacd"
        const val CORP_REASON_FOR_BOOKING = "crfb"
        const val CORP_GST = "cg"
        const val CORP_TRIP_TAG = "ctt"
        const val CORP_APPROVAL_BTNS = "cab"
        const val CORP_PRICE_DETAIL = "cpd"
        const val INSURANCE = "ins"
        const val CORP_APPROVAL_WORK_FLOW_STATUS = "cawfs"
        const val CORP_MSME_OFFER_CARD = "msme"
        const val BPG_CARD = "bic"
        const val BOOKING_INFO_CARD = "pbi"
        const val CORP_ADD_GST_INFORMATION = "cga"
        const val GST_DETAIL_CARD = "gst"
        const val RTB_CARD = "rtb2"
        const val CORP_SUBSCRIPTION_CARD = "csc"
        const val CHARITY_CARD = "cht"
        const val HOTEL_CLOUD = "hc"
        const val BUSINESS_FUNNEL_IDENTIFICATION = "bfi"
        const val CORP_APPROVAL_GUEST_HOUSE_INFO_CARD = "caghi"
        const val MMT_EXCLUSIVE_V2 = "mec"
        const val INTERNATIONAL_ROAMING_CARD = "irc"
        const val FREE_ADD_ON_CARD = "fao"
        const val REVIEW_STICKY_CARD = "rsc"
    }

}