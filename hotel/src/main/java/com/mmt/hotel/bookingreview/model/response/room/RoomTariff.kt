package com.mmt.hotel.bookingreview.model.response.room

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class RoomTariff(val numberOfAdults: Int,
                      val numberOfChildren: Int,
                      val roomCount: Int,
                      val childAges: List<Int>?) : Parcelable{
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (other is RoomTariff) {
            if (other === this) {
                return true
            }
            if (this.numberOfAdults == other.numberOfAdults &&
                    this.numberOfChildren == other.numberOfChildren &&
                    this.roomCount == other.roomCount) {
                return true
            }
        }
        return false
    }

    override fun hashCode(): Int {
        return ((numberOfAdults * 31 + numberOfChildren) * 31 + roomCount) * 31
    }

}