package com.mmt.hotel.bookingreview.dataModel

import android.os.Parcelable
import com.mmt.hotel.bookingreview.model.BookingReviewData
import kotlinx.parcelize.Parcelize

@Parcelize
class CouponFragmentData(
        val bookingReviewData: BookingReviewData?,
        val txnKey: String,
        val coupons: List<CouponItemUIData>?,
        val countryCode: String,
        val expData: String?,
        val showBnplText: Boolean = false,
        val isDayUseFunnel : Boolean = false,
        val quickCheckoutApplicable : Boolean = false,
        val shownBenefitDealInCoupons : <PERSON>olean = false,
) : Parcelable