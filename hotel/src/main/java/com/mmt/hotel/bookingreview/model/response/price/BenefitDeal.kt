package com.mmt.hotel.bookingreview.model.response.price

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.base.model.response.PopUpData
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.common.model.response.persuasionCards.CardActionV2
import com.mmt.hotel.common.model.response.persuasionCards.IconTag
import com.mmt.hotel.detail.model.response.BgLinearGradient
import kotlinx.android.parcel.Parcelize

@Parcelize
data class BenefitDeal(
    val iconUrl: String?,
    val title: String?,
    @SerializedName("subTitle",alternate = ["description"])
    val subTitle: String?,
    val promoIcon: String?,
    val bgLinearGradient: BgLinearGradient?,
    val subTextBgGradient: BgLinearGradient?,
    val dealCoupons: List<HotelBookingCoupon>?,
    val persuasionText: String? = null,
    val iconTags: IconTag?,
    val uiIdentifier: String?,
    val tncText: String? = null,
    val tncUrl: String? = null,
    val cta: CardActionV2? = null
) : Parcelable
