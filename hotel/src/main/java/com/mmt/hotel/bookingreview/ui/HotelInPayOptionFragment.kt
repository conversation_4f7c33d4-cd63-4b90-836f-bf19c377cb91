package com.mmt.hotel.bookingreview.ui

import android.os.Bundle
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.model.PayOptionData
import com.mmt.hotel.databinding.HotelInPayOptionFragmentBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HotelInPayOptionFragment : HotelPayOptionFragment<HotelInPayOptionFragmentBinding>() {

    companion object {
        @JvmStatic
        fun getInstance(payOptionData: PayOptionData): HotelInPayOptionFragment {
            val paymentDialogFragment = HotelInPayOptionFragment()
            val bundle = Bundle()
            bundle.putParcelable(PAYMENT_OPTION_ARGS, payOptionData);
            paymentDialogFragment.arguments = bundle;
            return paymentDialogFragment
        }
    }

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.hotel_in_pay_option_fragment
    }
}