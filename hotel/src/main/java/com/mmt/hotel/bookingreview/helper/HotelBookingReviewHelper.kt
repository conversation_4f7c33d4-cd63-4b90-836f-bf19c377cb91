package com.mmt.hotel.bookingreview.helper

import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.currency.CurrencyUtil
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.StringUtil
import com.mmt.data.model.payment.PaymentType
import com.mmt.hotel.R
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.helper.constants.PanCardState
import com.mmt.hotel.bookingreview.helper.constants.PriceKey
import com.mmt.hotel.bookingreview.model.*
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.request.AddOnSelected
import com.mmt.hotel.bookingreview.model.request.AddOnSelectedItem
import com.mmt.hotel.bookingreview.model.request.AddOnUnitSelected
import com.mmt.hotel.bookingreview.model.response.*
import com.mmt.hotel.bookingreview.model.response.addon.AddonDataV2
import com.mmt.hotel.bookingreview.model.response.addon.AddonV2Item
import com.mmt.hotel.bookingreview.model.response.coupon.HotelBookingCoupon
import com.mmt.hotel.bookingreview.model.response.price.AvailResponse
import com.mmt.hotel.bookingreview.model.response.room.RoomRatePlan
import com.mmt.hotel.bookingreview.model.response.validatecoupon.ValidateApiResponseV2
import com.mmt.hotel.bookingreview.ui.hotelTags.HotelTagPlaceholder
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.PaymentModeTypes
import com.mmt.hotel.common.data.RequestDetailsData
import com.mmt.hotel.common.model.PromoConsent
import com.mmt.hotel.common.model.request.HotelRequestConstants
import com.mmt.hotel.common.model.request.RoomCriteriaV2
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.model.response.HotelsUserBlackInfo
import com.mmt.hotel.common.model.tracking.PriceTrackingData
import com.mmt.hotel.common.util.*
import com.mmt.hotel.compose.noCostEmi.dataModel.NoCostEmiBundleData
import com.mmt.hotel.compose.review.dataModel.FlexiDetailBottomSheetData
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.dayuse.model.request.SlotAvailRequestData
import com.mmt.hotel.old.hotelreview.model.request.checkout.SpecialCheckoutRequest
import com.mmt.hotel.old.hotelreview.model.request.checkout.SpecialRequestItem
import com.mmt.hotel.old.model.hotelreview.response.specialRequest.Category
import com.mmt.hotel.old.model.hotelreview.response.specialRequest.SpecialRequestForm
import com.mmt.hotel.selectRoom.helper.constants.RoomSearchType
import com.mmt.uikit.util.isNotNullAndEmpty
import java.util.*
import javax.inject.Inject
import kotlin.math.max


open class HotelBookingReviewHelper @Inject constructor(private val dataWrapper: BookingReviewDataWrapper,
                                                        private val bookingPaymentHelper: HotelBookingPaymentHelper) {

    companion object {
        const val CHARITY_ADD_ON_ID = "CHARITY_ID"
        const val CHARITY_ADD_ON_ID_V2 = "CHARITY_ID_V2"
        const val INSURANCE_ADD_ON_TYPE = "INSURANCE"
        const val INSURANCE_ADD_ON_ID = "INSURANCE_ID"
        const val INSURANCE_WIDGET_ID = "INSURANCE_WIDGET"
        const val CORP_SUBSCRIPTION_ADD_ON_ID = "MBIZ_SUBSCRIPTION"
        const val DUMMY_ADD_ON_ID = "DUMMY"
    }

    /*
    *    return CharityAddon data if addon
    *    id equals to CHARITY_ADD_ON_ID
    * */
    fun getCharityAddonData(charityId : String = CHARITY_ADD_ON_ID): AddonDataV2? {
        val addOns: List<AddonDataV2> = dataWrapper.addons ?: emptyList()
        if (addOns.isEmpty()) {
            return null
        }
        for (addOn in addOns) {
            if (charityId.equals(addOn.id, true)) {
                return addOn
            }
        }
        return null
    }

    fun updateDomesticGSTNDetails(data: DomesticGSTNDetails?) {
        dataWrapper.updateDomesticGSTNDetails(data)
    }

    /*
    *   This method provides coupon which needs to be apply on first validate coupon api call,
    *   priority will be given to user applied coupon on detail/select room page,
    *   otherwise autoApplied coupon, if it exists
    * */
    fun getApplicableCoupon(): HotelBookingCoupon? {
        val coupon = checkForUserAppliedCoupon()
        if (coupon != null) {
            return coupon
        }
        return getAutoAppliedCouponCode()
    }

    /*
    *    return HotelBookingCoupon, if any coupon exits in AvailApiResponse
    *    with flag autoApplied set to true otherwise null
    * */
    fun getAutoAppliedCouponCode(): HotelBookingCoupon? {
        val autoAppliedCoupon: HotelBookingCoupon? = null
        val coupons: List<HotelBookingCoupon> = dataWrapper.coupons ?: emptyList()
        if (coupons.isEmpty()) {
            return autoAppliedCoupon
        }

        for (coupon in coupons) {
            if (coupon.autoApplicable) {
                return coupon
            }
        }
        return autoAppliedCoupon
    }

    /*
    *   return coupon if user applied coupon on detail/select room page
    *   exits in coupons provided by avail api,otherwise null
    * */
    private fun checkForUserAppliedCoupon(): HotelBookingCoupon? {
        val appliedCoupon = dataWrapper.bookingReviewData?.userAppliedCoupon
        if (appliedCoupon.isNullOrEmpty()) {
            return null
        }
        val coupons: List<HotelBookingCoupon> = dataWrapper.coupons ?: emptyList()
        if (coupons.isEmpty()) {
            return null
        }
        for (coupon in coupons) {
            if (appliedCoupon.equals(coupon.couponCode, true)) {
                return coupon
            }
        }
        return null
    }

    /**
     * is BNPL applicable as of now with coupen,insurance,rateplan
     */
    fun isBnplApplicable(): Boolean {
        return dataWrapper.bnplDetails?.bnplApplicable ?: false
    }

    fun getFullPaymentDetails(): HotelFullPaymentDetails?{
        return dataWrapper.fullPaymentDetails
    }

    /**
     * is QuickCheckoutApplicable applicable for quick checkout flow for frequent user and their paymode are saved
     */
    fun isQuickCheckoutApplicable(): Boolean {
        return (dataWrapper.quickCheckoutApplicable ?: false) &&  !LoginUtils.isCorporateUser && LoginUtils.isIndiaFunnelContext()
    }



    fun getBnplDetails() : HotelBnplDetails? {
        return dataWrapper.bnplDetails
    }

    /**
     * is BNPL available with selected rate plan
     */
    fun isBnplAvailable(): Boolean {
        return dataWrapper.bnplDetails != null
    }

    fun isEmiAvailable(): Boolean {
        return dataWrapper.emiDetails?.emiAvailable
                ?: false
    }

    fun getEmiMessage(): String? {
        return dataWrapper.emiDetails?.message
    }

    fun isBlackEligible(): Boolean {
        return dataWrapper.availResponse?.featureFlags?.blackEligible
                ?: false
    }

    fun getCorrelationKey(): String? {
        return dataWrapper.correlationKey
    }

    fun getTxnKey(): String? {
        return dataWrapper.txnKey
    }

    fun isB2CPinCodeValidationMandatory(): Boolean {
        return dataWrapper.isB2CPinCodeValidationMandatory()
    }

    fun getExpData() = dataWrapper.expData

    fun getCountryCode(): String? {
        return dataWrapper.userSearchData?.countryCode
    }

    fun getLocationId(): String? {
        return dataWrapper.userSearchData?.locationId
    }

    fun getPayMode(): String {
        return dataWrapper.availResponse?.featureFlags?.payMode
                ?: PaymentModeTypes.PAS
    }

    fun getPaymentType(): PaymentType {
        return dataWrapper.paymentType ?: PaymentType.FULL_PAYMENT
    }

    fun getCurrency() : String? {
        return dataWrapper.priceBreakUp?.currency
    }

    fun getCurrencySymbol() = HotelCurrencyUtil.getCurrencySymbolOrDefault(getCurrency())

    fun isRealWalletAvailable(): Boolean {
        return dataWrapper.availResponse?.featureFlags?.realWalletAvailable
                ?: false
    }

    fun isForeignTravel(): Boolean {
        return dataWrapper.availResponse?.featureFlags?.foreignTravel
                ?: false
    }

    fun isLeadPassengerMandatoryPerRoom(): Boolean {
        return dataWrapper.availResponse?.featureFlags?.leadPassengerMandatoryPerRoom
                ?: false
    }

    fun getRoomCount(): Int {
        return dataWrapper.userSearchData?.occupancyData?.roomCount
                ?: 0
    }

    fun getTotalAmount(): Double {
        return getAmount(PriceKey.TOTAL)
    }

    fun isRequestToBook(): Boolean {
        return dataWrapper.availResponse?.featureFlags?.requestToBook
                ?: false
    }

    fun rtbAutoCharge(): Boolean {
        return dataWrapper.availResponse?.featureFlags?.rtbAutoCharge
                ?: false
    }

    fun isRtbPreApproved(): Boolean {
        return dataWrapper.availResponse?.featureFlags?.rtbPreApproved
                ?: false
    }

    fun isRequestToBookFlow(): Boolean {
        return isRequestToBook() && !isRtbPreApproved()
    }

    fun getBaseAmount(): Double {
        return getAmount(PriceKey.BASE)
    }

    fun getTaxAmount(): Double {
        return getAmount(PriceKey.TAXES)
    }

    fun getPriceAfterDiscount(): Double{
        return getAmount(PriceKey.DISCOUNTED_PRICE)
    }

    fun getSericeChargeAndCommissionAmount(): Double {
        return (getBreakupAmount(PriceKey.TAXES, PriceKey.SERVICE_CHARGE) + getBreakupAmount(PriceKey.TAXES, PriceKey.MARK_UP))
    }

    fun getMmtServiceFee(): Double {
        return getBreakupAmount(PriceKey.TAXES, PriceKey.SERVICE_FEES)
    }

    fun getDiscountAmount(): Double {
        return getAmount(PriceKey.DISCOUNT)
    }

    fun getCdfDiscountAmount(): Double {
        return getBreakupAmount(PriceKey.DISCOUNT, PriceKey.CDF_DISCOUNT)
    }

    fun getWalletAmount(): Double {
        return getBreakupAmount(PriceKey.DISCOUNT, PriceKey.WALLET)
    }

    fun isWalletApplied(): Boolean {
        return getWalletAmount() > 0
    }

    fun getSpecialRequest(): SpecialRequestForm? {
        return dataWrapper.specialRequestForm
    }

    fun getHotelPolicyData(data: BookingReviewData?): HotelPolicyBundleData {
        return HotelPolicyBundleData(txnKey = dataWrapper.txnKey,
                checkInTime = data?.userSearchData?.checkInTime,
                checkOutTime = data?.userSearchData?.checkOutTime,
                countryCode = data?.userSearchData?.countryCode)
    }

    fun getNoCostEmiData(): NoCostEmiBundleData? {
        val data =  dataWrapper.bookingReviewData
        val pricingKey = dataWrapper.availResponse?.hotelPriceBreakUp?.pricingKey
        val noCostEmiCouponsList = getNoCostEmiCouponsList()
        val hotelName = dataWrapper.availResponse?.hotelInfo?.name ?: CoreConstants.EMPTY_STRING
        val userSearchData = data?.userSearchData?:return null
        return NoCostEmiBundleData(
                checkInDate = userSearchData.checkInDate,
                checkOutDate = userSearchData.checkOutDate,
                countryCode = userSearchData.countryCode,
                expData = data.expData,
                requestDetailsData = data.let {
                    RequestDetailsData(
                            funnel = userSearchData.funnelSrc,
                            pageContext = HotelRequestConstants.PAGE_CONTEXT_REVIEW,
                            zcpDataString = it.userSearchData.zcpDataString,
                            payMode = it.payMode,
                            preApprovedValidity = data.preApprovedValidity,
                            requisitionID = userSearchData.requisitionID,
                            myBizFlowIdentifier = userSearchData.myBizFlowIdentifier,
                            journeyId = userSearchData.journeyId,
                            workFlowId = userSearchData.workflowId,
                            forwardFlow = userSearchData.forwardBookingFlow
                    )
                },
                roomStayCandidates = getRoomCandidatesList(data.roomCriteria),
                pricingKeys = listOf(pricingKey ?: CoreConstants.EMPTY_STRING),
                coupons = noCostEmiCouponsList,
                hotelName = hotelName,
                selectedPricingKey = pricingKey
        )
    }

    private fun getNoCostEmiCouponsList(): MutableList<HotelBookingCoupon> {
        val noCostEmiCouponsList = mutableListOf<HotelBookingCoupon>()
        dataWrapper.availResponse?.hotelPriceBreakUp?.coupons?.filterTo(noCostEmiCouponsList) { it.noCostEmiApplicable }
        return noCostEmiCouponsList
    }

    private fun getRoomCandidatesList(roomCriteria: List<RoomCriteriaV2>?): List<RoomStayCandidatesV2> {
        val roomCandidatesList = mutableListOf<RoomStayCandidatesV2>()
        roomCriteria?.forEach {
            it.roomStayCandidates?.let { roomStayCandidates ->
                roomCandidatesList.addAll(roomStayCandidates)
            }
        }
        return roomCandidatesList
    }
    private fun getAmount(key: String): Double {
        return dataWrapper.priceBreakUp?.details?.let {
            for (priceItem in it) {
                if (priceItem.key == key) {
                    return priceItem.amount
                }
            }
            0.0
        } ?: kotlin.run {
            0.0
        }
    }

    private fun getBreakupAmount(parentKey: String, brakupKey: String): Double {
        return dataWrapper.priceBreakUp?.details?.let {
            for (priceItem in it) {
                if (priceItem.key == parentKey) {
                    priceItem.details?.let { itemBreakup ->
                        for (item in itemBreakup) {
                            if (item.key == brakupKey) {
                                return item.amount
                            }
                        }
                    }
                }
            }
            0.0
        } ?: kotlin.run {
            0.0
        }
    }

    fun getTotalPriceFooterData(): PriceFooter? {
        return dataWrapper.priceBreakUp?.priceFooter
    }

    fun isAmountPayingNowPresent(): Boolean? {
        return dataWrapper.priceBreakUp?.details?.any { it.key == AMOUNT_YOU_PAYING_NOW_KEY }
    }

    fun getAmountPayingNowString(): String? {
        return getAmountString(getAmount(AMOUNT_YOU_PAYING_NOW_KEY))
    }

    fun getTotalAmountString(): String? {
        return getAmountString(getTotalAmount())
    }

    private fun getAmountString(amount: Double): String? {
        val currencySymbol = getCurrencySymbol()
        return ResourceProvider.instance.getString(
            R.string.htl_text_cost, currencySymbol,
            StringUtil.getCommaSeparatedPrice(amount)
        )
    }

    fun getGstInfo(): HotelGstInfo? {
        return dataWrapper.availResponse?.gstInfo
    }

    fun getPanInfo(): HotelPanInfo? {
        return dataWrapper.availResponse?.panInfo
    }

    fun getTotalGuests(): Int {
        return (dataWrapper.userSearchData?.occupancyData?.adultCount
                ?: 0) + (dataWrapper.userSearchData?.occupancyData?.childAges?.size ?: 0)
    }

    fun getPanCardState(): PanCardState {
        if (isDayUseFunnel()) {
            return PanCardState.PAN_CARD_NOT_SHOWN
        }
        return getPanInfo()?.run {
            when (panCardRequired) {
                true -> PanCardState.PAN_CARD_NO_VALIDATION
                false -> PanCardState.PAN_CARD_NOT_SHOWN
            }
        } ?: run {
            PanCardState.PAN_CARD_NOT_SHOWN
        }
    }

    fun getPropertyTag(): String? {
        return dataWrapper.availResponse?.hotelInfo?.propertyLabel
    }

    fun getStarRating(): Int {
        return dataWrapper.availResponse?.hotelInfo?.starRating ?: 0
    }

    fun getListingType(): String? {
        return dataWrapper.availResponse?.hotelInfo?.listingType
    }

    fun getFlexiBleCheckinSelectedTimeSlotId(): String? {
        return dataWrapper.bookingReviewData?.flexibleCheckinSelectedTimeSlotInfo?.id
    }
    fun getSpecialCheckoutRequest(): SpecialCheckoutRequest? {
        val specialRequestForm = getSpecialRequest()
        if (specialRequestForm == null || specialRequestForm.categories?.isNullOrEmpty() == true) {
            return null
        }
        val categoryList = getCategoryList(specialRequestForm.categories)
        if (categoryList.isNotEmpty()) {
            val specialCheckoutRequest = SpecialCheckoutRequest()
            specialCheckoutRequest.categories = categoryList
            return specialCheckoutRequest
        }
        return null
    }

    fun getCategoryList(specialReqCategory: List<Category>): List<SpecialRequestItem> {
        if (specialReqCategory.isEmpty()) {
            return emptyList()
        }
        val categories: MutableList<SpecialRequestItem> = LinkedList()
        for (category in specialReqCategory) {
            if (category.isSelected) {
                val requestItem = SpecialRequestItem()
                requestItem.code = category.code
                if (category.selectedValue?.isNotEmpty() == true) {
                    requestItem.values = listOf(category.selectedValue)
                }
                if (category.subCategories?.isNotEmpty() == true) {
                    requestItem.subCategories = getCategoryList(category.subCategories)
                }
                categories.add(requestItem)
            }
        }
        return if (categories.size > 0) categories else emptyList()
    }

    fun getPropertyType(): String {
        return dataWrapper.propertyType
    }

    fun getBookingReviewData() = dataWrapper.bookingReviewData

    fun getBookingReviewDataWrapper()  = dataWrapper

    fun getRootLevelAlerts() = dataWrapper.rootLevelAlerts

    fun getCampaignAlert() = dataWrapper.campaignAlert

    fun getSearchType(): String? {
        return dataWrapper.bookingReviewData?.searchType
    }

    fun getUserAgreementUrl(): String {
        return HotelUtil.getPrivacyPolicy()
    }

    fun getAppliedCoupon(): HotelBookingCoupon? {
        return dataWrapper.appliedCoupon
    }

    fun getAppliedCouponSuccessMessage(): String? {
        return dataWrapper.appliedCouponSuccessMessage
    }

    fun getPaymentCheckoutData(): CheckoutData? {
        return dataWrapper.getPaymentCheckoutData()
    }

    fun getDomesticGSTNDetails() = dataWrapper.domesticGSTNDetails

    fun updateLastVisibleItemPosition(position: Int) {
        dataWrapper.updateLastVisibleItemPosition(position)
    }

    fun getBasePriceLabel(): String? {
        return dataWrapper.availResponse?.hotelInfo?.guestRoomValue
    }

    fun getHotelInPolicy(): Boolean? {
        return dataWrapper.corpApprovalInfo?.withinPolicy
    }

    fun getOutOfPolicyReasons() : List<String>? {
        return dataWrapper.corpApprovalInfo?.failureReasons
    }

    fun getLastVisibleItemPosition() = dataWrapper.getLastVisibleItemPosition()

    fun getUserBlackInfo(): HotelsUserBlackInfo? {
        return dataWrapper.userBlackInfo
    }

    fun getRatePlansAsString(): String {
        val roomCriteriaList = dataWrapper.bookingReviewData?.roomCriteria
                ?: return CoreConstants.EMPTY_STRING
        val sb = StringBuilder()
        for (roomCriteria in roomCriteriaList) {
            sb.append(roomCriteria.ratePlanCode).append("|")
        }
        sb.setLength(max(sb.length - 1, 0))
        return sb.toString()
    }

    fun getRatePlanListFromAvail(): List<RoomRatePlan>? {
        return dataWrapper.availResponse?.ratePlanList
    }

    fun getHotelCategories(): List<String> {
        return dataWrapper.availResponse?.hotelInfo?.categories ?: emptyList()
    }

    fun getCharityAddOn(isCharityAddOnSelected: Boolean): AddOnSelected? {
        val charityAddOn = getCharityAddonData() ?: return null
        val charityAddOnUnitSelected = if (isCharityAddOnSelected) {
            AddOnUnitSelected(adult = 1, child = 0)
        } else {
            AddOnUnitSelected(adult = 0, child = 0)
        }
        return AddOnSelected(
                addOnType = charityAddOn.type.orEmpty(),
                bucketId = charityAddOn.bucketId.orEmpty(),
                id = charityAddOn.id.orEmpty(),
                title = charityAddOn.title,
                validFrom = charityAddOn.validFrom,
                unitSelected = charityAddOnUnitSelected
        )
    }

    fun getCharityV2AddOn(charityItem: AddonV2Item, isSelection : Boolean): AddOnSelected? {
        val charityAddOn = getCharityAddonData(CHARITY_ADD_ON_ID_V2) ?: return null
        val charityAddOnUnitSelected = if (isSelection) {
            AddOnUnitSelected(adult = 1, child = 0)
        } else {
            AddOnUnitSelected(adult = 0, child = 0)
        }
        return AddOnSelected(
                addOnType = charityAddOn.type,
                bucketId = charityAddOn.bucketId,
                id = charityAddOn.id,
                title = charityAddOn.title,
                validFrom = charityAddOn.validFrom,
                unitSelected = charityAddOnUnitSelected,
                items = arrayListOf(AddOnSelectedItem(charityItem.id))
        )
    }

    fun getMyBizPlusAddOn(item: AddonDataV2): AddOnSelected {
        val addOnType = item.type
        val bucketId = item.bucketId
        val id = item.id
        val addOnUnitSelected = AddOnUnitSelected(adult = 1, child = 0)
        return AddOnSelected(
            addOnType = addOnType,
            bucketId = bucketId,
            id = id,
            unitSelected = addOnUnitSelected
        )
    }

    fun getDummyAddOn(): AddOnSelected {
        return AddOnSelected(
                addOnType = DUMMY_ADD_ON_ID,
                bucketId = CoreConstants.EMPTY_STRING,
                id = DUMMY_ADD_ON_ID,
                unitSelected = AddOnUnitSelected(0, 0)
        )
    }

    /*
    *  Currently this function will return true, incase, addnList is not empty in response,
    * in future this method needs to be modified based on addon type
    * */
    fun isCharityAddonIncludedInPrice(): Boolean {
        dataWrapper.selectedAddOns.forEach {
            if (CHARITY_ADD_ON_ID.equals(it.id, true) && it.unitSelected.adult != 0) {
                return true
            }
        }
        return false
    }

    fun isSMEIncludedInPrice(): Boolean {
        dataWrapper.selectedAddOns.forEach {
            if (CORP_SUBSCRIPTION_ADD_ON_ID.equals(it.addOnType, true) && it.unitSelected.adult != 0) {
                return true
            }
        }
        return false
    }

    /*
      Here corp function was put, because needed primary traveller details needed in BookingReviewPDTHelper
     */
    fun getCorpPrimaryTraveller(): CorpTravellerDetail? {
        return dataWrapper.corpPrimaryTraveller
    }

    fun isMMTValueStayHotel(): Boolean {
        val hotelTag = dataWrapper.availResponse?.hotelInfo?.hotelTags
        return hotelTag?.get(HotelTagPlaceholder.PC_HOTEL_TOP)?.type.equals(HotelConstants.VALUE_STAY_TYPE)
    }

    fun getSelectedAddOn(addOnId : String) : AddOnSelected? {
        return dataWrapper.selectedAddOns.find { it.id == addOnId }
    }

    fun getSelectedAddons() = dataWrapper.selectedAddOns

    fun getSelectedInsuranceIds():List<Int> {
        val insuranceAddon = getSelectedAddOn(INSURANCE_ADD_ON_ID)
        val selectedInsurance = insuranceAddon?.insuranceDataSelected?: emptyList()
        val ids = mutableListOf<Int>()
        for(item in selectedInsurance){
            ids.add(item.id)
        }
        return ids
    }

    fun getAddOn(addOnType: String): AddonDataV2? {
        return dataWrapper.addons?.find { it.type == addOnType }
    }

    fun getAddOnList() : List<AddonDataV2>? {
        return dataWrapper.addons
    }

    /**
     *  upsell , if available, is available only on the first ratePlan
     *  upsell is stored in cache on first api call only, and later fetched from there
     */
    fun getUpsellOptions(): List<UpsellInfo> {
        return dataWrapper.upsellOptions.drop(1)
    }

    fun isMultiRoomSearch(): Boolean {
        val searchType = getBookingReviewData()?.searchType ?: return true
        return when (searchType) {
            RoomSearchType.RECOMMENDED, RoomSearchType.OCCUPANCY_LESS -> true
            else -> false
        }
    }

    fun updateSelectedUpsell(ratePlanList: List<RoomRatePlan>) {
        dataWrapper.upsellOptions.map {
            it.isSelected = upsellRoomCriteriaMatching(it, ratePlanList)
        }
    }

    fun isAnyUpsellSelected():Boolean {
        val upsellOptions = dataWrapper.upsellOptions
        val totalUpSells = upsellOptions.size
        if (totalUpSells <= 1) {
            return false
        }
        // doing so as first upsell is always current rateplan selected for availApi request
        /**
         * refer [BookingReviewDataWrapper.updateUpsellInfo] for more details
         */
        val effectiveUpsells = dataWrapper.upsellOptions.subList(1, dataWrapper.upsellOptions.size)
        effectiveUpsells.forEach {
            if(it.isSelected) {
                return true
            }
        }
        return false
    }

    fun updateUpsell(upsellInfo: UpsellInfo) {
        dataWrapper.upsellOptions.map {
            if(it.addOnType == upsellInfo.addOnType) {
                it.successDisplayText = upsellInfo.successDisplayText
            }
        }
    }

    private fun upsellRoomCriteriaMatching(
        upsellInfo: UpsellInfo,
        ratePlanList: List<RoomRatePlan>
    ): Boolean {

        val roomCriteriaList = upsellInfo.roomCriteria
        if(roomCriteriaList.isNullOrEmpty()){
            return false
        }

        for (roomRatePlan in ratePlanList) {
            var matchFound = false

            for (roomCriteria in roomCriteriaList) {
                if (roomRatePlan.ratePlanCode == roomCriteria.ratePlanCode && roomRatePlan.roomCode == roomCriteria.roomCode && canSelectAddOn(upsellInfo)) {
                    matchFound = true
                    break
                }
            }
            if (!matchFound) {
                return false
            }
        }
        return true
    }

    private fun canSelectAddOn(upsellInfo: UpsellInfo): Boolean {
        if(!HotelConstants.ADD_ON_FLEXI_CANCEL.equals(upsellInfo.addOnType, true)) {
            return true
        }
        return !dataWrapper.selectedAddOnState.equals("UNSELECTED", true)
    }

    fun disabledUpsellOption(roomCriteriaV2: List<RoomCriteriaV2>? = null) {
        roomCriteriaV2?.let { roomCriteriaList ->
            for (upsell in dataWrapper.upsellOptions) {
                roomCriteriaList.forEachIndexed { index, roomCriteria ->
                    val upsellRoomCriteria = upsell.roomCriteria?.get(index)
                    if (roomCriteria.ratePlanCode != upsellRoomCriteria?.ratePlanCode && roomCriteria.roomCode != upsellRoomCriteria?.roomCode) {
                        return
                    }
                }

                upsell.isDisabled = true
            }
        }
    }

    fun getDayUseInfo() = dataWrapper.dayUseInfo

    fun isDayUseBooking(): Boolean {
        return dataWrapper.dayUseInfo != null
    }

    fun getSelectedUpsell(): UpsellInfo? {
        return dataWrapper.upsellOptions.drop(1).firstOrNull {
            it.isSelected
        }
    }

    fun getAllCouponsList():MutableList<HotelBookingCoupon>?{
        return dataWrapper.coupons
    }

    fun getBenefitDealObj() = dataWrapper.benefitDeal

    fun getMaxCouponsToShow() = dataWrapper.maxCouponsToShow

    fun getThankYouTrackingData(): ReviewToThankyouTrackingData {
        return ReviewToThankyouTrackingData(
            locusTrackingData = dataWrapper.bookingReviewData?.locusTrackingData,
            priceTrackingData = getPriceTrackingData(),
            roomSearchType = getSearchType(),
            userSearchData = dataWrapper.userSearchData,
            isUpsellSelectedOnReview = (getSelectedUpsell() != null),
            xUserType = getXUserType()
        )
    }

    private fun getPriceTrackingData(): PriceTrackingData {
        return PriceTrackingData(baseAmount = getBaseAmount().toString(),
                discountAmount = getDiscountAmount().toString(),
                taxAmount = getTaxAmount().toString(),
                serviceAndCommissionAmount = getSericeChargeAndCommissionAmount().toString(),
                walletAmount = getWalletAmount().toString(),
                mmtServiceFeeAmount = getMmtServiceFee().toString(),
                totalAmount = getTotalAmount().toString(),
                bnplVariant = getBnplDetails()?.bnplVariant,
                priceAfterDiscount = getPriceAfterDiscount().toString())
    }

    /**
     * method to get asp bracket for tracking purpose
     */
    fun getASPBucket(): String {
        var noOfNights = 1
        var funnel = HotelFunnel.HOTEL.funnelValue
        val roomCount = dataWrapper.roomCount
        val totalAmount = getAmount(key = KEY_TOTAL_AMOUNT)
        dataWrapper.userSearchData?.let {
            noOfNights = HotelUtil.getNoOfNights(it.checkInDate, it.checkOutDate, HotelConstants.SEARCH_DATE_FORMAT)
            funnel = it.funnelSrc
        }
        return getASPBucketValue(totalAmount = totalAmount, roomCount = roomCount, noOfNights = noOfNights, funnel)
    }

    fun updateAvailResponse(availResponse: AvailResponse?) {
        dataWrapper.availResponse = availResponse?.response
        dataWrapper.correlationKey = availResponse?.correlationKey.orEmpty()
    }

    fun updateCurrentSelectedSlot(currentSelectedSlot: SlotAvailRequestData?) {
        dataWrapper.currentSelectedSlot = currentSelectedSlot
    }


    fun tripMoneyBnplCardShown(): Boolean {
        dataWrapper.availResponse?.tripMoneyBnplData?.let {
            if (dataWrapper.availResponse?.featureFlags?.payLaterCard == true && LoginUtils.loggedInUser?.primaryContact.isNotNullAndEmpty() && BookingReviewPokusHelper().getCardSequence()
                    .contains(BookingRecyclerAdapterItemKeys.TRIPMONEY_BNPL_CARD)
            ) {
                return true
            }
        }
        return false
    }

    fun isGroupBookingPrice(): Boolean {
        return dataWrapper.availResponse?.hotelInfo?.groupBookingPrice ?: false
    }


    fun setDataWrapper(bookingReviewData: BookingReviewData) {
        dataWrapper.clearData()
        dataWrapper.bookingReviewData = bookingReviewData
        dataWrapper.userSearchData = bookingReviewData.userSearchData
    }

    fun updateFromValidateResponse(validateApiResponseV2: ValidateApiResponseV2?, requestId: String) {
        dataWrapper.updateFromValidateResponse(validateApiResponseV2, requestId)
    }

    fun isDayUseFunnel() = HotelFunnel.DAYUSE.funnelValue == dataWrapper.userSearchData?.funnelSrc

    fun getDayUseCheckInTime() = dataWrapper.getDayUseCheckInTime()

    fun getDayUseSlotDuration() = dataWrapper.getDayUseSlotDuration()

    fun getPaymentPlan() = dataWrapper.paymentPlan

    fun isEntireProperty() = dataWrapper.isEntireProperty()

    fun getPaymentEvent(skipReason: CorpReasons? = null, isQuickBook: Boolean = false,paymentTypeCardShown : Boolean = false): HotelEvent {
        return when {
            bookingPaymentHelper.canShowFlexiDetailBottomSheet(this) -> {
                HotelEvent(HotelBookingReviewActivityEvent.OPEN_FLEXI_DETAIL_BOTTOM_SHEET, bookingPaymentHelper.getFlexiDetailBottomSheetData(this))
            }
            bookingPaymentHelper.continuePahWithoutCC(this) -> {
                if (isQuickBook) {
                    HotelEvent(HotelBookingReviewActivityEvent.OPEN_PAH_ACTIVITY_QUICK_BOOK,
                            bookingPaymentHelper.getPahIntentData(this, dataWrapper, getThankYouTrackingData()))
                } else {
                    HotelEvent(HotelBookingReviewActivityEvent.OPEN_PAH_ACTIVITY)
                }
            }
            bookingPaymentHelper.continuePahWithCC(this) -> {
                HotelEvent(HotelBookingReviewActivityEvent.INITIATE_CHECKOUT, skipReason)
            }
            paymentTypeCardShown -> {
                dataWrapper.checkoutData.paymentDetail.isBNPL = dataWrapper.paymentType == PaymentType.DELAYED_PAYMENT
                dataWrapper.checkoutData.paymentType = dataWrapper.paymentType ?: PaymentType.FULL_PAYMENT
                HotelEvent(HotelBookingReviewActivityEvent.INITIATE_CHECKOUT, skipReason)
            }
            continueWithPayFragment() -> {
                HotelEvent(HotelBookingReviewActivityEvent.OPEN_PAY_FRAGMENT,
                        bookingPaymentHelper.getPayOptionData(this, dataWrapper, skipReason = skipReason))
            }
            else -> {
                HotelEvent(HotelBookingReviewActivityEvent.INITIATE_CHECKOUT, skipReason)
            }
        }
    }

    fun continueWithPayFragment(): Boolean {
        return bookingPaymentHelper.continueWithPayFragment(this)
    }

    fun updateConsentCardState(data: PromoConsent?) {
        dataWrapper.consentCardData = data
    }

    fun setPaymentType(paymentType: PaymentType) {
        dataWrapper.paymentType = paymentType
    }

    fun isBookingForMySelf() : Boolean{
        return dataWrapper.isBookingForMySelf
    }

    fun getXUserType(): String? {
        return dataWrapper.availResponse?.xUserType
    }

    fun updateRequestId(apiName: HotelPdtV2Constants.BackendApis, requestId: String) {
        dataWrapper.requestIds.put(apiName,requestId)
    }

    fun getRequestId(apiName: HotelPdtV2Constants.BackendApis): String? {
        return dataWrapper.requestIds.get(apiName)
    }

    fun getAllRequestIds(): List<String>? {
        return dataWrapper.requestIds.values.toList()
    }
    fun getFBPOfferType(): String? {
        return dataWrapper.priceBreakUp?.details?.find { it.key == PriceKey.DISCOUNT }?.couponPersuasion?.type
    }

    fun getUserSearchData() = dataWrapper.userSearchData
    fun getFlexiDetailBottomSheetData(): FlexiDetailBottomSheetData? {
        return dataWrapper.flexiDetailBottomSheet
    }

    fun getFlexiCancelState() = dataWrapper.selectedAddOnState
}