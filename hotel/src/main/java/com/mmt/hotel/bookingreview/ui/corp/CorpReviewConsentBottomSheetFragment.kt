package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import androidx.compose.runtime.Composable
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelComposeBottomSheetFragment
import com.mmt.hotel.bookingreview.model.response.checkout.ConsentData
import com.mmt.hotel.compose.review.viewModel.CorpReviewConsentVM
import com.mmt.hotel.compose.widgets.CorpReviewConsentBottomSheet
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CorpReviewConsentBottomSheetFragment : HotelComposeBottomSheetFragment<CorpReviewConsentVM>() {

    override fun initViewModel() = getViewModel<CorpReviewConsentVM>(factory)

    @Composable
    override fun MainContent() {
        val data = arguments?.getParcelable<ConsentData>(BUNDLE_DATA)?:return
        val source = arguments?.getString(SOURCE) ?: ""
        CorpReviewConsentBottomSheet(data = data, source = source, handleEvent = ::handleEvents){
            handleEvents(HotelEvent(DISMISS_CONSENT_BOTTOMSHEET))
        }
    }

    override fun handleEvents(event: HotelEvent) {
        when(event.eventID) {
            DISMISS_CONSENT_BOTTOMSHEET,
            ON_CONSENT_CONTINUE_CLICK -> {
                sendEventToActivity(event)
                dismiss()
            }
            else -> {
                sendEventToActivity(event)
            }
        }
    }

    companion object {
        const val BUNDLE_DATA = "BUNDLE_DATA"
        const val SOURCE = "SOURCE"
        const val TAG = "CorpReviewConsentBottomSheet"
        const val DISMISS_CONSENT_BOTTOMSHEET = "DISMISS_CONSENT_BOTTOMSHEET"
        const val ON_CONSENT_CONTINUE_CLICK = "ON_CONTINUE"
        const val SOURCE_CHECKOUT = "SOURCE_CHECKOUT"
        const val SOURCE_APPROVAL = "SOURCE_APPROVAL"
        const val SOURCE_ADD_TO_ITINERARY = "ADD_TO_ITINERARY"

        fun newInstance(consentData: ConsentData, source: String): CorpReviewConsentBottomSheetFragment {
            val fragment = CorpReviewConsentBottomSheetFragment()
            fragment.arguments = Bundle().apply {
                putParcelable(BUNDLE_DATA, consentData)
                putString(SOURCE, source)
            }
            return fragment
        }
    }

}

