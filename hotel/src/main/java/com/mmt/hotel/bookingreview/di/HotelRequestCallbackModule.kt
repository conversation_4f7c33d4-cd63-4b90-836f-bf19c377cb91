package com.mmt.hotel.bookingreview.di

import com.mmt.hotel.common.request_callback.repository.RequestCallbackRepository
import com.mmt.hotel.common.request_callback.repository.impl.RequestCallbackRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent

@Module
@InstallIn(ActivityComponent::class)
open class HotelRequestCallbackModule {

    @Provides
    open fun provideRequestCallbackRepository(repositoryImp: RequestCallbackRepositoryImpl): RequestCallbackRepository = repositoryImp

}