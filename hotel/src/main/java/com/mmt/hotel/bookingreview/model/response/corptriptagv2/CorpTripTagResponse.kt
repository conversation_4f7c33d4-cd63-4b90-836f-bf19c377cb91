package com.mmt.hotel.bookingreview.model.response.corptriptagv2

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CorpTripTagResponseV2(
    val attributeList: List<CorpTripTagFieldV2>? = null,
    val message: String?,
    val responseCode: String?,
    val statusCode: Int,
    val defaultGST: DefaultGST?,
    val status: String?,
    val captureGstn: Boolean?,
    val identifierDetails: IdentifierDetails?  // this node will come inplace of defaultGST when gst is not present but company name and address is present
) : Parcelable
