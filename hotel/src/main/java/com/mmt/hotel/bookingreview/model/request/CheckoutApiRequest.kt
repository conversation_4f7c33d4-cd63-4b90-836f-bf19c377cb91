package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import com.mmt.auth.login.util.LoginUtils
import com.mmt.hotel.bookingreview.model.DomesticGSTNDetails
import com.mmt.hotel.bookingreview.model.PostApprovalTripTag
import com.mmt.hotel.bookingreview.model.TravellerDetailV2
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.model.request.HotelRequestConstants
import com.mmt.hotel.common.model.request.RequestDetails
import com.mmt.hotel.old.hotelreview.model.request.checkout.SpecialCheckoutRequest
import kotlinx.parcelize.Parcelize

@Parcelize
data class CheckoutApiRequest(
    val transactionKey: String,
    val idContext: String = if (LoginUtils.isCorporateUser) "CORP" else "B2C",
    val requestDetails: RequestDetails?,
    val recheckRequired: Boolean = false,
    val travellerDetails: List<TravellerDetailV2>,
    val paymentDetail: PaymentDetail,
    var skipDoubleBlack: Boolean? = null,
    val authenticationDetail: AuthenticationDetail?,
    val specialRequest: SpecialCheckoutRequest?,
    val workflowStatus: String? = null,
    val skipRtbValidation: Boolean = false,
    val tripDetailsText: String? = null,
    val tripTag: PostApprovalTripTag? = null,
    val personalCorpBooking: Boolean = false,
    val reasonForSkipApproval: String? = null,
    val gstnDetail: DomesticGSTNDetails? = null,
    val currency: String,
    val promoConsent: Boolean? = null,
    val addOnDetail : Map<String, AddOnRequestData>? = null,
    val flexibleCheckinSlotId: String? = null
) : Parcelable

@Parcelize
data class PaymentDetail(var isBNPL: Boolean = false,
                         var emi: Boolean = false,
                         var rtbAutoCharge: Boolean = false,
                         val channel: String = HotelRequestConstants.CHANNEL,
                         var partialPayment: Boolean = false,
                         var bnplVariant: String = HotelConstants.BNPL_DEFAULT_VARIANT
) : Parcelable

@Parcelize
data class AuthenticationDetail(val otpDetail: OtpDetail) : Parcelable

@Parcelize
data class OtpDetail(val OTP: String, val key: String) : Parcelable