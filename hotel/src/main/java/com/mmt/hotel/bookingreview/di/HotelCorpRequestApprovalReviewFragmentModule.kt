package com.mmt.hotel.bookingreview.di


import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.corp.CorpApprovalRequestFragmentData
import com.mmt.hotel.bookingreview.ui.corp.HotelCorpRequestApprovalReviewFragment
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalReviewViewModel
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

/**
 * This class is created to provide hilt dependency in @HotelCorpRequestApprovalReviewFragment in case of QuickReviewActivity
 * will delete this when HotelBookingReview will be migrated to hilt
 */

@Module
@InstallIn(FragmentComponent::class)
open class HotelCorpRequestApprovalReviewFragmentModule {
    @Provides
    @IntoMap
    @ViewModelKey(HotelCorpRequestApprovalReviewViewModel::class)
    fun provideRequestApprovalReviewModel(viewModel: HotelCorpRequestApprovalReviewViewModel): HotelViewModel {
        return viewModel
    }

    @Provides
    fun provideCorpApprovalRequestFragmentData(fragment: Fragment): CorpApprovalRequestFragmentData {
        if(fragment is HotelCorpRequestApprovalReviewFragment) {
            return fragment.arguments?.getParcelable(HotelCorpRequestApprovalReviewFragment.TAG) ?: CorpApprovalRequestFragmentData(corpApprovalInfo = null,corpApprovingManagers = null)
        }
        return CorpApprovalRequestFragmentData(corpApprovalInfo = null,corpApprovingManagers = null)
    }
}
