package com.mmt.hotel.bookingreview.repository

import com.google.gson.Gson
import com.mmt.auth.login.helper.UserServiceRequestHelper
import com.mmt.auth.login.model.userservice.UserDetailRefreshResponse
import com.mmt.auth.login.util.AuthMigrationHelper
import com.mmt.auth.login.util.LoginController.Companion.AUTH_USER_SERVICE_HEADER
import com.mmt.auth.login.util.LoginController.Companion.getUserIdentifier
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.BaseLoginConstants
import com.mmt.core.constant.LoginControllerConstants.Companion.KEY_HEADER_USER_IDENTIFIER
import com.mmt.core.constant.LoginControllerConstants.Companion.KEY_VALUE_HEADER_AUTH
import com.mmt.core.constant.LoginControllerConstants.Companion.MMT_AUTH
import com.mmt.core.util.GsonUtils
import com.mmt.hotel.base.repository.HotelBaseRepository
import com.mmt.hotel.bookingreview.helper.BookingReviewRequestHelper
import com.mmt.hotel.bookingreview.model.BookingReviewData
import com.mmt.hotel.bookingreview.model.CheckoutData
import com.mmt.hotel.bookingreview.model.request.AddOnSelected
import com.mmt.hotel.bookingreview.model.request.ConsentAPIRequest
import com.mmt.hotel.bookingreview.model.request.TripMoneyBnplRequest
import com.mmt.hotel.bookingreview.model.request.UserIdentifierHeaderData
import com.mmt.hotel.bookingreview.model.response.ConsentAPIResponse
import com.mmt.hotel.bookingreview.model.response.PayLaterEligibilityResponse
import com.mmt.hotel.bookingreview.model.response.TotalPricingApiResponseV2
import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.model.response.price.AvailResponse
import com.mmt.hotel.bookingreview.model.response.validatecoupon.ValidateApiResponseV2
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.AUTHORIZATION
import com.mmt.hotel.common.constants.HotelConstants.COUNTRY_CODE_UNKNOWN
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.transform
import javax.inject.Inject

open class HotelBookingReviewRepositoryImpl @Inject constructor(private val bookingReviewRequestHelper: BookingReviewRequestHelper) : HotelBaseRepository(), HotelBookingReviewRepository {

    companion object {
        val AVAIL_ROOM_API = "$CLIENT_GATEWAY_BASE_URL/avail-rooms/$client/$apiVersionCode"
        val VALIDATE_COUPON_API = "$CLIENT_GATEWAY_BASE_URL/validate-coupon/$client/$apiVersionCode"
        val TOTAL_PRICING_API = "$CLIENT_GATEWAY_BASE_URL/total-pricing/$client/$apiVersionCode"
        val CHECKOUT_API = "$CLIENT_GATEWAY_BASE_URL/payment-checkout/$client/$apiVersionCode"
        val USER_DETAIL_API = AuthMigrationHelper.instance.getUserServiceVersionUrl() + "/user/details"
        val CONSENT_API = "$CONSENT_API_BASE_URL/ext/hcs/cmp/userconsents/save"
        val TRIPMONEY_BNPL_API = "$CLIENT_GATEWAY_BASE_URL/pay-later-eligibility/$client/$apiVersionCode"
    }

    override fun makeAvailRoomApiCall(bundleData: BookingReviewData, fetchUpsellInfo: Boolean, modifyBooking: Boolean): Flow<Pair<AvailResponse, String>> {
        val availRoomApiRequestV2 = bookingReviewRequestHelper.getAvailRoomApiData(bundleData, fetchUpsellInfo, modifyBooking)
        val responseFlow: Flow<AvailResponse> = makePostRequest(url = AVAIL_ROOM_API,
            postData = availRoomApiRequestV2,
            countryCode = bundleData.userSearchData.countryCode,
            queryParams = getMandatoryQueryParams(availRoomApiRequestV2.requestDetails))
        return responseFlow.map { Pair(it,availRoomApiRequestV2.requestDetails?.requestId.orEmpty()) }
    }

    /**
     * Fetch co travellers for logged-in user
     */
    override fun fetchCoTravellers(): Flow<UserDetailRefreshResponse> {
        val userIdentifier = getUserIdentifier(MMT_AUTH,
                com.mmt.auth.login.util.LoginUtils.loggedInUser?.mmtAuth)
        val headersMap: MutableMap<String, String?> = HashMap()
        headersMap[KEY_VALUE_HEADER_AUTH] = AUTH_USER_SERVICE_HEADER
        headersMap[KEY_HEADER_USER_IDENTIFIER] = GsonUtils.getInstance().serializeToJson(userIdentifier)

        return makePostRequest(url = USER_DETAIL_API,
                postData = UserServiceRequestHelper().getUserDetailRequestBody(BaseLoginConstants.TRAVELLER_DETAIL_QUERY),
                countryCode = COUNTRY_CODE_UNKNOWN, //Not used, so hardcoded
                headerMap = headersMap)
    }

    /**
     * Update user profile if any of below field is empty for logged-in user
     * Title, Name, Surname, EmailId, ContactNo
     */
//    ToDo Ankur Ignore
//    override fun updateUserProfileDetail(): Observable<UpdateProfileResponse>? {
//        val user = com.mmt.auth.login.util.LoginUtils.INSTANCE.loggedInUser
//        user?.let {
//            val userIdentifier = getUserIdentifier(MMT_AUTH,
//                    it.getMmtAuth())
//
//            val headersMap: MutableMap<String, String?> = HashMap()
//            headersMap[KEY_VALUE_HEADER_AUTH] = AUTH_USER_SERVICE_HEADER
//            headersMap[HttpUtils.ACCEPT] = HttpUtils.APPLICATION_JSON
//            headersMap[HttpUtils.CONTENT_TYPE] = HttpUtils.APPLICATION_JSON
//            headersMap[KEY_HEADER_USER_IDENTIFIER] = GsonUtils.getInstance().serializeToJson(userIdentifier)
//
//            val clonedUser = ProfileUtil.getClonedUserForProfile(it)
//            val travelDoc = ProfileUtil.getUserFirstPassportDoc(clonedUser)
//            if (travelDoc?.passport_num == null) {
//                clonedUser.travellerDocuments = null
//            }
//
//            return makePostRequest(url = AppConstants.NEW_UPDATE_PROFILE_URL,
//                    postData = UserServiceRequestHelper().getUserUpdateRequest(clonedUser),
//                    countryCode = COUNTRY_CODE_UNKNOWN, //Not used, so hardcoded
//                    headerMap = headersMap)
//        } ?: run {
//            return null
//        }
//    }

    /**
     * update server with booking data before moving to payment screen
     */
    override fun makeCheckoutRequest(checkoutData: CheckoutData): Flow<Pair<CheckoutResponse, String?>> {
        val requestData = bookingReviewRequestHelper.getCheckoutApiData(checkoutData)
        val requestFlow: Flow<CheckoutResponse> = makePostRequest(
            url = CHECKOUT_API,
            postData = requestData,
            countryCode = checkoutData.countryCode,
            queryParams = getMandatoryQueryParams(requestData.requestDetails)
        )
        return requestFlow.map { Pair(it, requestData.requestDetails?.requestId) }
    }

    override fun makeValidateCouponRequest(
        bookingReviewData: BookingReviewData?,
        couponCode: String,
        isCouponApplied: Boolean,
        countryCode: String,
        expData: String?,
        txnKey: String,
        quickCheckoutApplicable: Boolean
    ): Flow<Pair<ValidateApiResponseV2, String>> {
        val apiRequest = bookingReviewRequestHelper.getValidateCouponApiData(
            bookingReviewData,
            txnKey,
            couponCode,
            isCouponApplied,
            expData,
            quickCheckoutApplicable
        )
        val apiResponseFlow: Flow<ValidateApiResponseV2> = makePostRequest(
            url = VALIDATE_COUPON_API,
            postData = apiRequest,
            countryCode = countryCode,
            queryParams = getMandatoryQueryParams(apiRequest.requestDetails)
        )
        return apiResponseFlow.map { Pair(it, apiRequest.requestDetails?.requestId.orEmpty()) }
    }

    override fun makeTotalPricingRequest(
        data: BookingReviewData?,
        txnKey: String,
        addonSelected: List<AddOnSelected>,
        countryCode: String,
        expData: String?,
        enableTcs: Boolean
    ): Flow<Pair<TotalPricingApiResponseV2, String>> {
        val request = bookingReviewRequestHelper.getTotalPricingRequestData(data,txnKey, addonSelected, countryCode, expData, enableTcs)
        val apiResponseFlow : Flow<TotalPricingApiResponseV2> = makePostRequest(
            url = TOTAL_PRICING_API,
            postData = request,
            countryCode = countryCode,
            queryParams = getMandatoryQueryParams(request.requestDetails))
        return apiResponseFlow.map { Pair(it,request.requestDetails?.requestId.orEmpty()) }
    }

    override fun makeConsentApiRequest(data: ConsentAPIRequest, countryCode: String): Flow<ConsentAPIResponse> {
        val headersMap: MutableMap<String, String?> = HashMap()
        LoginUtils.mmtAuth?.let{
            headersMap[HotelConstants.USER_IDENTIFIER] = Gson().toJson(UserIdentifierHeaderData(type = HotelConstants.USER_IDENTIFIER_AUTH, value = it))
            headersMap[AUTHORIZATION] = HotelConstants.AUTHORIZATION_KEY
        }
        return makePostRequest(
            url = CONSENT_API,
            postData = data,
            headerMap = headersMap,
            countryCode = countryCode
        )
    }

    override fun makeTripMoneyBnplValidationRequest(tripMoneyBnplRequest: TripMoneyBnplRequest, countryCode: String): Flow<PayLaterEligibilityResponse>{
        return makePostRequest(
            url = TRIPMONEY_BNPL_API,
            postData = tripMoneyBnplRequest,
            countryCode = countryCode
        )
    }

    override fun getSelectedCurrency(): String {
        return bookingReviewRequestHelper.getSelectedCurrency()
    }

}