package com.mmt.hotel.bookingreview.model.response.validatecoupon

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.response.validatecoupon.ValidateCouponResponse
import com.mmt.hotel.common.model.response.HotelApiError
import kotlinx.parcelize.Parcelize

@Parcelize
data class ValidateApiResponseV2(@SerializedName("response")
                                 val apiResponse: ValidateCouponResponse?,
                                 val error: HotelApiError?,
                                 val correlationKey: String?) : Parcelable