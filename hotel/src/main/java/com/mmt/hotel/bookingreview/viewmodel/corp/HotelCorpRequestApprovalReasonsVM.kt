package com.mmt.hotel.bookingreview.viewmodel.corp

import android.os.Handler
import androidx.core.text.HtmlCompat
import androidx.databinding.ObservableArrayList
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.viewmodel.corp.HotelCorpRequestApprovalReviewViewModel.Companion.REQUEST_APPROVAL_REASONS_ITEM
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.corpapproval.event.HotelCorpApprovalEvents
import com.mmt.hotel.corpapproval.model.response.CorpCategoryReasons
import com.mmt.hotel.corpapproval.model.response.CorpReasons
import com.mmt.hotel.corpapproval.viewModel.CorpApprovalReasonItemVM
import com.mmt.hotel.corpapproval.viewModel.HotelCorpApproverActionDialogFragmentVM

class HotelCorpRequestApprovalReasonsVM(private val categoryReason: CorpCategoryReasons, val eventStream: MutableLiveData<HotelEvent>) : AbstractRecyclerItem {

    var travelOptionSelected = ObservableField(CoreConstants.EMPTY_STRING)
    val comments = ObservableField(CoreConstants.EMPTY_STRING)
    private val localStream = MutableLiveData<HotelEvent>()
    val itemList = ObservableArrayList<LinearLayoutItemData>()
    private val approvalReasonItemVMList = mutableListOf<CorpApprovalReasonItemVM>()
    var showErrorObservable = ObservableBoolean(false)

    init {
        categoryReason.options.forEach {
            val data = CorpApprovalReasonItemVM(it, localStream, comments, showDivider = categoryReason.options.last() != it)
            approvalReasonItemVMList.add(data)
            itemList.add(LinearLayoutItemData(R.layout.htl_corp_approval_reason_item, BR.viewModel, data))
        }
        listenToSelectionEvents()
    }

    private fun listenToSelectionEvents() {
        localStream.observeForever {
            handleEvents(it)
        }
    }

    private fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            HotelCorpApprovalEvents.EVENT_REASON_ITEM_CLICK -> {
                showErrorObservable.set(false)
                handleReasonItemClick(event.data as CorpApprovalReasonItemVM)
            }
            HotelCorpApprovalEvents.EVENT_COMMENT_TEXT_CHANGE -> {
                showErrorObservable.set(false)
            }
        }
    }

    private fun handleReasonItemClick(data: CorpApprovalReasonItemVM) {
        travelOptionSelected.set(data.reason.text) // set the option which is being selected
        if (data.reason.inputType == HotelCorpApproverActionDialogFragmentVM.TEXTBOX) { // show commentBox in case of inputType = "others"
            data.showCommentBox.set(true)
        } else {
            comments.set(CoreConstants.EMPTY_STRING)
        }

        approvalReasonItemVMList.filterNot { it == data }.forEach {
            it.isSelected.set(false) // set selected values of other radioButtons
            it.showCommentBox.set(false)
        }
        Handler().postDelayed({
            eventStream.postValue(HotelEvent(HotelCorpBookingReviewActivityEvent.EVENT_HANDLE_ELEVATION))
        }, 500)
    }

    fun getTitle(): String {
        return categoryReason.title
    }

    fun getReasonKey(): String {
        return categoryReason.reasonKey
    }

    override fun getItemType(): Int {
        return REQUEST_APPROVAL_REASONS_ITEM
    }
}