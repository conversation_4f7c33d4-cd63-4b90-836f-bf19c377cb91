package com.mmt.hotel.bookingreview.viewmodel

import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.bookingreview.helper.constants.RatePlanUpgradeTrackingConstants
import com.mmt.hotel.bookingreview.model.response.RatePlansUpgrade
import com.mmt.hotel.bookingreview.tracking.HotelBookingReviewTrackingHelper
import com.mmt.hotel.bookingreview.ui.HotelRatePlanUpgradeFragment
import com.mmt.uikit.util.isNotNullAndEmpty
import javax.inject.Inject

class RatePlanUpgradeFragmentViewModel @Inject constructor(
    val data: RatePlansUpgrade,
    val trackingHelper: HotelBookingReviewTrackingHelper
): HotelViewModel() {

    fun onAcceptUpgradeClicked() {
        trackingHelper.trackShownEvent(
            OmnitureTrackingHelper.OEPK_c1,
            RatePlanUpgradeTrackingConstants.UPGRADE_ACCEPT.format(data.upgradeType)
        )
        updateEventStream(HotelEvent(HotelRatePlanUpgradeFragment.DISMISS_FRAGMENT))
    }

    fun onDeclineUpgradeClicked() {
        trackingHelper.trackShownEvent(
            OmnitureTrackingHelper.OEPK_c1,
            RatePlanUpgradeTrackingConstants.UPGRADE_REJECT.format(data.upgradeType)
        )
        updateEventStream(HotelEvent(HotelRatePlanUpgradeFragment.DECLINE_UPGRADE))
    }

    fun onCrossClicked() {
        trackingHelper.trackShownEvent(
            OmnitureTrackingHelper.OEPK_c1,
            RatePlanUpgradeTrackingConstants.UPGRADE_DISMISSED.format(data.upgradeType)
        )
        updateEventStream(HotelEvent(HotelRatePlanUpgradeFragment.DISMISS_FRAGMENT))
    }

    fun trackClickOutside() {
        trackingHelper.trackShownEvent(
            OmnitureTrackingHelper.OEPK_c1,
            RatePlanUpgradeTrackingConstants.UPGRADE_IGNORED.format(data.upgradeType)
        )
    }

    fun showSelectedRatePlans(): Boolean = data.selectedRateplans.isNotNullAndEmpty()
    fun showUpgradedRatePlans(): Boolean = data.upgradedRateplans.isNotNullAndEmpty()

    fun getSelectedRatePlanVMList(): List<RatePlanUpgradeItemVM> {
        return data.selectedRateplans?.map { RatePlanUpgradeItemVM(it) } ?: listOf()
    }

    fun getUpgradedRatePlanVMList(): List<RatePlanUpgradeItemVM> {
        return data.upgradedRateplans?.map { RatePlanUpgradeItemVM(it) } ?: listOf()
    }

}