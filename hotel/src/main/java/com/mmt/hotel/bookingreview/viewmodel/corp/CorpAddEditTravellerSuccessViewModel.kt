package com.mmt.hotel.bookingreview.viewmodel.corp

import com.mmt.hotel.R
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.bookingreview.event.HotelRequestApprovalReviewEvent.DISMISS_FRAGMENT
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerSuccessFragmentData
import com.mmt.hotel.common.constants.CorpConstants.GUEST
import com.mmt.hotel.common.constants.CorpConstants.EMPLOYEE
import javax.inject.Inject

class CorpAddEditTravellerSuccessViewModel  @Inject constructor(val data: CorpAddEditTravellerSuccessFragmentData?): HotelViewModel() {

    val resourceProvider = ResourceProvider.instance
    val travellerType = data?.travellerDetail?.travellerType ?: GUEST

    fun getTitle(): String {
        return if(travellerType == GUEST) {
            resourceProvider.getString(R.string.htl_add_edit_traveller_success, GUEST)
        }else{
            resourceProvider.getString(R.string.htl_add_edit_traveller_success, EMPLOYEE)
        }
    }

    fun getSubTitle(): String {
        return if(travellerType == GUEST) {
            resourceProvider.getString(R.string.htl_add_edit_guest_success_sub_info)
        }else{
            resourceProvider.getString(R.string.htl_add_edit_employee_success_sub_info, data?.travellerDetail?.emailId ?: CoreConstants.EMPTY_STRING)
        }
    }

    fun onContinueClick() {
        updateEventStream(HotelEvent(DISMISS_FRAGMENT))
    }
}