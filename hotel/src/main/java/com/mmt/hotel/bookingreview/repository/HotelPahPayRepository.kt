package com.mmt.hotel.bookingreview.repository

import com.mmt.hotel.bookingreview.model.response.checkout.CheckoutResponse
import com.mmt.hotel.bookingreview.model.response.otp.OtpResponse
import com.mmt.hotel.bookingreview.model.CheckoutData
import com.mmt.hotel.bookingreview.model.PahIntentData
import com.mmt.hotel.bookingreview.model.request.OtpValidationRequest
import kotlinx.coroutines.flow.Flow


/*
*   Class will contains all the network call related to PahPayActivity
* */

interface HotelPahPayRepository {
    fun makeOtpGenerationRequest(pahData: PahIntentData): Flow<OtpResponse>
    fun makeOtpValidationRequest(otpValidationRequest: OtpValidationRequest): Flow<OtpResponse>
    fun makeCheckoutRequest(checkoutData: CheckoutData): Flow<CheckoutResponse>
}