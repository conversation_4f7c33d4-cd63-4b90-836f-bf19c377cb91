package com.mmt.hotel.bookingreview.helper

import com.mmt.hotel.bookingreview.model.PostApprovalTripTag
import com.mmt.hotel.bookingreview.model.TravellerDetailV2
import com.mmt.hotel.bookingreview.model.TripTagSubmitDetails
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.corp.PrimaryTraveller
import com.mmt.hotel.bookingreview.model.request.TripTagForRequestApproval
import com.mmt.hotel.bookingreview.model.response.CorpApprovalInfo
import com.mmt.hotel.bookingreview.model.response.CorpApprovingManager
import com.mmt.hotel.bookingreview.model.response.UpdatedCorpPolicyResponse
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagFieldV2
import com.mmt.hotel.bookingreview.model.response.corptriptagv2.CorpTripTagResponseV2
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.common.constants.CorpConstants.GUEST
import com.mmt.hotel.common.constants.GuestType
import com.mmt.uikit.util.isNotNullAndEmpty
import javax.inject.Inject

open class HotelCorpBookingReviewHelper @Inject constructor(private val dataWrapper: BookingReviewDataWrapper,
                                                            bookingPaymentHelper: HotelBookingPaymentHelper) : HotelBookingReviewHelper(dataWrapper,bookingPaymentHelper) {

    companion object {
        const val MALE = "MALE"
        const val FEMALE = "FEMALE"
    }

    fun updateCorpApprovalInfo(corpApprovalInfo: CorpApprovalInfo?) {
        dataWrapper.corpApprovalInfo = corpApprovalInfo
    }

    fun getCorpApprovalInfo(): CorpApprovalInfo? {
        return dataWrapper.corpApprovalInfo
    }

    fun updateCorpApprovingManager(corpApprovingManagers: List<CorpApprovingManager>?) {
        dataWrapper.corpApprovingManagers = corpApprovingManagers
    }

    fun getCorpApprovingManager(): List<CorpApprovingManager>? {
        return dataWrapper.corpApprovingManagers
    }

    fun updateCorpTripTagsResponse(corpTripTagResponseV2: CorpTripTagResponseV2){
        dataWrapper.corpTripTagResponseV2 = corpTripTagResponseV2
    }

    fun getCorpTripTagsResponse(): CorpTripTagResponseV2? {
        return dataWrapper.corpTripTagResponseV2
    }

    fun updateCorpPolicyResponse(corpPolicyResponse: UpdatedCorpPolicyResponse) {
        dataWrapper.corpPolicyResponse = corpPolicyResponse
    }

    fun getCorpPolicyResponse(): UpdatedCorpPolicyResponse? {
        return dataWrapper.corpPolicyResponse
    }

    fun validateGstResponse(data: GSTNDetails?): Boolean {
        var isValid: Boolean = false
        data?.let {
            isValid = it.gstn.isNotNullAndEmpty() && it.organizationName.isNotNullAndEmpty()
        }
        return isValid
    }

    fun updateGSTDetails(gstDetails: GSTNDetails?) {
        dataWrapper.gstDetails = gstDetails
    }

    fun updateCorpPrimaryTraveller(primaryTraveller: CorpTravellerDetail) {
        dataWrapper.corpPrimaryTraveller = primaryTraveller
    }

    fun updateCorpCoTravellerList(coTravellerList: List<CorpTravellerDetail>) {
        dataWrapper.corpCoTravellerList = coTravellerList
    }

    private fun getCorpCoTravellerList(): List<CorpTravellerDetail>? {
        return dataWrapper.corpCoTravellerList
    }

    fun getTravellerDetailsList(): List<TravellerDetailV2> {
        val travellerList= mutableListOf<TravellerDetailV2>()
        val gstData = dataWrapper.gstB2BEnteredDetails ?: dataWrapper.gstDetails

        dataWrapper.corpPrimaryTraveller?.let {
            if(dataWrapper.gstB2BEnteredDetails != null && gstData != null){
              travellerList.add(getTravellerDetailV2WithGst(it, gstData, true))
            } else if(gstData != null && validateGstResponse(gstData)) {
                travellerList.add(getTravellerDetailV2WithGst(it, gstData, true))
            } else {
                travellerList.add(getTravellerDetailV2(it, true))
            }
        }
        val corpCoTravellerList = getCorpCoTravellerList()
        corpCoTravellerList?.forEach {
            // For Guest Traveller - Add Primary Traveller's Email-ID
            if(it.travellerType == GUEST && it.emailId.isEmpty()) {
                dataWrapper.corpPrimaryTraveller?.let { primaryTraveller ->
                    it.emailId = primaryTraveller.emailId
                }
            }

            if (dataWrapper.gstB2BEnteredDetails != null && gstData != null) {
                travellerList.add(getTravellerDetailV2WithGst(it, gstData, false))
            } else if (gstData != null && validateGstResponse(gstData)) {
                travellerList.add(getTravellerDetailV2WithGst(it, gstData, false))
            } else {
                if(it.emailId.isNullOrEmpty()) {
                    dataWrapper.corpPrimaryTraveller?.let { primaryTraveller ->
                        it.emailId = primaryTraveller.emailId
                    }
                }
                travellerList.add(getTravellerDetailV2(it, false))
            }
        }
        return travellerList
    }

    private fun getTravellerDetailV2(data: CorpTravellerDetail, isPrimary: Boolean): TravellerDetailV2 {
        return TravellerDetailV2(
            firstName = data.firstName,
            lastName = data.lastName,
            masterPax = isPrimary,
            paxType = GuestType.ADULT.name,
            title = data.title,
            emailID = data.emailId,
            isdCode = data.isdCode.toString(),
            mobileNo = data.contactNo,
            gender = getGender(data.title),
            saveTravellerDetails = data.saveTravellerDetails,
            guestTraveller = if(isPrimary) data.isPrimary == PrimaryTraveller.GUEST else null
        )
    }

    private fun getTravellerDetailV2WithGst(data: CorpTravellerDetail, gstDetails: GSTNDetails, isPrimary: Boolean): TravellerDetailV2 {
        return TravellerDetailV2(
            firstName = data.firstName,
            lastName = data.lastName,
            gender = getGender(data.title),
            masterPax = isPrimary,
            paxType = GuestType.ADULT.name,
            emailID = data.emailId,
            isdCode = data.isdCode.toString(),
            title = data.title,
            mobileNo = data.contactNo,
            registerGstinNum = gstDetails.gstn,
            gstinCompanyName = gstDetails.organizationName,
            gstinCompanyAddress = gstDetails.address1,
                state = gstDetails.city,
                saveGstDetails = gstDetails.saveGstDetails,
        saveTravellerDetails = data.saveTravellerDetails,
            guestTraveller = if(isPrimary) data.isPrimary == PrimaryTraveller.GUEST else null)
    }

    fun updateInputGSTDetails(gstDetails: GSTNDetails?){
        dataWrapper.gstB2BEnteredDetails = gstDetails
    }

    private fun getGender(nameTitle: String): String {
        if(nameTitle == "Ms." || nameTitle == "Mrs.")  return FEMALE
        return MALE
    }

    fun getSelectedTripTagData(): PostApprovalTripTag? {
        if( filterSelectedTripTagData(getCorpTripTagsResponse()?.attributeList).isNullOrEmpty() )
            return null
        return PostApprovalTripTag(TripTagSubmitDetails(attributeList = filterSelectedTripTagData(getCorpTripTagsResponse()?.attributeList),
                primaryPaxEmail = getCorpPrimaryTraveller()?.emailId))
    }

     fun filterSelectedTripTagData(tripTagFields: List<CorpTripTagFieldV2>? ): List<TripTagForRequestApproval> {
        val data = mutableListOf<TripTagForRequestApproval>()
        if( tripTagFields.isNullOrEmpty() )
            return data
        tripTagFields.forEach {
            if(!it.attributeSelectedValue.isNullOrEmpty()) {
                data.add(TripTagForRequestApproval(attributeId = it.attributeId,
                        attributeName = it.attributeName,
                        attributeSelectedValue = it.attributeSelectedValue,
                        mandatoryCheck = it.mandatoryCheck))
            }
        }
        return data
    }
}