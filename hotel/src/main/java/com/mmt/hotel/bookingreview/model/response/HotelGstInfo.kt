package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelGstInfo(val gstin: String?,
                        @SerializedName("text")
                        val message: String?,
                        val claimText: String?,
                        val gstStateCode: String?) : Parcelable