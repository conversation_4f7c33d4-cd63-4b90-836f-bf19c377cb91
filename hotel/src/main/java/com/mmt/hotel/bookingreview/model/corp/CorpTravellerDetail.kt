package com.mmt.hotel.bookingreview.model.corp

import android.os.Parcelable
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.viewmodel.adapter.corp.CorpPrimaryTravellerViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.HotelUtil
import kotlinx.parcelize.Parcelize

@Parcelize
data class CorpTravellerDetail(
    var title: String = CorpPrimaryTravellerViewModel.DEFAULT_TITLE,
    var firstName: String = CoreConstants.EMPTY_STRING,
    var lastName: String = CoreConstants.EMPTY_STRING,
    var firstNameBg: Int = CorpPrimaryTravellerViewModel.NORMAL_BG,
    var lastNameBg: Int = CorpPrimaryTravellerViewModel.NORMAL_BG,
    var fullNameErrorMsg: String? = null,
    var showFirstNameError: Boolean = false,
    var showLastNameError: Boolean = false,
    var isdCode: Int = HotelUtil.getIsdCodeForTravellerForm(),
    var contactNo: String = CoreConstants.EMPTY_STRING,
    var contactNoBg: Int = CorpPrimaryTravellerViewModel.NORMAL_BG,
    var contactNoErrorMsg: String? = null,
    var showContactError: Boolean = false,
    var emailId: String = CoreConstants.EMPTY_STRING,
    var isPrimary: PrimaryTraveller = PrimaryTraveller.NOT_PRIMARY,
    var gender: String = CoreConstants.EMPTY_STRING,
    var travellerType: String = CoreConstants.EMPTY_STRING,
    var saveTravellerDetails: Boolean = false,
    var showSaveTravellerCheckbox: Boolean = false
) : Parcelable {

    fun getTravellerName(): String {
        return "$firstName $lastName".trim()
    }

    fun checkFirstNameAndSetError(): Boolean {
        val result: Boolean
        if (firstName.trim().isEmpty()) {
            result = false
            fullNameErrorMsg = ResourceProvider.instance.getString(R.string.htl_name_empty)
        } else {
            result = HotelUtil.validateRegEx(firstName, HotelConstants.FIRST_NAME_REGEX)
                    && !HotelUtil.validateRegEx(firstName, HotelConstants.DOT_AND_SPACE_REGEX)

            fullNameErrorMsg = if (result) {
                                    if(showLastNameError){
                                        fullNameErrorMsg
                                    }else{
                                        null
                                    }
                                } else { ResourceProvider.instance.getString(R.string.htl_name_error) }
        }
        firstNameBg =
            if (result) CorpPrimaryTravellerViewModel.NORMAL_BG else CorpPrimaryTravellerViewModel.ERROR_BG
        showFirstNameError = !result
        return result
    }

    fun checkLastNameAndSetError(): Boolean {
        val result = isLastNameValid()
        fullNameErrorMsg = if (lastName.trim().isEmpty()) {
            ResourceProvider.instance.getString(R.string.htl_name_empty)
        } else {
            if(showFirstNameError) fullNameErrorMsg
            else if(result) null
            else ResourceProvider.instance.getString(R.string.htl_name_error)
        }
        lastNameBg =
            if (result) CorpPrimaryTravellerViewModel.NORMAL_BG else CorpPrimaryTravellerViewModel.ERROR_BG
        showLastNameError = !result
        return result
    }

    fun isLastNameValid(): Boolean {
        return if (lastName.trim().isEmpty()) {
            false
        } else {
            HotelUtil.validateRegEx(lastName, HotelConstants.LAST_NAME_REGEX)
                    && !HotelUtil.validateRegEx(lastName, HotelConstants.DOT_AND_SPACE_REGEX)
        }
    }

    fun isContactNoValid(): Boolean {
        val result: Boolean
        if (contactNo.isEmpty()) {
            result = false
            contactNoErrorMsg = ResourceProvider.instance.getString(R.string.htl_contact_empty)
        } else {
            if (isdCode == FunnelContext.INDIA.mobileCode.toInt()) {
                if (contactNo.length != 10) {
                    result = false
                    contactNoErrorMsg = ResourceProvider.instance.getString(R.string.htl_india_contact_length_error)
                } else {
                    result = HotelUtil.validateRegEx(contactNo, HotelConstants.IND_PHONE_REGEX)
                    contactNoErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.htl_contact_error)
                }
            } else {
                if (contactNo.length in 14..4) {
                    result = false
                    contactNoErrorMsg = ResourceProvider.instance.getString(R.string.htl_contact_length_error)
                } else {
                    result = HotelUtil.validateRegEx(contactNo, HotelConstants.PHONE_REGEX)
                    contactNoErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.htl_contact_error)
                }
            }
        }
        showContactError = !result
        contactNoBg = if (result) CorpPrimaryTravellerViewModel.NORMAL_BG else CorpPrimaryTravellerViewModel.ERROR_BG
        return result
    }
}

enum class PrimaryTraveller {
    MYSELF,
    COLLEAGUE,
    GUEST,
    NOT_PRIMARY
}