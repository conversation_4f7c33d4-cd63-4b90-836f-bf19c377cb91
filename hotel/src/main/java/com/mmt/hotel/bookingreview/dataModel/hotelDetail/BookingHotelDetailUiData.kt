package com.mmt.hotel.bookingreview.dataModel.hotelDetail

import android.os.Parcelable
import com.mmt.hotel.bookingreview.dataModel.CheckInCheckOutDetails
import com.mmt.hotel.bookingreview.model.response.HotelTagInfo
import com.mmt.hotel.bookingreview.model.response.InstantFareInfo
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.userReviews.featured.observables.HotelUserRatingDataObservable
import kotlinx.parcelize.Parcelize

data class BookingHotelDetailUiData(val name: String,
                                    val starRating: Int,
                                    val starRatingType: String?,
                                    val address: String,
                                    val imgUrl: String?,
                                    val stayDetailUiData: StayDetailUiData,
                                    val checkInCheckOutDetails: CheckInCheckOutDetails,
                                    val hotelCategories: List<LinearLayoutItemData>,
                                    val mmtSpecificCategories: List<LinearLayoutItemData>,
                                    val isAltAcco: Boolean,
                                    val countryCode: String,
                                    val hotelTags: Map<String, HotelTagInfo>?,
                                    val userRatingDataObservable: HotelUserRatingDataObservable?,
                                    val instantFareInfo: InstantFareInfo? = null
)

@Parcelize
data class AddOnInfo(
    val title: String? = null,
    val subtitle: String? = null,
    val tag: BasicTagInfo,
    val descriptionText: String? = null,
    val bgUrl: String? = null
) : Parcelable

@Parcelize
data class BasicTagInfo(
    val title: String? = null,
    val color: String? = null
) : Parcelable

@Parcelize
data class RTBInfo(
    val title: String? = null,
    @Deprecated("deprecated 15 Dec'23 (being used in older apps) - using rtbInfoList now to show points")
    val subTitle: String? = null,
    val bnplText: String? = null,
    val rtbInfoList: List<String>? = null,
    val type: String? = null,
    val persuasionIcon: String? = null,
    val persuasionText: String? = null
): Parcelable