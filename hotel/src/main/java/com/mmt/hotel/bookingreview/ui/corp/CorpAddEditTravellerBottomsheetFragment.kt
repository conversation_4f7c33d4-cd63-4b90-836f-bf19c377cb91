package com.mmt.hotel.bookingreview.ui.corp

import android.os.Bundle
import android.view.View
import androidx.databinding.ViewDataBinding
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.mmt.core.util.DeviceUtil
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.bookingreview.event.CorpEmployeeSearchActivityEvent
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpAddEditTravellerViewModel
import com.mmt.hotel.databinding.HtlCorpReviewAddEditTravellerV2Binding
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class CorpAddEditTravellerBottomsheetFragment: HotelBottomSheetDialogFragment<CorpAddEditTravellerViewModel, HtlCorpReviewAddEditTravellerV2Binding> () {

    @Inject
    lateinit var factory: HotelViewModelFactory
    private lateinit var activitySharedViewModel: HotelEventSharedViewModel

    companion object {
        const val TAG = "CorpAddEditTravellerBottomsheetFragment"

        @JvmStatic
        fun newInstance(bundle: Bundle): CorpAddEditTravellerBottomsheetFragment {
            val fragment = CorpAddEditTravellerBottomsheetFragment()
            fragment.arguments = bundle;
            return fragment
        }
    }

    override fun initViewModel(): CorpAddEditTravellerViewModel =   getViewModel(factory)


    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.htl_corp_review_add_edit_traveller_v2
    }

    override fun initFragmentView() {
        activitySharedViewModel = getActivityViewModel()
    }

    override fun handleEvents(event: HotelEvent) {
        when(event.eventID) {
            CorpEmployeeSearchActivityEvent.TRAVELLER_DETAILS_ADDED -> {
                dismiss()
                sendEventToActivity(event)
            }
            CorpEmployeeSearchActivityEvent.ON_BACK_PRESSED -> {
                //KeyBoardUtils.hideKeyboard(activity)
                dismiss()
            }
        }
    }

    private fun sendEventToActivity(event:HotelEvent) {
        activitySharedViewModel?.updateEventStream(event)
    }
}