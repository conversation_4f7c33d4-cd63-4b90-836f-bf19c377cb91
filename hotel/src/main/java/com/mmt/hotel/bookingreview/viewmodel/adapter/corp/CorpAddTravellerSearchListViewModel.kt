package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import androidx.databinding.BaseObservable
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.model.Employee
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.CorpEmployeeSearchAdapterItem
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.uikit.util.isNotNullAndEmpty

class CorpAddTravellerSearchListViewModel(var data: Employee, var eventStream: MutableLiveData<HotelEvent>) : BaseObservable(), AbstractRecyclerItem {

    private val userDetails: CorpTravellerDetail = CorpTravellerDetail()

    fun updateData(data: Employee) {
        this.data = data
        notifyChange()
    }

    fun getEmployeeName(): String = data.name.capitalizeWords()

    fun getEmployeeEmail(): String = data.businessEmailId

    override fun getItemType(): Int {
        return CorpEmployeeSearchAdapterItem.CORP_EMPLOYEE_LIST_DETAIL
    }

    fun onEmployeeClicked() {
        userDetails.apply {
            val namePair = HotelUtil.getFirstAndLastNamePair(data.name)
            firstName = namePair.first
            lastName = namePair.second
            title = data.title ?: CorpPrimaryTravellerViewModel.DEFAULT_TITLE
            contactNo = data.phoneNumber ?: CoreConstants.EMPTY_STRING
            emailId = data.businessEmailId ?: CoreConstants.EMPTY_STRING
            gender = data.gender ?: CoreConstants.EMPTY_STRING
            isdCode = if (data.countryCode.isNotNullAndEmpty()) data.countryCode.toInt() else HotelUtil.getIsdCodeForTravellerForm()
        }
        eventStream.value = HotelEvent(HotelCorpBookingReviewFragmentEvent.ON_EMPLOYEE_CLICKED, userDetails)
    }

    private fun String.capitalizeWords(): String = split(" ").map { it.capitalize() }.joinToString(" ")
}

