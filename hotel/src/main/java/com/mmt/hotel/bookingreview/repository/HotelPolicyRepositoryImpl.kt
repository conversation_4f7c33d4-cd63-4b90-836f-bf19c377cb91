package com.mmt.hotel.bookingreview.repository

import com.mmt.hotel.base.repository.HotelBaseRepository
import com.mmt.hotel.bookingreview.model.request.HotelPolicyRequest
import com.mmt.hotel.bookingreview.model.response.HotelPolicyResponseV2
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class HotelPolicyRepositoryImpl @Inject constructor() : HotelBaseRepository(), HotelPolicyRepository {

    companion object {
        val HOTEL_POLICY_API = "$CLIENT_GATEWAY_BASE_URL/get-policies/$client/$apiVersionCode"
    }

    override fun fetchHotelPolicies(hotelPolicyRequest: HotelPolicyRequest, countryCode: String): Flow<HotelPolicyResponseV2> {
        return makePostRequest(url = HOTEL_POLICY_API,
                postData = hotelPolicyRequest,
                countryCode = countryCode)
    }
}