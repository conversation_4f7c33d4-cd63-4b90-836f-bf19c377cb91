package com.mmt.hotel.bookingreview.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.bookingreview.model.HotelPolicyBundleData
import com.mmt.hotel.bookingreview.repository.HotelPolicyRepository
import com.mmt.hotel.bookingreview.repository.HotelPolicyRepositoryImpl
import com.mmt.hotel.compose.review.ui.fragment.HotelPolicyRuleFragment
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
class HotelPolicyFragmentModule {

    @Provides
    fun provideHotelPolicyRepository(repositoryImp: HotelPolicyRepositoryImpl): HotelPolicyRepository = repositoryImp

    @Provides
    fun provideBundleData(fragment: Fragment): HotelPolicyBundleData? {
        if (fragment is HotelPolicyRuleFragment) {
            return fragment.arguments?.getParcelable(
                HotelPolicyRuleFragment.KEY_BUNDLE_PARAM
            )
        }
        return null
    }
}