package com.mmt.hotel.bookingreview.model.request

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class ConsentAPIRequest(
    val org: String,
    val author: String,
    val consents: ArrayList<ConsentInfo>
): Parcelable

@Parcelize
class ConsentInfo(
    val consent_id: String,
    val value: ConsentValue,
    val status_active: Boolean,
    val lob: String
): Parcelable

@Parcelize
class ConsentValue(
    val accepted: Boolean,
    val phone_number: String,
    val hotel_id: String
): Parcelable