package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class FeatureFlags(val soldOut: Boolean,
                        val realWalletAvailable: Boolean,
                        val showFCBanner: <PERSON><PERSON>an,
                        val pahWalletApplicable: Boolean,
                        val payMode: String,
                        val foreignTravel: Boolean,
                        val blackEligible: <PERSON><PERSON>an,
                        val leadPassengerMandatoryPerRoom:Boolean?,
                        val requestToBook:Boolean ?= null,
                        val rtbPreApproved:Boolean ?= null,
                        val rtbAutoCharge:Boolean ?= null,
                        val showScrolldown: Boolean = false,
                        val payLaterCard: Boolean ?= null,
                        val captureAllPaxDetailsHotel: Boolean? = null
) : Parcelable