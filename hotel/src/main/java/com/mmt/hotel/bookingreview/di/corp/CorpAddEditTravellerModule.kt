package com.mmt.hotel.bookingreview.di.corp

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.bookingreview.model.corp.CorpAddEditTravellerFragmentData
import com.mmt.hotel.bookingreview.ui.corp.CorpAddEditTravellerBottomsheetFragment
import com.mmt.hotel.bookingreview.ui.corp.CorpAddEditTravellerFragment
import com.mmt.hotel.bookingreview.viewmodel.corp.CorpAddEditTravellerViewModel
import dagger.Binds
import dagger.BindsInstance
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class CorpAddEditTravellerModule {

    @Provides
    @IntoMap
    @ViewModelKey(CorpAddEditTravellerViewModel::class)
    fun provideAddEditTravellerViewModel(viewModel: CorpAddEditTravellerViewModel): HotelViewModel {
        return viewModel
    }

    @Provides
    fun bundleData(fragment: Fragment) : CorpAddEditTravellerFragmentData? {
        if(fragment is CorpAddEditTravellerBottomsheetFragment){
            return fragment.arguments?.getParcelable(CorpAddEditTravellerBottomsheetFragment.TAG) as CorpAddEditTravellerFragmentData?
        }
        return null
    }

}

