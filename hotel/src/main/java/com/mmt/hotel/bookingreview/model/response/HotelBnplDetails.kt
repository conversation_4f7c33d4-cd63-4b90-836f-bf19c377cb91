package com.mmt.hotel.bookingreview.model.response

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelBnplDetails(
    val bnplApplicable: Boolean?,
    val bnplPersuasionMsg: String?,
    val bnplPolicyText: String?,
    val bnplText: String?,
    val bnplSubText: String?,
    val bnplVariant: String?,
    val priceFooter: PriceFooter?,
    val finalPrice: String?
) : Parcelable