package com.mmt.hotel.bookingreview.viewmodel.adapter.corp

import android.text.Editable
import androidx.databinding.BaseObservable
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewActivityEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent
import com.mmt.hotel.bookingreview.event.HotelCorpBookingReviewFragmentEvent.SHOW_UPDATED_PRIMARY_TRAVELLER_NAME
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.bookingreview.model.GstInputDetail
import com.mmt.hotel.bookingreview.model.corp.CorpTravellerDetail
import com.mmt.hotel.bookingreview.model.corp.PrimaryTraveller
import com.mmt.hotel.bookingreview.model.response.gstn.GSTNDetails
import com.mmt.hotel.bookingreview.viewmodel.TravelerViewModel
import com.mmt.hotel.common.constants.CorpConstants.GUEST
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.uikit.util.isNotNullAndEmpty

class CorpPrimaryTravellerViewModel(
    var primaryTravellerDetail: CorpTravellerDetail,
    val eventStream: MutableLiveData<HotelEvent>,
    private val isForeignTravel: Boolean,
    val enableCard: Boolean = true
) : BaseObservable(), TravelerViewModel {

    companion object {
        var NORMAL_BG = R.drawable.htl_traveller_input_text_bg
        var ERROR_BG = R.drawable.htl_traveller_input_text_error_bg
        val DEFAULT_TITLE: String = ResourceProvider.instance.getStringArray(R.array.TRAVELLER_TITLE)[0]
    }

    var primaryTraveller = ObservableField<CorpTravellerDetail>(primaryTravellerDetail)
    val isEmailVisible = ObservableBoolean(true)
    val setEmailFocus = ObservableBoolean(true)
    private var isBookingProfileOfGuestType: Boolean = false
    private var primaryTravellerEmailForGuestType = ""
    private val lastNameInitialStateValid = primaryTravellerDetail.isLastNameValid()
    val firstNameValidation: () -> Boolean = { primaryTraveller.get()?.checkFirstNameAndSetError() ?: true }
    val lastNameValidation: () -> Boolean = { primaryTraveller.get()?.checkLastNameAndSetError() ?: true}
    val contactValidation: () -> Boolean = { primaryTraveller.get()?.isContactNoValid() ?: true}
    var gstAvailable = false
    var gstDetail: GstInputDetail

    init {
        if (primaryTravellerDetail.isPrimary == PrimaryTraveller.GUEST) {
            isBookingProfileOfGuestType = true
            primaryTravellerEmailForGuestType = primaryTravellerDetail.emailId
        }
        gstDetail = GstInputDetail()
    }

    fun getGstInput(): GSTNDetails {
        return GSTNDetails(
            gstn = gstDetail.number,
            organizationName = gstDetail.name,
            address1 = gstDetail.address
        )
    }

    private fun updatePrimaryTraveller() {
        primaryTraveller.set(primaryTravellerDetail)
        eventStream.postValue( HotelEvent(HotelCorpBookingReviewFragmentEvent.ON_PRIMARY_EMAIL_CHANGE, primaryTravellerDetail.emailId))
    }

    fun validateCorpPrimaryTravellerData(): Boolean{
        var result = validateCorpPrimaryTravellerDetail()
        if (gstAvailable) {
            result = validateGstDetail() && result
        }
        notifyChange()
        return result
    }

    private  fun validateCorpPrimaryTravellerDetail(): Boolean {
        return primaryTravellerDetail.run {
            var result = checkFirstNameAndSetError()
            result = checkLastNameAndSetError() && result
            result = isContactNoValid() && result
            result
        }
    }

    private fun validateGstDetail(): Boolean {
        return gstDetail.run {
            var result = isNumberValid()
            result = isNameValid() && result
            result = isAddressValid() && result
            result
        }
    }

    fun makeCoTravellerPrimary(coTravellerDetail: CorpTravellerDetail, isCoTravellerLoggedInUser: Boolean) {
        primaryTravellerDetail.isPrimary = PrimaryTraveller.NOT_PRIMARY
        eventStream.value = HotelEvent(HotelCorpBookingReviewFragmentEvent.MAKE_PRIMARY_TRAVELLER_CO, primaryTravellerDetail)
        primaryTravellerDetail = coTravellerDetail

        when {
            isCoTravellerLoggedInUser -> primaryTravellerDetail.isPrimary = PrimaryTraveller.MYSELF
            isBookingProfileOfGuestType -> primaryTravellerDetail.emailId = primaryTravellerEmailForGuestType
            else -> primaryTravellerDetail.isPrimary = PrimaryTraveller.COLLEAGUE
        }
        isEmailVisible.set(true)
        updatePrimaryTraveller()
    }

    fun onRemoveClick() {
        isEmailVisible.set(false)
        eventStream.value = HotelEvent(HotelCorpBookingReviewFragmentEvent.MAKE_PRIMARY_TRAVELLER_VIEW_GONE, this)
        primaryTravellerDetail = CorpTravellerDetail()
        updatePrimaryTraveller()
    }

    fun updatePrimaryTravellerDetails(primaryTravellerDetail: CorpTravellerDetail, isPrimaryTravellerLoggedInUser: Boolean) {
        primaryTravellerDetail.isPrimary = PrimaryTraveller.NOT_PRIMARY
        this.primaryTravellerDetail = primaryTravellerDetail
        if (isPrimaryTravellerLoggedInUser)
            primaryTravellerDetail.isPrimary = PrimaryTraveller.MYSELF
        else
            primaryTravellerDetail.isPrimary = PrimaryTraveller.COLLEAGUE
        updatePrimaryTraveller()
        isEmailVisible.set(true)
        eventStream.value = HotelEvent(
            HotelCorpBookingReviewFragmentEvent.MAKE_PRIMARY_TRAVELLER_VIEW_VISIBLE,
            primaryTravellerDetail
        )
    }

    fun firstNameTextChanged(s: Editable) {
        primaryTravellerDetail.firstName = s.toString()
        postUpdatedNameForCoTravelerCard()
    }

    fun lastNameTextChanged(s: Editable) {
        primaryTravellerDetail.lastName = s.toString()
        postUpdatedNameForCoTravelerCard()
    }

    private fun postUpdatedNameForCoTravelerCard() {
        var data = primaryTravellerDetail.getTravellerName()
        primaryTraveller.get()?.travellerType?.let {
            if (it == GUEST) {
                data += " ($it) "
            }
        }
        eventStream.value = HotelEvent(SHOW_UPDATED_PRIMARY_TRAVELLER_NAME, data)
    }

    fun isCrossVisible(): Boolean {
        return !isBookingProfileOfGuestType && enableCard
    }
    fun openSearchActivityWithPrimaryEmail() {
        if (isBookingProfileOfGuestType){
            setEmailFocus.set(false)
            return
        }
        eventStream.value = HotelEvent(
            HotelCorpBookingReviewActivityEvent.OPEN_SEARCH_ACTIVITY_FOR_PRIMARY_EMPLOYEE_WITH_EMAIL,
            primaryTravellerDetail.emailId
        )
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.CORP_ADD_TRAVELLER
    }

    fun firstNameFieldEnabled(): Boolean {
        if (!enableCard && primaryTraveller.get()?.firstName.isNotNullAndEmpty()) {
            return false
        }
        return true
    }

    fun lastNameFieldEnabled(): Boolean {
        if (!enableCard && lastNameInitialStateValid) {
            return false
        }
        return true
    }

    override fun cardOrder(): String {
        return BookingRecyclerAdapterItemKeys.CORP_ADD_TRAVELLER
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as? CorpPrimaryTravellerViewModel)
        return this == matchedWith
    }

    override fun cardName(): String {
        return "Corp Review Add Traveller"
    }

    fun invokeAndNotify(validationMethod: () -> Boolean): Boolean {
        val result = validationMethod.invoke()
        notifyChange()
        return result
    }

}