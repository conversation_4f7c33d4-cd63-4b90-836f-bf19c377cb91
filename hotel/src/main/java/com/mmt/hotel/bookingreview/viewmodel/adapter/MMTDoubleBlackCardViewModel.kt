package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.databinding.BaseObservable
import com.mmt.hotel.R
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.bookingreview.adapter.HotelBookingReviewAdapterItem
import com.mmt.hotel.bookingreview.helper.constants.BookingRecyclerAdapterItemKeys
import com.mmt.hotel.compose.review.viewModel.PropertyRulesViewModelV2
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem

class MMTDoubleBlackCardViewModel(private var title: String, private var description: String) : BaseObservable(),
    DiffUtilRecycleItem {

    fun getTitle() = title

    fun getDescription() = description

    fun getBgStartColor(): Int {
        return ResourceProvider.instance.getColor(R.color.double_black_bg_start)
    }

    fun getBgEndColor(): Int {
        return ResourceProvider.instance.getColor(R.color.double_black_bg_end)
    }

    override fun cardOrder(): String {
       return BookingRecyclerAdapterItemKeys.DOUBLE_BLACK_CARD
    }

    override fun isSame(item: DiffUtilRecycleItem): Boolean {
        val matchedWith = (item as MMTDoubleBlackCardViewModel)
        return this == matchedWith    }

    override fun cardName(): String {
        return  "DOUBLE BLACK CARD"
    }

    override fun getItemType(): Int {
        return HotelBookingReviewAdapterItem.MMT_DOUBLE_BLACK
    }
}