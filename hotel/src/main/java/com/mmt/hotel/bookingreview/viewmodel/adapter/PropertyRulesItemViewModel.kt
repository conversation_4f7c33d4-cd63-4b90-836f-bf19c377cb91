package com.mmt.hotel.bookingreview.viewmodel.adapter

import com.mmt.hotel.binding.HotelDrawableProvider
import com.mmt.hotel.bookingreview.model.response.property.RuleItem

class PropertyRulesItemViewModel(val data: RuleItem){

    fun background(): Int = HotelDrawableProvider.getDrawableResource(data.iconType.orEmpty())

    fun showIcon() = data.showIcon

    fun title() = data.title

    fun getMaxLines():Int{
        return if(data.isFromBookingPage){
            3
        }else{
            Int.MAX_VALUE
        }
    }
}
