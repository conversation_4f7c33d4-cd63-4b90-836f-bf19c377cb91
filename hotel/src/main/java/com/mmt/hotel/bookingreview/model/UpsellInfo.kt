package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.common.model.request.RoomCriteriaV2
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
data class UpsellInfo(val displayText: String?, // default text when unselected
                      var successDisplayText : String? = null, // successText when the option has been successfully availed
                      val failureDisplayText : String? = null, // failure text while trying to opt for this upsell but failed
                      val displaySubText: String? = null,
                      val roomCriteria: List<RoomCriteriaV2>? = null,
                      val addOnType: String? = null,
                      val upsellType: String? = null,
                      @SerializedName("flexiCancellationDetails")
                      val addonExtraDetails: AddonExtraDetails? = null,
                      val trackingStr : String? = null
) : Parcelable {
    @IgnoredOnParcel
    var isSelected = false

    @IgnoredOnParcel
    var isDisabled = false
}

@Parcelize
data class AddonExtraDetails(
        val imageUrl: String?,
        val cta: String?,
        val ctaUrl: String?,
        val title: String?
):Parcelable