package com.mmt.hotel.bookingreview.observable.snackbar

import android.graphics.drawable.Drawable
import androidx.annotation.DrawableRes
import androidx.databinding.BaseObservable
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.dataModel.snackbar.SnackBarCouponItemUiData
import com.mmt.hotel.bookingreview.helper.constants.DiscountBreakUpKey

class SnackBarDiscountItemObservable(private val data: SnackBarCouponItemUiData) : BaseObservable() {

    fun getLabel() = data.label

    fun getAmount() = data.amount

    fun getEffectiveCouponLabel() = data.map[DiscountBreakUpKey.EFFECTIVE_COUPON_APPLIED]?.first.orEmpty()

    fun getEffectiveCouponAmount() = data.map[DiscountBreakUpKey.EFFECTIVE_COUPON_APPLIED]?.second.orEmpty()

    fun getReversalFeeLabel() = data.map[DiscountBreakUpKey.SERVICE_FEES_REVERSAL]?.first.orEmpty()

    fun getReversalFeeAmount() = data.map[DiscountBreakUpKey.SERVICE_FEES_REVERSAL]?.second.orEmpty()

    fun isExpanded() = data.isExpanded

    fun onArrowClicked() {
        data.isExpanded = !data.isExpanded
        notifyChange()
    }

    fun showDownArrow(): Boolean {
        return data.isBreakUpAvailable && !data.isExpanded
    }

    fun showExpanded(): Boolean {
        return data.isBreakUpAvailable && data.isExpanded
    }

    fun getArrowTheme(): Int {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
            ResourceProvider.instance.getColor(R.color.ff664b)
        } else {
            ResourceProvider.instance.getColor(R.color.color_008cff)
        }
    }

    fun getLabelColor(): Int {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
            R.color.htl_corp_total_discount_label
        } else {
            R.color.htl_total_discount_label
        }
    }

    fun getAmountColor(): Int {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
           R.color.grey_hotel
        } else {
           R.color.htl_total_discount_label
        }
    }

    @DrawableRes
    fun getIconDrawable(): Int {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
            R.drawable.ic_corp_tick_review
        } else {
            R.drawable.ic_green_tick_new
        }
    }

    fun getBelowDealDrawable(): Drawable {
        return if( com.mmt.auth.login.util.LoginUtils.isCorporateUser ) {
            ResourceProvider.instance.getDrawable(R.drawable.corp_coupon_bottom_line)!!
        }else{
            ResourceProvider.instance.getDrawable(R.drawable.coupon_botoom_line)!!
        }
    }
}