package com.mmt.hotel.bookingreview.viewmodel.adapter

import androidx.compose.ui.text.font.FontWeight
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.dataModel.PaymentPolicyItemUiData
import com.mmt.hotel.bookingreview.model.response.PaymentPlan
import com.mmt.hotel.common.data.HotelTextConfigData
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.compose.MMTFontStyle
import com.mmt.uikit.fonts.FontConstants
import com.mmt.uikit.fonts.latoFont

class PartPaymentOptionViewModel(
    paymentPlan: PaymentPlan,
    private val showOnlyPolicies: Boolean = false,
    marginBottom: Int = R.dimen.margin_0dp,
    currencySymbol: String
): PaymentPolicyCardViewModel(paymentPlan, showOnlyPolicies,false, marginBottom,currencySymbol = currencySymbol) {

    override fun createPaymentPolicyList(paymentPlan: PaymentPlan): List<LinearLayoutItemData> {
        val itemList = mutableListOf<LinearLayoutItemData>()

        // add top policy
        if (!showOnlyPolicies) {
            itemList.add(
                LinearLayoutItemData(
                    R.layout.htl_payment_policy_card_item, BR.model,
                    PaymentPolicyItemUiData(
                        showIcon = false,
                        amount = HotelUtil.getCommaSeparatedPriceWithCurrency(paymentPlan.amount),
                        amountTextConfig = HotelTextConfigData(
                            textSizeResId = R.dimen.htl_text_size_large,
                            fontResId = FontConstants.LATO_BOLD,
                            fontResComposeId= MMTFontStyle(
                                fontFamily = latoFont,
                                fontWeight = FontWeight.Black
                            )

                        ),
                        title = paymentPlan.text,
                        titleTextConfig = HotelTextConfigData(
                            textSizeResId = R.dimen.htl_text_size_large,
                            fontResId = FontConstants.LATO_REGULAR,
                            textColor = R.color.grey_hotel,
                            fontResComposeId= MMTFontStyle(
                                fontFamily = latoFont,
                                fontWeight = FontWeight.Bold
                            )
                        ),
                        marginBottom = R.dimen.margin_small_extra
                    )
                )
            )
        }

        // add payment policies
        paymentPlan.paymentPolicy?.mapIndexed { index, it ->
            val item = LinearLayoutItemData(R.layout.htl_payment_policy_card_item, BR.model,
                PaymentPolicyItemUiData(
                    index = it.sequence,
                    amount = HotelUtil.getCommaSeparatedPriceWithCurrency(it.amount),
                    amountTextConfig = HotelTextConfigData(
                        textSizeResId = R.dimen.htl_text_size_medium,
                        fontResId = FontConstants.LATO_BOLD,
                        fontResComposeId = MMTFontStyle(fontFamily = latoFont,
                            fontWeight = FontWeight.Bold )
                    ),
                    showDivider = index > 0,
                    title = it.text,
                    titleTextConfig = HotelTextConfigData(
                        textSizeResId = R.dimen.detail_page_text_size_tiny,
                        fontResId = FontConstants.LATO_REGULAR,
                        textColor = R.color.grey_hotel,
                        fontResComposeId = MMTFontStyle(fontFamily = latoFont,
                            fontWeight = FontWeight.Normal )
                    )
                )
            )
            itemList.add(item)
        }
        return itemList
    }

}