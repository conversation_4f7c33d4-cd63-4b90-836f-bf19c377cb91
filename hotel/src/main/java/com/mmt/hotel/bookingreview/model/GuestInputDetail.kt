package com.mmt.hotel.bookingreview.model

import android.os.Parcelable
import androidx.databinding.BaseObservable
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.constants.HotelConstants.DEFAULT_TITLE
import com.mmt.hotel.common.constants.HotelConstants.DOT_AND_SPACE_REGEX
import com.mmt.hotel.common.constants.HotelConstants.ET_ERROR_BG
import com.mmt.hotel.common.constants.HotelConstants.ET_NORMAL_BG
import com.mmt.hotel.common.constants.HotelConstants.FIRST_NAME_REGEX
import com.mmt.hotel.common.constants.HotelConstants.LAST_NAME_REGEX
import com.mmt.hotel.common.util.HotelUtil.validateRegEx
import kotlinx.parcelize.Parcelize

@Parcelize
data class GuestInputDetail(var title: String = DEFAULT_TITLE,
                            var name: String = EMPTY_STRING,
                            var nameBg: Int = ET_NORMAL_BG,
                            var surname: String = EMPTY_STRING,
                            var surnameBg: Int = ET_NORMAL_BG,
                            var nameErrorMsg: String? = null,
                            var child: Boolean = false,
                            var showNameError: Boolean = false,
                            var showSurnameError: Boolean = false): BaseObservable(), Parcelable {
    fun isNameValid(): Boolean {
        val result: Boolean
        if (name.isEmpty()) {
            result = false
            nameErrorMsg = ResourceProvider.instance.getString(R.string.htl_name_empty)
        } else {
            result = validateRegEx(name, FIRST_NAME_REGEX) && !validateRegEx(name, DOT_AND_SPACE_REGEX)
            nameErrorMsg = if (result) null else ResourceProvider.instance.getString(R.string.htl_name_error)
        }
        nameBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        showNameError = !result
        notifyChange()
        return result
    }

    fun isSurnameValid(): Boolean {
        val result: Boolean
        if (surname.isEmpty()) {
            result = false
            nameErrorMsg = ResourceProvider.instance.getString(R.string.htl_name_empty)
        } else {
            result = validateRegEx(surname, LAST_NAME_REGEX) && !validateRegEx(surname, DOT_AND_SPACE_REGEX)
            nameErrorMsg = nameErrorMsg?.let {
                nameErrorMsg
            }?: kotlin.run {
                if (result) null else ResourceProvider.instance.getString(R.string.htl_name_error)
            }
        }
        surnameBg = if (result) ET_NORMAL_BG else ET_ERROR_BG
        showSurnameError = !result
        notifyChange()
        return result
    }

    fun clear() {
        name = EMPTY_STRING
        nameBg = ET_NORMAL_BG
        surname = EMPTY_STRING
        surnameBg = ET_NORMAL_BG
        nameErrorMsg = null
        child = false
        showNameError = false
        showSurnameError = false
        notifyChange()
    }
}