package com.mmt.hotel.bookingreview.ui.viewholder

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ArrayAdapter
import com.mmt.hotel.R
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.bookingreview.viewmodel.CoTravellerAddViewModel
import com.mmt.hotel.databinding.HtlCoTravellerAddItemBinding

class CoTravellerAddViewHolder(layoutInflater: LayoutInflater, layoutId: Int, parent: ViewGroup)
    : HotelRecyclerViewHolder<HtlCoTravellerAddItemBinding, CoTravellerAddViewModel>(layoutInflater, layoutId, parent) {

    private val spinnerAdapter = ArrayAdapter.createFromResource(dataBinding.root.context,
        R.array.TRAVELLER_TITLE, R.layout.view_spinner_item)

    override fun bindData(data: CoTravellerAddViewModel, position: Int) {
        dataBinding.spTitle.adapter = spinnerAdapter
        dataBinding.model = data
        dataBinding.executePendingBindings()
    }
}