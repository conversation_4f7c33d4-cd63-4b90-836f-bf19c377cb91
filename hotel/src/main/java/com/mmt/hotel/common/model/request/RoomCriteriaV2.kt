package com.mmt.hotel.common.model.request

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.common.model.OccupancyData
import kotlinx.parcelize.Parcelize

@Parcelize
data class RoomCriteriaV2(val ratePlanCode: String,
                          val roomCode: String,
                          val pricingKey: String? = null,
                          val mtKey: String? = null,
                          val roomStayCandidates: List<RoomStayCandidatesV2>?,
                          val sellableType: String? = null,
                          val isLucky: Boolean? = null,
                          val recommendedType: String? = null,
                          val ratePlanType: String? = null,
) : Parcelable

fun List<RoomCriteriaV2>.toOccupancyData(): OccupancyData {
    var totalAdults = 0
    val childAges = arrayListOf<Int>()
    for (roomCriteria in this) {
        val roomStayCandidates = roomCriteria.roomStayCandidates ?: emptyList()
        for (roomStayCandidate in roomStayCandidates) {
            totalAdults += roomStayCandidate.adultCount
            childAges.addAll(roomStayCandidate.childAges ?: emptyList())
        }
    }
    return OccupancyData(size, totalAdults, childAges)
}