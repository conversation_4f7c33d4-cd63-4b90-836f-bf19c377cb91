package com.mmt.hotel.common.helper

import com.mmt.auth.login.model.login.DomainType
import com.mmt.auth.logout.interfaces.LogoutTaskInterface

class HTLLogout : LogoutTaskInterface {

    override suspend fun logoutPersonal(domainType: DomainType){
        HTLLocalCacheManager.clearTravellerInfo()
    }

    override suspend fun logoutCorp() {
        HTLLocalCacheManager.clearTravellerInfo()
    }

    override suspend fun logoutAll(){
        HTLLocalCacheManager.clearTravellerInfo()
    }
}