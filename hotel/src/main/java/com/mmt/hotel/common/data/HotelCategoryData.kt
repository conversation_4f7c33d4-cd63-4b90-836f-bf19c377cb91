package com.mmt.hotel.common.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 *
 *  data class for showing HotelCategories like - MMT_ASSURED, Couple Friendly , MySafe categories etc
 *  data is powered by mobconfig api
 *  handling for both imgData & nativeData is different
 */

@Parcelize
data class HotelCategoryData(val imgTagData: HotelCategoryImageTagData?,
                             val nativeData: HotelCategoryNativeTagData?) : Parcelable