package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.AREA_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.CITY_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.COUNTRY_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.CUSTOM_LOCATION_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.GPOI_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.GPS_NEARBY_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.HOTEL_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.POI_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.REGION_SEARCH

@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [<PERSON><PERSON><PERSON>_SEARCH,
    <PERSON><PERSON><PERSON>_SEARCH,
    AR<PERSON>_SEARCH,
    <PERSON><PERSON><PERSON>_<PERSON>ARCH,
    GPS_NEARBY_SEARCH,
    CUSTOM_LOCATION_SEARCH,
    POI_SEARCH,
    GPOI_SEARCH,
    COUNTRY_SEARCH])
annotation class HotelsSearchRequestType {
    companion object {
        const val REGION_SEARCH = "REGION"
        const val CITY_SEARCH = "CTY"
        const val AREA_SEARCH = "AREA"
        const val HOTEL_SEARCH = "HTL"
        const val GPS_NEARBY_SEARCH = "NEARBY"
        const val CUSTOM_LOCATION_SEARCH = "GOOGLE"
        const val POI_SEARCH = "POI"
        const val GPOI_SEARCH = "LPOI"
        const val SEARCH_ZONE = "zone"
        const val COLLECTION_SEARCH = "storefront"
        const val COUNTRY_SEARCH = "COUNTRY"
    }
}