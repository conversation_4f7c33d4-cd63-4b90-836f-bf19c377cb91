package com.mmt.hotel.common.constants

enum class APICachingStrategy {
    FROM_CACHE_OR_SERVER_SAVE_IN_CACHE, // Checks the Cache for response, if not found, incoming server response is SAVED in Cache
    FROM_CACHE_OR_SERVER,               // Checks the Cache for response, if not found, incoming server response is NOT SAVED in Cache
    FROM_SERVER_SAVE_IN_CACHE,          // DOES NOT check Cache for response, always hits the server and incoming server response is SAVED in Cache
    FROM_SERVER;                         // DOES NOT check Cache for response, incoming server response is NOT SAVED in Cache


    fun shouldUseCache(): <PERSON><PERSON><PERSON> {
        return this == FROM_CACHE_OR_SERVER || this == FROM_CACHE_OR_SERVER_SAVE_IN_CACHE
    }

    fun shouldSaveInCache(): <PERSON><PERSON><PERSON> {
        return this == FROM_CACHE_OR_SERVER_SAVE_IN_CACHE || this == FROM_SERVER_SAVE_IN_CACHE
    }

}