package com.mmt.hotel.common.data

import android.view.View
import androidx.annotation.ColorInt
import androidx.annotation.DimenRes
import com.mmt.hotel.widget.ToolTipDirection


data class ToolTipConfig(val anchorView: View,
                         val toolTipText: String,
                         @ColorInt val backGroundColor: Int,
                         val showDismissIcon: Boolean = false,
                         @DimenRes val maxWidth : Int = -1,
                         val toolTipDirection: ToolTipDirection = ToolTipDirection.BOTTOM)