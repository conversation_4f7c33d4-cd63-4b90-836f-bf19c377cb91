package com.mmt.hotel.common.data

import androidx.annotation.LayoutRes

/*
*   Use this class for inflating list of items in your ViewGroup,
*
* refer : com.mmt.uikit.binding.LinearLayoutBindingAdapter.
**/

//data class TableLayoutData(@LayoutRes val headerLayoutId: Int?,
//                           val headerVariableId: Int?, // id of bindingVariable defined in xml use BR.
//                           val headerData: ArrayList<Any>?,
//                            @LayoutRes val itemLayoutId: Int, //  layout id
//                           val itemVariableId: Int, // id of bindingVariable defined in xml use BR.
//                           var itemData: List<TableLayoutItemData>)

data class TableLayoutData(val headerData: TableLayoutItemData?,
                           var itemData: List<TableLayoutItemData>)

data class TableLayoutItemData(@LayoutRes val layoutId: Int, //  layout id
                               val dataVariableId: Int, // id of bindingVariable defined in xml use BR.
                               var data: Any)