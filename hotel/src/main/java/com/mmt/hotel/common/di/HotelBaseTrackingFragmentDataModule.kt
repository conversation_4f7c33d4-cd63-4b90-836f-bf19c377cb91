package com.mmt.hotel.common.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.base.model.tracking.HotelBaseTrackingData
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import javax.inject.Named

@Module
@InstallIn(FragmentComponent::class)
class HotelBaseTrackingFragmentDataModule {
    @Provides
    @Named(NamedConstants.BASE_HOTEL_TRACKING_DATA)
    fun provideBaseTrackingData(fragment: Fragment): HotelBaseTrackingData {
        return if (fragment is BaseTrackingFragmentDataInjector) {
            fragment.getBaseTrackingData()
        } else {
            throw RuntimeException("implement BaseTrackingDataInjector to inject HotelBaseTrackingData")
        }
    }
}


interface BaseTrackingFragmentDataInjector {
    fun getBaseTrackingData() : HotelBaseTrackingData
}