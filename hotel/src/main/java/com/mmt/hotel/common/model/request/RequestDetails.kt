package com.mmt.hotel.common.model.request

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.prefs.FunnelContext
import kotlinx.parcelize.Parcelize

@Parcelize
data class RequestDetails(
        var visitorId: String? = null,
        var visitNumber: Int = 0,
        var loggedIn: Boolean = false,
        @SerializedName("srCon")
        var sourceCountry: String? = null,
        @SerializedName("srCty")
        var sourceCity: String? = null,
        @SerializedName("srLat")
        var sourceLatitude: Double? = null,
        @SerializedName("srLng")
        var sourceLongitude: Double? = null,
        @SerializedName("semanticSearchDetails")
        val semanticSearchDetails: SemanticSearchDetails? = null,
        var funnelSource: String? = null,
        var idContext: String? = null,
        var payMode: String? = null,
        var couponCount: Int = 1,
        var trafficSource: TrafficSource? = null,
        var siteDomain: String? = FunnelContext.INDIA.countryCode,
        var pageContext: String? = CoreConstants.EMPTY_STRING,
        var channel: String = HotelRequestConstants.CHANNEL,
        var preApprovedValidity: Long? = null,
        var notifCoupon: String? = null,
        val zcp: String?,
        val requisitionID: String? = null,
        val myBizFlowIdentifier: String? = null,
        val oldWorkflowId: String? = null,
        val forwardBookingFlow: Boolean ?= null,
        val promoConsent: Boolean? = null,
        val requestId: String,
        val journeyId: String,
        val sessionId: String,
        @Deprecated("backend is using callBackType instead")
        val isRequestCallBack: Boolean = false,
        val premium: Boolean = false,
        @Deprecated("backend is using callBackType instead")
        val isListAllPropCall: Boolean = false,
        val callBackType: String = "",
        val myraMsgId: String? = null,
        val subPageContext: String? = CoreConstants.EMPTY_STRING,
        var checkDuplicateBooking: Boolean? = null,
        var flowIdentifier: String? = null,
) : Parcelable {

        override fun hashCode(): Int {
                var result = visitorId?.hashCode() ?: 0
                result = 31 * result + visitNumber
                result = 31 * result + loggedIn.hashCode()
                result = 31 * result + (sourceCountry?.hashCode() ?: 0)
                result = 31 * result + (sourceCity?.hashCode() ?: 0)
                result = 31 * result + (sourceLatitude?.hashCode() ?: 0)
                result = 31 * result + (sourceLongitude?.hashCode() ?: 0)
                result = 31 * result + (funnelSource?.hashCode() ?: 0)
                result = 31 * result + (idContext?.hashCode() ?: 0)
                result = 31 * result + (payMode?.hashCode() ?: 0)
                result = 31 * result + couponCount
                result = 31 * result + (trafficSource?.hashCode() ?: 0)
                result = 31 * result + (siteDomain?.hashCode() ?: 0)
                result = 31 * result + (pageContext?.hashCode() ?: 0)
                result = 31 * result + channel.hashCode()
                result = 31 * result + (preApprovedValidity?.hashCode() ?: 0)
                result = 31 * result + (notifCoupon?.hashCode() ?: 0)
                result = 31 * result + (zcp?.hashCode() ?: 0)
                result = 31 * result + (requisitionID?.hashCode() ?: 0)
                result = 31 * result + (myBizFlowIdentifier?.hashCode() ?: 0)
                result = 31 * result + (promoConsent?.hashCode() ?: 0)
                result = 31 * result + (myraMsgId?.hashCode() ?: 0)
                result = 31 * result + (semanticSearchDetails?.hashCode() ?: 0)
                result = 31 * result + (flowIdentifier?.hashCode() ?: 0)
                return result
        }
}