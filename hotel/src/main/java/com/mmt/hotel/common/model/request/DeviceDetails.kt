package com.mmt.hotel.common.model.request

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class DeviceDetails(val appVersion: String?,
                         val deviceId: String?,
                         val deviceType: String?,
                         val bookingDevice: String?,
                         var networkType: String?,
                         var resolution: String?,
                         val deviceName: String?,
                         val simSerialNo: List<String>? = null) : Parcelable
