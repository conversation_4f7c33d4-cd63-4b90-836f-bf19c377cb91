package com.mmt.hotel.common.model.request

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.R
import com.mmt.hotel.common.util.HotelStringsProvider
import kotlinx.parcelize.Parcelize

@Parcelize
data class RoomStayCandidatesV2(
    @SerializedName("adultCount", alternate = ["numberOfAdults"])
    val adultCount: Int,
    var childAges: List<Int>?,
    val rooms: Int?,  //this will not be used in select room or at rate plan level
    var travellingWithPets: Boolean = false,
    val childBuckets: List<ChildBucket>? = null, //received in search-rooms response, use only for Select Room Context as part of response
    var isFlexiWithRoomCountCheckboxChecked: Boolean = false
) : Parcelable

@Parcelize
data class ChildBucket(
    val minAge: Int,
    val maxAge: Int,
    val count: Int,
    val rangeText: String?,
) : Parcelable {
    fun getBucketRangeText(): String {
        return if (rangeText.isNullOrEmpty()) {
            HotelStringsProvider.getFormattedString(R.string.htl_child_range_text, minAge, maxAge)
        } else rangeText
    }
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ChildBucket

        if (minAge != other.minAge) return false
        if (maxAge != other.maxAge) return false
        if (count != other.count) return false

        return true
    }
    override fun hashCode(): Int {
        var result = minAge
        result = 31 * result + maxAge
        result = 31 * result + count
        result = 31 * result + (rangeText?.hashCode() ?: 0)
        return result
    }

}