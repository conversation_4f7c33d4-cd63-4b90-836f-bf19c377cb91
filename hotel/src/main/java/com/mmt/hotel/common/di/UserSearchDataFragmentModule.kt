package com.mmt.hotel.common.di

import android.app.Activity
import androidx.fragment.app.Fragment
import com.mmt.hotel.common.model.UserSearchData
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.hilt.android.components.FragmentComponent
import javax.inject.Named

class UserSearchDataFragmentModule {

    @Module
    @InstallIn(FragmentComponent::class)
    class UserSearchDataActivityModule {
        @Provides
        @Named(NamedConstants.USER_SEARCH_DATA_FOR_FRAGMENT)
        fun provideUserSearchData(fragment : Fragment) : UserSearchData {
            return if(fragment is UserSearchDataFragmentInjector) {
                fragment.getUserSearchData()
            } else {
                throw RuntimeException("implement UserSearchDataInjector to inject UserSearchData")
            }
        }
    }
}

interface UserSearchDataFragmentInjector {
    fun getUserSearchData() : UserSearchData
}