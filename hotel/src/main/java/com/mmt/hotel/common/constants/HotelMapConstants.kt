package com.mmt.hotel.common.constants

import androidx.annotation.IntDef
import com.mmt.hotel.common.constants.HotelMapConstants.LocationPageStates.Companion.FETCHING_NEAR_BY
import com.mmt.hotel.common.constants.HotelMapConstants.LocationPageStates.Companion.FETCHING_PLACES
import com.mmt.hotel.common.constants.HotelMapConstants.LocationPageStates.Companion.ONLY_MAP
import com.mmt.hotel.common.constants.HotelMapConstants.LocationPageStates.Companion.PLACES_HIDDEN
import com.mmt.hotel.common.constants.HotelMapConstants.LocationPageStates.Companion.SHOWING_NEAR_BY
import com.mmt.hotel.common.constants.HotelMapConstants.LocationPageStates.Companion.SHOWING_PLACES
import com.mmt.hotel.common.constants.HotelMapConstants.LocationPageStates.Companion.SHOW_POI
import java.lang.annotation.Retention
import java.lang.annotation.RetentionPolicy

object HotelMapConstants {
    enum class HOTEL_MARKER_TYPE {
        TYPE1,   //without name
        TYPE2    //with name
    }

    @Retention(RetentionPolicy.SOURCE)
    @IntDef(FETCHING_PLACES, SHOWING_PLACES, PLACES_HIDDEN, SHOWING_NEAR_BY, FETCHING_NEAR_BY, ONLY_MAP, SHOW_POI)
    annotation class LocationPageStates {
        companion object {
            const val FETCHING_PLACES = 1
            const val SHOWING_PLACES = 2
            const val PLACES_HIDDEN = 3
            const val SHOWING_NEAR_BY = 4
            const val FETCHING_NEAR_BY = 5
            const val ONLY_MAP = 6
            const val SHOW_POI = 7
        }
    }

    const val API_KEY = "api-key"
    const val TRACKING_POLYGON_API_KEY = "HOTELS-ANDROID-81520512191144181594"
    const val LOC_ID = "locid"
    const val ZONE_ID = "zoneid"
}