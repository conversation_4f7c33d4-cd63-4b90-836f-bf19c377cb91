package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.AvailRequestType.Companion.EXACT
import com.mmt.hotel.common.constants.AvailRequestType.Companion.OCCUPANCY_LESS
import com.mmt.hotel.common.constants.AvailRequestType.Companion.RECOMMENDED
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.GETAWAYS
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.HOME_STAY
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.HOTELS
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.STAYCATION

@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [OCCUPANCY_LESS, RECOMMENDED, EXACT])
annotation class AvailRequestType {
    companion object {
        const val OCCUPANCY_LESS = "O"
        const val RECOMMENDED = "R"
        const val EXACT = "E"
    }
}