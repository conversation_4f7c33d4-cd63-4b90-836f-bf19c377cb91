package com.mmt.hotel.common.extensions

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.annotation.LayoutRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.mmt.hotel.R
import com.gommt.logger.LogUtils

/**
 * Created by <PERSON><PERSON><PERSON> on 24/07/20.
 */

fun ViewGroup.addView(@LayoutRes layout: Int, inflater: LayoutInflater = LayoutInflater.from(context)): View {
    val view = inflater.inflate(layout, this, false)
    addView(view)
    return view
}

fun ViewGroup.addViewWithDataBinding(@LayoutRes layout: Int, inflater: LayoutInflater = LayoutInflater.from(context)): ViewDataBinding {
    return with(inflateWithDataBinding<ViewDataBinding>(layout, inflater)) {
        addView(root)
        this
    }
}

fun <T : ViewDataBinding> ViewGroup.inflateWithDataBinding(@LayoutRes layout: Int, inflater: LayoutInflater = LayoutInflater.from(context),attachToParent: Boolean = false): T {
    return DataBindingUtil.inflate(inflater, layout, this, attachToParent)
}

inline fun <T : List<View>> T.afterMeasured(crossinline func: T.() -> Unit) {
    this.forEach {
        LogUtils.debug("afterMeasured", "afterMeasured()..:$it")
            val globalLayoutListener= object :
                ViewTreeObserver.OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    LogUtils.debug("afterMeasured", "onGlobalLayout()..:${it}")
                    if (it.measuredWidth > 0 && it.measuredHeight > 0) {
                        LogUtils.debug("afterMeasured", "onGlobalLayout()..:${it}")
                        <EMAIL> {
                            LogUtils.debug("afterMeasured", "onGlobalLayout()..:${it}")
                            it.viewTreeObserver.removeOnGlobalLayoutListener(it.getTag(R.id.globalLayoutListener) as ViewTreeObserver.OnGlobalLayoutListener)
                        }
                        LogUtils.debug("afterMeasured", "before func()..:$it")
                        func()
                    }
                }
            }
        it.setTag(R.id.globalLayoutListener,globalLayoutListener)
        it.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)
        }
    }


