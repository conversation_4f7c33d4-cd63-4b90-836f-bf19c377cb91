package com.mmt.hotel.common.data

import android.os.Parcelable
import java.lang.StringBuilder

/**
 * This class should be implemented by  response items,
 * Basic purpose of this class is to include only those fields, which we wants to get compared for searching text
 */
abstract class SearchableResponseItem: Parcelable {
    protected var builder :StringBuilder? = null

    companion object{
        const val SEARCHABLE_CONTENT_SEPARATOR = "\n"
    }

    fun getSearchableContent(): StringBuilder {
        return builder ?:run{
            builder = StringBuilder()
            populateBuilder(builder!!)
            builder!!
        }
    }

    abstract fun populateBuilder(builder: StringBuilder)

}