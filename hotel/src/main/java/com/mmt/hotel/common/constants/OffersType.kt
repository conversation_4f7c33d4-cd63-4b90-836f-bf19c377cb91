package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.OffersType.Companion.OFFER_CASHBACK_TO_CARD
import com.mmt.hotel.common.constants.OffersType.Companion.OFFER_EMI
import com.mmt.hotel.common.constants.OffersType.Companion.OFFER_EXTEND_YOUR_STAY
import com.mmt.hotel.common.constants.OffersType.Companion.OFFER_FLEXIBLE_CHECKIN_CHECKOUT
import com.mmt.hotel.common.constants.OffersType.Companion.OFFER_NON_APPLIED_COUPON

@Retention(AnnotationRetention.SOURCE)
@StringDef(
        value = [OFFER_EXTEND_YOUR_STAY,
            OFFER_FLEXIBLE_CHECKIN_CHECKOUT,
            OFFER_CASHBACK_TO_CARD,
            OFFER_EMI,
            OFFER_NON_APPLIED_COUPON]
)
annotation class OffersType {
    companion object {
        const val OFFER_EXTEND_YOUR_STAY = "EXTEND_YOUR_STAY"
        const val OFFER_FLEXIBLE_CHECKIN_CHECKOUT = "FLEXIBLE_CHECKIN_CHECKOUT"
        const val OFFER_CASHBACK_TO_CARD = "CASHBACK_TO_CARD"
        const val OFFER_EMI = "EMI"
        const val OFFER_NON_APPLIED_COUPON = "NON_APPLIED_COUPON"
    }
}