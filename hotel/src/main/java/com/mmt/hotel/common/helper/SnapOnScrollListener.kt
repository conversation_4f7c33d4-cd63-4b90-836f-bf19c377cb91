package com.mmt.hotel.common.helper

import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SnapHelper

/**
 * Created by sunil.jain on 2019-10-07.
 */
class SnapOnScrollListener(
    private val snapHelper: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    var behavior: Behavior = Behavior.NOTIFY_ON_SCROLL,
    var onSnapPositionChangeListener: OnSnapPositionChangeListener? = null
) : RecyclerView.OnScrollListener() {

    enum class Behavior {
        NOTIFY_ON_SCROLL,
        NOTIFY_ON_SCROLL_STATE_IDLE,
        NOTIFY_ONLY_USER_SCROLL // Only notifies when user manually scrolls the recyclerview, avoids scrolls done programmatically
    }

    private var snapPosition = RecyclerView.NO_POSITION
    private var scrolledByUser = false

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        if (behavior == Behavior.NOTIFY_ON_SCROLL) {
            checkSnapPositionChange(recyclerView, true)
        }
    }

    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        if (behavior == Behavior.NOTIFY_ONLY_USER_SCROLL && newState == RecyclerView.SCROLL_STATE_DRAGGING){
            scrolledByUser = true
        } else if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            val shouldNotify = when (behavior) {
                Behavior.NOTIFY_ONLY_USER_SCROLL -> { scrolledByUser }
                Behavior.NOTIFY_ON_SCROLL_STATE_IDLE -> { true }
                else -> false
            }
            checkSnapPositionChange(recyclerView, shouldNotify)
            scrolledByUser = false
        }
    }

    private fun checkSnapPositionChange(recyclerView: RecyclerView, shouldNotifyIfChanged: Boolean) {
        val snapPosition = snapHelper.getSnapPosition(recyclerView)
        val snapPositionChanged = this.snapPosition != snapPosition
        if (snapPositionChanged) {
            if (shouldNotifyIfChanged) {
                onSnapPositionChangeListener?.onSnapPositionChange(this.snapPosition, snapPosition)
            }
            this.snapPosition = snapPosition
        }
    }
}

interface OnSnapPositionChangeListener {
    fun onSnapPositionChange(oldPosition: Int, newPosition: Int)
}

fun SnapHelper.getSnapPosition(recyclerView: RecyclerView): Int {
    val layoutManager = recyclerView.layoutManager ?: return RecyclerView.NO_POSITION
    val snapView = findSnapView(layoutManager) ?: return RecyclerView.NO_POSITION
    return layoutManager.getPosition(snapView)
}

fun attachSnapHelperWithListener(recyclerView: RecyclerView, snapHelper: SnapHelper,
                                 behavior: SnapOnScrollListener.Behavior = SnapOnScrollListener.Behavior.NOTIFY_ON_SCROLL,
                                 onSnapPositionChangeListener: OnSnapPositionChangeListener
) {
    snapHelper.attachToRecyclerView(recyclerView)
    val snapOnScrollListener = SnapOnScrollListener(snapHelper, behavior, onSnapPositionChangeListener)
    recyclerView.addOnScrollListener(snapOnScrollListener)
}


//fun deAttachSnapHelperWithListener(recyclerView: RecyclerView, snapHelper: SnapHelper,
//                                 behavior: SnapOnScrollListener.Behavior = SnapOnScrollListener.Behavior.NOTIFY_ON_SCROLL,
//                                 onSnapPositionChangeListener: OnSnapPositionChangeListener
//) {
//    snapHelper.attachToRecyclerView(recyclerView)
//    val snapOnScrollListener = SnapOnScrollListener(snapHelper, behavior, onSnapPositionChangeListener)
//    recyclerView.removeOnScrollListener(snapOnScrollListener)
//}
