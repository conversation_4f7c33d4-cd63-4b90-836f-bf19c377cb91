package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.PaymentModeTypes.Companion.PAH1
import com.mmt.hotel.common.constants.PaymentModeTypes.Companion.PAH2
import com.mmt.hotel.common.constants.PaymentModeTypes.Companion.PAH_WITHOUT_CC
import com.mmt.hotel.common.constants.PaymentModeTypes.Companion.PAH_WITH_CC
import com.mmt.hotel.common.constants.PaymentModeTypes.Companion.PAS

@Retention(AnnotationRetention.SOURCE)
@StringDef(
    value = [
        PAH1, PAH2, PAS, PAH_WITH_CC, PAH_WITHOUT_CC
    ]
)
annotation class PaymentModeTypes {
    companion object {
        const val PAH1 = "PAH1"
        const val PAH2 = "PAH2"
        const val PAS = "PAS"
        const val PAH_WITH_CC = "PAH_WITH_CC"
        const val PAH_WITHOUT_CC = "PAH_WITHOUT_CC"
    }
}