package com.mmt.hotel.common.helper

import android.view.View
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.viewModelScope
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.view_360.event.View360Event
import com.mmt.hotel.view_360.tracking.View360OmnitureTrackingHelper.Companion.VIEW_360_FEEDBACK_DISMISS
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch


class UserFeedbackVM(
    val hotelId: String,
    private val isFirstTimeFeedbackLoad: Boolean,
    private val eventFlow: MutableSharedFlow<HotelEvent>
) : HotelViewModel() {

    companion object {
        const val USER_FEEDBACK_DELAY_TIME = 6000L
    }

    val showFeedbackWidget = ObservableBoolean(false)
    private var feedbackWidgetClicked = false

    init {
        viewModelScope.launch {
            if(!checkHotelIdAlreadyExist(hotelId)){
                if(isFirstTimeFeedbackLoad) delay(USER_FEEDBACK_DELAY_TIME)
                eventFlow.emit(HotelEvent(View360Event.FEEDBACK_SHOWN))
                showFeedbackWidget.set(true)
            }
        }
    }

    fun onLike(view: View) {
        if (feedbackWidgetClicked) return
        feedbackWidgetClicked = true
        view.isSelected = true
        addHotelId(hotelId)
        onFeedbackIconClick(true)
    }

    fun onDisLike(view: View) {
        if (feedbackWidgetClicked) return
        feedbackWidgetClicked = true
        view.isSelected = true
        addHotelId(hotelId)
        onFeedbackIconClick(false)
    }

    fun removeWidget() {
        viewModelScope.launch {
            addHotelId(hotelId)
            showFeedbackWidget.set(false)
            eventFlow.emit(HotelEvent(View360Event.FEEDBACK_TRACKING, VIEW_360_FEEDBACK_DISMISS))
        }
    }

    private fun onFeedbackIconClick(isLike: Boolean) {
        viewModelScope.launch {
            delay(200)
            eventFlow.emit(HotelEvent(View360Event.FEEDBACK_ICON_CLICK, isLike))
            delay(1000)
            showFeedbackWidget.set(false)
        }
    }

    private fun addHotelId(hotelId: String) {
        val hotelIds = HotelSharedPrefUtil.instance.getUserFeedbackHotelId()
        val hotelIdArray = hotelIds.split(",").toMutableList()
        if (hotelIdArray.size >= 10) {
            hotelIdArray.removeAt(0)
        }
        hotelIdArray.add(hotelId)
        HotelSharedPrefUtil.instance.setUserFeedbackHotelId(hotelIdArray.joinToString(","))
    }

    private fun checkHotelIdAlreadyExist(hotelId: String): Boolean {
        val hotelIds = HotelSharedPrefUtil.instance.getUserFeedbackHotelId()
        val hotelIdArray = hotelIds.split(",").toMutableList()
        return hotelIdArray.contains(hotelId)
    }

}