package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.GETAWAYS
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.HOME_STAY
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.HOTELS
import com.mmt.hotel.common.constants.HotelsFunnelName.Companion.STAYCATION

@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [
    HOTELS,
    HOME_STAY,
    STAYCATION,
    GETAWAYS])
annotation class HotelsFunnelName {
    companion object {
        const val HOTELS = "hotels"
        const val HOME_STAY = "homestay"
        const val STAYCATION = "staycation"
        const val GETAWAYS = "getaway"
    }
}