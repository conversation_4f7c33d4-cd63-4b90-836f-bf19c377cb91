package com.mmt.hotel.common.helper

import com.mmt.auth.login.model.Employee
import com.mmt.auth.login.model.old.corporate.ApprovingManager
import com.mmt.core.util.AESEncrytionHelper
import com.mmt.hotel.common.extensions.convertTo
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.uikit.util.isNotNullAndEmpty

object HotelEncryptionHelper {
    fun encryptSearchRequest(searchRequest: SearchRequest): SearchRequest {
        // Currently only primary traveller has information that needs to be encrypted
        return if (searchRequest.isEncrypted) {
            searchRequest
        } else if (searchRequest.primaryTraveller.isNullOrEmpty()) {
            searchRequest.apply { isEncrypted = false }
        } else {
            val newRequest: SearchRequest = searchRequest.convertTo()
            newRequest.primaryTraveller = newRequest.primaryTraveller?.map {
                encryptEmployee(it)
            }
            newRequest.apply { isEncrypted = true }
        }
    }

    fun decryptSearchRequest(searchRequest: SearchRequest): SearchRequest {
        // Currently only primary traveller has information that needs to be decrypted
        return if (!searchRequest.isEncrypted) {
            searchRequest
        } else if (searchRequest.primaryTraveller.isNullOrEmpty()) {
            searchRequest.apply { isEncrypted = false }
        } else {
            val newRequest: SearchRequest = searchRequest.convertTo()
            newRequest.primaryTraveller = newRequest.primaryTraveller?.map {
                decryptEmployee(it)
            }
            newRequest.apply { isEncrypted = false }
        }
    }

    private fun encryptEmployee(employee: Employee): Employee {
        if (employee.name.isNotNullAndEmpty())
            employee.name = AESEncrytionHelper.encryptText(employee.name)
        if (employee.mmtUserId.isNotNullAndEmpty())
            employee.mmtUserId = AESEncrytionHelper.encryptText(employee.mmtUserId)
        if (employee.employeeStatus.isNotNullAndEmpty())
            employee.employeeStatus = AESEncrytionHelper.encryptText(employee.employeeStatus)
        if (employee.businessEmailId.isNotNullAndEmpty())
            employee.businessEmailId = AESEncrytionHelper.encryptText(employee.businessEmailId)
        if (employee.phoneNumber.isNotNullAndEmpty())
            employee.phoneNumber = AESEncrytionHelper.encryptText(employee.phoneNumber)
        if (employee.countryCode.isNotNullAndEmpty())
            employee.countryCode = AESEncrytionHelper.encryptText(employee.countryCode)
        if (employee.employeeId.isNotNullAndEmpty())
            employee.employeeId = AESEncrytionHelper.encryptText(employee.employeeId)
        if (employee.personalEmailId.isNotNullAndEmpty())
            employee.personalEmailId = AESEncrytionHelper.encryptText(employee.personalEmailId)
        if (employee.updatedBy.isNotNullAndEmpty())
            employee.updatedBy = AESEncrytionHelper.encryptText(employee.updatedBy)
        if (employee.type.isNotNullAndEmpty())
            employee.type = AESEncrytionHelper.encryptText(employee.type)
        if (employee.designation.isNotNullAndEmpty())
            employee.designation = AESEncrytionHelper.encryptText(employee.designation)
        if (employee.invitationCode.isNotNullAndEmpty())
            employee.invitationCode = AESEncrytionHelper.encryptText(employee.invitationCode)
        if (employee.gender.isNotNullAndEmpty())
            employee.gender = AESEncrytionHelper.encryptText(employee.gender)
        if (employee.title.isNotNullAndEmpty())
            employee.title = AESEncrytionHelper.encryptText(employee.title)
        if (employee.roles.isNotNullAndEmpty())
            employee.roles = employee.roles.map { AESEncrytionHelper.encryptText(it) }
        if (employee.approvingManager1 != null)
            employee.approvingManager1 = encryptApprovingManager(employee.approvingManager1)
        if (employee.approvingManager2 != null)
            employee.approvingManager2 = encryptApprovingManager(employee.approvingManager2)
        return employee
    }

    private fun encryptApprovingManager(manager: ApprovingManager): ApprovingManager {
        if (manager.name.isNotNullAndEmpty())
            manager.name = AESEncrytionHelper.encryptText(manager.name)
        if (manager.businessEmailId.isNotNullAndEmpty())
            manager.businessEmailId = AESEncrytionHelper.encryptText(manager.businessEmailId)
        if (manager.phoneNumber.isNotNullAndEmpty())
            manager.phoneNumber = AESEncrytionHelper.encryptText(manager.phoneNumber)
        if (manager.designation.isNotNullAndEmpty())
            manager.designation = AESEncrytionHelper.encryptText(manager.designation)
        if (manager.employeeId.isNotNullAndEmpty())
            manager.employeeId = AESEncrytionHelper.encryptText(manager.employeeId)
        return manager
    }

    private fun decryptApprovingManager(manager: ApprovingManager): ApprovingManager {
        if (manager.name.isNotNullAndEmpty())
            manager.name = AESEncrytionHelper.decryptText(manager.name)
        if (manager.businessEmailId.isNotNullAndEmpty())
            manager.businessEmailId = AESEncrytionHelper.decryptText(manager.businessEmailId)
        if (manager.phoneNumber.isNotNullAndEmpty())
            manager.phoneNumber = AESEncrytionHelper.decryptText(manager.phoneNumber)
        if (manager.designation.isNotNullAndEmpty())
            manager.designation = AESEncrytionHelper.decryptText(manager.designation)
        if (manager.employeeId.isNotNullAndEmpty())
            manager.employeeId = AESEncrytionHelper.decryptText(manager.employeeId)
        return manager
    }

    private fun decryptEmployee(employee: Employee): Employee {
        if (employee.name.isNotNullAndEmpty())
            employee.name = AESEncrytionHelper.decryptText(employee.name)
        if (employee.mmtUserId.isNotNullAndEmpty())
            employee.mmtUserId = AESEncrytionHelper.decryptText(employee.mmtUserId)
        if (employee.employeeStatus.isNotNullAndEmpty())
            employee.employeeStatus = AESEncrytionHelper.decryptText(employee.employeeStatus)
        if (employee.businessEmailId.isNotNullAndEmpty())
            employee.businessEmailId = AESEncrytionHelper.decryptText(employee.businessEmailId)
        if (employee.phoneNumber.isNotNullAndEmpty())
            employee.phoneNumber = AESEncrytionHelper.decryptText(employee.phoneNumber)
        if (employee.countryCode.isNotNullAndEmpty())
            employee.countryCode = AESEncrytionHelper.decryptText(employee.countryCode)
        if (employee.employeeId.isNotNullAndEmpty())
            employee.employeeId = AESEncrytionHelper.decryptText(employee.employeeId)
        if (employee.personalEmailId.isNotNullAndEmpty())
            employee.personalEmailId = AESEncrytionHelper.decryptText(employee.personalEmailId)
        if (employee.updatedBy.isNotNullAndEmpty())
            employee.updatedBy = AESEncrytionHelper.decryptText(employee.updatedBy)
        if (employee.type.isNotNullAndEmpty())
            employee.type = AESEncrytionHelper.decryptText(employee.type)
        if (employee.designation.isNotNullAndEmpty())
            employee.designation = AESEncrytionHelper.decryptText(employee.designation)
        if (employee.invitationCode.isNotNullAndEmpty())
            employee.invitationCode = AESEncrytionHelper.decryptText(employee.invitationCode)
        if (employee.gender.isNotNullAndEmpty())
            employee.gender = AESEncrytionHelper.decryptText(employee.gender)
        if (employee.title.isNotNullAndEmpty())
            employee.title = AESEncrytionHelper.decryptText(employee.title)
        if (employee.roles.isNotNullAndEmpty())
            employee.roles = employee.roles.map { AESEncrytionHelper.decryptText(it) }
        if (employee.approvingManager1 != null)
            employee.approvingManager1 = decryptApprovingManager(employee.approvingManager1)
        if (employee.approvingManager2 != null)
            employee.approvingManager2 = decryptApprovingManager(employee.approvingManager2)
        return employee
    }


}