package com.mmt.hotel.common.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.autoSuggest.model.response.CenterLocation
import com.mmt.hotel.autoSuggest.model.response.HotelierTimezoneInfo
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.constants.HotelConstants.SEARCH_DATE_FORMAT
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.request.SemanticSearchDetails
import com.mmt.hotel.common.util.HotelDateUtil
import com.mmt.hotel.dayuse.util.getCheckInTimeInMillsFromDateForDayUse
import com.mmt.hotel.dayuse.util.getUserCheckInTimeForDayUse
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.*

@Parcelize
data class UserSearchData(
    var id: String?, // can be of city, hotel,region, poi, gpoi,hotel etc updated from suggestResult
    var funnelSrc: Int,
    var hotelId: String = CoreConstants.EMPTY_STRING,
    var hotelName: String = CoreConstants.EMPTY_STRING,
    @SerializedName(
        "locationName",
        alternate = ["cityName"]
    ) // cityName is deprecated and defined only for backward compatibility
    var locationName: String = CoreConstants.EMPTY_STRING,
    var country: String = CoreConstants.EMPTY_STRING,
    var countryCode: String = CoreConstants.EMPTY_STRING,
    var locationId: String = CoreConstants.EMPTY_STRING,
    var locationType: String = CoreConstants.EMPTY_STRING,
    var cityCode: String? = CoreConstants.EMPTY_STRING,
    var originalLocusType: String? = CoreConstants.EMPTY_STRING,
    var displayName: String? = CoreConstants.EMPTY_STRING,
    var subtext: String? = CoreConstants.EMPTY_STRING,
    var searchType: String = CoreConstants.EMPTY_STRING,
    var position: Int = 0,
    var tripType: String? = null,
    var travellerType: Int = 0,
    var occupancyData: OccupancyData = OccupancyData(1, 2),
    var checkInDate: String = CoreConstants.EMPTY_STRING,
    var checkInTime: String? = CoreConstants.EMPTY_STRING,
    var checkOutDate: String = CoreConstants.EMPTY_STRING,
    var checkOutTime: String? = CoreConstants.EMPTY_STRING,
    var hType: String? = CoreConstants.EMPTY_STRING,
    var checkInTimeInMills: Long? = null, // to be use in  [com.mmt.hotel.common.constants.HotelFunnel.DAYUSE]
    var zcpDataString: String? = null,
    val requisitionID: String? = null,
    val myBizFlowIdentifier: String? = null,
    val workflowId: String? = null,
    val forwardBookingFlow: Boolean? = false,
    var centerLocation: CenterLocation? = null,
    var hashForJourney: Int? = null,
    var journeyId: String? = null,
    var locusLocationName: String? = null, //this will contain the name corresponding to locationId,locationType
    var treelId: String? = null,
    var searchIntent: String? = null,
    var userInputMandatory: Boolean? = false,
    var semanticSearchQueryText: String? = null,
    var semanticSearchData: String? = null,
    var selectedCurrency: String? = HotelCurrencyUtil.getSelectedCurrencyCode(),
    var isRecentSearch: Boolean = false,
    var timezoneInfo: HotelierTimezoneInfo? = null,
    var maskedPropertyName: Boolean? = null
) : Parcelable {

    override fun hashCode(): Int {
        // include hash code for all fields except isRecentSearch
        return Objects.hash(
            id,
            funnelSrc,
            hotelId,
            hotelName,
            locationName,
            country,
            countryCode,
            locationId,
            locationType,
            cityCode,
            originalLocusType,
            displayName,
            subtext,
            searchType,
            position,
            tripType,
            travellerType,
            occupancyData,
            checkInDate,
            checkInTime,
            checkOutDate,
            checkOutTime,
            hType,
            checkInTimeInMills,
            zcpDataString,
            requisitionID,
            myBizFlowIdentifier,
            workflowId,
            forwardBookingFlow,
            centerLocation,
            hashForJourney,
            journeyId,
            locusLocationName,
            treelId,
            searchIntent,
            userInputMandatory,
            semanticSearchQueryText,
            semanticSearchData,
            selectedCurrency,
            timezoneInfo,
            maskedPropertyName
        )

    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is UserSearchData) return false

        return id == other.id &&
                funnelSrc == other.funnelSrc &&
                hotelId == other.hotelId &&
                hotelName == other.hotelName &&
                locationName == other.locationName &&
                country == other.country &&
                countryCode == other.countryCode &&
                locationId == other.locationId &&
                locationType == other.locationType &&
                cityCode == other.cityCode &&
                originalLocusType == other.originalLocusType &&
                displayName == other.displayName &&
                subtext == other.subtext &&
                searchType == other.searchType &&
                position == other.position &&
                tripType == other.tripType &&
                travellerType == other.travellerType &&
                occupancyData == other.occupancyData &&
                checkInDate == other.checkInDate &&
                checkInTime == other.checkInTime &&
                checkOutDate == other.checkOutDate &&
                checkOutTime == other.checkOutTime &&
                hType == other.hType &&
                checkInTimeInMills == other.checkInTimeInMills &&
                zcpDataString == other.zcpDataString &&
                requisitionID == other.requisitionID &&
                myBizFlowIdentifier == other.myBizFlowIdentifier &&
                workflowId == other.workflowId &&
                forwardBookingFlow == other.forwardBookingFlow &&
                centerLocation == other.centerLocation &&
                hashForJourney == other.hashForJourney &&
                journeyId == other.journeyId &&
                locusLocationName == other.locusLocationName &&
                treelId == other.treelId &&
                searchIntent == other.searchIntent &&
                userInputMandatory == other.userInputMandatory &&
                semanticSearchQueryText == other.semanticSearchQueryText &&
                semanticSearchData == other.semanticSearchData &&
                selectedCurrency == other.selectedCurrency &&
                timezoneInfo == other.timezoneInfo &&
                maskedPropertyName == other.maskedPropertyName
    }

    /**
     * method to update [checkInDate], [checkOutDate], and [checkInTimeInMills] date
     * for DayUse funnel
     */
    fun updateDateForDayUse(date: Date) {
        val dateFormatter = SimpleDateFormat(SEARCH_DATE_FORMAT, Locale.ENGLISH)
        val displayDate = dateFormatter.format(date)
        checkInDate = displayDate
        checkOutDate = displayDate
        updateCheckInTimeInMillisWithDate(date)
    }

    /**
     * method to update [checkInTimeInMills] with checkInDate
     */
    private fun updateCheckInTimeInMillisWithDate(checkInDate: Date) {
        val hourOfDay = checkInTimeInMills?.let {
            HotelDateUtil.getHoursFromTimeStamp(it)
        } ?: return
        checkInTimeInMills = getUserCheckInTimeForDayUse(
            getCheckInTimeInMillsFromDateForDayUse(checkInDate, hourOfDay)
        )
    }

    fun isAltAccoFunnel(): Boolean {
        return funnelSrc == HotelFunnel.HOMESTAY.funnelValue
    }

    fun getCacheKey():String {
        return checkInDate +"_"+checkOutDate + "_"+ occupancyData.getCacheKey()
    }

}