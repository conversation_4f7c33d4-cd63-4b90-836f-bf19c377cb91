package com.mmt.hotel.common.extensions

import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.gommt.logger.LogUtils
import com.mmt.hotel.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/*
@ExperimentalCoroutinesApi
fun <T> Observable<T>.asFlow() = callbackFlow<T> {
    val dispose =
        doOnComplete { channel.close() }
            .subscribe({
                try {
                    sendBlocking(it)
                } catch (e: Exception) {
                    e.printStackTrace() // Stream Closed
                }
            }, {
                cancel(CancellationException(it.localizedMessage, it))
            })
    awaitClose { dispose?.dispose() }
}
*/
const val TAG = "CoroutineExt"

val View.viewScope: CoroutineScope
    get() {
        val storedScope = getTag(R.string.view_coroutine_scope + id) as? CoroutineScope
        if (storedScope != null) return storedScope

        val newScope = ViewCoroutineScope()
        setTag(R.string.view_coroutine_scope + id, newScope)
        if (!ViewCompat.isAttachedToWindow(this)) {
            LogUtils.warning(
                TAG,
                "Creating a CoroutineScope before ${javaClass.name} attaches to a window. " +
                        "Coroutine jobs won't be canceled if the view has never been attached to a window."
            )
        }
        addOnAttachStateChangeListener(newScope)

        return newScope
    }

private class ViewCoroutineScope : CoroutineScope, View.OnAttachStateChangeListener {
    override val coroutineContext = Job() + Dispatchers.Main

    override fun onViewAttachedToWindow(view: View) = Unit

    override fun onViewDetachedFromWindow(view: View) {
        view.removeOnAttachStateChangeListener(this)
        coroutineContext.cancel()
        view.setTag(R.string.view_coroutine_scope + view.id, null)
    }
}

suspend fun ioThread(action: suspend () -> Unit): Unit =
    withContext(Dispatchers.IO) {
        return@withContext action()
    }

suspend fun computationThread(action: suspend () -> Unit): Unit =
    withContext(Dispatchers.Default) {
        return@withContext action()
    }

suspend fun mainThread(action: suspend () -> Unit): Unit =
    withContext(Dispatchers.Main) {
        return@withContext action()
    }


fun ViewPager2.setCurrentItemWithDelay(
    activity: FragmentActivity,
    item: Int,
    delayMillis: Long
) {
    activity.lifecycleScope.launch {
        delay(delayMillis)
        if (<EMAIL> && !activity.isFinishing && !activity.isDestroyed) {
            setCurrentItem(item, true)
        }
    }
}
