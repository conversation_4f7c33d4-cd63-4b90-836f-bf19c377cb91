package com.mmt.hotel.common.helper

import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.extensions.safeReturnString
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.old.landing.model.response.SearchContext
import com.mmt.hotel.old.util.HotelPriceUtil

/**
 * Created by <PERSON><PERSON><PERSON> on 18/07/20.
 */

interface PriceTypeCreator {
    fun getPriceType(searchContext: SearchContext?, funnel: Int, rooms: Int?): String
}

class PriceTypeCreatorImp(private val experimentProvider: ExperimentProvider):
        PriceTypeCreator{

    override fun getPriceType(searchContext: SearchContext?, funnel: Int, rooms: Int?): String {
        return searchContext.safeReturnString {
            HotelPriceUtil.getListingHotelNodeText(
                    experimentProvider.providePriceExperiment(funnel),
                    HotelUtil.getNoOfNights(searchContext!!.checkIn, searchContext.checkOut, HotelConstants.SEARCH_DATE_FORMAT)
                    , false,rooms,funnel)
        }
    }
}