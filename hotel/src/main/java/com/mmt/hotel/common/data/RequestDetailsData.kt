package com.mmt.hotel.common.data

import com.mmt.hotel.common.model.request.SemanticSearchDetails

import android.os.Parcelable
import com.gommt.pan.utility.EMPTY_STRING
import kotlinx.parcelize.Parcelize

@Parcelize
data class RequestDetailsData(
    val funnel: Int,
    val pageContext: String,
    val zcpDataString: String? = null,
    val requisitionID: String? = null,
    val workFlowId: String? = null,
    val forwardFlow: Boolean ?= null,
    val myBizFlowIdentifier: String? = null,
    val payMode: String? = null,
    val preApprovedValidity: Long? = null,
    val journeyId:String?,
    val isRequestCallBack: Boolean = false,
    val isABO: Boolean = false,
    val semanticSearchDetails: SemanticSearchDetails? = null,
    val callBackType: String = "",
    val myraMsgId: String? = null,
    val subPageContext: String = EMPTY_STRING,
    val checkDuplicateBooking: Boolean? = null,
    val flowIdentifier: String? = null,
): Parcelable