package com.mmt.hotel.common.extensions

import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.recyclerview.widget.RecyclerView

fun RecyclerView.runOnceAfterRendering(delay: Long = 0L, action: () -> Unit) {
    val layoutChangeListener = object : View.OnLayoutChangeListener {
        override fun onLayoutChange(
            v: View?,
            left: Int,
            top: Int,
            right: Int,
            bottom: Int,
            oldLeft: Int,
            oldTop: Int,
            oldRight: Int,
            oldBottom: Int
        ) {
            Handler(Looper.getMainLooper()).postDelayed({
                <EMAIL>(this)
                action()
            }, delay)
        }

    }
    this.addOnLayoutChangeListener(layoutChangeListener)
}