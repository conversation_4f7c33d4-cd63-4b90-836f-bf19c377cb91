package com.mmt.hotel.common.helper

import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.response.TagSelectionForListingV2
import com.mmt.hotel.common.model.tracking.LocusPDTPoiData
import com.mmt.hotel.common.model.tracking.LocusTrackingData
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.SEARCH_CTY_TAG
import com.mmt.hotel.common.constants.HotelConstants.SEARCH_HTL_TAG
import com.mmt.hotel.common.constants.HotelConstants.SEARCH_REGIONS_TAG
import com.mmt.hotel.listingV2.dataModel.HotelFilterModelV2

/**
 * Created by sunil.jain on 06/05/21.
 */
fun getLocusTrackingData(userSearchData: UserSearchData, filters: HotelFilterModelV2): LocusTrackingData {
    val countryCode = userSearchData.countryCode
    val locationId = userSearchData.locationId
    val locationType = userSearchData.locationType

    val regionId = if (locationType.equals(SEARCH_REGIONS_TAG, ignoreCase = true)) locationId else null
    val cityId = if (locationType.equals(SEARCH_CTY_TAG, ignoreCase = true)) locationId else null
    val hotelId = filters.hotels?.firstOrNull()?.hotelId
    val searchedText = userSearchData.displayName
    val areasSelected = filters.locationFiltersV2.appliedAreasTags?.map { it.tagAreaId.orEmpty() }
    val poisSelected = filters.locationFiltersV2.appliedPoiTags?.let { getAppliedPoi(it) }
    val matchMakerApplied = checkIsMatchMakerApplied(userSearchData.id, filters)

    val locusType = getLocusType(userSearchData, filters)
    return LocusTrackingData(locusType, countryCode, cityId, regionId, searchedText, hotelId, areasSelected, matchMakerApplied, poisSelected)
}

private fun checkIsMatchMakerApplied(entryId: String?, filters: HotelFilterModelV2): Boolean {
    with(filters.locationFiltersV2) {
        return when {
            entryId == null -> false
            appliedAreasTags?.any { it.tagAreaId == entryId } == true -> true
            else -> appliedPoiTags?.any { it.placeId == entryId } == true
        }
    }

}

private fun getLocusType(userSearchData: UserSearchData,filters: HotelFilterModelV2): String {

    var locusType = CoreConstants.EMPTY_STRING
    if (SEARCH_REGIONS_TAG.equals(userSearchData.locationType, true)) {
        locusType = SEARCH_REGIONS_TAG
    } else if (SEARCH_CTY_TAG.equals(userSearchData.locationType, true)) {
        locusType = SEARCH_CTY_TAG
    }

    if(filters.hotels?.isNotEmpty() == true){
        locusType = SEARCH_HTL_TAG
    }
    with(filters.locationFiltersV2){
        if (filters.locationFiltersV2.appliedAreasTags?.isNotEmpty() == true){
            locusType = HotelConstants.SEARCH_AREA_TAG
        }

        val types = filters.locationFiltersV2.appliedPoiTags?.distinctBy { it.autoSuggestType }
                ?.map { it.autoSuggestType } // can be POI or GPOI or both

        if (appliedAreasTags?.isNotEmpty() == true && appliedPoiTags?.isNotEmpty() == true || types?.size == 2){
            locusType =  HotelConstants.SEARCH_MM
        }else{
            val type  = types?.firstOrNull()
            type?.let { locusType = it }
        }
    }
    return locusType
}

private fun getAppliedPoi(poi: List<TagSelectionForListingV2>): ArrayList<LocusPDTPoiData> {
    val list = ArrayList<LocusPDTPoiData>()
    poi.forEach {
        if (it.placeId != null) {
            list.add(LocusPDTPoiData(it.tagDescription, it.placeId, it.latitude, it.longitude, it.autoSuggestType))
        }
    }
    return list
}