package com.mmt.hotel.common.constants

import com.mmt.pokus.LOB
import com.mmt.pokus.model.Experiment
import com.mmt.pokus.model.ExperimentsHelper

object ExperimentsHotel {

    val altAccoBaseCardOrder: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "altAccoBaseCardOrder", "", "17/07/2023"))

    val altAccoLuxeCardOrder: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "altAccoLuxeCardOrder", "", "17/07/2023"))

    val hotelBaseCardOrder: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "hotelBaseCardOrder", "", "17/07/2023"))

    val hotelLuxeCardOrder: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "hotelLuxeCardOrder", "", "17/07/2023"))

    val dayUseCardOrderV2: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "dayUseCardOrderV2", "[[\"rc\",\"ccc\"],[\"rd\"],[\"hrc\",\"pl\"],[\"pc\"],[\"bpb\"],[\"gst\"],[\"cd\"],[\"td\"]]", "17/07/2023"))

    val reviewDetailCardOrderB2C: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "reviewDetailCardOrderB2C", "", "10/08/2020"))

    val reviewDetailCardOrderB2B: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "reviewDetailCardOrderB2B", "", "12/11/2020"))


    val packageDealMaxInclusions: Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL,"packageDealMaxInclusions",10,"04/08/2021"))

    val hotelSequence: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "hotelSequence", "", "29/10/2020")) //Only to use in debug mode

    val hotelThankYouDevMode: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "hotelThankYouDevMode", false, "13/02/2022")) //Only to use in debug mode

    val immediateAppUpdate: Experiment<Boolean> =
        ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "forceUpgrade", false, "26/04/2023"))

    val flexibleAppUpdate: Experiment<Boolean> =
        ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "appUpgrade", false, "26/04/2023"))


    /**
    set default attributes value depending
    upon htlHomestayAttributesValue Experiment
    0 -> current , fill all
    1 -> fill only city | date, occupancy empty
    2 -> fill city, dates | occupancy empty
    3 -> fill city, occupancy | date empty
    4 -> fill none
     * */
    val htlLandingParams: Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL,"htlLandingParams",0,"06/06/2022"))



    val htlPricingExpName: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL,"htlPricingExpName","PN","20/10/2022"))


    val filterBottomSheet: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "filterBottomSheet", "A", "10/02/2022"))
    val homeStayFilterBottomSheet: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "homeStayFilterBottomSheet", "A", "10/02/2022"))

    /*
        We used to put 2 child per room, But this experiment keeps allocating child Occupancy same as Adults
     */


    val dayUseLastCheckIn: Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "dayUseLastCheckIn", 18, "03/01/2023"))


    val htlAutoSuggestExp: Experiment<String> = ExperimentsHelper.addExperiment(
        Experiment(
            LOB.HOTEL,
            "HtlAutoSuggestExp",
            "expscore1",
            "27/03/2023"
        )
    )

    val hideBottomBar: Experiment<Boolean> =
        ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "hideBottomBar", false, "21/06/2023"))

    val bnplDefault: Experiment<Boolean> =
        ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "bnplDefault", false, "21/06/2023"))

    /*
        The new Pokus portal migration has led to a key name change for the product. The default below specifies the sublob name, page name, and related fields,
        following the mandatory new nomenclature structure.
     */
    val videoAutoPlay : Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment((LOB.HOTEL),"mmt.app.hotel.default.listing.default.videoAutoPlay", true, "26/03/24"))

//    val muteTreelsAudio : Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment((LOB.HOTEL),"muteTreelsAudio", false, "25/04/24"))

    val htlMultiCurrency: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment((LOB.HOTEL), "htlMultiCurrency", false, "24/03/24"))


    /*
    this field can have values
    1-> field is shown and it is optional
    2 -> field is shown and it is mandatory
    anything else -> field is not shown
     */
    val nationality : Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "mmt.app.hotel.default.review.default.nationality", 0, "03/03/24"))

    val hotelListingCompose: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "hotelListingCompose", false, "20/05/2024"))

    val galleryCompose: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "galleryCompose", false, "27/12/2024"))

    val hotelLandingCompose: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "hotelLandingCompose", false, "15/10/2024"))

    val altAccoLandingCompose: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "altAccoLandingCompose", false, "15/12/2024"))

    val aaBedroomInput: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "aaBedroomInput", false, "13/12/2024"))
        /*
        * LOB.HOTEL experiments ends
        */
    val unmuteListingVideoLocationCard : Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "unmuteListingVideoLocationCard", false, "01/05/24"))

  /*  shouldShowMakerAndAnimate ==0
    shouldShowMarker ==1
    shouldAnimateCamera == 2
    default 3*/
    val streetViewMarker : Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "mmt.app.hotel.default.default.default.streetViewMarker", 3, "15/05/24"))

    val currencyAutoSelectWithLocation: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "currencyAutoSelectWithLocation", false, "27/06/2024"))

    val wishlistBottomBar: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "wishlistBottomBar", false, "7/07/2024"))

    val showTravelTipsToolTip: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "showTravelTipsToolTip", false, "21/07/2024"))

    val flexibleRoomsLanding: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "flexibleRoomsLanding", false, "16/08/2024"))

    val autoSuggestUiV2: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "autoSuggestUiV3", false, "08/08/2024"))

    val chainRatingUi: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "chainRatingInfo", false, "08/10/2024"))

    val androidVideoCardAutoplay: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "androidVideoCardAutoplay", false, "16/09/2024"))

    val longStayGCCNudge: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "longStayGCCNudge", false, "07/08/2024"))

    val premiumSection: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "premiumSection", false, "12/09/2024"))

    val enableTimezoneCheck: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "enableTimezoneCheck", false, "07/10/2024"))

    val gccAlternateDates: Experiment<String> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "gccalternative", "none", "18/10/2024")) // possible values are persuasion, persuasion_bottomsheet, none

    val cacheTravellerDetails: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "cacheTravellerDetails", false, "17/10/2024"))

    val collectionListing: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "collectionListing", false, "16/12/2024"))

    val bnplPreTxnDuration: Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "bnplPreTxnDuration", 0, "17/12/2024"))

    val maskPropertyName: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "maskPropertyName", false, "6/12/2024"))

    val newMapIconEnabled: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "newMapIconEnabled", false, "6/12/2024"))

    val fullScreenImageCompose: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "fullScreenImageCompose", false, "29/01/2025"))

    val composeHotelCards: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "composeHotelCards", false, "29/01/2025"))

    val isSemanticSearchInAutoSuggest: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "semantic_apps", true, "16/1/2025"))

    val filterScreenV4: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "filterScreenV4", false, "9/01/2025"))

    val imageVariantReduction: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "imageVariantReduction", false, "17/2/2025"))

    val htlTravelplex: Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "htlTravelplex", 0, "07/03/2025"))

    val composePersuasions: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "composePersuasions", false, "5/03/2025"))

    val listingApiMigration: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "listingApiMigration", false, "12/2/2025"))

    val ugcTrustMarker: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "ugcTrustMarker", false, "1/04/2025"))

    val preAppliedInsurance: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "preAppliedInsurance", false, "25/03/2025"))

    val travelTipsCompose: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "travelTipsCompose", true, "7/04/2025"))

    val tripTagApiMandatory: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "tripTagApiMandatory", false, "29/04/2025"))

    val thankYouPageRevamp: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "thankYouPageRevamp", false, "7/04/2025"))

    val htlTranslation: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "htlTranslation", false, "26/03/2025"))

    val htlSpeechInput: Experiment<Boolean> =  ExperimentsHelper.addExperiment(Experiment(LOB.HOTEL, "htlSpeechInput", false, "30/04/2025"))

}
