package com.mmt.hotel.common.model.request

import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants

class HotelRequestConstants {
    companion object {
        const val REQUEST_TYPE = "B2CAgent"
        const val DEVICE_TYPE = "Mobile"
        const val BOOKING_DEVICE = "ANDROID" // used as data field in api
        const val CLIENT = "android" // used in api path
        const val CHANNEL = "Native"
        const val LOB_NAME = "HOTEL"
        const val ID_CONTEXT_B2C = "B2C"
        const val ID_CONTEXT_B2B = "CORP"
        const val PAGE_CONTEXT_REVIEW = "REVIEW"
        const val PAGE_CONTEXT_DETAIL = "DETAIL"
        const val PAGE_CONTEXT_TREEL = "TREEL_LISTING"
        const val PAGE_CONTEXT_LISTING = "LISTING"
        const val PAGE_CONTEXT_SELECT_ROOM = "SELECT_ROOM"
        const val PAGE_CONTEXT_ALT_ACCO_LANDING = "AltAccoLanding"
        const val PAGE_CONTEXT_ALT_ACCO_TRENDING = "ALTACCOTRENDING"
        const val PAGE_CONTEXT_STAYCATION_LANDING = "StayCationLanding"
        const val PAGE_CONTEXT_LANDING = "LANDING"
        const val PAGE_CONTEXT_DAY_USE_LANDING = "DAYUSE_LANDING"
        const val PAGE_CONTEXT_GROUP_BOOKING_LANDING = "GROUP_LANDING"
        const val PAGE_CONTEXT_SHORT_STAY_LANDING = "SHORT_STAY_LANDING"
        const val PAGE_CONTEXT_THANK_YOU = "THANK_YOU"
        const val SUB_PAGE_CONTEXT_LISTING_COLLECTION = "LISTING_COLLECTION"

        fun toPageName(pageContext: String): HotelPdtV2Constants.PageName {
            return when(pageContext) {
                PAGE_CONTEXT_LANDING -> {
                    HotelPdtV2Constants.PageName.landing
                }
                PAGE_CONTEXT_LISTING -> {
                    HotelPdtV2Constants.PageName.listing
                }
                PAGE_CONTEXT_REVIEW -> {
                    HotelPdtV2Constants.PageName.review
                }
                PAGE_CONTEXT_THANK_YOU -> {
                    HotelPdtV2Constants.PageName.thank_you
                }
                PAGE_CONTEXT_DETAIL -> {
                    HotelPdtV2Constants.PageName.detail
                }
                PAGE_CONTEXT_SELECT_ROOM -> {
                    HotelPdtV2Constants.PageName.select_room
                }
                PAGE_CONTEXT_TREEL -> {
                    HotelPdtV2Constants.PageName.treels
                }
                else -> {
                    HotelPdtV2Constants.PageName.landing
                }
            }
        }
    }
}