package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.MyBizCardType.Companion.MYBIZ_CARD_DETAIL
import com.mmt.hotel.common.constants.MyBizCardType.Companion.MYBIZ_CARD_LISTING
import com.mmt.hotel.common.constants.MyBizCardType.Companion.MYBIZ_CARD_LISTING_FILTER
import com.mmt.hotel.common.constants.MyBizCardType.Companion.MYBIZ_CARD_MATCHMAKER
import com.mmt.hotel.common.constants.MyBizCardType.Companion.MYBIZ_CARD_REVIEW
import com.mmt.hotel.common.constants.MyBizCardType.Companion.MYBIZ_CARD_THANK_YOU

@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [MYBIZ_CARD_LISTING,
    MYBIZ_CARD_MATCHMAKER,
    MYBIZ_CARD_DETAIL,
    MY<PERSON><PERSON>_CARD_REVIEW,
    MY<PERSON><PERSON>_CARD_THANK_YOU,
    MY<PERSON><PERSON>_CARD_LISTING_FILTER])
annotation class MyBizCardType {
    companion object {
        const val MYBIZ_CARD_LISTING = "mybizListing"
        const val MYBIZ_CARD_MATCHMAKER = "mybizMatchmaker"
        const val MYBIZ_CARD_DETAIL = "mybizDetail"
        const val MYBIZ_CARD_REVIEW = "mybizReview"
        const val MYBIZ_CARD_THANK_YOU = "mybizThankYou"
        const val MYBIZ_CARD_LISTING_FILTER = "myBizListingFilter"
    }
}