package com.mmt.hotel.common.di

import android.app.Activity
import com.mmt.hotel.common.model.UserSearchData
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent

@InstallIn(ActivityComponent::class)
@Module
class UserSearchDataActivityModule {
    @Provides
    fun provideUserSearchData(activity : Activity) : UserSearchData {
        return if(activity is UserSearchDataInjector) {
            activity.getUserSearchData()
        } else {
            throw RuntimeException("implement UserSearchDataInjector to inject UserSearchData")
        }
    }
}

interface UserSearchDataInjector {
    fun getUserSearchData() : UserSearchData
}