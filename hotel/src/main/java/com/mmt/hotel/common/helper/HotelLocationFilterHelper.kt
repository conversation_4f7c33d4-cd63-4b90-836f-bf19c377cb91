package com.mmt.hotel.common.helper

import com.mmt.hotel.common.model.response.persuasionCards.CtxLocationFilterV2
import com.mmt.hotel.common.model.response.persuasionCards.LocusNearbyAreaDataV2
import com.mmt.hotel.common.model.response.TagSelectionForListingV2
import com.mmt.hotel.common.constants.HotelConstants

object HotelLocationFilterHelper {

    fun convertNearAreasFilterToTags(tag: LocusNearbyAreaDataV2): TagSelectionForListingV2 {
        return TagSelectionForListingV2(
                tagDescription = tag.locName,
                tagAreaId = tag.locId,
                tagTypeId = HotelConstants.POPULAR_AREA_TAG_TYPE_ID,
                categoryId = HotelConstants.POPULAR_AREA_CATEGORY_ID,
                isLocation = false
        )
    }

    fun convertCTXLocationFilterToTags(locationFilters: List<CtxLocationFilterV2>?): List<TagSelectionForListingV2> {
        return locationFilters?.map{
            TagSelectionForListingV2(
                    tagDescription = it.locationName,
                    tagAreaId = it.areaId,
                    tagId = it.locationId?.toInt() ?: 0,
                    tagTypeId = HotelConstants.POPULAR_AREA_TAG_TYPE_ID,
                    categoryId = HotelConstants.POPULAR_AREA_CATEGORY_ID,
                    isLocation = false)
        } ?: emptyList()
    }

}