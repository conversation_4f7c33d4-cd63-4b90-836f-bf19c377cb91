package com.mmt.hotel.common.di

import android.app.Activity
import com.mmt.hotel.base.model.tracking.HotelBaseTrackingData
import com.mmt.hotel.common.model.UserSearchData
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent

@InstallIn(ActivityComponent::class)
@Module
class HotelBaseTrackingDataModule {
    @Provides
    fun provideBaseTrackingData(activity : Activity) : HotelBaseTrackingData {
        return if(activity is BaseTrackingDataInjector) {
            activity.getBaseTrackingData()
        } else {
            throw RuntimeException("implement BaseTrackingDataInjector to inject HotelBaseTrackingData")
        }
    }
}

interface BaseTrackingDataInjector {
    fun getBaseTrackingData() : HotelBaseTrackingData
}