package com.mmt.hotel.common.constants

enum class LocationTagType {
    CITY,
    AREA,
    REGION,
    POI,
    GPOI
}

enum class POICategory(val category: String) {
    AIRPORT("Airport"),
    RESTAURANT("Restaurant"),
    HOSPITAL("Hospital"),
    BUS_TERMINAL("Bus Terminal"),
    TEMPLE("Temple"),
    CHURCH("Church"),
    GURUDWARA("Gurudwara"),
    MOSQUE("Mosque"),
    SHOPPING("Shopping"),
    RECREATION("Recreation"),
    METRO_STATION("Metro Station"),
    SPORTS_COMPLEX("Sports Complex"),
    FERRY("Ferry"),
    RELIGIOUS_PLACE("Religious Place"),
    INDUSTRIAL_BUSINESS_HUB("Industrial/Business Hub"),
    EDUCATION("Education"),
    RAILWAY("Railway"),
    NATURE("Nature"),
    LANDMARK("Landmark"),
    TOURIST_ATTRACTION("Tourist Attraction"),
    TRANSIT_POINT("Transit Point"),
    GOVERNMENT_OFFICE("Government Office"),
    WATERFALLS("Waterfalls"),
    CURRENCY_EXCHANGE("Currency Exchange"),
    POPULAR_AREAS("popular"),
    ATTRACTION("attraction"),
    BEACH("Beach"),
    TRAIN_STATION("Train Station")
}