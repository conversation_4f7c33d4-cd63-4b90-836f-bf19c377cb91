package com.mmt.hotel.common.di

import android.app.Activity
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.analytics.pdtMetrics.HotelScreenMetricsTracker
import com.mmt.hotel.common.ui.HotelComposeBaseActivity
import com.mmt.hotel.landingV3.tracking.HotelLandingTracker
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.hilt.android.scopes.ActivityScoped
import javax.inject.Named


@Module
@InstallIn(ActivityComponent::class)
class HotelActivityComponentModule {

    @ActivityScoped
    @Provides
    fun provideScreenMetricsTracker(activity : Activity): HotelScreenMetricsTracker {
        return if (activity is HotelComposeBaseActivity<*>) {
            activity.screenMetricsTracker
        } else {
            HotelScreenMetricsTracker()
        }
    }

    @ActivityScoped
    @Provides
    @Named(NamedConstants.METRICS_TRACKER_FOR_ENTRY_POINT)
    fun provideMetricsTrackerForEntryPoint(): HotelScreenMetricsTracker {
        return HotelScreenMetricsTracker()
    }

    @ActivityScoped
    @Provides
    @Named(NamedConstants.HOTEL_LANDING_TRACKER_FOR_ENTRY_POINT)
    fun provideHotelLandingTrackerForEntryPoint(hotelLandingTracker: HotelLandingTracker): HotelLandingTracker {
        return hotelLandingTracker
    }

    @ActivityScoped
    @Provides
    @Named(NamedConstants.ALT_ACCO_TRACKER_FOR_ENTRY_POINT)
    fun provideAltAccoTrackerForEntryPoint(altAccoTracker: AltAccoTracker): AltAccoTracker {
        return altAccoTracker
    }

}