package com.mmt.hotel.common.constants

import androidx.annotation.StringDef

@Target(AnnotationTarget.TYPE)
@Retention(AnnotationRetention.SOURCE)
@StringDef(
    value = [
        HotelExpKeys.SPONSORED_HOTELS,
        HotelExpKeys.PRICE_DISPLAY_OPTION,
        HotelExpKeys.SHOW_PAN_CARD_REVIEW,
        HotelExpKeys.DUMMY_PAN_CARD_REVIEW,
        HotelExpKeys.HTL_BOOK_NOW_PAY_LATER,
        HotelExpKeys.MMT_BLACK_V2,
        HotelExpKeys.ALTACCO_ROOM_INFO_REQD,
        HotelExpKeys.CAN_HANDLE_PARTIALLY_CONFIRMED,
        HotelExpKeys.MATCHMAKER_VERSION,
        HotelExpKeys.FORK_FCNR,
        HotelExpKeys.SERVER_PRICE_BUCKET,
        HotelExpKeys.FILTER_BASED_PRICE,
        HotelExpKeys.ENABLE_PAH_ADDON,
        HotelExpKeys.ENABLE_VISA_ADDON,
        HotelExpKeys.HTL_EXP_KEY_MULTI_CURRENCY,
        HotelExpKeys.RECOMMENDED_COUPON,
        HotelExpKeys.ONLY_PAH,
        HotelExpKeys.B2B_PAY_AT_HOTEL,
        HotelExpKeys.EMI,
        HotelExpKeys.PAH,
        HotelExpKeys.LOCAL_NOTIFICATION,
        HotelExpKeys.HOTEL_MULTI_ROOM_SEARCH,
        HotelExpKeys.HTL_INSURANCE_ADD_ON,
        HotelExpKeys.MULTIROOM_PRICE_DISPLAY_APPROACH_STR,
        HotelExpKeys.LIST_NEAR_BY,

        HotelExpKeys.HTL_DETAIL_CARD_ORDER,
        HotelExpKeys.HTL_IMAGE_SEQUENCING,
        HotelExpKeys.HTL_PREFETCH_IMAGES,
        HotelExpKeys.HTL_RM_STAR_RATING_FRM_HOMESTAYS,
        HotelExpKeys.HTL_SAVED_CARDS_FULL_SCREEN,
        HotelExpKeys.NEW_LISTING_PAGE,
        HotelExpKeys.HTL_SHOW_TAX,
        HotelExpKeys.HTL_VIDEO,
        HotelExpKeys.HOTEL_NEARBY_EXPERIMENTS,
        HotelExpKeys.HOTEL_DETAIL_V3,
        HotelExpKeys.LONG_STAY_OFFER,
        HotelExpKeys.SOLD_OUT_CASE,
        HotelExpKeys.REVIEW_RANK_RELEVANCE,
        HotelExpKeys.ALL_INCLUSIVE_PLAN,
        HotelExpKeys.AIRPORT_TRANSFER,
        HotelExpKeys.CITY_GUIDE,
        HotelExpKeys.HTL_LISTING_FILTER_CHANGE,
        HotelExpKeys.SORT_CRITERIA,
        HotelExpKeys.GROUP_BOOKING_ENABLED,
        HotelExpKeys.ENABLE_FAMILY_FARES,
        HotelExpKeys.LISTING_AD_TECH_CARD,
        HotelExpKeys.FILTER_BOTTOM_SHEET,
        HotelExpKeys.TRANSFER_FEE_TEXT,
        HotelExpKeys.SOLD_OUT_UI_V2,
        HotelExpKeys.CALENDAR_VERSION_V2,
        HotelExpKeys.CALENDAR_MLOS,
        HotelExpKeys.NHL,
        HotelExpKeys.AALV2,
        HotelExpKeys.GCC_EXCLUDED_CHARGES,
        HotelExpKeys.MMT_BLACK_DETAIL_TOP_CARD,
        HotelExpKeys.LUX_SUPER_PACKAGE,
        HotelExpKeys.IMPROVE_WISHLIST_DISCOVERY
    ]
)
annotation class HotelExpKeys {
    companion object {
        const val SPONSORED_HOTELS = "SPS"
        const val PRICE_DISPLAY_OPTION = "PDO"
        const val SHOW_PAN_CARD_REVIEW = "SPCR"
        const val DUMMY_PAN_CARD_REVIEW = "DPCR"
        const val HTL_BOOK_NOW_PAY_LATER = "BNPL"
        const val MMT_BLACK_V2 = "BLACK"
        const val ALTACCO_ROOM_INFO_REQD = "AARI"
        const val CAN_HANDLE_PARTIALLY_CONFIRMED = "CHPC"
        const val MATCHMAKER_VERSION = "MMRVER"
        const val FORK_FCNR = "OCCFCNR"
        const val SERVER_PRICE_BUCKET = "FLTRPRCBKT"
        const val FILTER_BASED_PRICE = "FBP"
        const val ENABLE_PAH_ADDON = "ADDON"
        const val ENABLE_VISA_ADDON = "ADDV"
        const val HTL_EXP_KEY_MULTI_CURRENCY = "MCUR"
        const val RECOMMENDED_COUPON = "RCPN"
        const val ONLY_PAH = "PAH5"
        const val B2B_PAY_AT_HOTEL = "B2BPAH"
        const val EMI = "EMI"
        const val PAH = "PAH"
        const val LOCAL_NOTIFICATION = "LN"
        const val HOTEL_MULTI_ROOM_SEARCH = "MRS"
        const val HTL_INSURANCE_ADD_ON = "IAO"
        const val MULTIROOM_PRICE_DISPLAY_APPROACH_STR = "PSD"
        const val LIST_NEAR_BY = "LSTNRBY"
        const val CHAIN_RATING_INFO = "CRI"

        const val HTL_DETAIL_CARD_ORDER = "DCO"
        const val HTL_IMAGE_SEQUENCING = "HIS"
        const val HTL_PREFETCH_IMAGES = "HPI"
        const val HTL_RM_STAR_RATING_FRM_HOMESTAYS = "HRSRFHS"
        const val HTL_SAVED_CARDS_FULL_SCREEN = "HSCFS"
        const val NEW_LISTING_PAGE = "NLP"
        const val HTL_SHOW_TAX = "ST"
        const val HTL_VIDEO = "VIDEO"
        const val HOTEL_NEARBY_EXPERIMENTS = "HRNB"
        const val HOTEL_DETAIL_V3 = "detailV3"
        const val LONG_STAY_OFFER = "LSOF"
        const val SOLD_OUT_CASE = "SOC"
        const val REVIEW_RANK_RELEVANCE = "RRR"
        const val ALL_INCLUSIVE_PLAN = "AIP"
        const val AIRPORT_TRANSFER = "APT"
        const val CITY_GUIDE = "CGC"
        const val HTL_LISTING_FILTER_CHANGE = "HAFC"
        const val SORT_CRITERIA = "LSC"
        const val GROUP_BOOKING_ENABLED = "GBE"
        const val ENABLE_FAMILY_FARES = "EFF"
        const val LISTING_AD_TECH_CARD = "ADC"
        const val FILTER_BOTTOM_SHEET = "FBS"
        const val LISTING_SEARCH_BUTTON = "LSB"
        const val SHOW_LOAD_MORE_CARD = "SMC"
        const val TRANSFER_FEE_TEXT = "TFT"
        const val SOLD_OUT_UI_V2 = "SOU"
        const val CALENDAR_VERSION_V2 = "CV2"
        const val CALENDAR_MLOS = "MLOS"
        const val SELECT_ROOM_REVAMPED_RATE_PLAN = "SRRP"
        const val HTL_FILTER_CONTEXT = "HFC"
        const val NHL = "NHL"
        const val AALV2 = "AALV2"
        const val SSV2 = "SSV2"
        const val GCC_EXCLUDED_CHARGES = "GEC"
        const val GCC_BEST_RATE_PLANS = "GBRP"
        const val BNPL_ZERO = "BNPL0"
        const val GROUP_BOOKING_ROOM_PER_NIGHT = "GRPN"
        const val DETAIL_HOST_V2 = "HSTV2"
        const val RTB_PERSUASION_REMOVAL = "RTBC"
        const val MMT_BLACK_DETAIL_TOP_CARD = "MBDTC"
        const val PROPERTY_LAYOUT_V2 = "PLV2"
        const val LUX_SUPER_PACKAGE = "SPKG"
        const val GALLERY_V2 = "GALLERYV2"
        const val FUNNEL_ENTRY = "FE"
        const val UGC_V2 = "UGCV2"
        const val IMPROVE_WISHLIST_DISCOVERY = "IWD"
        const val ALT_ACCO_BEDROOM_INPUT = "AABI"
        const val MMC = "mmc" // matchmaker cleanup
        const val PRS = "PRS" // Premium Section
        const val PRSBS = "PRSBS" // Premium Section Bottom Sheet
        const val CL = "CL" // Collection Listing
        const val MPN = "MPN" // Masked Property Name
        const val FSV4 = "FSV4" // Filter Screen V4
        const val LISTING_MIGRATION = "LAM" // Listing API migration
        const val TPE = "TPE" // Travel Plex Experiment
        const val TPR = "TPR" // Thankyou Page revamp

    }
}
