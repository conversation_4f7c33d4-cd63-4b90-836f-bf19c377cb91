package com.mmt.hotel.common.extensions

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.graphics.Typeface
import android.text.Html
import android.text.SpannableStringBuilder
import android.view.View
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleCoroutineScope
import com.mmt.auth.login.model.Employee
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.DateUtil
import com.mmt.core.util.GsonUtils
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.hotel.hotelsearchrequest.GuestCount
import com.mmt.data.model.hotel.hotelsearchrequest.RoomStayCandidate
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelFragment
import com.mmt.hotel.bookingreview.model.DomesticGSTNDetails
import com.mmt.hotel.common.constants.EmployeeType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.PersuasionTemplate
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.model.response.persuasion.PersuasionDataModel
import com.mmt.hotel.common.util.HotelDateUtil.getNights
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.detail.model.response.FlyFishRatingV2
import com.mmt.hotel.gallery.dataModel.HotelMediaTypeKey
import com.mmt.hotel.gallery.dataModel.MediaV2
import com.mmt.hotel.landingV3.model.response.SearchContextRoomStayCandidates
import com.mmt.hotel.landingV3.model.response.SearchContextV2
import com.mmt.hotel.listingV2.event.ListingPersuasionPlaceHolder
import com.mmt.hotel.listingV2.model.response.hotels.*
import com.mmt.hotel.listingV2.viewModel.DELAY_TIME
import com.mmt.hotel.old.landing.model.response.SearchContext
import com.mmt.hotel.old.model.hotelListingResponse.HotelListOld
import com.mmt.hotel.selectRoom.model.response.room.SeekTag
import com.mmt.hotel.userReviews.featured.model.FlyFishReview
import com.mmt.hotel.userReviews.featured.model.SubConcept
import com.mmt.hotel.userReviews.featured.util.RatingUtilsV2
import com.mmt.profile.model.GSTNData
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.flow
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by Gurtek Singh on 13/07/20.
 */


inline fun <T, reified C> T.convertTo(): C {
    val gson = GsonUtils.getInstance()
    return gson.deserializeJSON(gson.serializeToJson(this), C::class.java)
}

fun CharSequence.asSpannableBuilder(): SpannableStringBuilder {
    return this as SpannableStringBuilder
}

fun Int.fontTypeFace(): Typeface? {
    return ResourceProvider.instance.getFont(this)
}

fun Any.asBoolean() = (this as? Boolean) ?: false

/** Block inside safe return will return an empty string by default if block value is null*/
inline fun <T> T?.safeReturnString(block: (T) -> String): String {
    return if (this == null) EMPTY_STRING else block(this)
}


fun SimpleDateFormat.parseOrTodayDate(date: String): Date {
    return try {
        parse(date)
    } catch (exception: Exception) {
        Date()
    }
}


fun List<RoomStayCandidatesV2>.toOccupancyData(): OccupancyData {
    var totalAdults = 0
    val childAges = arrayListOf<Int>()
    var roomCount: Int? = null //Room input is optional in Alt Acco funnel, null implies no explicit room input by user in Alt Acco funnel
    for (candidate in this) {
        totalAdults += candidate.adultCount
        childAges.addAll(candidate.childAges ?: emptyList())
        candidate.rooms?.let {
            roomCount = if (roomCount == null) { it } else { roomCount!! + it }
        }
    }
    return OccupancyData(roomCount = roomCount, totalAdults, childAges)
}

fun <T, R> List<T>.mapUntill(elementsCount: Int, transform: (T) -> R): List<R> {
    if(elementsCount < 0) return emptyList()
    val listSize = size.coerceAtMost(elementsCount)
    val list = ArrayList<R>(listSize)
    for (i in 0 until listSize) {
        list.add(transform(this[i]))
    }
    return list
}

fun List<SearchContextRoomStayCandidates>.toRoomStayCandidatesV2(): MutableList<RoomStayCandidatesV2> {
    val roomStayCandidatesV2List = mutableListOf<RoomStayCandidatesV2>()
    var adultCount = 0
    val childAges = mutableListOf<Int>()
    forEach {
        for (guests in it.guestCounts) {
            if (HotelConstants.ADULT_AGE_QUALIFYING_VALUE.equals(guests.ageQualifyingCode, true)) {
                adultCount += guests.count.toInt()
            } else if (HotelConstants.CHILD_AGE_QUALIFYING_VALUE.equals(guests.ageQualifyingCode, true)) {
                childAges.addAll(guests.ages ?: emptyList())
            }
        }
    }
    if (childAges.isEmpty()) {
        roomStayCandidatesV2List.add(RoomStayCandidatesV2(adultCount, null,rooms = this.size))
    } else {
        roomStayCandidatesV2List.add(RoomStayCandidatesV2(adultCount, childAges,rooms = this.size))
    }
    return roomStayCandidatesV2List
}

fun List<RoomStayCandidatesV2>?.totalGuestCount(): Int {
    return this?.sumBy { it.adultCount + (it.childAges?.size ?: 0) } ?: 0
}

fun List<SearchContextRoomStayCandidates>.toOldRoomStayCandidates(): MutableList<RoomStayCandidate> {
    val roomStayCandidateList = mutableListOf<RoomStayCandidate>()
    forEach { searchContextRoomStayCandidates ->
        roomStayCandidateList.add(RoomStayCandidate().apply {
            guestCounts = searchContextRoomStayCandidates.guestCounts.map {
                GuestCount().apply {
                    count = it.count
                    ageQualifyingCode = it.ageQualifyingCode
                    ages = it.ages
                }
            }
        })
    }
    return roomStayCandidateList
}

fun SearchContextV2.toSearchContext(): SearchContext {
    return SearchContext(
            cityCode = this.cityCode,
            checkIn = this.checkIn,
            checkOut = this.checkOut,
            countryCode = this.countryCode,
            locationId = this.locationId,
            locationType = this.locationType,
            cityName = this.cityName,
            roomStayCandidates = this.roomStayCandidates.toOldRoomStayCandidates()
    )
}

/**
 * method to get cache key for Hotel Details (hotel static response)
 *
 * key's structure would be <hotelId>_<tripType>_<LOS>_<RoomCount>_<AdultsCount>-<ChildrenCount>
 *
 * <LOS> ~ Length of Stay {difference between checkIn/checkOut date}
 */
fun UserSearchData?.getHotelDetailResponseCacheKey(): String {
    if (this == null) {
        return EMPTY_STRING
    }

    val key = StringBuilder(hotelId) // hotelId
    key.append(HotelConstants.HOTEL_DETAIL_CACHE_KEY_SEPARATOR).append(funnelSrc)
    if (tripType.isNotNullAndEmpty()) {
        key.append(HotelConstants.HOTEL_DETAIL_CACHE_KEY_SEPARATOR).append(tripType?.lowercase(Locale.ENGLISH))  // tripType
    }

    key.append(getNights(checkInDate = checkInDate, checkOutDate = checkOutDate)) // length of stay

    key.append(HotelConstants.HOTEL_DETAIL_CACHE_KEY_SEPARATOR).append(occupancyData.roomCount?: 0) // roomCount
            .append(HotelConstants.HOTEL_DETAIL_CACHE_KEY_SEPARATOR).append(occupancyData.adultCount) // adultCount
    if (occupancyData.childAges.isNotEmpty()) {
        key.append(HotelConstants.HOTEL_DETAIL_CACHE_KEY_SEPARATOR).append(occupancyData.childAges.size) // childCount
    }

    return key.toString()
}

/**
 * function to remove all the white spaces from a given string
 */
fun String.clearSpaces() = this.replace("\\s".toRegex(), "")

fun SubConcept.toSeekTag() : SeekTag {
    return SeekTag(
            sentiment = this.sentiment,
            name = this.subConcept,
            id = this.subConcept,
            priorityScore = this.priorityScore,
            tagType = this.tagType,
            source = this.source
    )
}

fun String.toServerDateFormat(currentDateFormat : String = HotelConstants.CURRENT_DATE_FORMAT): String {
    return DateUtil.convertFromOneFormatToAnother(this, currentDateFormat, HotelConstants.NEW_DATE_FORMAT)
}

fun FlyFishReview.toV2(): FlyFishRatingV2 = convertTo()

fun HotelListOld.toHotel() : Hotel {
    val locationDetail= LocationDetail(countryCode , countryName ,cityCode ?: EMPTY_STRING,cityName ?:EMPTY_STRING,EMPTY_STRING )
    val mediaList = mainImages?.map {
        val media = MediaV2()
        media.mediaType= HotelMediaTypeKey.IMAGE_MEDIA
        media.url = it
        media
    }?: emptyList()
    val hotelReview=flyfishReviewSummary?: emptyMap()
    val source = RatingUtilsV2.getRatingSourceV1(hotelReview, HotelUtil.isDom(countryCode))
    val flyfishReview1 = hotelReview[source]
    val review = flyfishReview1?.toV2()?.apply { this.source =source?:EMPTY_STRING }
    var priceDetail: PriceDetail? = null
    displayFare?.displayPriceBreakDown?.let {
        val sellingPrice = it.displayPrice
        val originalPrice = it.nonDiscountedPrice
        val tax = it.totalTax
        val pricingKey = it.pricingKey ?: EMPTY_STRING
        priceDetail = PriceDetail(sellingPrice,sellingPrice+tax,originalPrice,originalPrice+tax,0.0,0.0,tax,pricingKey,null,null,null,null)
    }
    return Hotel(
        categories = categories?.toMutableList(),
        geoLocation = geoLocation,
        id = id,
        isAltAcco = isAltAcco,
        highSellingAltAcco = highSellingAltAcco,
        locationDetail = locationDetail,
        locationPersuasion = locationPersuasion,
        multiRoomRecommendation = false,
        name = name,
        propertyType = propertyType ?: EMPTY_STRING,
        soldOut = isSoldOut(),
        starRating = starRating,
        starRatingType = fetchStarRatingType(),
        stayType = stayType ?: EMPTY_STRING,
        persuasions = createLocationPersuasion(persuasions , locationPersuasion),
        trackingInfo = sponsoredTrackingInfoModel,
        soldOutInfo = soldOutInfo,
        reviewDeeplinkUrl = appDeeplink,
        propertyViewType = propertyViewType,
        crossSellTag = crossSellTag,
        deepLink = appDeeplink,
        isWishListed = isWishListed,
        totalRoomCount = totalRoomCount,
        media = mediaList,
        totalImageCount = mediaList?.size,
        review = review,
        priceDetail = priceDetail,
        propertyLabel = null,
        slotDetail = null,
        appDeeplink = appDeeplink,
        currencyCode = currencyCode?.id,
        spotlightApplicable = spotlightApplicable)
}

fun createLocationPersuasion(
    persuasionsMap: Map<String, TemplatePersuasion>?,
    locationPersuasion: List<String>?
): Map<String, TemplatePersuasion>? {
    if (persuasionsMap?.containsKey(ListingPersuasionPlaceHolder.PLACEHOLDER_CARD_M1) == true || locationPersuasion == null) {
        return persuasionsMap
    }
    val text = locationPersuasion?.reduce { v1, v2 -> "$v1 | $v2" }
    val data = listOf(
        PersuasionDataModel(
            id = "LOC_PERSUASION_1",
            persuasionText = text,
            template = PersuasionTemplate.IMAGE_TEXT_H
        )
    )
    val templatePersuasion = TemplatePersuasion(PersuasionTemplate.IMAGE_TEXT_H, data)
    return persuasionsMap?.plus(ListingPersuasionPlaceHolder.PLACEHOLDER_CARD_M1 to templatePersuasion)
}

fun java.lang.StringBuilder.appendWithBullet(value: String?): java.lang.StringBuilder {
    if (!value.isNullOrBlank()) {
        if (length > 0) {
            this.append(CoreConstants.SPACE)
                .append(CoreConstants.BULLET)
                .append(CoreConstants.SPACE)
        }

        append(value)
    }
    return this
}

fun List<HotelEmployee>.toEmployeeOld(): List<Employee> {
    val employeeList = mutableListOf<Employee>()
    forEach {
        val employee = Employee()
        employee.name = it.firstName
        if (!it.lastName.isNullOrBlank()) {
            employee.name += CoreConstants.SPACE + it.lastName
        }

        employee.title = if (!it.title.isNullOrBlank()) {
            it.title
        } else if (it.gender == HotelConstants.GENDER_FEMALE) {
            ResourceProvider.instance.getString(R.string.htl_traveller_title_ms)
        } else {
            ResourceProvider.instance.getString(R.string.htl_traveller_title_mr)
        }
        employee.gender = it.gender
        employee.businessEmailId = it.emailId
        employee.phoneNumber = it.primaryContact
        employee.type = if (it.emailId == null) {
            EmployeeType.GUEST.value
        } else if (LoginUtils.loggedInUserEmail == it.emailId) {
            EmployeeType.MYSELF.value
        } else {
            EmployeeType.COLLEAGUE.value
        }
        employeeList.add(employee)
    }
    return employeeList
}

/**
 * make sure to catch exception
 */
@Throws
public fun <T> Flow<T>.dropFlow(throwException: Boolean = false, keySelector: (t: T) -> Boolean): Flow<T> = flow {
    collect { value ->
        if (keySelector.invoke(value)) {
            if(throwException) {
                throw Exception()
            }
        } else {
            emit(value)
        }
    }
}


/**
 * Enables accessibility focus to the View.
 *
 * @param lifecycleCoroutineScope Required to check for the Lifecycle controlling this LifecycleCoroutineScope to be at least in Lifecycle.State.RESUMED state.
 * Because at the time of lifecycle start, the screen reader might not be ready.
 */
fun View.setAccessibilityFocus(lifecycleCoroutineScope : LifecycleCoroutineScope) {
    try {
        if(!CoreUtil.isTalkbackEnabled()){
            return
        }
        lifecycleCoroutineScope.launchWhenResumed {
            performAccessibilityAction(AccessibilityNodeInfo.ACTION_ACCESSIBILITY_FOCUS, null)
            sendAccessibilityEvent(AccessibilityEvent.TYPE_VIEW_SELECTED)
        }
    }catch (e : Exception){
        e.printStackTrace()
    }
}

fun Boolean.toInt() = if(this) 1 else 0

fun Boolean.toChar() = if(this) "t" else "f"

fun Fragment?.sendEvent(event: HotelEvent) {
    (this as? HotelFragment<*,*>)?.viewModel?.eventStream?.value = event
}

fun <T> MutableList<T>.addIfNotNull(item: T?) : Boolean {
    return item?.let { add(it) } ?: false
}

fun GSTNData.toDomesticGSTNDetails(): DomesticGSTNDetails {
    return DomesticGSTNDetails(address = this.address, pinCode = this.pinCode, state = this.state)
}

/*
This method will run given block of code only if string is not null and not blank
 */
fun String?.runIfNotNullNorBlank(block: (String) -> Unit) {
    if (!this.isNullOrBlank()) {
        block(this)
    }
}

/*
This method will run given block of code only if string is not null and not blank
 */
@Composable
fun String?.ifNotNullNorBlank(block: @Composable (String) -> Unit) {
    if (!this.isNullOrBlank()) {
        block(this)
    }
}

fun String?.orIfNullOrBlank(default: String = ""): String =
    if (this != null && this.isNotBlank()) this else default

/*
This method will run given block of code only if string is not null and not blank
 */
inline fun <T> List<T>?.runIfNotNullNorEmpty(block: (List<T>) -> Unit) {
    if (!this.isNullOrEmpty()) {
        block(this)
    }
}

fun <T> Flow<T>.ignoreSameObject(keySelector: (t: T) -> String): Flow<T> {
    return debounce(DELAY_TIME)
        .distinctUntilChangedBy(keySelector)
}

fun View.fadeIn() {
    visibility = View.VISIBLE
    val fadeInAnimator = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f)
    fadeInAnimator.duration = 500 // milliseconds
    fadeInAnimator.start()
}

fun View.fadeOut() {
    val fadeOutAnimator = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f)
    fadeOutAnimator.duration = 500 // milliseconds
    fadeOutAnimator.addListener(object : AnimatorListenerAdapter() {
        override fun onAnimationEnd(animation: Animator) {
            visibility = View.GONE
        }
    })
    fadeOutAnimator.start()
}

/**
 * Helper function to count all occurrence of a substring in a string.
 */
fun String.count(element: String): Int {
    var count = 0
    var lastIndex = indexOf(element, 0)
    while (lastIndex >= 0) {
        count += 1
        lastIndex = indexOf(element, lastIndex + 1)
    }
    return count
}

/**
 * Helper function to find all indices of a substring in a string.
 */
fun String.indicesOf(substring: String): List<Int> {
    val indices = mutableListOf<Int>()
    var index = indexOf(substring)
    while (index >= 0) {
        indices.add(index)
        index = indexOf(substring, index + 1)
    }
    return indices
}

// Extension function to convert HTML to a normal string
fun String.fromHtml(): String {
    return Html.fromHtml(this, Html.FROM_HTML_MODE_LEGACY).toString()
}

fun DialogFragment.showFragment(manager: FragmentManager?, tag: String) {
    if (manager == null) {
        return
    }
    if (manager.isStateSaved.not()) {
        show(manager, tag)
    }
}

/**
 * Calculates the visible percentage of an item at a given index in a LazyList.
 *
 * @param index The index of the item in the LazyList.
 * @param itemSizePx The size of the item in pixels.
 * @return The visible percentage of the item, clamped between 0 and 100.
 */
fun LazyListState.percentageOfItemAtIndex(index: Int, itemSizePx: Int): Float {
    return if (firstVisibleItemIndex == index) {
        val visibleHeight = itemSizePx - firstVisibleItemScrollOffset
        val visiblePercentage = (visibleHeight.toFloat() / itemSizePx.toFloat()) * 100
        val clampedPercentage = visiblePercentage.coerceIn(0f, 100f)
        clampedPercentage
    } else {
        0f
    }
}