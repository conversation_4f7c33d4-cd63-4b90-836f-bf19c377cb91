package com.mmt.hotel.common.bottomsheet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.HotelBaseBottomSheet
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.getNavigationBarHeightDp
import com.mmt.hotel.compose.widgets.OutOfPolicyBottomsheet
import com.mmt.hotel.detail.ui.compose.AltAccoEducationBottomSheetContent
import com.mmt.hotel.listingV2.model.response.hotels.BottomSheetData
import com.mmt.hotel.listingV2.model.response.hotels.SectionFeature
import com.mmt.hotel.selectRoom.compose.DragHandle
import com.mmt.hotel.selectRoom.event.SelectRoomEvent
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.coroutines.launch

class HeaderSubtitleInclusionsBottomSheet() : BottomSheetDialogFragment() {

    private val data: BottomSheetData? by lazy { arguments?.getParcelable(KEY_DATA) }

    companion object {
        const val TAG = "HeaderSubtitleInclusionsBottomSheet"
        const val KEY_DATA = "data"

        fun newInstance(data: BottomSheetData): HeaderSubtitleInclusionsBottomSheet {
            return HeaderSubtitleInclusionsBottomSheet().apply {
                arguments = Bundle().apply {
                    putParcelable(KEY_DATA, data)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.HotelBottomSheetCornerRadiusDialogTheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
        setContent {
            data?.let { HeaderSubtitleInclusionsBottomSheetContent(data = it, onDismiss = ::dismiss) }
        }
    }

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HeaderSubtitleInclusionsBottomSheet(
    modifier: Modifier = Modifier,
    data: BottomSheetData?,
    onDismiss: () -> Unit
) {

    val density = LocalDensity.current
    val sheetState = remember { SheetState(skipPartiallyExpanded = true, density = density) }
    val sheetScope = rememberCoroutineScope()

    if (data != null) {
        ModalBottomSheet(
            modifier = modifier
                .padding(0.dp)
                .offset(
                    y = -(getNavigationBarHeightDp(
                        LocalContext.current
                    ))
                ),
            sheetState = sheetState,
            onDismissRequest = onDismiss,
            containerColor = colorResource(id = R.color.white),
            dragHandle = null,
            content = {
                HeaderSubtitleInclusionsBottomSheetContent(data = data, onDismiss = {
                    sheetScope.launch {
                        sheetState.hide()
                        onDismiss()
                    }
                })
            }
        )
    }
}


@Composable
fun HeaderSubtitleInclusionsBottomSheetContent(
    data: BottomSheetData,
    onDismiss: () -> Unit = {}
) {

    val iconPadding = dimensionResource(id = R.dimen.margin_tiny)
    val iconSize = dimensionResource(id = R.dimen.htl_room_highlights_icon_size)
    val fontSize = spDimensionResource(id = R.dimen.htl_text_size_small)

    HotelBaseBottomSheet(
        title = data.heading,
        showCrossIcon = true,
        onDismiss = onDismiss
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            MmtComposeTextView(
                text = data.subHeading,
                mmtFontStyle = latoBold,
                modifier = Modifier.fillMaxWidth(),
                fontSize = spDimensionResource(
                    id = R.dimen.htl_text_size_small
                ),
                color = colorResource(id = R.color.color_4a4a4a)
            )
            if (data.sectionFeatures.isNotNullAndEmpty()) {
                Spacer(modifier = Modifier.height(dimensionResource(id = R.dimen.margin_small)))
                data.sectionFeatures?.forEach {
                    Spacer(modifier = Modifier.height(dimensionResource(id = R.dimen.margin_small)))
                    Row(modifier = Modifier.fillMaxWidth()) {
                        LoadImage(
                            modifier = Modifier,
                            imageUrl = it.iconURL,
                            resourceId = if (it.iconURL.isEmpty()) R.drawable.ic_htl_default_dot else null,
                            contentScale = ContentScale.FillBounds
                        )
                        MmtComposeTextView(
                            text = it.heading,
                            mmtFontStyle = latoRegular,
                            maxLines = 3,
                            fontSize = fontSize,
                            color = colorResource(id = R.color.secondary_color),
                            modifier = Modifier
                                .weight(1f)
                                .padding(start = iconPadding),
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun HeaderSubtitleInclusionsPreview() {

    val sectionFeatures = mutableListOf<SectionFeature>().apply {
        add(SectionFeature("", "Free cancellation available till 12:00 PM on 20th May 2022"))
        add(SectionFeature("", "Pay at hotel available"))
        add(SectionFeature("", "Room service available"))
    }

    HeaderSubtitleInclusionsBottomSheetContent(
        data = BottomSheetData(
            heading = "Availability of Meals",
            subHeading = "Meals are not included in this option but they are available on request at extra charges (payable at property).",
            imgUrl = "",
            cta = "",
            ctaAction = "",
            infoText = "",
            sectionFeatures = sectionFeatures
        )
    )
}

@Preview
@Composable
fun HeaderSubtitlePreview() {
    HeaderSubtitleInclusionsBottomSheetContent(
        data = BottomSheetData(
            heading = "Availability of Meals",
            subHeading = "Meals are not included in this option but they are available on request at extra charges (payable at property).",
            imgUrl = "",
            cta = "",
            ctaAction = "",
            infoText = "",
            sectionFeatures = listOf()
        )
    )
}