package com.mmt.hotel.common.helper

import android.content.res.Resources;
import android.view.View
import android.view.ViewGroup
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.data.ToolTipConfig
import javax.inject.Inject
import com.mmt.hotel.widget.HotelToolTip
import com.mmt.hotel.widget.ToolTipDirection

/**
 * Created by <PERSON><PERSON><PERSON> on 09/04/21.
 */
class HotelToolTipManager @Inject constructor() {

    private val activeToolTips = ArrayList<HotelToolTip>()

    fun show(config: ToolTipConfig) {
        val context = config.anchorView.context
        val toolTip = HotelToolTip(context).apply {
            setToolTipText(config.toolTipText)
            showDismissIcon(config.showDismissIcon)
            setDirection(config.toolTipDirection)
            setToolTipBackGroundColor(config.backGroundColor)
           if (config.maxWidth != -1) setToolTipMaxWidth(ResourceProvider.instance.getDimension(config.maxWidth))
        }
        addToolTipToScreen(config.anchorView,toolTip)
        activeToolTips.add(toolTip)
    }

    fun dismissActiveToolTips() {
        activeToolTips.forEach {
            if (it.parent != null) it.rootView().removeView(it)
        }
    }

    private fun addToolTipToScreen(anchorView: View,toolTip: HotelToolTip) {
       val viewGroup = anchorView.rootView()
        toolTip.anchorWithView(anchorView)
        viewGroup.addView(toolTip)
    }

    private fun HotelToolTip.anchorWithView(view: View) {

        val rootLocationOnScreen = IntArray(2)
        view.rootView().getLocationOnScreen(rootLocationOnScreen)

        val rootViewX = rootLocationOnScreen[0].toFloat()
        val rootViewY = rootLocationOnScreen[1].toFloat()

        val locationOnScreen = IntArray(2)
        view.getLocationOnScreen(locationOnScreen)

        val viewX = locationOnScreen[0].toFloat() - rootViewX
        val viewY = locationOnScreen[1].toFloat() - rootViewY

        val spec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        measure(spec, spec)

        val offset = view.context.resources.getDimension(R.dimen.margin_small)

        y = if(getToolTipDirection() == ToolTipDirection.BOTTOM){
            viewY + view.height - offset
        }else{
            viewY + measuredHeight + offset
        }
        val toolTip = findViewById<View>(R.id.iv_tooltip)
        if (viewX + view.width < Resources.getSystem().displayMetrics.widthPixels) {
            toolTip.scaleX = 1F
            x = viewX
        } else {
            toolTip.scaleX = -1F
            x = viewX + view.width - measuredWidth
        }

    }

    private fun View.rootView() : ViewGroup {
        return rootView.findViewById<ViewGroup>(android.R.id.content).getChildAt(0) as ViewGroup
    }
}