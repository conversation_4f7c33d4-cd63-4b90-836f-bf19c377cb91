package com.mmt.hotel.common.extensions

import com.gommt.logger.LogUtils
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by <PERSON><PERSON><PERSON> on 28/09/20.
 */
/*Safely parse date or return current time date object*/
fun SimpleDateFormat.safeParse(date: String): Date {
    return try {
        if (date.isEmpty()){
               Date()
        } else {
            parse(date)
        }
    } catch (exception: Exception) {
        LogUtils.error("DateFormetExt", exception)
        return Date()
    }
}

fun SimpleDateFormat.parseOrNull(date: String): Date? {
    return try {
        if (date.isEmpty()){
            null
        } else {
            parse(date)
        }
    } catch (exception: Exception) {
        LogUtils.error("DateFormetExt", exception)
        return null
    }
}