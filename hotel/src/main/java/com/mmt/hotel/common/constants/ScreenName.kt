package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.ScreenName.Companion.BOOKING_REVIEW
import com.mmt.hotel.common.constants.ScreenName.Companion.DETAIL
import com.mmt.hotel.common.constants.ScreenName.Companion.GALLERY
import com.mmt.hotel.common.constants.ScreenName.Companion.LIST
import com.mmt.hotel.common.constants.ScreenName.Companion.SELECT_ROOM
import com.mmt.hotel.common.constants.ScreenName.Companion.LUX_BG_IMAGE
import com.mmt.hotel.common.constants.ScreenName.Companion.PAYMENT

@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [LIST, DETAIL, BOOKING_REVIEW, GALLERY, SELECT_ROOM,LUX_BG_IMAGE, PAYMENT])
annotation class ScreenName {
    companion object{
        const val LIST = "list"
        const val DETAIL = "detail"
        const val BOOKING_REVIEW = "review"
        const val GALLERY = "gallery"
        const val SELECT_ROOM = "select_room"
        const val PAYMENT = "payment"
        const val LUX_BG_IMAGE = "lux_bg_image"
        const val THANK_YOU_PAGE = "thank_you_page"
        const val THANK_YOU_PAGE_V2 = "thank_you_page_v2"
    }
}