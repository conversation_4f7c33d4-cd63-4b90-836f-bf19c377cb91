package com.mmt.hotel.common.helper

import com.mmt.core.util.DeviceUtil
import com.mmt.analytics.omnitureclient.OmnitureTracker
import com.mmt.auth.login.util.LoginUtils
import com.mmt.data.model.util.SharedPreferenceUtils
import com.mmt.data.model.util.SharedPreferenceUtils.KEY_HTL_NUMBER_OF_COUPON_ON_REVIEW
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.location.LocationUtil
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.CoreUtil
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.data.RequestDetailsData
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.model.request.*
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.deeplink.constants.HotelDeepLinkKeys
import com.pdt.eagleEye.managers.IdsHelper
import java.util.*
import javax.inject.Inject


/*
*   Class will contain common data/ helper functions for creating request POJO's for all features
* */

class HotelCommonRequestHelper @Inject constructor() {

    fun getSelectedCurrency(): String {
        val currency =  if (MMTCore.iAuth.isMultiCurrencyEnabled()) {
            HotelCurrencyUtil.getSelectedCurrencyCode()
                    ?: LoginUtils.getFunnelContextCurrencyCodeV1()
        } else {
            LoginUtils.getFunnelContextCurrencyCodeV1()
        }
        return currency.toUpperCase(Locale.ROOT)
    }

    fun getDeviceDetails(): DeviceDetails {
        return DeviceDetails(CoreUtil.getAppVersionName(), CoreUtil.getDeviceId(), HotelRequestConstants.DEVICE_TYPE,
                HotelRequestConstants.BOOKING_DEVICE,
                HotelMigratorHelper.instance.getCurrentInternetConnection(),
                HotelUtil.getResolution(), DeviceUtil.getDeviceModel())
    }

    fun getRequestDetails(requestDetailsData: RequestDetailsData): RequestDetails {
        return RequestDetails(
                visitorId = OmnitureTracker.getMcid(),
                visitNumber = HotelSharedPrefUtil.instance.getInt(SharedPrefKeys.VISITOR_NUMBER, 1),
                loggedIn = LoginUtils.isLoggedIn,
                sourceCountry = HotelSharedPrefUtil.instance.getString(LocationUtil.COUNTRY_NAME),
                sourceCity = HotelSharedPrefUtil.instance.getString(LocationUtil.CITY),
                sourceLatitude = try {HotelSharedPrefUtil.instance.getString(LocationUtil.LAT)?.toDouble()} catch (e: Exception) {null},
                sourceLongitude = try {HotelSharedPrefUtil.instance.getString(LocationUtil.LONGITUDE)?.toDouble()} catch (e: Exception) {null},
                funnelSource = HotelUtil.getFunnelSource(requestDetailsData.funnel),
                idContext = getRequestIdContext(),
                trafficSource = getTrafficSource(zcpDataString = requestDetailsData.zcpDataString),
                couponCount = getCouponCount(),
                siteDomain = LoginUtils.getPreferredRegionCode(),
                pageContext = requestDetailsData.pageContext,
                zcp = requestDetailsData.zcpDataString,
                payMode = requestDetailsData.payMode,
                preApprovedValidity = requestDetailsData.preApprovedValidity,
                requisitionID = requestDetailsData.requisitionID,
                myBizFlowIdentifier = requestDetailsData.myBizFlowIdentifier,
                oldWorkflowId = requestDetailsData.workFlowId,
                forwardBookingFlow = requestDetailsData.forwardFlow,
                sessionId = IdsHelper.getSessionID(),
                requestId = IdsHelper.getRequestId(),
                journeyId = requestDetailsData.journeyId ?: HotelUtil.getJourneyId(),
                isRequestCallBack = requestDetailsData.isRequestCallBack,
                premium = HotelUtil.isPremiumExperience(),
                semanticSearchDetails = requestDetailsData.semanticSearchDetails,
                isListAllPropCall = requestDetailsData.isABO,
                callBackType = requestDetailsData.callBackType,
                myraMsgId = requestDetailsData.myraMsgId,
                subPageContext = requestDetailsData.subPageContext,
                checkDuplicateBooking = requestDetailsData.checkDuplicateBooking,
                flowIdentifier = requestDetailsData.flowIdentifier
        )
    }

    private fun getTrafficSource(zcpDataString : String? = null): TrafficSource? {
        zcpDataString?.let {
            return TrafficSource(type = HotelFunnel.HOMESTAY.funnelName.lowercase(), source = HotelDeepLinkKeys.ZCP)
        } ?: run {
            val prefUtil = SharedPreferenceUtils.getInstance()
            if (System.currentTimeMillis() - prefUtil.getLong(CoreConstants.KEY_CMP_TIME) <= HotelConstants.TIME_30_MINS) {
                return TrafficSource(type = HotelConstants.KEY_CMP, source = prefUtil.getString(CoreConstants.KEY_CMP_DATA))
            }
        }

        return null
    }

    private fun getCouponCount(): Int {
        return if (LoginUtils.isCorporateUser) {
            1
        } else {
            HotelSharedPrefUtil.instance.getInt(KEY_HTL_NUMBER_OF_COUPON_ON_REVIEW, 10)
        }
    }

    private fun getRequestIdContext(): String {
        return if (LoginUtils.isCorporateUser) {
            HotelRequestConstants.ID_CONTEXT_B2B
        } else {
            HotelRequestConstants.ID_CONTEXT_B2C
        }
    }

}