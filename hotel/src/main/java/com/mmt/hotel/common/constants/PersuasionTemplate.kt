package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.PersuasionActionType.Companion.BOTTOM_SHEET
import com.mmt.hotel.common.constants.PersuasionActionType.Companion.DEEP_LINK
import com.mmt.hotel.common.constants.PersuasionActionType.Companion.LOGIN
import com.mmt.hotel.common.constants.PersuasionActionType.Companion.WEB_VIEW
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.ANIMATED_IMAGE_TEXT
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.BLACK_CARD_BOTTOM
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.DEAL_BOX_BUTTON
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.DEAL_BOX_DOUBLE_SPLIT
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.DEAL_BOX_IMAGE_TEXT
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.DEAL_BOX_PARTITION
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.DEAL_BOX_TIMER
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.IMAGE_TEXT_H
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.IMAGE_TEXT_V
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.LONGSTAY_CARD_BOTTOM
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.MULTI_PERSUASION_H
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.MULTI_PERSUASION_V
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.OVAL
import com.mmt.hotel.common.constants.PersuasionTemplate.Companion.TEXT_WITH_BG_IMAGE

/**
 * Created by Gurtek Singh on 04/01/21.
 */
@StringDef(value = [IMAGE_TEXT_H,
    IMAGE_TEXT_V,
    MULTI_PERSUASION_V,
    MULTI_PERSUASION_H,
    ANIMATED_IMAGE_TEXT,
    DEAL_BOX_IMAGE_TEXT,
    DEAL_BOX_DOUBLE_SPLIT,
    DEAL_BOX_PARTITION,
    DEAL_BOX_BUTTON,
    DEAL_BOX_TIMER,
    BLACK_CARD_BOTTOM,
    LONGSTAY_CARD_BOTTOM,
    TEXT_WITH_BG_IMAGE,
    OVAL])
annotation class PersuasionTemplate {
    companion object {
        const val IMAGE_TEXT_H = "IMAGE_TEXT_H"
        const val IMAGE_TEXT_V = "IMAGE_TEXT_V"
        const val MULTI_PERSUASION_V = "MULTI_PERSUASION_V"
        const val MULTI_PERSUASION_H = "MULTI_PERSUASION_H"
        const val ANIMATED_IMAGE_TEXT = "ANIMATED_NOTG_TEMPLATE"
        const val DEAL_BOX_IMAGE_TEXT = "DEAL_BOX_IMAGE_TEXT"
        const val DEAL_BOX_DOUBLE_SPLIT = "DEAL_BOX_DOUBLE_SPLIT"
        const val DEAL_BOX_PARTITION = "DEAL_BOX_PARTITION"
        const val DEAL_BOX_BUTTON = "DEAL_BOX_BUTTON"
        const val DEAL_BOX_TIMER = "DEAL_BOX_TIMER"
        const val BLACK_CARD_BOTTOM = "BLACK_CARD_BOTTOM"
        const val LONGSTAY_CARD_BOTTOM = "LONGSTAY_CARD_BOTTOM"
        const val TEXT_WITH_BG_IMAGE = "TEXT_WITH_BG_IMAGE"
        const val OVAL = "OVAL"
        const val MULTI_PERSUASION_FLEX = "MULTI_PERSUASION_FLEX"
        const val TEXT_IMG = "TEXT_IMG"
        const val MULTI_PERSUASION_CAROUSEL = "MULTI_PERSUASION_CAROUSEL"
        const val MULTI_PERSUASION_V_ANIMATION = "MULTI_PERSUASION_V_ANIMATION"
    }
}

@StringDef(value = [WEB_VIEW, DEEP_LINK, LOGIN, BOTTOM_SHEET])
annotation class PersuasionActionType{
    companion object{
        const val WEB_VIEW = "WEBVIEW"
        const val DEEP_LINK = "DEEPLINK"
        const val LOGIN = "LOGIN"
        const val BOTTOM_SHEET = "BOTTOMSHEET"
    }
}