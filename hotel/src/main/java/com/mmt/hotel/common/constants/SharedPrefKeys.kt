package com.mmt.hotel.common.constants

object SharedPrefKeys {
    const val EXP_RNK_ORDR = "exp_rnk_ordr"
    const val EXP_RNK_ALGO_VER = "exp_rnk_algo_ver"
    const val KEY_TRAVELING_PURPOSE = "key_traveling_purpose"
    const val KEY_TRAVELING_PURPOSE_OPTED = "key_traveling_purpose_opted"

    const val VISITOR_NUMBER = "visitor_number"

    const val KEY_HTL_CHARITY_ADDON_TITLE = "key_htl_charity_Addon_title"
    const val KEY_HTL_EMPERIA_USER_LOCATION = "key_htl_emperia_user_location"
    const val KEY_HTL_EMPERIA_LANDING_DATA = "key_htl_emperia_landing_data"

    const val LAST_REVIEW_HIT_TIME = "lastReviewHitTime"
    const val LAST_SAVED_TIME_FOR_MOB = "last_saved_time_for_mob"

    const val KEY_HTL_FILTER_TOOLTIP_VISIT_COUNT = "htl_star_tooltip_visit_count"

    const val OMNITURE_HOTEL_DH_VISIT_COUNT = "omniture_hotel_dh_visit_count"
    const val OMNITURE_HOTEL_IH_VISIT_COUNT = "omniture_hotel_ih_visit_count"

    const val KEY_HTL_MIN_DISCOUNT_PERCENT = "htl_min_discount_percent"

    const val ADDONS_DETAILS_COUNT = "addons_details"


    const val PRE_CHAT_TOOL_TIP_SHOWN_COUNT = "PRE_CHAT_TOOL_TIP_DAY_COUNT"
    const val CHAT_BOT_TOOL_TIP_SHOWN_COUNT = "CHAT_BOT_TOOL_TIP_SHOWN_COUNT"
    const val CHAT_BOT_TOOL_TIP_SHOWN_IN_SESSION = "CHAT_BOT_TOOL_TIP_SHOWN_IN_SESSION"
    const val CHAT_BOT_ICON_CLICKED_ONCE = "CHAT_BOT_ICON_CLICKED_ONCE"


    const val KEY_HTL_LAST_SAVED_REQUEST = "htl_last_saved_request"

    const val KEY_HTL_ECO_FRIENDLY_DETAIL_MSG = "key_htl_eco_friendly_detail_msg"
    const val KEY_HTL_MANUAL_RATING_MESSAGE = "key_htl_manual_rating_message"
    const val KEY_HTL_MMT_BLACK_WEB_URL = "key_htl_mmt_black_we_url"


    const val KEY_HTL_THANK_YOU_CONFIG_STRING = "key_htl_thank_you_config_strings"


    const val KEY_HTL_CHECKOUT_ERRORS = "htl_checkout_errors"
    const val KEY_HTL_APP_UPDATE_PREF = "key_hotel_app_update_pref"
    const val KEY_IMG_SIZE_FACTOR = "key_img_size_factor"
    const val KEY_SHORT_STAYS_ZONE_ZOOM_MAP = "key_short_stays_zone_zoom_map"
    const val KEY_DEFAULT_DAYUSE_CHECKIN = "key_default_dayuse_checkin"
    const val KEY_GCC_PROMO_CONSENT = "key_gcc_promo_consent"
    const val KEY_IMAGE_SEQUENCE_ALT_ACCO_V2 = "key_image_sequence_alt_acco_v2"
    const val ALT_ACCO_SESSION_MODEL = "alt_acco_session_model"
    const val TOTAL_ALT_ACCO_SESSIONS = "total_alt_acco_sessions"

    const val MULTIWAVE_PROGRESS_COUNT = "multiwave_progress_count"

    const val GROUP_BOOKING_CONFIG = "group_booking_config"
    const val PROPERTY_CERTIFICATES_CONFIG = "property_certificates_config"
    const val DETAIL_SEARCH_SUGGESTIONS = "detail_search_suggestions"
    const val ADDONS_LISTING_COUNT = "addons_listing"
    const val NOTIFICATION_COUPON_CITY = "notification_coupon_city"
    const val LAST_NOTIFICATION_COUPON_TIME = "notif_coupon_time"
    const val NOTIFICATION_COUPON = "notifCoupon"

    const val MATCH_MAKER_CITIES = "mm_cities"

    const val PRICE_EXPERIMENT_KEY_B2C = "price_experiment_key_b2c"
    const val PRICE_EXPERIMENT_KEY_B2B = "price_experiment_key_b2b"
    const val PRICE_EXPERIMENT_KEY_GCC = "price_experiment_key_gcc"

    const val FILTER_BANNER_MESSAGE = "filter_banner_message"
    const val HC_API_APP_VERSION = "hotel_config_api_app_version"
    const val HC_VARIANT = "hotel_config_variant"
    const val LAST_HOTEL_CONFIG_FETCH_TIME = "last_htl_config_fetch"
    const val LAST_HOTEL_CONFIG_LANGUAGE = "last_htl_config_language"
    const val HC_API_VERSION = "hotel_config_api_client_version"
    const val HC_SYNC_IN_PROGRESS = "hotel_config_sync_in_progress"
    const val KEY_LANGUAGE_MESSAGE_SHOW = "hotel_language_message_show"
    const val LANDING_PAX_CONFIG = "landing_pax_config"
    const val SLOT_FILTERS = "slot_filters"
    const val GB_MIN_ROOM_V2 = "group_min_room_v2"
    const val KEY_HOTEL_REQ_COUNT = "key_hotel_req_count"
    const val KEY_DEFAULT_HOTELS_FETCH_COUNT = "default_hotels_fetch_count"
    const val WITH_IN_DAYS = "within_range"
    const val AP_CRITERIA = "ap_criteria"
    const val LAST_SEARCH_TIME = "last_search_time"
    const val LAST_CHECK_IN_TIME = "last_check_in"
    const val KEY_HTYPE_LOCATION_ICON_MAP = "key_htype_location_icon_map"
    const val CHILD_PAX_INFO = "child_pax_info"
    const val EASY_SELECTION_SESSION_COUNT_CONTROL = "easy_selection_session_count_control"
    const val HOTEL_CARD_DOT_MENU_DATA = "hotel_card_dot_menu_data"
    const val HOTEL_NOT_INTERESTED_HOTEL_LIST = "HOTEL_NOT_INTERESTED_HOTEL_LIST"

    const val CONNECT_OPTION = "connect_option"

    const val KEY_UGC_EMOJI_GIF_URLS = "key_ugc_emoji_gif_urls"
    const val HOTEL_TREEL_GATEWAY_DATA = "HOTEL_TREEL_GATEWAY_DATA"
    const val HOTEL_TREEL_PLAYER_SOUND = "HOTEL_TREEL_PLAYER_SOUND"
    const val ONBOARDING_VIDEO_PLAYED = "ONBOARDING_VIDEO_PLAYED_COUNT"


    const val KEY_EASY_SELECTION_BS_SHOWN_COUNT_HOTEL = "easy_selection_hotel_bs_shown_count_HOTELS"
    const val KEY_EASY_SELECTION_BS_SHOWN_COUNT_HOMESTAY = "easy_selection_hotel_bs_shown_count_HOMESTAY"
    const val KEY_EASY_SELECTION_BS_SHOWN_COUNT_HOTEL_B2B = "easy_selection_hotel_bs_shown_count_HOTELS_B2B"


    const val LOADERS = "LOADERS"
    const val LISTING_ACTION_INFO = "LISTING_ACTION_INFO"
    const val PET_FRIENDLY_FILTER = "PET_FRIENDLY_FILTER"

    const val HTL_PROPERTY_LAYOUT_INFO = "HTL_PROPERTY_LAYOUT_INFO"
    const val HTL_STAR_RATING_INFO = "HTL_STAR_RATING_INFO"
    const val ENTIRE_PROPERTY_TEXT = "ENTIRE_PROPERTY_TEXT"
    const val TRAVEL_TIPS_INFO = "TRAVEL_TIPS_INFO"
    const val AUTO_SUGGEST_HINT = "AUTO_SUGGEST_HINT"
    const val DETAIL_FAB_WIDGET_INFO = "DETAIL_FAB_WIDGET_INFO"
    const val MULTIROOM_CONFIG = "MULTIROOM_CONFIG"
    const val FLEXI_WITH_ROOM_COUNT = "FLEXI_WITH_ROOM_COUNT"
    const val TIME_RANGE_INFO = "TIME_RANGE_INFO"
    const val PREMIUM_NEAR_ME_CARD_INFO = "PREMIUM_NEAR_ME_CARD_INFO"
    const val COLLECTION_LISTING = "COLLECTION_LISTING"


    const val HTL_CALL_TO_BOOK_TIMESTAMP = "HTL_CALL_TO_BOOK_TIMESTAMP"
    const val HTL_WISHLIST_TOOLTIP_SHOWN_COUNT = "HTL_WISHLIST_TOOLTIP_SHOWN_COUNT"
    const val HTL_LAST_WISHLIST_TOOLTIP_SHOWN_SESSION_ID = "HTL_LAST_WISHLIST_TOOLTIP_SHOWN_SESSION_ID"

    const val KEY_VIEW_360_COACH_MARK_SHOWN = "view_360_coach_mark_shown"
    const val KEY_VIEW_360_COACH_MARK_SHOWN_COUNT = "view_360_coach_mark_shown_count"
    const val KEY_STREET_VIEW_COACH_MARK_SHOWN_COUNT = "street_view_coach_mark_shown_count"
    const val KEY_STREET_VIEW_COACH_MARK_SHOWN = "street_view_coach_mark_shown"
    const val KEY_STREET_HOTEL_ID = "street_hotels_id"
    const val KEY_USER_FEEDBACK_HOTEL_ID = "user_feedback_hotels_id"

    const val B2C_USER_SELECTED_CURRENCY = "B2C_USER_SELECTED_CURRENCY"
    const val GCC_USER_SELECTED_CURRENCY = "GCC_USER_SELECTED_CURRENCY"

    const val HOTEL_LANDING_CARDS_PRIORITY = "HOTEL_LANDING_CARDS_PRIORITY"

    const val LONG_STAY_NUDGE = "long_stay_nudge"
    const val LONG_STAY_GCC_NUDGE = "long_stay_gcc_nudge"

    const val FLEXI_CHECKIN_EDUCATION_INFO = "FLEXI_CHECKIN_EDUCATION_INFO"

    const val PREMIUM_BOTTOM_SHEET_SHOWN_IN_SESSION = "PREMIUM_BOTTOM_SHEET_SHOWN"
    const val PREMIUM_BOTTOM_SHEET_SHOWN_COUNT = "PREMIUM_BOTTOM_SHEET_SHOWN_COUNT"

    const val WORKSTAYS_BOTTOMSHEET_SHOWN_IN_SESSION = "WORKSTAYS_BOTTOMSHEET_SHOWN_IN_SESSION"
    const val WORKSTAYS_BOTTOMSHEET_SHOWN_COUNT = "WORKSTAYS_BOTTOMSHEET_SHOWN_COUNT"

    const val KEY_ALTERNATE_DATES_DISMISSED_DATE = "key_alternate_dates_dismissed_date"
    const val KEY_ALTERNATE_DATES_DISMISSED_COUNT_IN_SESSION = "key_alternate_dates_dismissed_count_in_session"

    const val RUSH_DEALS_BOTTOM_SHEET_SHOWN_IN_SESSION = "RUSH_DEALS_BOTTOM_SHEET_SHOWN"
    const val CHAT_BOT_REDIRECT_DELAY = "CHAT_BOT_REDIRECT_DELAY"

    const val COLLECTION_LISTING_BOTTOM_SHEET_SHOW = "COLLECTION_LISTING_BOTTOM_SHEET_SHOW"
    const val BNPL_INTERMEDIATERY_SCREEN_INFO = "BNPL_INTERMEDIATERY_SCREEN_INFO"
    const val SPOTLIGHT_INFO = "SPOTLIGHT_INFO"

    const val SHOW_TRANSLATION_FOR_REVIEW = "show_translation_for_review"
    
    const val HTL_VOICE_INPUT_SPEAKER = "HTL_VOICE_INPUT_SPEAKER"
}