package com.mmt.hotel.common.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
*  data class for drawing hotel tags
*  @param iconUrl can be null, otherwise, image should be downloaded from url
*  @param iconResId can be null, otherwise, show bundled icon
*  @param all colorCodes must be proper hexCodes
*  @param startColor & @param endColor are added to support gradient drawable, incase, uniform background is required use same values for both params
**/
@Parcelize
data class HotelCategoryNativeTagData(val iconUrl: String?,
                                      val iconResCode: String?,
                                      val title: String?,
                                      val displayText:String?,
                                      val borderColor: String?,
                                      val startColor: String? = "#ffffff",
                                      val endColor: String? = "#ffffff",
                                      val textColor: String? = "#4a4a4a") : Parcelable