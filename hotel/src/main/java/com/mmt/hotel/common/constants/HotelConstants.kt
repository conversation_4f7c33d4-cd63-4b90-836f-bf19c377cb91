package com.mmt.hotel.common.constants

import androidx.annotation.StringRes
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.util.HtlUrlConstants
import com.mmt.hotel.deeplink.constants.HotelDeepLinkKeys
import com.mmt.hotel.listingV2.adapter.ListingAdapterTypes

object HotelConstants {


    const val LOB_NAME = "HOTEL"

    const val BUNDLE_USER_REVIEW_GENERATION = "BUNDLE_USER_REVIEW_GENERATION"

    const val GETAWAYS_FLAG = HotelDeepLinkKeys.FUNNEL + CoreConstants.EQUAL_SIGN + "GETAWAY"

    const val PREMIUM_FUNNEL = "PREMIUM"

    const val AUTOSUGGEST_COLLECTION_SUFFIX_TRUE = "&sf=true"
    const val AUTOSUGGEST_UI_VERSION_SUFFIX = "&expui="
    const val AUTOSUGGEST_UI_V2 = "v2"
    const val AUTOSUGGEST_EXP_SCORE_SUFFIX = "&exps="
    const val AUTOSUGGEST_SEARCH_GOOGLE_RESULTS_SUFFIX = "&sgr=" // value : t/f | remove Google Places results from auto suggest or not
    const val AUTOSUGGEST_COUNTRY_CLICKABLE_SUFFIX = "&cc="
    const val AUTOSUGGEST_HIGHLIGHT_COUNTRY_NAME_SUFFIX = "&hcn=1"  // default value is set to true
    const val AUTOSUGGEST_MASKED_PROPERTY_NAME_SUFFIX = "&mpn="  // default value is set to true
    const val AUTOSUGGEST_SEMANTIC_SEARCH_PARAM = "&resultType=all"
    const val INSTAGRAM = "HOTEL_INSTAGRAM"
    const val FETCH_EMI_IDENTIFIER = "COUPON_GREEN_STRIP"

    const val HOTEL_DETAIL_CACHE_KEY_SEPARATOR = "_"

    const val AKAMAI_BOT_HEADER = "mmt-bot-details"

    const val COUNTRY_CODE_UNKNOWN = "UNKNOWN"
    const val COUNTRY_CODE_INDIA = "IN"
    const val CURRENCY_CODE_INDIA = "INR"

    const val PERSUASION_TRACK_PIPE = "|"
    const val HTTPS_STRING = "https:"
    const val HTTP_PREFIX = "http:"
    const val BOOKING_DETAIL = "bookingDetail"
    const val LISTING_MAP_REQUEST_HOTEL_LIMIT = 20
    const val VALID_PARAM_LENGTH_FOR_AREA_DEEPLINK = 2
    const val VALID_PARAM_LENGTH_FOR_POI_WITHOUT_LAT_LONG = 3
    const val VALID_PARAM_LENGTH_FOR_POI_GOOGLE_DEEPLINK = 5
    const val ESCAPED_PIPE = "\\|"
    const val TILDE_STRING = "~"
    const val LOCUS_GOOGLE_POI_DEEPLINK = "LPOI"
    const val LOCUS_UNKNOWN_ID_TAG = "UnknownID"


    const val SEARCH_DATE_FORMAT = "MMddyyyy"
    const val CURRENT_DATE_FORMAT = "MMddyyyy"
    const val HOTEL_FUNNEL_DATE_FORMAT = "MMddyyyy"
    const val SERVER_DATE_FORMAT = "yyyy-MM-dd"
    const val NEW_DATE_FORMAT = "yyyy-MM-dd"
    const val LISTING_DATE_FORMAT = "MMM dd"
    const val DETAILS_CHECKIN_DATE_FORMAT = "dd MMM"
    const val DAY_YEAR_FORMAT = "E, yyyy"
    const val YEAR_DAY_FORMAT = "yyyy, EEE"
    const val DAY_MONTH_DAYNAME_FORMAT = "dd MMM, EEE"
    const val DAY_MONTH_YEAR_FORMAT = "dd MMM yy"
    const val DAY_FORMAT = "dd"
    const val YEAR_FORMAT = "yyyy"
    const val DAY_MONTH_YEAR_DISPLAY_FORMATTER = "dd MMM, yyyy"
    const val DATE_FORMAT_dd_MM_yyyy_HH_mm_ss = "dd-MM-yyyy HH:mm:ss"
    const val DATE_FORMAT_yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss"
    const val DATE_FORMAT_MMM_dd_yyyy = "MMM dd, yyyy"



    const val HH_A_TIME_FORMAT = "h a"
    const val HH_TIME_FORMAT = "HH"
    const val HH_MM_A_TIME_FORMAT = "hh:mm a"
    const val HH_MM_24HR_TIME_FORMAT = "HH:mm"
    const val YY_E = "‘yy, E"

    const val TRAVELLER_TYPE_LEISURE = 1
    const val TRAVELLER_TYPE_BUSINESS = 2

    const val FORCE_COMPARATOR_UUID = ":COMPARATOR_HOTEL_DETAIL"

    const val SPACE_URL_ENCODED_LABEL = "%20"
    const val QUERY_PARAMETER_SEPARATOR = "?"
    const val QUERY_STRING_DATA_SEPARATOR = "&"
    const val MMT_DOWNSIZE_HEIGHT_WIDTH_REGEX = "downsize=[0-9]*:[0-9]*[&]*"
    const val MMT_CROP_PARAM_REGEX = "crop=[0-9]*:[0-9]*;[0-9]*,[0-9]*[&]*"
    const val MMT_DOWNSIZE_WIDTH_PARAM = "downsize="


    const val DEFAULT_TRAVELLER_TYPE_LEISURE = "Leisure"
    const val DEFAULT_TRAVELLER_TYPE_NONE = "None"
    const val DEFAULT_TRAVELLER_TYPE_BUSINESS = "Business"

    const val BUNDLE_PRE_SELECTED_CITY = "bundle_pre_selected_city"

    const val FIRST_NAME_REGEX = "^[a-zA-Z\\s.]{2,}$"
    const val LAST_NAME_REGEX = "^[a-zA-Z\\s.]+$"
    const val IND_PHONE_REGEX = "^[0-9]{10}$"
    const val PHONE_REGEX = "^[0-9]{5,13}$"
    const val DOT_AND_SPACE_REGEX = "^[\\s.]{1,}$"
    const val PAN_REGEX = "^([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}?$"
    const val FLIGHT_GST_REGEX = "flight_gst_regex"
    const val DEFAULT_GST_REGEX = "^([0-9]){2}([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}([0-9a-zA-Z]){1}([a-zA-Z]){1}([0-9a-zA-Z]){1}?$"

    const val PAH_ERROR_CODE = "400301"
    const val SOLD_OUT_CODE = "204"


    const val CHILD_AGE_QUALIFYING_VALUE = "8"
    const val ADULT_AGE_QUALIFYING_VALUE = "10"

    const val HOTEL_MIN_DISCOUNT_PERCENT = 10

    const val CORPORATE_APPROVAL_DATE_FORMAT = "E, dd MMM yyyy, hh:mm a"

    const val PROPERTY_TYPE_HOTEL = "HOTEL"
    const val MY_TRIPS_REDIRECT_URL = "mmyt://mytrips"
    const val THANKYOU_PAGE = "THANKYOU"
    const val LOCATION_TYPE = "LOCATION"

    val ET_NORMAL_BG = R.drawable.htl_traveller_input_text_bg
    val ET_ERROR_BG = R.drawable.htl_traveller_input_text_error_bg

    val DEFAULT_TITLE = ResourceProvider.instance.getStringArray(R.array.TRAVELLER_TITLE)[0]
    val FEMALE_TITLE = ResourceProvider.instance.getStringArray(R.array.TRAVELLER_TITLE)[1]

    const val HOTEL_LAT_PARAM = "lat="
    const val HOTEL_LONG_PARAM = "&lng="

    const val POPULAR_AREA_TAG_TYPE_ID = 3
    const val POPULAR_AREA_CATEGORY_ID = 6

    const val MODIFY_SEARCH = 2022
    const val REVIEW_ACTIVITY = 2023
    const val STORY_VIEW_ACTIVITY = 2024
    const val KEY_HOTEL_SEARCH_REQUEST_DATA_HELPER = "KEY_HOTEL_SEARCH_REQUEST_DATA_HELPER"
    const val BUNDLE_HOTEL_SEARCH_REQUEST = "HOTELSEARCHREQUEST"
    const val FROM_LISTING = "from_listing"
    const val TRAVELLING_WITH_PETS = "TRAVELLING_WITH_PETS"
    const val IS_FLEXI_WITH_ROOM_COUNT = "IS_FLEXI_WITH_ROOM_COUNT"
    const val STAYCATION = "staycation"
    const val CITY = "city"
    const val FILTER = "filter"
    const val ACTION_APPLY = "apply_filter"
    const val SELLABLE_TYPE_BED = "bed"

    const val MIN_GROUP_BOOKING_ROOMS = 5
    const val PROPERTY_TYPE_HOSTEL = "Hostel"

    const val SEARCH_CTY_TAG = "City"
    const val SEARCH_REGIONS_TAG = "Region"
    const val SEARCH_AREA_TAG = "Area"
    const val SEARCH_HTL_TAG = "Hotel"
    const val SEARCH_MM = "MM"
    const val ON = "ON"

    const val ROOM_QUALIFIER_TOKENIZER = "e"

    const val VALUE_STAY_TYPE = "VALUE_STAYS"
    const val MMT_ASSURED_NAME = "MMT Assured"
    const val MONEY_BACK_GUARANTEE_NAME = "VERIFIED STAYS ALT ACCO"
    const val MMT_MYBIZ_ASSURED = "MyBiz Assured"
    const val MMT_LUXE = "MMT_LUXE"
    const val HIDDEN_GEMS = "HIDDEN_GEMS"
    const val MMT_VALUE_STAYS = "MMT_VALUE_STAYS"
    const val ECO_FRIENDLY = "ECO FRIENDLY"
    const val MMT_VALUE_STAYS_CATEGORY = "Value Stays"
    const val COUPLE_FRIENDLY_CATEGORY_NAME = "Couple Friendly"
    const val SHOW_FILTER_BOTTOMSHEET = "showFilterBottomSheetBasedOnFlow"

    const val KEY_HOTEL_SEARCH_REQUEST_V2 = "HOTEL_SEARCH_REQUEST_V2"
    const val KEY_HOTEL_LANDING_TOP_CARD = "KEY_HOTEL_LANDING_TOP_CARD"
    const val SAVE_RECENT_SEARCH_ONLINE = "SAVE_RECENT_SEARCH_ONLINE"

    const val AREA_FIELD_EDITABLE = "AREA_FIELD_EDITABLE"
    const val IS_FROM_APP_LANDING = "IS_FROM_LANDING"
    const val IS_FROM_APP_GCC_LANDING = "IS_FROM_APP_GCC_LANDING"
    const val OPEN_SHORTSTAY_LISTING = "OPEN_SHORTSTAY_LISTING"
    const val UPDATE_STATUS_BAR = "UPDATE_STATUS_BAR"
    const val REQUEST_TRANSPARENT_BACKGROUND = "REQUEST_TRANSPARENT_BACKGROUND"
    const val OPEN_CHECK_IN = "openCheckIn"
    const val LAUNCH_IN_FUNNEL = "LAUNCH_IN_FUNNEL"
    const val SHOW_BOTTOM_BAR = "SHOW_BOTTOM_BAR"
    const val IS_INVALID_DEEPLINK = "IS_INVALID_DEEPLINK"

    const val CONFIRMED_LATER_TARIFF = "NON_INSTANT"
    const val CONFIRMED_INSTANT_TARIFF = "INSTANT"

    const val ROOM_BOOKING_PLAN_TYPE_FREE_CANCELLATIION = "FC"
    const val ROOM_BOOKING_PLAN_TYPE_NON_REFUNDABLE = "NR"

    const val PROPERTY_TYPE_ENTIRE = "entire"
    const val MMT_BLACK_INCLUSION_IDENTIFIER = "black"

    const val VENDOR_STATE_CODE  = "vendorStateCode"
    const val KEY_PERSONAL_BOOKING = "personalBooking"

    const val PAYMENT_DOM_HOTEL_PRODUCT = "Hotel"
    const val PAYMENT_INTL_HOTEL_PRODUCT = "HotelIntl"

    const val NOTIFICATION = "notification"
    const val AREA = "area"

    const val EARLY_CHECK_IN_SHORT_DESC = "early_cin"

    const val CHECKOUT_RESPONSE = "checkout_response"

    const val PAH_PAYMENT_ACTIVITY_REQUEST_CODE = 1555
    const val BUNDLE_FROM_REVIEW_FRAUD = "FROM_REVIEW_FRAUD"

    const val PICASSO_LIST_ITEM_PREFETCH_TAG = "picasso_list_item_prefetch_tag"
    const val PICASSO_IMAGE_PREFETCH_TAG = "picasso_image_prefetch_tag"
    const val PICASSO_SR_IMAGES_PREFETCH_TAG = "picasso_sr_images_prefetch_tag"
    const val PICASSO_ROOM_DETAIL_IMAGES_PREFETCH_TAG = "picasso_room_detail_images_prefetch_tag"

    var KEYBOARD_HT_THRESHOLD: Float = CoreUtil.convertDpToPixel(100f)

    const val AUTH_EXPIRY = "3401"
    const val SEARCH_PRICE_CACHE_EXPIRED = "400136"
    const val SOLD_OUT = "400847"
    const val SOLD_OUT_RTB = "400859"
    const val SOLD_OUT_MLOS = "400858"
    const val SOLD_OUT_INADEQUATE_QUALITY = "400860"
    const val AVAILABLE_BUT_SOLD_OUT = "400857"
    const val AVAILABLE_BUT_OFFLINE = "400894"
    const val LISTING_ERROR_NO_MORE_HOTELS = "400814"
    const val LISTING_ERROR_NO_MORE_HOTELS_2 = "400816"

    const val UTF_8 = "UTF-8"

    const val NO_RATING = "0.0"
    const val MAX_RATING = 5

    const val SMALL_ROOM_QUALIFIER = "E"

    const val ATHENA_ALL_CATEGORY = "All"

    const val SHOW_PAN_CARD_AND_USD = 2
    const val PAY_ENTIRE_EMI_BNPL = 4
    const val PAH_ONLY = "5"
    const val IMG_SEQ = "1234"

    const val DETAIL_V3_MAX_HIGHLIGHTED_AMENITIES = 3
    const val DEEP_LINK_INTENT_DATA = "deep_link_intent_data"
    const val APPROVAL_ID = "approvalid"
    const val CORP_HOTEL_SOLD_OUT_CODE = "204"
    const val UNAUTHORISED_USER = "400378"
    const val TARIFF_SOLD_OUT = "400847"
    const val PICASSO_DETAIL_IMAGES_PREFETCH_TAG  = "PICASSO_DETAIL_IMAGES_PREFETCH_TAG"
    const val ADDONS_LISTING_DEFAULT_COUNT = 1
    const val ADDONS_DETAILS_DEFAULT_COUNT = 3
    const val IS_HOTEL_SEARCH_REQUEST_MODIFIED = "is_hsr_modified"
    const val GROUP_BOOKING = "GroupBooking"
    const val CURRENCY_PARAM = "_uCurrency"
    const val TYPE = "type"
    const val APP_VERSION = "appVersion"
    const val DEVICE_ID = "deviceId"
    const val BOOKING_DEVICE = "bookingDevice"
    const val DEVICE_TYPE = "deviceType"
    const val VISITOR_ID = "visitorId"
    const val VISITOR_NUMBER = "visitorNumber"
    const val FUNNEL_SOURCE = "funnelSource"
    const val ID_CONTEXT = "idContext"
    const val DEFAULT_MAX_CHILD_AGE = 12

    const val TIME_30_MINS = 30 * 60 * 1000L
    const val KEY_CMP = "CMP"
    const val SEARCH_HOTEL = "HTL"

    const val GETAWAYS_SUFFIX_FALSE = "&z=false"
    const val EXP_PARAM = "&exp="

    const val SHORT_STAYS_LOCATION_SELECTION_CACHE_KEY = "shorts_stay_1"
    const val HOTEL_COLLECTION_RESPONSE_CACHE_CACHE_KEY = "hotel_collection_1"
    const val SHORT_STAY_COLLECTION_RESPONSE_CACHE_KEY = "short_stay_2"
    const val ALT_ACCO_AUTO_SUGGEST_PARAMS = "&a=true"
    const val PLACE_ID_PARAM = "placeid="

    const val AUTO_SUGGEST_THRESHOLD_QUERY_SIZE = 3
    const val AUTO_SUGGEST_RECENT_SEARCH_MAX_ITEMS = 10

    const val CONTENT_TYPE_JSON = "application/json"

    const val API_KEY = "api-key"
    const val HOTSTORE_MAPI_API_KEY = "HOTELS-ANDROID-81520512191144181594"

    const val USER_IDENTIFIER = "User-Identifier"
    const val AUTHORIZATION = "Authorization"
    const val USER_IDENTIFIER_AUTH = "auth"
    const val AUTHORIZATION_KEY = "Oe0\$J%1+R^7G"
    const val LOCATION_SERVICES_REQUEST_CODE = 20000
    const val LOCATION_SERVICES_REQUEST = 1005

    const val NEAR_ME_DISPLAY_TEXT = "Near Me"

    const val AREA_SEARCH_TAG_ID = -1
    const val AREA_SEARCH_CATEGORY_ID = 6
    const val AREA_SEARCH_TAG_TYPE_ID = 3
    const val PERSONALIZED_SECTIONS_SIZE_ONE = 1
    const val HOTEL_COUNT_ONE = 1

    const val PAGE_NAME_LISTING = "listing"
    const val PAGE_NAME_DETAILS = "details"
    const val PAGE_NAME_SELECT_ROOM = "select_room"
    const val PAGE_NAME_REVIEW = "review"
    const val PAGE_NAME_LANDING = "landing"
    const val PAGE_NAME_THANK_YOU = "thank_you"
    const val PAGE_NAME_PAH_PAY = "pah_pay"
    const val IS_FROM_DEEPLINK = "is_from_deeplink"
    const val SEARCH_ZONE = "zone"
    const val ROAD_TRIP = "roadtrip"
    const val PAGE_NAME_HOTEL_FULL_SCREEN_GALLERY = "image_gallery"

    const val PAGE_NAME_TREELS_LISTING = "treels"
    const val PAGE_NAME_AUTO_SUGGEST = "autoSuggest"


    const val HOTEL_LISTING_SEARCH_BUTTON = "hotelListingSearchButton"

    const val COOK = "cook"

    const val CHECKOUT_ERROR_PRICE_CHANGE_CODE = "116"
    const val CHECKOUT_ERROR_SOLD_OUT_CODE = "117"
    const val CORP_APPROVAL_REQUEST_EXPIRY_CODE = "7201"

    const val HOTEL_LISTING_PAGE_DEFAULT_LIMIT = 10
    const val SHORTSTAY_MAP_PAGE_DEFAULT_LIMIT = 20

    const val HOTEL_LISTING_SIMILAR_HOTEL_DEFAULT_LIMIT = 5

    const val LOCATION_ARRAY_SIZE = 2
    const val TWO_DIGIT_NUMBER_FORMAT = "%1$02d"
    const val HOMESTAY_EMPERIA_OFFERS_CARD_KEY = "HOMESTAYS_OFFERS_V2_B2C_IN_DEF"

    const val AUTOBOOK_COUPON_ERROR_CODE = "400188"
    const val AUTOBOOK_BOOKING_STATUS = "autobook_in_progress"
    const val APPROVAL_ALREADY_CANCELLED_RESPONSE_CODE = "ARCR"
    const val APPROVAL_ALREADY_APPROVED_RESPONSE_CODE = "ARAR"

    const val WISHLIST_DEEPLINK_DELETE_PARAM = "delete=false"

    const val GREATER_THAN = ">"
    const val LESS_THAN = "<"
    const val SLOT_FILTERS = "slot_filters"
    const val KEY_SOURCE = "source"
    const val ERROR_FRAG_ARGS: String = "error_info"
    const val DEFAULT_HOTELS_STORY_COUNT = 3
    const val DETAIL_STORY_CARD_BG_ALPHA = 0.4f
    const val LISTING_STORY_CARD_BG_ALPHA = 0.6f
    const val CATEGORY_VALUE_STAYS = "MMT Value Stays"
    const val VALUE_STAYS_PROPERTY_TYPE = "ValueStays"
    const val URL_PARAM_DOWNSIZE = "downsize"
    const val URL_PARAM_CROP = "crop"
    const val CORNER_RADIUS_16 = "16"

    const val TIMER_TIME_FORMAT = "H'h':mm'm':ss's'"

    const val BNPL_DEFAULT_VARIANT = CoreConstants.EMPTY_STRING

    const val MIN_ICON_SIZE = 12


    const val GRADIENT_HORIZONTAL = "horizontal"
    const val GRADIENT_VERTICAL = "vertical"
    const val GRADIENT_DIAGONAL_TOP = "diagonal_top"
    const val GRADIENT_DIAGONAL_BOTTOM = "diagonal_bottom"

    const val HOTEL_LISTING_MAX_IMAGE_COUNT = 20
    const val HOTEL_REVIEW_MAX_SEEK_TAGS_COUNT = 2

    // add all hotels card in list
    val hotelCards = listOf(
        ListingAdapterTypes.HOTEL,
        ListingAdapterTypes.RECENT_HOTEL,
        ListingAdapterTypes.HORIZONTAL_HOTELS,
        ListingAdapterTypes.ONE_CLICK_HOTEL,
        ListingAdapterTypes.GROUP_BOOKING_HOTEL_CARD,
        ListingAdapterTypes.GROUP_BOOKING_DIRECT_HOTEL_CARD,
        ListingAdapterTypes.HOTEL_SMALL_CARD,
        ListingAdapterTypes.DAY_USE_HOTEL_CARD,
        ListingAdapterTypes.SHORT_STAY_HOTEL_CARD,
        ListingAdapterTypes.HOTEL_WITH_LIMITED_PERSUASIONS,
    )
    const val DOMESTIC_LOB_CATEGORY = "dh"
    const val INTERNATIONAL_LOB_CATEGORY = "ih"
    const val HOTEL_JOURNEY_TYPE = "OW"
    const val GENDER_FEMALE = "F"

    const val HOTEL_BR_PRICE_GREEN_COLOR_HEX = "#007E7D"
    const val WHITE_COLOR_HEX = "#FFFFFF"
    const val BLACK_COLOR_HEX = "#000000"
    const val JOURNEY_ID = "jId"
    const val REQUEST_ID = "requestId"

    const val DEAL_TYPE_FLASH_SALE = "FLASHSALE"
    const val COUPON_CODE_COPY ="COPY_COUPON_CODE"

    const val PRIVATE_SPACE_TAB = "PRIVATE"
    const val SHARED_SPACE_TAB = "SHARED"
    const val EXPANDABLE_TEXT_DEFAULT_MINIMUM_LINE = 3


    const val GALLERY_PAGER_INDICATOR_MAX_DOTS = 5
    const val RESEND_OTP_TIMER_DURATION_IN_MILLISECONDS: Long = 30000 // 30 Second (30 * 1000)
    const val B2B_EMAIL_VERIFY = "B2B_EMAIL_VERIFY"

    const val ADD_ON_FLEXI_CANCEL = "FLEXI_CANCEL"
    const val IH_CASHBACK_CARD = "CASHBACK"
    const val MEAL = "Meal"
    const val MULTI_ROOM = "MULTI_ROOM"

    const val CALL_TO_BOOK_UI_VERSION_2 = 2
    const val WISHLIST_TOOLTIP_MAX_SHOW_COUNT = 3

    const val JSON_KEY_ERROR = "error"
    const val JSON_KEY_JS_LOAD = "jsLoaded"
    const val JSON_KEY_APP_LOADED = "appLoaded"

    const val PREMIUM_BOTTOM_SHEET_MAX_COUNT = 2

    const val INDIA_TIMEZONE_SECONDS = 19800L
    const val INDIA_TIMEZONE_MILLISECONDS = INDIA_TIMEZONE_SECONDS.times(1000L)
    const val userInfoEncryptionKey = "awefewfuihewfhewfuhiebc"
    const val CONSENT_API_LOB_NAME = "HOTELS"

    const val PERSUASION_DELAY = 3000L
    const val MYRA_EXPAND_DELAY = 5000L

    const val GUEST_HOUSE_REASON_KEY = "guestHouseReasons"
    const val POPULAR_AMENITIES_KEY = "POPULAR"

    const val ICON_BOOKING_FAILED = "https://go-assets.ibcdn.com/u/MMT/images/1741612369696-state_pending.png"

    const val KEY_LOB = "lob"



}

/**
 * class that define all possible share types like whatsapp, generic etc
 */
sealed class ShareType(val value : String) {
    object WhatsApp  : ShareType("whatsapp")
    object Generic  : ShareType("generic")
}

enum class SubConceptSentiments {
    POSITIVE, NEGATIVE
}

enum class MBGConsts(val value: String, @StringRes val defaultRes: Int){
    KEY_MBG_DETAIL_TITLE("mbg_detail_title", R.string.htl_MONEY_BACK_GUARANTEE_TITLE),
    KEY_MBG_DETAIL_TEXT("mbg_detail_text", R.string.htl_MONEY_BACK_GUARANTEE_SUBTITLE_DETAIL),
}

enum class CouponTypes(val value: String){
    BENEFIT_DEAL("BENEFIT_DEAL")
}

enum class SoldOutType {
    // BRE - Hotel booking blocked on corp due to Inadequate Service Quality
    DEFAULT, RTB, MLOS, BRE, ABSO, ABO
}

enum class UGCReviewEmojiTypes(val selectedIconURLs: String) {
    GREAT("${HtlUrlConstants.PROMOS_URL}/Hotels_product/UGC/Excellent.gif"),
    GOOD("${HtlUrlConstants.PROMOS_URL}/Hotels_product/UGC/Good.gif"),
    OK("${HtlUrlConstants.PROMOS_URL}/Hotels_product/UGC/Average.gif"),
    BAD("${HtlUrlConstants.PROMOS_URL}/Hotels_product/UGC/Poor.gif"),
    TERRIBLE("${HtlUrlConstants.PROMOS_URL}/Hotels_product/UGC/Terrible.gif")
}

enum class NATIONALITY_FIELD(val value:Int) {
    OPTIONAL(1),
    MANDATORY(2)
}

enum class GalleryTabs(val value: String){
    PROPERTY("property"),
    VIEW_360("360"),
    INSTAGRAM("instagram"),
    TRAVELLER("guest"),
    TREELS("treels")
}