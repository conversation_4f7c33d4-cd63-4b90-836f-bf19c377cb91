package com.mmt.hotel.common.data

import android.os.Parcelable
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import kotlinx.parcelize.Parcelize

@Parcelize
data class PriceScratchCardData(
    val id: String,
    val title: String,
    val slashedPrice: String,
    val discountedPrice: String,
    val priceDescription: String,
    val totalPriceText: String
): Parcelable {

    fun getPriceDescriptionLabel(): String {
        return ResourceProvider.instance.getString(R.string.htl_text_with_colon, priceDescription)
    }

}