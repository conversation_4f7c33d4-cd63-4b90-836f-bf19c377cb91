package com.mmt.hotel.common.di

import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.analytics.pdtMetrics.HotelScreenMetricsTracker
import com.mmt.hotel.landingV3.tracking.HotelLandingTracker
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import javax.inject.Named

@EntryPoint
@InstallIn(ActivityComponent::class)
interface HotelActivityEntryPoint {

    @Named(NamedConstants.METRICS_TRACKER_FOR_ENTRY_POINT)
    fun createHotelScreenMetricsTracker(): HotelScreenMetricsTracker

    @Named(NamedConstants.HOTEL_LANDING_TRACKER_FOR_ENTRY_POINT)
    fun createHotelLandingTracker(): HotelLandingTracker

    @Named(NamedConstants.ALT_ACCO_TRACKER_FOR_ENTRY_POINT)
    fun createAltAccoTracker(): AltAccoTracker
}