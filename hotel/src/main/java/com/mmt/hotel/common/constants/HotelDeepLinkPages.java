package com.mmt.hotel.common.constants;

import com.mmt.hotel.deeplink.constants.HotelDeeplinkConstants;

public enum HotelDeepLinkPages {

    HOTEL_LANDING_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_LANDING_PREFIX),
    HOMESTAY_LANDING_PAGE(HotelDeeplinkConstants.HOME_STAY_DEEP_LINK_LANDING_PREFIX),
    GETAWAYS_LANDING_PAGE(HotelDeeplinkConstants.GETAWAYS_DEEP_LINK_PREFIX),
    HOTEL_SEARCH_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_LISTING_PREFIX),
    HOTEL_DETAIL_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_DETAIL_PREFIX),
    PWA_LINK_HOTEL_DETAIL_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_DETAIL_PWA_PREFIX),
    HOTELS_LINK_HOTEL_DETAIL_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_DETAIL_HOTELS_PREFIX),
    PWA_HOTEL_SEARCH_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_SERACH_PWA_PREFIX),
    HOTEL_LISTING_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_SERACH_LISTING_PREFIX),
    HOTEL_MYREVIEW_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_MYREVIEW_PREFIX),
    RIGHT_STAY_PAGE(HotelDeeplinkConstants.RIGHT_STAY_DEEP_LINK_PREFIX),
    ACTION_LAUNCH_HOTEL_FLY_FISH_REVIEW_SUBMISSION_URLS(HotelDeeplinkConstants.HOTEL_FLY_FISH_REVIEW_SUBMISSION_URLS_PREFIX),
    ACTION_LAUNCH_HOTEL_FLY_FISH_REVIEW_QUESTIONNAIRE_URL(HotelDeeplinkConstants.HOTEL_FLY_FISH_QUESTIONNAIRE_URL_PREFIX),
    HOTEL_LISTING_WITH_HOTEL_ID(HotelDeeplinkConstants.ACTION_LAUNCH_HOTEL_DETAIL),
    HOTEL_CORP_APPROVAL_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_CORP_APPROVAL_PREFIX),
    HOTEL_BOOKING_REVIEW_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_BOOKING_REVIEW_PAGE),
    HOTEL_SELECT_ROOM_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_SELECT_ROOM_PAGE),
    HOTEL_TREEL_PAGE(HotelDeeplinkConstants.HOTEL_DEEP_LINK_TREEL);


    private final String path;

    HotelDeepLinkPages(String path) {

        this.path = path;
    }

    public boolean contains(String url) {
        return url != null && !url.trim().isEmpty() && url.contains(path);
    }
}
