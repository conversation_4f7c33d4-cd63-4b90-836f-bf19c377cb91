package com.mmt.hotel.common.constants

import com.mmt.pokus.LOB
import com.mmt.pokus.model.Experiment
import com.mmt.pokus.model.ExperimentsHelper

object ExperimentsGrowth {
    val corpChildBookingEnabled : Experiment<Int> = ExperimentsHelper.addExperiment(Experiment(LOB.COMMON, "mecb", 1, "30/06/2021"))

    val bookingForShown: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.COMMON, "mpoa", false, "12/08/2020"))

    val hotelLandingFiltersB2B: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.COMMON, "hotelLandingFiltersB2B", false, "16/03/2021"))

    val hotelLandingFilters: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.COMMON, "hotelLandingFilters", false, "16/03/2021"))

    val hotelWishlist: Experiment<Boolean> = ExperimentsHelper.addExperiment(Experiment(LOB.COMMON, "hotelWishlist", true, "01/03/2022"))
}