@file:JvmName("LinearLayoutBindingAdapter")
package com.mmt.hotel.common.data

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.annotation.LayoutRes
import androidx.databinding.BindingAdapter
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.mmt.core.util.CollectionUtil
import com.mmt.hotel.BR


@BindingAdapter(value = ["layoutItems"], requireAll = true)
fun bindLinearLayoutItemData(linearLayout: LinearLayout, linearLayoutItems: List<LinearLayoutItemData>?) {

    linearLayout.removeAllViews()
    if (CollectionUtil.isNullOrEmpty(linearLayoutItems)) {
        return
    }

    val layoutInflater: LayoutInflater = LayoutInflater.from(linearLayout.context)
    try {
        for (dataItems in linearLayoutItems!!) {
            val binding: ViewDataBinding = DataBindingUtil.inflate(layoutInflater, dataItems.layoutId, linearLayout, false)
            binding.setVariable(dataItems.dataVariableId, dataItems.data)
            binding.executePendingBindings()
            linearLayout.addView(binding.root)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }

}

@BindingAdapter(value = ["htlBindChildModels", "htlBindChildLayout", "htlBindChildRemoveViews", "htlIgnoreIfChildAdded"], requireAll = false)
fun <T> bindChildViews(parent: ViewGroup, modelList: List<T>?,
                       @LayoutRes childLayoutID: Int, removeAllViews: Boolean, ignoreIfChildAdded: Boolean) {
    if (modelList.isNullOrEmpty() || (ignoreIfChildAdded && parent.childCount > 0)) {
        return
    }
    if (removeAllViews) {
        parent.removeAllViews()
    }
    val inflater = LayoutInflater.from(parent.context)
    for (model in modelList) {
        val binding: ViewDataBinding = DataBindingUtil.inflate(inflater, childLayoutID, parent, false)
        binding.setVariable(BR.model, model)
        binding.executePendingBindings()
        parent.addView(binding.root)
    }
}
