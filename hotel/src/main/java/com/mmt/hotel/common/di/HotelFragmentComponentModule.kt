package com.mmt.hotel.common.di

import androidx.fragment.app.Fragment
import com.mmt.hotel.analytics.pdtMetrics.HotelScreenMetricsTracker
import com.mmt.hotel.base.ui.fragment.HotelComposeFragment
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.hilt.android.scopes.FragmentScoped
import javax.inject.Named


@Module
@InstallIn(FragmentComponent::class)
class HotelFragmentComponentModule {

    @FragmentScoped
    @Provides
    @Named(NamedConstants.METRICS_TRACKER_FOR_FRAGMENT)
    fun provideScreenMetricsTrackerForFragment(fragment : Fragment): HotelScreenMetricsTracker {
        return if (fragment is HotelComposeFragment<*>) {
            fragment.screenMetricsTracker
        } else {
            HotelScreenMetricsTracker()
        }
    }

}