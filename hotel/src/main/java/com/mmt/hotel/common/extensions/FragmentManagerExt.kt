package com.mmt.hotel.common.extensions

import androidx.annotation.AnimRes
import androidx.annotation.AnimatorRes
import androidx.annotation.IdRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentResultListener
import androidx.lifecycle.LifecycleOwner
import com.mmt.hotel.common.model.SharedElementTransitionModel

fun FragmentManager.openFragment(
    fragment: Fragment,
    @IdRes container: Int,
    addToBackStack: Boolean = true,
    addFragment: Boolean = false,
    @AnimRes @AnimatorRes enterAnim: Int? = null,
    @AnimRes @AnimatorRes exitAnim: Int? = null,
    tag: String? = null,
    forceAdd: Boolean = false,
    sharedElementTransitionModel: SharedElementTransitionModel? = null
) {
    if(forceAdd || findFragmentByTag(tag) == null) {
        val transition = beginTransaction()

        sharedElementTransitionModel?.let {
            transition.addSharedElement(it.sharedElement, it.transitionName)
        }

        when {
            addFragment -> transition.add(container, fragment, tag)
            else -> transition.replace(container, fragment, tag)
        }
        if (enterAnim != null && exitAnim != null) {
            transition.setCustomAnimations(enterAnim, exitAnim, enterAnim, exitAnim)
        }

        if (addToBackStack) transition.addToBackStack(tag)
        transition.commitAllowingStateLoss()
    }
}

inline fun <reified T : Fragment> FragmentManager.fragment(tag: String): T? {
    return findFragmentByTag(tag) as? T
}

fun FragmentManager.setFragmentResultListener(requestKey: List<String>, lifecycleOwner: LifecycleOwner, listener: FragmentResultListener) {
    for (key in requestKey) {
        this.setFragmentResultListener(key, lifecycleOwner, listener)
    }
}