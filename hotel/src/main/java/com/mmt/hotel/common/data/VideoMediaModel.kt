package com.mmt.hotel.common.data

import android.net.Uri
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Video Media model to hold information about the URI to be played along with some other metaData like the source
 * and should volume be kept on or not
 *
 * create by <PERSON><PERSON><PERSON> on 19/05/21
 */
@Parcelize
data class VideoMediaModel(val mediaUri: Uri, val source: String, val isVolumeOn: Boolean = true, var isFullScreen : Boolean = true) : Parcelable