package com.mmt.hotel.common.data

import android.os.Parcelable
import androidx.annotation.LayoutRes
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

/*
*   Use this class for inflating list of items in your ViewGroup,
*
* refer : com.mmt.uikit.binding.LinearLayoutBindingAdapter.
**/

@Parcelize
data class LinearLayoutItemData(@LayoutRes val layoutId: Int, //  layout id
                                val dataVariableId: Int, // id of bindingVariable defined in xml use BR.
                                var data: @RawValue Any): Parcelable