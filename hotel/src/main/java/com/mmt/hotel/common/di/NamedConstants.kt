package com.mmt.hotel.common.di

object NamedConstants {
    const val ALT_ACCO_REPOSITORY = "ALT_ACCO_REPOSITORY"
    const val STAYCATION_REPOSITORY = "STAYCATION_REPOSITORY"
    const val HOTEL_LANDING_REPOSITORY = "HOTEL_LANDING_REPOSITORY"
    const val SOLD_OUT = "soldOut"
    const val CALENDAR_AVAILABLE = "calendarAvailable"
    const val CALENDAR_MLOS = "calendarMlos"
    const val HOTEL_LANDING_SEARCH_MODIFY_FUNNEL_TYPE = "HotelLandingSearchModifyFunnelType"
    const val IS_FROM_GCC_LANDING = "isFromGccLanding"
    const val ALTACCO_SEARCH_MODIFY_REPO = "AltAccoSearchModifyRepo"
    const val GROUP_SEARCH_MODIFY_REPO = "GroupBookingSearchModifyRepo"
    const val DAYUSE_SEARCH_MODIY_REPO = "DayUseSearchModifyRepo"
    const val SHORTSTAY_SEARCH_MODIY_REPO = "ShortStaySearchModifyRepo"
    const val SHORT_STAYS_LOCATION_SELECTION_REPO = "ShortStayLocationSelectionRepo"
    const val HOTEL_LANDING_SEARCH_MODIFY_REPO ="HotelLandingSearchModifyRepo"
    const val MORE_FILTER_BOTTOMSHEET_PARENT_PAGE_CONTEXT = "MoreFilterBottomSheet_parentPageContext"
    const val MORE_FILTER_BOTTOMSHEET_USERSEARCHDATA = "MoreFilterBottomSheet_userSearchData"
    const val HOTEL_LOCATION_FILTER_SOURCE = "HotelLocationFilterSource"
    const val CORP_EMPLOYEE_SEARCH_ACTIVITY = "CORP_EMPLOYEE_SEARCH_ACTIVITY"
    const val DAYUSE_FRAGMENT = "DayUseFragment"
    const val CITYGUIDE_FRAGMENT_SCROLL_TITLE = "CITYGUIDE_FRAGMENT_SCROLL_TITLE"
    const val HOTEL_CALENDAR_PARENT_PAGE_CONTEXT = "HOTEL_CALENDAR_PARENT_PAGE_CONTEXT"
    const val HOTEL_CALENDAR_PARENT_PAGE_REQUEST_ID = "HOTEL_CALENDAR_PARENT_PAGE_REQUEST_ID"
    const val HOTEL_CALENDAR_PARENT_PAGE_ALL_REQUEST_ID = "HOTEL_CALENDAR_PARENT_PAGE_ALL_REQUEST_ID"
    const val HOTEL_CALENDAR_FRAGMENT_USER_SEARCH_DATA = "HOTEL_CALENDAR_FRAGMENT_USER_SEARCH_DATA"
    const val HOTEL_FILTER_TYPE = "HOTEL_FILTER_TYPE"
    const val HOMESTAY_LANDING_CONFIG = "HOMESTAY_LANDING_CONFIG"
    const val PARENT_PAGE_CONTEXT = "parentPageContext"
    const val HOTEL_USER_SEARCH_DATA = "hotelUserSearchData"
    const val HOTEL_CURRENT_PAGE = "hotelCurrentPage"
    const val HOTEL_BASE_TRACKING_DATA = "hotelBaseTrackingData"
    const val FragmentCategoryReviewsV2 = "FragmentCategoryReviewsV2"
    const val FRAGMENT_HOTEL_REVIEWS_SHOWTARATINGV2 = "FragmentHotelReviews_SHOWTARATING_V2"
    const val FRAGMENT_HOTEL_REVIEWS_RATINGSOURCEV2 = "FragmentHotelReviews_RATINGSOURCE_V2"
    const val FRAGMENT_HOTEL_REVIEWS_BUNDLE_DATA = "FRAGMENT_HOTEL_REVIEWS_BUNDLE_DATA"
    const val IS_FROM_MAP = "IS_FROM_MAP"
    const val IS_LOCATION_V2 = "IS_LOCATION_V2"
    const val USER_SEARCH_DATA_FOR_FRAGMENT = "USER_SEARCH_DATA_FOR_FRAGMENT"
    const val METRICS_TRACKER_FOR_FRAGMENT = "METRICS_TRACKER_FOR_FRAGMENT"
    const val METRICS_TRACKER_FOR_ENTRY_POINT = "METRICS_TRACKER_FOR_ENTRY_POINT"
    const val HOTEL_LANDING_TRACKER_FOR_ENTRY_POINT = "HOTEL_LANDING_TRACKER_FOR_ENTRY_POINT"
    const val ALT_ACCO_TRACKER_FOR_ENTRY_POINT = "ALT_ACCO_TRACKER_FOR_ENTRY_POINT"
    const val BASE_HOTEL_TRACKING_DATA = "BASE_HOTEL_TRACKING_DATA"
    const val MMT_REVIEW_DATA = "MMT_REVIEW_DATA"
    const val USER_REVIEW_DATA = "USER_REVIEW_DATA"
    const val CURRENCY_SYMBOL = "currencySymbol"
    const val THANKYOU_V2_REQUEST_DATA = "thankyou_v2_request_data"
    const val THANKYOU_V2_REPO = "thankyou_v2_repo"



}