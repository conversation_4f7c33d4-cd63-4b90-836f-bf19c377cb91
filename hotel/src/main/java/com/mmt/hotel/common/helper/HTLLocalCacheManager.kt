package com.mmt.hotel.common.helper

import com.mmt.auth.login.util.LoginUtils
import com.mmt.auth.logout.LogoutManager
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.compose.review.uiModel.TravelerInformationUiModel

object HTLLocalCacheManager {

    private var travellerInfo : TravelerInformationUiModel? = null
    private var isLoggedIn : Boolean = false
    private var regionCode : String? = null

    init {
        LogoutManager.addLogoutTask(HTLLogout())
    }

    fun saveTravellerInfo(travellerInfo: TravelerInformationUiModel) {
        // do not save in cache if experiment is not enabled
        if(!ExperimentUtil.canCacheTravellerDetails()){
            return
        }
        isLoggedIn = travellerInfo.isUserLoggedIn
        regionCode = LoginUtils.getPreferredRegionCode()
        this.travellerInfo = travellerInfo
    }

    fun getTravellerInfo(): TravelerInformationUiModel? {
        // always retuning null if experiment is not enabled
        if(!ExperimentUtil.canCacheTravellerDetails()){
            return null
        }

        // clear cache if login state is changed or funnel context is changed (i.e. region is changed)
        if(LoginUtils.isLoggedIn != isLoggedIn || LoginUtils.getPreferredRegionCode() != regionCode){
            clearTravellerInfo()
        }
        return travellerInfo
    }

    fun clearTravellerInfo() {
        travellerInfo = null
    }
}