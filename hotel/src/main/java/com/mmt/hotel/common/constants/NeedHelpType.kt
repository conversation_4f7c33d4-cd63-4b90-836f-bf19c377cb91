package com.mmt.hotel.common.constants

import androidx.annotation.StringDef
import com.mmt.hotel.common.constants.NeedHelpType.Companion.NEED_HELP
import com.mmt.hotel.common.constants.NeedHelpType.Companion.CALL
import com.mmt.hotel.common.constants.NeedHelpType.Companion.CALL_TO_BOOK
import com.mmt.hotel.common.constants.NeedHelpType.Companion.FORM

@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [NEED_HELP, CALL, FORM, CALL_TO_BOOK])
annotation class NeedHelpType {
    companion object {
        const val NEED_HELP = "Need_Help"
        const val CALL = "call"
        const val FORM = "form"
        const val CALL_TO_BOOK = "callToBook"
    }
}