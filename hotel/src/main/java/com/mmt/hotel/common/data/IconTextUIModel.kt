package com.mmt.hotel.common.data

import android.os.Parcelable
import com.mmt.hotel.R
import com.mmt.hotel.listingV2.model.response.hotels.BottomSheetData
import kotlinx.parcelize.Parcelize

@Parcelize
data class IconTextUIModel(
    val textObj : TextUIModel? = null,
    val iconObj : IconUIModel? = null,
    val trailingCta: String? = null,
    val trailingCtaBottomSheet: BottomSheetData? = null
) : Parcelable

@Parcelize
data class TextUIModel(
    val text : String? = null,
    val textColor : Int = R.color.grey_hotel,
    val isBold : Boolean = false
) : Parcelable

@Parcelize
data class IconUIModel(
    val iconType : String? = null,
    val tint : Int? = null,
    val iconUrl : String? = null
) : Parcelable