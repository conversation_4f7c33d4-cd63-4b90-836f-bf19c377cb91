package com.mmt.hotel.common.helper

import com.mmt.hotel.common.extensions.orIfNullOrBlank
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.detail.dataModel.HotelDetailData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.selectRoom.model.SelectRoomData
import com.mmt.hotel.selectRoom.model.request.SearchRoomsRequestData

object SearchRoomsRequestDataCreator {

    fun create(selectRoomData: SelectRoomData): SearchRoomsRequestData {
        return with(selectRoomData) {
            SearchRoomsRequestData(
                userSearchData = userSearchData,
                appliedFilters = appliedFilters,
                roomStayCandidates = roomStayCandidates,
                experimentData = expData,
                apiCacheKey = searchPriceCacheKey.orIfNullOrBlank(selectRoomData.userSearchData.hotelId +System.currentTimeMillis()),
                pageName = hotelBaseTrackingData.basePageName.orEmpty(),
                corpPrimaryTraveller = corpPrimaryTraveller,
                personalCorpBooking = personalCorpBooking,
                maskedPropertyName = maskedPropertyName
            )
        }
    }

    fun create(data: HotelDetailData, cacheKey: String? = null): SearchRoomsRequestData {
        return with(data) {
            SearchRoomsRequestData(
                userSearchData = userData,
                appliedFilters = appliedFilters.selectedFilters,
                roomStayCandidates = roomStayCandidate,
                experimentData = experimentData,
                apiCacheKey = cacheKey.orIfNullOrBlank(userData.hotelId + System.currentTimeMillis()),
                pageName = trackingData.hotelBaseTrackingData.basePageName.orEmpty(),
                corpPrimaryTraveller = corpPrimaryTraveller,
                personalCorpBooking = personalCorpBooking,
                selectedRatePlan = data.selectedRatePlan,
                guestHouseAvailable = data.guestHouseAvailable,
                hotelType = data.hotelData?.hotelType,
                maskedPropertyName = maskedPropertyName
            )
        }
    }

    fun create(request: SearchRequest,pageName: String): SearchRoomsRequestData {
        return with(request) {
            SearchRoomsRequestData(
                userSearchData = userSearchData!!,
                appliedFilters = listingSearchData?.appliedFilterList,
                roomStayCandidates = roomStayCandidate.orEmpty(),
                experimentData = HotelUtil.getExperimetsForApiRequest(userSearchData),
                apiCacheKey =  userSearchData?.hotelId + System.currentTimeMillis(),
                pageName = pageName,
                corpPrimaryTraveller = primaryTraveller,
                personalCorpBooking = personalCorpBooking
            )
        }
    }

}