package com.mmt.hotel.common.model.request

import com.mmt.data.model.homepage.personalizationSequenceAPI.response.locationdata.BaseLocationData
import com.mmt.hotel.autoSuggest.constants.LocusResultType

data class HotelUserLocation(
    val city: String,
    val state: String,
    val country: String
)

/**
 * data class for inflating userSearch on first visit using emperia response
 */
data class HotelLandingData(
    val id: String, val name: String, val searchType: String,
    val countryName: String, val countryCode: String,
    val lat: Float? = null,
    val long: Float? = null
)

fun BaseLocationData?.toHotelLandingData():HotelLandingData? {
    if(this == null) {
        return null
    }
    val cityDetail = this.city?.firstOrNull()?: return null
    val cityId = cityDetail.id
    val cityName = cityDetail.name
    val countryName = this.country?.name.orEmpty()
    val countryCode = this.country?.id.orEmpty()
    if(cityId.isEmpty() or cityName.isEmpty() or  countryName.isEmpty() or countryCode.isEmpty()) {
        return null
    }
    var lat:Float? = null
    var long:Float? = null
    val coordinates = cityDetail.centre?.coordinates?: emptyList<Double>()
    if (coordinates.size == 2) {
        lat = coordinates[1].toFloat()
        long = coordinates[0].toFloat()
    }
    return HotelLandingData(id = cityId, name =  cityName, searchType = LocusResultType.LOCUS_RESULT_TYPE_CITY,
        countryName = countryName , countryCode = countryCode, lat = lat, long = long)
}

fun BaseLocationData?.toHotelUserLocation():HotelUserLocation? {
    if(this == null) {
        return null
    }
    val city = this.city?.firstOrNull()?.id.orEmpty()
    val state = this.state?.firstOrNull()?.id.orEmpty()
    val country = this.country?.id.orEmpty()
    return if(city.isNotEmpty() || state.isNotEmpty() || country.isNotEmpty()){
        HotelUserLocation(city = city, state = state, country = country)
    } else {
        null
    }
}