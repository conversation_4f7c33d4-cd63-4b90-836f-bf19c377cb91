package com.mmt.hotel.common.helper

import android.view.MotionEvent
import android.view.View
import androidx.databinding.adapters.ListenerUtil
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.recyclerview.widget.RecyclerView
import com.mmt.core.util.getLifeCycleOwner
import com.mmt.hotel.R
import com.mmt.hotel.binding.htlScrollRecyclerViewOnce
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.ScheduledThreadPoolExecutor
import java.util.concurrent.TimeUnit

class AutoScroller(private val initialDelayInMs: Long = 2000, private val periodInMs: Long = 3000) {
    private var executor: ScheduledThreadPoolExecutor? = null
    private var autoScrollTask = arrayOfNulls<ScheduledFuture<*>>(1)
    private var recyclerView: RecyclerView? = null
    private var isTaskRunning = false

    fun initialize() {
        if (executor == null)
            executor = ScheduledThreadPoolExecutor(1)
    }

    fun onPause() {
        shutDownTask()
    }


    fun onResume() {
        initializeTask()
    }

    fun onDestroy() {
        disableAutoScroll()
    }


    fun enableAutoScroll(recyclerView: RecyclerView, touchListener: View.OnTouchListener?= null) {
        initializeTask()
        this.recyclerView = recyclerView
        recyclerView.context?.getLifeCycleOwner()?.lifecycle?.addObserver(LifecycleEventObserver { source, event ->
            when (event) {
                Lifecycle.Event.ON_RESUME -> {
                    onResume()
                }
                Lifecycle.Event.ON_PAUSE -> {
                    onPause()
                }
                Lifecycle.Event.ON_DESTROY -> {
                    onDestroy()
                }
                else -> {}
            }
        })

        recyclerView.setOnTouchListener { view, motionEvent ->
            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN -> {
                    onPause()
                }
                MotionEvent.ACTION_UP -> {
                    onResume()
                }
            }
            touchListener?.onTouch(view, motionEvent)?:false
        }


        val newListener: View.OnAttachStateChangeListener =
            object : View.OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(view: View) {
                    initializeTask()
                }

                override fun onViewDetachedFromWindow(view: View) {
                    shutDownTask()
                }
            }

        val oldListener = ListenerUtil.trackListener(
            recyclerView,
            newListener,
            R.id.onAttachStateChangeListener
        )
        if (oldListener != null) {
            recyclerView.removeOnAttachStateChangeListener(oldListener)
        }
        recyclerView.addOnAttachStateChangeListener(newListener)
    }

    private fun initializeTask() {
        if (isTaskRunning) return

        initialize()
        autoScrollTask[0] = executor?.scheduleAtFixedRate(
            {
                isTaskRunning = true
                htlScrollRecyclerViewOnce(recyclerView)
            },
            initialDelayInMs,
            periodInMs,
            TimeUnit.MILLISECONDS
        )
    }


    private fun disableAutoScroll() {
        shutDownTask()
        recyclerView = null
        autoScrollTask[0] = null

    }

    private fun shutDownTask() {
        autoScrollTask[0]?.cancel(true)
        executor?.shutdown()
        isTaskRunning = false
        executor = null
    }
}