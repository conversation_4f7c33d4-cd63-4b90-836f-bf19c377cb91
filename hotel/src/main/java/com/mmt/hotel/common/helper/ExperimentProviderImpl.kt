package com.mmt.hotel.common.helper


import com.mmt.hotel.common.constants.ExperimentsHotel
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.*
import com.mmt.hotel.old.util.HotelPriceUtil

/**
 * Created by <PERSON><PERSON><PERSON> on 14/07/20.
 */
/**
 * Pricing Experiment Wrapped because
 * @see HotelUtils getHtlPricingExperiment calls network which make test cases fails
 * to inject mock for this behavior price experiment wrap here
 * @note This class can be change to wrap pokus experiment value in if need to wrap other values
 * */
class ExperimentProviderImpl : ExperimentProvider {

    override fun providePriceExperiment(funnel: Int): String {
        return HotelPriceUtil.getHtlPricingExperiment(funnel)
    }

    override fun getExperimentData(userSearchData: UserSearchData?): String {
        return HotelUtil.getExperimetsForApiRequest(userSearchData)
    }

    override fun getDetailRecycleCardOrder(isLuxeProperty:<PERSON><PERSON><PERSON>, isAltAcco:Boolean): String {
        if(isAltAcco){
            return if(isLuxeProperty) {
                ExperimentsHotel.altAccoLuxeCardOrder.getPokusValue()
            } else {
                ExperimentsHotel.altAccoBaseCardOrder.getPokusValue()
            }
        }
        return getHotelDetailCardOrderV3(isLuxeProperty)
    }
}