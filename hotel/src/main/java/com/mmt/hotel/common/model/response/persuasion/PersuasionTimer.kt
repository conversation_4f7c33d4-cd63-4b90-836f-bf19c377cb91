package com.mmt.hotel.common.model.response.persuasion

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Created by <PERSON><PERSON><PERSON> on 25/01/21.
 */
@Parcelize
class PersuasionTimer(
        @SerializedName("expiry")
        val expiry: Long,
        @SerializedName("style")
        val style: PersuasionStyle?) : Parcelable {

    val styleOrDefault: PersuasionStyle
        get() {
            return style ?: PersuasionStyle()
        }

}