package com.mmt.hotel.common.data

import androidx.compose.ui.text.font.FontWeight
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.MMTFontStyle
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.uikit.fonts.FontConstants
import com.mmt.uikit.fonts.latoFont

/**
 * class to be used for assigning style to TextView
 */
data class HotelTextConfigData(
    val textSizeResId: Int = R.dimen.htl_text_size_small,
    val fontResId: Int = FontConstants.LATO_REGULAR,
    val textColor: Int = R.color.grey_hotel,
    val fontWeight:FontWeight = FontWeight.Normal,
    val fontResComposeId: MMTFontStyle = MMTFontStyle(fontFamily = latoFont,
        fontWeight = FontWeight.Normal)
    )