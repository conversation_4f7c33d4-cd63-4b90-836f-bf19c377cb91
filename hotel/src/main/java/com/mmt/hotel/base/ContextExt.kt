package com.mmt.hotel.base

import android.content.Context
import android.content.ContextWrapper
import android.view.ContextThemeWrapper
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LifecycleOwner
import com.mmt.hotel.common.data.HotelBottomSheetInfoUiData
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.viewmodel.HotelBottomSheetEvents
import com.mmt.hotel.old.common.ui.HotelBottomSheetDialog

fun Context.showBottomSheet(title: String, items: List<LinearLayoutItemData>, showCrossIcon: Boolean = true) {
    val bottomSheet = HotelBottomSheetDialog(HotelBottomSheetInfoUiData(title, showCrossIcon, items), this)
    val lifecycleOwner = getLifecycleOwnerFromContext(this) ?: return
    bottomSheet.getLiveData().observe(lifecycleOwner) { event ->
        when (event.eventID) {
            HotelBottomSheetEvents.DISMISS_BOTTOM_SHEET -> {
                bottomSheet.dismiss()
            }
        }
    }
    bottomSheet.show()
}

fun getLifecycleOwnerFromContext(context: Context): LifecycleOwner? {
    return if (context is LifecycleOwner) {
        context
    } else {
        null
    }
}

fun Context.getActivity(): AppCompatActivity? {
    return when (this) {
        is AppCompatActivity -> {
            this
        }

        is ContextThemeWrapper -> {
            if (this.baseContext is ContextWrapper) {
                (this.baseContext as ContextWrapper).getActivity()
            } else if (this.baseContext is AppCompatActivity) {
                (this.baseContext as AppCompatActivity)
            } else {
                null
            }
        }

        is ContextWrapper -> {
            (this.baseContext as AppCompatActivity)
        }

        else -> {
            null
        }
    }
}