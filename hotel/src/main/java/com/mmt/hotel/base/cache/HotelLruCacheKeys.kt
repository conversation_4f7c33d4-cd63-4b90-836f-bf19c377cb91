package com.mmt.hotel.base.cache

import android.util.LruCache
import androidx.annotation.StringDef

object HotelLruCacheKeys {


    @StringDef(
        HOTEL_LRU_CACHE_KEYS.HOTEL_TREELS_RESPONSE_CACHE_PREFIX_KEY
    )
    annotation class HOTEL_LRU_CACHE_KEYS {
        companion object {
            const val HOTEL_TREELS_RESPONSE_CACHE_PREFIX_KEY = "hotel_treel_response_prefix_key_"
        }
    }


    private val instance = LruCache<String, Any>(20) // No of entries in cache

    fun put(key: String, value: Any) {
        instance.put(key, value)
    }

    fun get(key: String): Any? {
        return instance.get(key)
    }

    fun size(): Int {
        return instance.size()
    }

    fun evictAll() {
        instance.evictAll()
    }

    fun remove(key: String) {
        instance.remove(key)
    }

    fun containsKey(key: String): Boolean {
        return instance.snapshot().containsKey(key)
    }
}