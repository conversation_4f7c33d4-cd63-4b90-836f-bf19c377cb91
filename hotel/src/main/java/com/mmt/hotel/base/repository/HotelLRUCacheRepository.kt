package com.mmt.hotel.base.repository

import com.google.gson.reflect.TypeToken
import com.mmt.network.NetworkHelper
import com.mmt.core.util.LruCacheUtil
import com.mmt.hotel.base.model.response.ResponseValidator
import com.mmt.hotel.base.model.response.ResponseValidity
import com.mmt.hotel.common.model.response.HotelResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import java.io.IOException

open class HotelLRUCacheRepository : HotelBaseRepository() {

    companion object {
        const val MAX_TTL = 300 * 1000L
        const val TAG = "HotelCacheRepository"
    }

    @Throws(Exception::class)
    inline fun <reified T : Any, reified V : Any> makeRequest(
        url: String,
        postData: T?,
        countryCode: String,
        @LruCacheUtil.LRU_CACHE_KEYS prefixKey: String,
        cacheKey: String,
        requestMethod: String,
        headers: MutableMap<String, String?>,
        queryParams: HashMap<String, String> = HashMap()
    ): Flow<HotelResponse<V>> {
//        val params = HashMap<String, String>()
        queryParams[PARAM_COUNTRY_CODE] = countryCode

        return makeNetworkRequest<T, V>(url = url, postData = postData, queryParams = queryParams, headerMap = headers, requestMethod = requestMethod)
            .map { response ->
                response.responseData?.let {
                    if((it as? ResponseValidator)?.isResponseValid() != ResponseValidity.IN_VALID) {
                        writeDataToCache(it, prefixKey, cacheKey)
                    }
                    HotelResponse(it, false)
                } ?: run {
                    deleteDataFromCache(prefixKey, cacheKey)
                    throw Exception()
                }
            }
            .flowOn(Dispatchers.IO)
    }

    inline fun <reified V : Any> getDataFromCache(@LruCacheUtil.LRU_CACHE_KEYS prefixKey: String, cacheKey: String): Flow<HotelResponse<V>> {
        val lruCacheUtil = LruCacheUtil.getInstance()
        val response = flow {
            val value = lruCacheUtil.readObjectFromCache<V>(prefixKey, cacheKey, TypeToken.get(V::class.java))
            if (value != null) {
                emit(value)
            }else{
                throw IOException()
            }
        }
        return response.map {
            HotelResponse(it,true)
        }.flowOn(Dispatchers.IO)
    }

    @Throws(Exception::class)
    inline fun <reified T : Any, reified V : Any> makePostRequestWithCaching(
        url: String,
        postData: T,
        countryCode: String,
        @LruCacheUtil.LRU_CACHE_KEYS prefixKey: String,
        cacheKey: String,
        headers: MutableMap<String, String?> = mutableMapOf(),
        ttlInMs: Long = MAX_TTL,
        queryParams: HashMap<String, String> = HashMap()
    ): Flow<HotelResponse<V>> {
        return makeNetworkRequestWithCaching(url = url, postData = postData, countryCode = countryCode, prefixKey = prefixKey, cacheKey = cacheKey, requestMethod = NetworkHelper.REQUEST_METHOD_POST, headers = headers, ttl = ttlInMs, queryParams = queryParams)
    }


    inline fun <reified V : Any> writeDataToCache(networkResponse:V, @LruCacheUtil.LRU_CACHE_KEYS prefixKey: String, cacheKey: String) {
        val lruCacheUtil = LruCacheUtil.getInstance()
        if (!lruCacheUtil.isCacheReady) {
            return
        }
        lruCacheUtil.writeStringToCacheAsync(prefixKey
                , cacheKey, networkResponse
                , TypeToken.get(V::class.java))
    }

    fun deleteDataFromCache(@LruCacheUtil.LRU_CACHE_KEYS prefixKey: String, cacheKey: String) {
        val lruCacheUtil = LruCacheUtil.getInstance()
        if (!lruCacheUtil.isCacheReady) {
            return
        }
        lruCacheUtil.deleteEntryFromCacheAsync(prefixKey, cacheKey)
    }

    @Throws(Exception::class)
    inline fun <reified T : Any, reified V : Any> makeNetworkRequestWithCaching(
            url: String,
            postData: T?,
            countryCode: String,
            @LruCacheUtil.LRU_CACHE_KEYS prefixKey: String,
            cacheKey: String,
            requestMethod: String,
            headers: MutableMap<String, String?>,
            ttl: Long = MAX_TTL,
            queryParams: HashMap<String, String> = HashMap()
    ): Flow<HotelResponse<V>> {
        val lruCacheUtil = LruCacheUtil.getInstance()
        return if (!lruCacheUtil.isCacheReady || !lruCacheUtil.doesCacheEntryExist(prefixKey,
                cacheKey, ttl)) {
            makeRequest(url = url, postData = postData, countryCode = countryCode, prefixKey = prefixKey, cacheKey = cacheKey, requestMethod = requestMethod, headers = headers, queryParams = queryParams)
        } else {
            getDataFromCache<V>(prefixKey, cacheKey)
                .catch {
                    // Error in getting value from cache will try network call
                    emitAll(makeRequest(url = url, postData = postData, countryCode = countryCode, prefixKey = prefixKey, cacheKey = cacheKey, requestMethod = requestMethod, headers = headers, queryParams = queryParams))
                }
        }
    }

}