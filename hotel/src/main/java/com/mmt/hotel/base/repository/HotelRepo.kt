package com.mmt.hotel.base.repository

import com.google.gson.reflect.TypeToken
import com.mmt.core.MMTCore
import com.mmt.data.model.network.NetworkUtil
import com.mmt.network.MMTNetwork
import com.mmt.network.NetworkHelper
import com.mmt.network.model.NetworkRequest
import com.mmt.network.model.NetworkResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.flowOn
import java.io.IOException

/**
 * Created by sunil.jain on 8/4/21.
 */
class HotelRepo {

    @Throws(Exception::class)
    fun <T, V> makeNetworkRequest(url: String,
                                  postData: T?,
                                  queryParams: HashMap<String, String>,
                                  headerMap: MutableMap<String, String?>? = null,
                                  cookiesEnabled: Boolean = false,
                                  requestMethod: String,
                                  typeToken: TypeToken<V>): Flow<NetworkResponse<V>> {
        val requestHeaderMap = MMTNetwork.iNetworkHeaders.getDefaultHeaders(MMTCore.mContext).toMutableMap().apply {
            putAll(headerMap ?: emptyMap())
        }
        val requestUrl = NetworkUtil.getCompleteUrlForGetRequest(url, queryParams)
        val networkRequest: NetworkRequest = NetworkRequest.Builder(requestUrl)
                .data(postData)
                .headersMap(requestHeaderMap)
                .cookiesEnabled(cookiesEnabled)
                .requestMethod(requestMethod)
                .build()

        return NetworkHelper.makeFlowRequest(networkRequest, typeToken, NetworkUtil.getInterceptorsForHttpUtils()).flowOn(Dispatchers.IO)
    }

    @Throws(Exception::class)
    fun <T, V> makePostRequest(url: String, postData: T, countryCode: String,
                               headerMap: MutableMap<String, String?>? = null, cookiesEnabled: Boolean = false,
                               typeToken: TypeToken<V>): Flow<V> {
        val params = HashMap<String, String>()
        params[HotelBaseRepository.PARAM_COUNTRY_CODE] = countryCode

        return makeNetworkRequest<T, V>(url = url, postData = postData, queryParams = params,
                headerMap = headerMap, cookiesEnabled = cookiesEnabled, requestMethod = NetworkHelper.REQUEST_METHOD_POST, typeToken = typeToken)
                .map {
                    if (it.responseData != null) {
                        it.responseData!!
                    } else {
                        throw IOException()
                    }
                }
    }

    @Throws(Exception::class)
    fun <V> makeGetRequest(url: String, countryCode: String,
                           headerMap: MutableMap<String, String?>? = null, cookiesEnabled: Boolean = false,
                           typeToken: TypeToken<V>): Flow<V> {
        val params = HashMap<String, String>()
        params[HotelBaseRepository.PARAM_COUNTRY_CODE] = countryCode

        return makeNetworkRequest<Any, V>(url = url, postData = null, queryParams = params,
                headerMap = headerMap, cookiesEnabled = cookiesEnabled, requestMethod = NetworkHelper.REQUEST_METHOD_GET, typeToken = typeToken)
                .map {
                    if (it.responseData != null) {
                        it.responseData!!
                    } else {
                        throw IOException()
                    }
                }
    }
}