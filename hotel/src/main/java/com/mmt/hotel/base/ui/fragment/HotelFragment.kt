package com.mmt.hotel.base.ui.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.view.ContextThemeWrapper
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.gommt.logger.LogUtils
import com.mmt.core.util.Utils.initFabricWithDefault
import com.mmt.core.util.performIfActivityActive
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.common.data.HotelBottomSheetInfoUiData
import com.mmt.hotel.common.data.LinearLayoutItemData
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.viewmodel.HotelBottomSheetEvents
import com.mmt.hotel.old.common.ui.HotelBottomSheetDialog
import com.mmt.uikit.helper.setAccessibilityFocus
import kotlinx.coroutines.launch
import java.util.UUID

abstract class HotelFragment<T : HotelViewModel, V : ViewDataBinding> : Fragment() {

    lateinit var viewDataBinding: V
    abstract fun  initViewModel():T
    open fun injectDependency() {}

    /**
     * this method is called in onAttach , it must only be used for data which is required for dependency injection
     */
    open fun initAndValidate() {}
    private  var onBackPressedCallback: OnBackPressedCallback? = null

    val viewModel: T by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        initViewModel()
    }

    lateinit var fragmentID: String

    //Do not make public or update this variable, intended to only be used in between onCreate() and onResume(), use isFragmentRecreating() to get value
    private var isRecreating : Boolean = false

    fun isViewDataBindingInitialized() = ::viewDataBinding.isInitialized

    override fun onAttach(context: Context) {
        super.onAttach(context)
        injectDependency()
        initAndValidate()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        isRecreating = savedInstanceState != null
        super.onCreate(savedInstanceState)
        fragmentID = savedInstanceState?.getString(FRAGMENT_ID, UUID.randomUUID().toString()) ?: UUID.randomUUID().toString()
        addOnBackPressedDispatcher()
        initExtraDataLoggingForFabric()
        LogUtils.debug("HotelFragment","OnCreate()..:"+javaClass.simpleName)
    }

    fun isFragmentRecreating() : Boolean {
        return isRecreating
    }

    private fun addOnBackPressedDispatcher() {
        if(!shouldInterceptBackPress()) {
            return
        }
        onBackPressedCallback = object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                onHandleBackPress()
            }
        }
    }

    /**
     * registering [onBackPressedCallback] in [onResume] as , when apps comes
     * from background, activity is getting registered to stack at last and will consume
     * backpressed event
     */
    override fun onResume() {
        super.onResume()
        isRecreating = false
        onBackPressedCallback?.let {
            activity?.onBackPressedDispatcher?.addCallback(this, it)
        }
    }

    override fun onPause() {
        super.onPause()
        onBackPressedCallback?.remove()
    }



    open fun onHandleBackPress() {
        try {
            onBackPressedCallback?.isEnabled = false
            activity?.onBackPressedDispatcher?.onBackPressed()
        } catch (e : Exception){
            LogUtils.error("Hotel Fragment", e)
        }
    }

    open fun shouldInterceptBackPress() = false

    final override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        setWindowProperties()
        viewDataBinding = DataBindingUtil.inflate(getThemeInflater(inflater), getLayoutId(), container, false)
        setDataBinding()
        initFragmentView()
        viewDataBinding.root.isClickable = true

        return viewDataBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewDataBinding.root.setAccessibilityFocus(lifecycleScope)
    }

    private fun getThemeInflater(inflater: LayoutInflater): LayoutInflater {
        val themeId = setTheme()
        if(themeId == -1){
            return inflater
        }
        activity?.let {
            val contextThemeWrapper = ContextThemeWrapper(it, themeId)
            return inflater.cloneInContext(contextThemeWrapper)
        }
        return inflater
    }

    open fun setTheme() = -1

    open fun setWindowProperties(){
        setLightStatusBar()
    }

    abstract fun setDataBinding()

    abstract fun getLayoutId(): Int

    abstract fun initFragmentView()

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel.eventStream.observe(viewLifecycleOwner) {
            handleEvents(it)
        }
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.eventStreamFlow.collect{
                    handleEvents(it)
                }
            }
        }

    }

    fun isViewDataBindingInitialised() = ::viewDataBinding.isInitialized

    protected open fun dismissFragment() {
        performIfActivityActive(activity) { activity ->
            val supportFragmentManager = (activity as FragmentActivity).supportFragmentManager
            if(supportFragmentManager.backStackEntryCount > 0){
                supportFragmentManager.popBackStack()
            } else {
                try {
                    onBackPressedCallback?.isEnabled = false
                    activity.onBackPressedDispatcher.onBackPressed()
                } catch (e : Exception){
                    LogUtils.error("Hotel Fragment", e)
                }
            }
        }
    }

    override fun onStop() {
        super.onStop()
        viewModel.clearEventStream()
    }


    /**
     * method for showing simple ui msgs in  bottom sheet
     * use this if bottom sheet requires only title , msg and cross button
     */
    protected fun showBottomSheet(title: String, items: List<LinearLayoutItemData>, showCrossIcon: Boolean = true) {
      //  activity?.showBottomSheet(title, items, showCrossIcon)
        this.context?.let {
            val bottomSheet = HotelBottomSheetDialog(HotelBottomSheetInfoUiData(title, showCrossIcon, items), it)
            bottomSheet.getLiveData().observe(viewLifecycleOwner, { event ->
                when (event.eventID) {
                    HotelBottomSheetEvents.DISMISS_BOTTOM_SHEET -> {
                        bottomSheet.dismiss()
                    }
                }
            })
            bottomSheet.show()
        }
    }

    open fun onUpdateChatBotUnreadMsg(hasUnreadMsg: Boolean) {
        viewModel.chatBotVM.get()?.updateChatBotUnreadCount(hasUnreadMsg)
    }

    open fun onShowHideWebView(show: Boolean){
        // implement as required
    }

    abstract fun handleEvents(event: HotelEvent)

    protected fun initExtraDataLoggingForFabric() {
        initFabricWithDefault(this.javaClass.simpleName)
    }

    protected fun setLightStatusBar(){
        HotelUtil.setLightStatusBar(activity)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putString(FRAGMENT_ID, fragmentID)
        super.onSaveInstanceState(outState)
    }

    companion object {
        const val FRAGMENT_ID = "FRAGMENT_ID"
    }

}