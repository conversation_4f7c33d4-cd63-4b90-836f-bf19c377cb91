package com.mmt.hotel.base.viewModel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import dagger.MapKey
import javax.inject.Inject
import javax.inject.Provider
import kotlin.reflect.KClass

/**
 * Created by <PERSON><PERSON><PERSON> on 25/06/20.
 */
/**
 * This annotation used to add ViewModel provider in dagger graph with viewModel class as a
 * key which later used to retrive viewmodel provider from dagger
 * */
@Target(AnnotationTarget.FUNCTION, AnnotationTarget.PROPERTY_GETTER, AnnotationTarget.PROPERTY_SETTER)
@kotlin.annotation.Retention(AnnotationRetention.RUNTIME)
@MapKey
annotation class ViewModelKey(val value: KClass<out ViewModel>)

/**
 * A Common ViewModelFactory class which will be injected with all the viewmodels provider
 * created in subComponent or component of the activity or fragment where it will be inject
 * @param viewModels will contains all the viewModel Provider reference which is provide in the
 * subComponent of the activity and fragment
 * */
class HotelViewModelFactory @Inject constructor(private val viewModels: MutableMap<Class<out ViewModel>, Provider<HotelViewModel>>) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        val viewModel = viewModels[modelClass]!!.get()
        return viewModel as T
    }
}