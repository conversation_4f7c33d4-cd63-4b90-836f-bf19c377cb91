package com.mmt.hotel.base.viewModel

import com.mmt.hotel.common.model.HotelError

abstract class HotelErrorFragmentViewModel(val error: HotelError) : HotelViewModel() {

    companion object {
        const val DISMISS_ERROR_FRAG = "DISMISS_ERROR_FRAG"

        const val DISMISS_POPUP = 0
    }

    fun onNegativeAction() {
        error.negativeBtnText?.let {
            performAction(error.negativeAction!!)
        } ?: run {
            performAction(DISMISS_POPUP)
        }
    }

    fun onPositiveAction() {
        performAction(error.positiveAction)
    }

    abstract fun performAction(action: Int)
}