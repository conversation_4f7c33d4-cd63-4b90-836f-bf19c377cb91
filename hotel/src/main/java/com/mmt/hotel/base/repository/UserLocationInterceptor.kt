package com.mmt.hotel.base.repository

import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.util.HtlUrlConstants
import okhttp3.Interceptor
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer
import org.json.JSONObject

class UserLocationInterceptor : Interceptor {

    companion object {
        const val PATH_URL = HtlUrlConstants.IndianHosts.CBDOM_HOST+"/clientbackend"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        var request = chain.request()
        val url = request.url.toUrl()
        val requestBody = request.body
        val hostAndPath = url.host + url.path

        val userLocation = getUserLocation()
        if (hostAndPath.contains(PATH_URL) && userLocation!= null && requestBody != null) {
            val buffer = Buffer()
            requestBody.writeTo(buffer)
            val strOldBody = buffer.readUtf8()
            buffer.clear()
            buffer.close()

            val updatedRequestBody = JSONObject(strOldBody).put("userLocation", userLocation).toString()
            request = chain.request().newBuilder()
                .method(request.method, updatedRequestBody.toRequestBody(requestBody.contentType()))
                .build()
        }
        return chain.proceed(request)
    }

    private fun getUserLocation(): JSONObject? {
        return try{
            val userLocation:String? = HotelSharedPrefUtil.instance.getString(SharedPrefKeys.KEY_HTL_EMPERIA_USER_LOCATION)
            if (userLocation != null) {
                JSONObject(userLocation)
            } else {
                null
            }
        } catch (e:Exception) {
            null
        }
    }
}