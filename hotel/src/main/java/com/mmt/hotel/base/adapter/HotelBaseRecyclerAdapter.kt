package com.mmt.hotel.base.adapter


import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.gommt.logger.LogUtils
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder

abstract class HotelBaseRecyclerAdapter(private val itemList: MutableList<AbstractRecyclerItem>)
    : RecyclerView.Adapter<HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>>() {

    private var animator: HotelItemAnimator? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem> {
        val layoutInflater = LayoutInflater.from(parent.context)
        return getViewHolder(viewType, layoutInflater, parent)
    }


    protected abstract fun getViewHolder(viewType: Int, layoutInflater: LayoutInflater, parent: ViewGroup):
            HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>

     override fun getItemCount(): Int {
        return itemList.size
    }

    fun getItems():List<AbstractRecyclerItem> {
        return itemList
    }

    override fun onBindViewHolder(holder: HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>, position: Int) {
        holder.bindData(getItem(position), position)
        animator?.animate(holder,position)
    }

   final  override fun getItemViewType(position: Int): Int {
        return getItem(position).getItemType()
    }

   open fun getItem(position: Int) : AbstractRecyclerItem {
        if(position < 0 || position > itemCount) {
            throw IndexOutOfBoundsException()
        } else {
            return itemList[position]
        }
    }

    open fun getItemOrNull(position: Int) : AbstractRecyclerItem? {
        return if(position < 0 || position >= itemCount) {
            null
        } else {
            itemList[position]
        }
    }

    fun setItemAnimator(animator: HotelItemAnimator?){
        this.animator = animator
    }

    fun addItem(position: Int, item: AbstractRecyclerItem) {
        try {
            itemList.add(position, item)
        } catch (e: Exception) {
            LogUtils.error("TAG", "IndexOutOfBoundsException")
        }
        notifyItemInserted(position)
    }

    fun addItems(items : List<AbstractRecyclerItem>, addAtStart : Boolean = false) {
        if(addAtStart) {
            itemList.addAll(0,items)
        } else {
            itemList.addAll(items)
        }
        notifyDataSetChanged()
    }

    fun removeItem(item: AbstractRecyclerItem) {
        if( !itemList.contains(item) )
            return
        val position: Int = itemList.indexOf(item)
        itemList.removeAt(position)
        notifyItemRemoved(position)
    }

    fun addItem(item: AbstractRecyclerItem){
        itemList.add(item)
        notifyItemInserted(itemCount - 1)
    }

    fun modifyItem(position: Int, item: AbstractRecyclerItem) {
        if (itemList.size <= position) {
            return
        }
        itemList[position] = item
        notifyItemChanged(position)
    }

    fun modifyItem(item: AbstractRecyclerItem) {
        val position = itemList.indexOf(item)
        modifyItem(position, item)
    }

    fun updateList(dataList: List<AbstractRecyclerItem>, ignoreEmptyList: Boolean = true) {
        itemList.clear()
        if (dataList.isEmpty() && ignoreEmptyList) {
            return
        }
        itemList.addAll(dataList)
        notifyDataSetChanged()
    }

    /**
     * This will not notify adapter after updating list. This method is useful
     * when notifying list update thorough diff utils
     * */
    open fun updateListOnly(dataList: List<AbstractRecyclerItem>){
        itemList.clear()
        itemList.addAll(dataList)
    }

    fun clear() {
        itemList.clear()
        notifyDataSetChanged()
    }
}