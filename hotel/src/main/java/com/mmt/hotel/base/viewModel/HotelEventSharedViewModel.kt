package com.mmt.hotel.base.viewModel

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.base.events.HotelEvent
import javax.inject.Inject

class HotelEventSharedViewModel @Inject constructor(): HotelViewModel() {

    private val _activityResultState = mutableStateOf(HotelEvent(CoreConstants.EMPTY_STRING))
    val sharedEvent: MutableState<HotelEvent> get() = _activityResultState
    fun handleSharedEvents(event: HotelEvent) {
        _activityResultState.value = event
    }

    override fun updateEventStream(eventData: HotelEvent) {
        super.updateEventStream(eventData)
        _activityResultState.value = eventData
    }

    override fun updateEventStream(eventID: String, data: Any?) {
        super.updateEventStream(eventID, data)
        _activityResultState.value = HotelEvent(eventID, data)
    }
}

