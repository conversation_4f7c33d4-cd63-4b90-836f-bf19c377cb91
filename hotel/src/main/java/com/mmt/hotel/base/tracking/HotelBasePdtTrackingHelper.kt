package com.mmt.hotel.base.tracking

import android.os.Build
import com.google.gson.annotations.SerializedName
import com.mmt.analytics.AnalyticsConstants
import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureTracker
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.analytics.pdtclient.PdtPagenameConstants
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreHotelConstants
import com.mmt.core.util.CollectionUtil.isEmptyCollection
import com.mmt.core.util.CoreSharedPreferenceUtil
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.DateUtil
import com.mmt.core.util.GsonUtils
import com.mmt.data.model.util.CommonMigrationHelper
import com.mmt.hotel.analytics.pdt.TrackingConstants
import com.mmt.hotel.base.model.tracking.HotelBaseTrackingData
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.extensions.addIfNotNull
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelDateUtil
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.listingV2.dataModel.ListingTrackingModel
import com.mmt.hotel.old.pdt.model.HotelPricePdtInfo
import com.mmt.hotel.old.pdt.model.PDTMapper
import com.mmt.hotel.old.pdt.model.PdtHotelDetail
import com.mmt.hotel.old.pdt.model.PdtReview
import com.mmt.hotel.selectRoom.event.RatePlanSelectionEventData
import com.mmt.hotel.selectRoom.helper.constants.RoomSearchType
import com.gommt.logger.LogUtils
import com.mmt.hotel.detail.compose.model.SelectedRatePlanInfo
import com.mmt.network.connectivity.MobileNetworkUtils
import com.mmt.pokus.LOB
import com.mmt.pokus.PokusV2Helper
import com.mmt.uikit.util.isNotNullAndEmpty
import com.pdt.pdtDataLogging.events.model.TrackingDataWrapper
import org.json.JSONObject
import java.lang.reflect.Field
import java.util.*
import kotlin.math.floor


object HotelBasePdtTrackingHelper {

    const val DATE_FORMAT = "MMddyyyy"
    const val PDT_DATE_FORMAT = "yyyy-MM-dd"
    const val ANDROID = "ANDROID"
    const val DIRECT = "Direct"
    const val LOGGED = "true"
    const val NOT_LOGGED = "false"
    const val LOB_NH7 = "NH7"
    const val LOB_NN7 = "NN7"
    const val TAG = "HotelBasePdtTrackingHelper"

    @JvmStatic
    fun addToTrackingMapFromInitialMap(map: MutableMap<String, Any?>?,
                                       eventParams: MutableMap<String, Any?>): MutableMap<String, Any?> {

        map?.let {
            for ((key, value) in it) {
                if (eventParams.containsKey(key)) {
                    eventParams[key] = eventParams[key].toString() + "|$value"
                } else {
                    eventParams[key] = value
                }
            }
        }
        return eventParams
    }

    fun covertPojoToMap(obj: Any): HashMap<String, Any>? {
        val data: HashMap<String, Any> = HashMap()
        val flds: Array<Field> = obj.javaClass.declaredFields
        for (fld in flds) {
            try {
                fld.setAccessible(true)
                val anotation: Annotation = fld.getAnnotation(SerializedName::class.java)
                val nameAnot: SerializedName = anotation as SerializedName
                if (nameAnot != null) {
                    val attrName: String = nameAnot.value
                    val value: Any? = fld.get(obj)
                    if (null != value) {
                        data[attrName] = value
                    }
                }
            } catch (ex: IllegalAccessException) {
            }
        }
        return data
    }

    fun getSelectRoomPricePDTInfo(ratePlanEventData: RatePlanSelectionEventData): HotelPricePdtInfo? {
        val price = ratePlanEventData.selectedTariffPriceMap?.get(ratePlanEventData.effectivePriceKey)
                ?: return null
        try {
            val discountedPriceUiData = price.discountedPrice.replace("[^0-9]".toRegex(), "")
            val discountedPrice = if (discountedPriceUiData.isEmpty()) {
                0F
            } else {
                discountedPriceUiData.toFloat()
            }
            return if (RoomSearchType.EXACT_MATCH.equals(ratePlanEventData.searchType, true)) {
                HotelPricePdtInfo.Builder()
                        .displayedPrice(floor(discountedPrice.toDouble()).toInt())
                        .priceUnit(HotelPricePdtInfo.PER_NIGHT_woGST)
                        .priceType(ratePlanEventData.payMode)
                        .tariffSelectedCount(1)
                        .tariffType(HotelPricePdtInfo.TARIFF_EXACT)
                        .build()
            } else {
                HotelPricePdtInfo.Builder()
                        .displayedPrice(floor(discountedPrice.toDouble()).toInt())
                        .priceUnit(HotelPricePdtInfo.PER_NIGHT_woGST)
                        .priceType(ratePlanEventData.payMode)
                        .tariffType(HotelPricePdtInfo.TARIFF_OCCUPANCY_LESS)
                        .build()
            }
        } catch (exception: Exception) {
            return null
        }
    }

    fun getSelectRoomPricePDTInfo(ratePlanEventData: SelectedRatePlanInfo,couponCode:String): HotelPricePdtInfo? {
        val price = ratePlanEventData.selectedTariffPriceMap.get(couponCode)
            ?: return null
        try {
            val discountedPriceUiData = price.discountedPrice.replace("[^0-9]".toRegex(), "")
            val discountedPrice = if (discountedPriceUiData.isEmpty()) {
                0F
            } else {
                discountedPriceUiData.toFloat()
            }
            return if (RoomSearchType.EXACT_MATCH.equals(ratePlanEventData.searchType, true)) {
                HotelPricePdtInfo.Builder()
                    .displayedPrice(floor(discountedPrice.toDouble()).toInt())
                    .priceUnit(HotelPricePdtInfo.PER_NIGHT_woGST)
                    .priceType(ratePlanEventData.payMode)
                    .tariffSelectedCount(1)
                    .tariffType(HotelPricePdtInfo.TARIFF_EXACT)
                    .build()
            } else {
                HotelPricePdtInfo.Builder()
                    .displayedPrice(floor(discountedPrice.toDouble()).toInt())
                    .priceUnit(HotelPricePdtInfo.PER_NIGHT_woGST)
                    .priceType(ratePlanEventData.payMode)
                    .tariffType(HotelPricePdtInfo.TARIFF_OCCUPANCY_LESS)
                    .build()
            }
        } catch (exception: Exception) {
            return null
        }
    }

    @JvmStatic
    fun mergePdtReviews(pdtHotelDetail: PdtHotelDetail?) {
        if (null == pdtHotelDetail || isEmptyCollection(pdtHotelDetail.reviews)) {
            return
        }
        val pdtReviewList = pdtHotelDetail.reviews
        val pdtReview = PdtReview()
        val filterTagsSet: MutableSet<String> = HashSet()
        for (review in pdtReviewList) {
            pdtReview.hotelRating = review.hotelRating
            pdtReview.isSeen = review.isSeen
            pdtReview.cleanlinessScore = review.cleanlinessScore
            pdtReview.facilitiesScore = review.facilitiesScore
            pdtReview.locationScore = review.locationScore
            pdtReview.timeSpent = pdtReview.timeSpent + review.timeSpent
            pdtReview.totalReviews = review.totalReviews
            pdtReview.noOfReviewsSeen = pdtReview.noOfReviewsSeen + review.noOfReviewsSeen
            filterTagsSet.addAll(review.filterTagUsed)
        }
        pdtReview.filterTagUsed = ArrayList(filterTagsSet)
        pdtHotelDetail.reviews.clear()
        pdtHotelDetail.reviews.add(pdtReview)
    }

    @JvmStatic
    fun getPdtHotelDetailAsString(pdtHotelDetail: PdtHotelDetail?): String {
        return pdtHotelDetail?.let {
            GsonUtils.getInstance().serializeToJson(it)?: CoreConstants.EMPTY_STRING
        } ?: CoreConstants.EMPTY_STRING
    }

    @JvmStatic
    fun getHotelPriceInfoAsString(pricePdtInfo: HotelPricePdtInfo?): String {
        return pricePdtInfo?.let {
            GsonUtils.getInstance().serializeToJson(it)?: CoreConstants.EMPTY_STRING
        } ?: CoreConstants.EMPTY_STRING
    }

    fun pdtTrack(events: Events, pdtMapper: PDTMapper,
                 userSearchData: UserSearchData,
                 experimentData: String?) {
        try {
            val isDomestic = HotelConstants.COUNTRY_CODE_INDIA.equals(userSearchData.countryCode, true)
            val checkInCal = HotelDateUtil.getCalendarDate(userSearchData.checkInDate, DATE_FORMAT)
            val checkOutCal = HotelDateUtil.getCalendarDate(userSearchData.checkOutDate, DATE_FORMAT)
            val numberOfNights = HotelDateUtil.getDaysBetweenTwoDates(checkInCal, checkOutCal).toInt()

            pdtMapper.advancePurchase = getAdvancePurchase(userSearchData).toString()
            pdtMapper.check_in = DateUtil.formatDateTime(checkInCal, PDT_DATE_FORMAT)
            pdtMapper.check_Out = DateUtil.formatDateTime(checkOutCal, PDT_DATE_FORMAT)
            pdtMapper.city_CD = userSearchData.locationId
            pdtMapper.countryCD = userSearchData.countryCode
            pdtMapper.currencyCD = HotelCurrencyUtil.getSelectedCurrencyCode()
            pdtMapper.no_of_Adults = userSearchData.occupancyData.adultCount
            pdtMapper.no_Of_Nights = numberOfNights
            pdtMapper.no_Of_Rooms = userSearchData.occupancyData.roomCount ?: if (userSearchData.isAltAccoFunnel()) 0 else 1
            pdtMapper.number_of_Children = userSearchData.occupancyData.childAges.size
            pdtMapper.roomStayQualifier = HotelUtil.getRoomStayQualifier(userSearchData.occupancyData.adultCount, userSearchData.occupancyData.childAges.size)
            pdtMapper.travel_Type = if (isDomestic) {
                CoreHotelConstants.HOTEL_DOMESTIC
            } else {
                CoreHotelConstants.HOTEL_INTERNATIONAL
            }
            pdtMapper.device_Manufacturer = Build.MANUFACTURER
            pdtMapper.device_app_ID = CoreUtil.getDeviceId()
            pdtMapper.type_of_device = "MOBILE"
            pdtMapper.deviceName = Build.DEVICE
            pdtMapper.mobile_Operating_System = ANDROID
            pdtMapper.mobileOperatingSystemVersion = Build.VERSION.RELEASE
            pdtMapper.screenSize = CoreUtil.getScreenSize(true)
            pdtMapper.omniture_Visitor_ID_ = OmnitureTracker.getVisitorID()
            pdtMapper.jSessionID = java.lang.String.valueOf(HotelSharedPrefUtil.instance.getInt(SharedPrefKeys.VISITOR_NUMBER))
            pdtMapper.client_IP = MobileNetworkUtils.getLocalIpAddress()
            pdtMapper.mobile_App_version = CoreUtil.getAppVersionName()
            pdtMapper.clientTimeStamp = System.currentTimeMillis().toString()
            pdtMapper.tag = "prod"
            pdtMapper.timeStampCreated = java.lang.Long.toString(System.currentTimeMillis())
            pdtMapper.user_Agent = CoreConstants.ANDROID + "_" + CoreUtil.getAppVersionName()
            pdtMapper.location = CommonMigrationHelper.instance.getLastKnownLocation()
            pdtMapper.traffic_type = DIRECT
            pdtMapper.query_type = userSearchData.searchType
            if (LoginUtils.isLoggedIn) {
                pdtMapper.loggedIN_status = LOGGED
                addCorpData(pdtMapper)
            } else {
                pdtMapper.loggedIN_status = NOT_LOGGED
            }
            pdtMapper.internet_connection = HotelMigratorHelper.instance.getCurrentInternetConnection()
            pdtMapper.notificationID = HotelSharedPrefUtil.instance.getString(CoreSharedPreferenceUtil.KEY_APP_GCM_ID)
            if (isDomestic) {
                pdtMapper.lob_Name = LOB_NH7
            } else {
                pdtMapper.lob_Name = LOB_NN7
            }
            if (!experimentData.isNullOrEmpty()) {
                val experimentName = arrayOf(experimentData)
                pdtMapper.experiment_Name = experimentName
            }
            startPDTTracking(events, pdtMapper)
        } catch (e: Exception) {
        }
    }

    private fun getAdvancePurchase(userSearchData: UserSearchData): Int {
        val checkInDate = DateUtil.convertStringToDate(userSearchData.checkInDate, DATE_FORMAT)
        return DateUtil.getDiffDate(Date(), checkInDate)
    }

    private fun addCorpData(pdtMapper: PDTMapper) {
        if (!LoginUtils.isCorporateUser) {
            pdtMapper.logged_in_Channel = LoginUtils.loggedInUser!!.loginType
            return
        }
        pdtMapper.setCorpData(AnalyticsConstants.CHANNEL_CORPORATE)
        if (!pdtMapper.pageName.isNullOrEmpty()) {
            pdtMapper.pageName = pdtMapper.pageName + AnalyticsConstants.CORPORATE_OMNITURE_PAGE_NAME_ADDITION
        }
        if (!pdtMapper.templateID.isNullOrEmpty() && HotelMigratorHelper.instance.getPdtMapperTemplateId(pdtMapper.templateID) != null) {
            pdtMapper.templateID = HotelMigratorHelper.instance.getPdtMapperTemplateId(pdtMapper.templateID)
        }
        pdtMapper.logged_in_Channel = AnalyticsConstants.CORPORATE_LOGIN_TYPE
    }

    private fun startPDTTracking(events: Events, pdtMap: PDTMapper) {
        try {
            val jsonString = GsonUtils.getInstance().serializeToJson(covertPojoToMap(pdtMap))
            OmnitureTrackingHelper.trackAppStateWithJSONObject(events, JSONObject(jsonString))
        } catch (e: Exception) {
        }
    }

    fun setTrackingVarsForListing(userSearchData: UserSearchData, appliedFilters: String, sortType: String?,
                                  hotelBaseTrackingData: HotelBaseTrackingData, experimentData: String,
                                  correlationKey: String?, timeSpentOnPage: Long,
                                  listingTrackingModelList: List<ListingTrackingModel>) {
        try {
            val isDomestic = HotelUtil.isDom(userSearchData.countryCode)
            val pdtMapper = PDTMapper()

            pdtMapper.activityName = "pl"
            pdtMapper.previouspage = hotelBaseTrackingData.previousPage.orEmpty()
            pdtMapper.funnel_Step = TrackingConstants.FUNNEL_HOTEL_LISTING
            pdtMapper.templateID = TrackingConstants.TEMPLATE_ID_LISTING
            pdtMapper.topicID = TrackingConstants.TOPIC_ID_LISTING
            pdtMapper.hotel_ID = userSearchData.hotelId
            pdtMapper.timeSpent = timeSpentOnPage.toString()
            pdtMapper.viewHotelData = arrayOf(GsonUtils.getInstance().serializeToJson(listingTrackingModelList))
            pdtMapper.viewHotelCount = arrayOf(listingTrackingModelList.sumBy { it.hotelInfoList.size }.toString())
            pdtMapper.sortName = sortType
            pdtMapper.onward_filter_value = appliedFilters
            if (correlationKey.isNotNullAndEmpty()) {
                pdtMapper.correlationKey = correlationKey
            }
            if (isDomestic) {
                pdtMapper.pageName = PdtPagenameConstants.HOTEL_PRODUCT_LISTING_DOM_PAGE_NAME
                HotelBasePdtTrackingHelper.pdtTrack(Events.MMT_TRACKER_DOM_HOTEL_LISTING, pdtMapper, userSearchData, experimentData)
            } else {
                pdtMapper.pageName = PdtPagenameConstants.HOTEL_PRODUCT_LISTING_INTL_PAGE_NAME
                HotelBasePdtTrackingHelper.pdtTrack(Events.MMT_TRACKER_INTL_HOTEL_LISTING, pdtMapper, userSearchData, experimentData)
            }
        } catch (e: Exception) {
            LogUtils.error(TAG, e)
        }
    }

    fun getPokusPdtTrackingData(): List<Any> {
        val pokusExpData: MutableList<Any> = ArrayList()
        val pokusHelper = PokusV2Helper
        val expDataDom = pokusHelper.getPdtTrackingData(LOB.HOTEL.name)
        expDataDom?.second?.let {
            pokusExpData.addAll(it)
        }
        val expDataIntl = pokusHelper.getPdtTrackingData(LOB.HOTEL_INT.name)
        expDataIntl?.second?.let {
            pokusExpData.addAll(it)
        }
        return pokusExpData
    }

    @Deprecated("Use getPdtV2TrackingData()")
    fun getPokusPdtTrackingDataV2(): TrackingDataWrapper {
        val expData = PokusV2Helper.getPdtTrackingDataV2(LOB.HOTEL.name)
        val expIntData: Pair<String?, List<Any>?>? =
            PokusV2Helper.getPdtTrackingDataV2(LOB.HOTEL_INT.name)
        val trackingData = TrackingDataWrapper(expData)
        expIntData?.let {
            trackingData.plus(it)
        }
        return trackingData
    }

    fun getPdtV2TrackingData() : Pair<String,List<String>> {
        val hotelData = PokusV2Helper.getLobValue(LOB.HOTEL.name)
        val hotelIntlData = PokusV2Helper.getLobValue(LOB.HOTEL_INT.name)
        val variantKeys = hotelData?.variantKey.orEmpty() +CoreConstants.COMMA+ hotelIntlData?.variantKey.orEmpty()
        val experimentList = mutableListOf<String>()
        hotelData?.expDetails?.let {
            experimentList.addAll(it)
        }
        hotelIntlData?.expDetails?.let {
            experimentList.addAll(it)
        }
        return Pair(variantKeys,experimentList)
    }
}