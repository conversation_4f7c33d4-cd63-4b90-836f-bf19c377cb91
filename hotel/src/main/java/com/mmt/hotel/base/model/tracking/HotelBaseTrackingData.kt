package com.mmt.hotel.base.model.tracking

import android.os.Parcelable
import com.mmt.hotel.analytics.pdt.TrackingConstants
import com.mmt.hotel.detail.constants.DetailPageViewType
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelBaseTrackingData(
    var tripType: String?,
    var stayType: String?,
    var cmpId: String?,
    var position: Int,
    var previousPage: String?,
    var starRating: Int? = null,
    val userRating: Float? = null,
    var propertyType: String? = null,
    var headerImgUrl: String? = null,
    var prevFunnelStepPdt: String? = null,
    var prevPageNamePdt: String? = null,
    var originalPrice: Float? = 0f,
    var discountedPrice: Float? = 0f,
    val isHotelMyBizAssured: Boolean = false,
    @DetailPageViewType
    var propertyViewType: String? = null,
    var basePageName: String? = null,
    //To be used in day use tracking
    var selectedSlotDuration: Int? = null,
    var supplierType: String? = null
) : Parcelable{

    fun setPreviousPageToBookingReview() {
        this.prevFunnelStepPdt = TrackingConstants.HOTEL_REVIEW_PAGE
        this.prevPageNamePdt = TrackingConstants.HOTEL_REVIEW_PAGE
    }

    fun setPreviousPageToSelectRoom() {
        this.prevFunnelStepPdt = TrackingConstants.ROOM_SELECTION
        this.prevPageNamePdt = TrackingConstants.ROOM_SELECTION
    }

    fun setPreviousPageToDetail() {
        this.prevFunnelStepPdt = TrackingConstants.HOTEL_DETAIL
        this.prevPageNamePdt = TrackingConstants.HOTEL_DETAIL
    }
    fun setPreviousPageToAltAcctInterstitial(){
        this.prevFunnelStepPdt = TrackingConstants.HOTEL_ACCO_INTERSTITIAL
        this.prevPageNamePdt = TrackingConstants.HOTEL_ACCO_INTERSTITIAL
    }
}