package com.mmt.hotel.base.model.request

import android.os.Parcelable
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import kotlinx.parcelize.Parcelize

@Parcelize
open class SearchCriteria(var checkIn: String = EMPTY_STRING,  //dd-mm-yyyy
                          var checkOut: String = EMPTY_STRING,
                          var countryCode: String = EMPTY_STRING,
                          var locationId: String = EMPTY_STRING,
                          var locationType: String = EMPTY_STRING,
                          var currency: String = EMPTY_STRING) : Parcelable {

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is SearchCriteria) return false
        if (checkIn != other.checkIn) return false
        if (checkOut != other.checkOut) return false
        if (countryCode != other.countryCode) return false
        if (locationId != other.locationId) return false
        if (locationType != other.locationType) return false
        if (currency != other.currency) return false
        return true
    }

    override fun hashCode(): Int {
        var result = checkIn.hashCode()
        result = 31 * result + checkOut.hashCode()
        result = 31 * result + countryCode.hashCode()
        result = 31 * result + locationId.hashCode()
        result = 31 * result + locationType.hashCode()
        result = 31 * result + currency.hashCode()
        return result
    }
}