package com.mmt.hotel.base.ui.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import com.mmt.core.util.Utils
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel

abstract class HotelDialogFragment<T : HotelViewModel, V : ViewDataBinding> : DialogFragment() {
    lateinit var viewDataBinding: V
    abstract fun initViewModel(): T

    /**
     * this method is called in onAttach , it must only be used for data which is required for dependency injection
     */
    open fun initAndValidate() {}

    val viewModel: T by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        initViewModel()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        initAndValidate()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initExtraDataLoggingForFabric()
    }

    final override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        viewDataBinding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false)
        setDataBinding()
        initFragmentView()
        viewDataBinding.root.isClickable = true
        return viewDataBinding.root
    }

    abstract fun setDataBinding()

    abstract fun getLayoutId(): Int

    abstract fun initFragmentView()

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel.eventStream.observe(viewLifecycleOwner, Observer {
            handleEvents(it)
        })
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.clearEventStream()
    }

    abstract fun handleEvents(event: HotelEvent)

    private fun initExtraDataLoggingForFabric() {
        Utils.initFabricWithDefault(this.javaClass.simpleName)
    }
}