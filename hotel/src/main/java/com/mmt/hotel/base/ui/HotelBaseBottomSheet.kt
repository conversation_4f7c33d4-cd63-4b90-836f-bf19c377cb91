package com.mmt.hotel.base.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.MMTFontStyle
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.getCTABrushTheme
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun HotelBaseBottomSheet(
    title:String?,
    showCrossIcon:Boolean,
    onDismiss: () -> Unit,
    content: @Composable () -> Unit
) {
    Column(modifier = Modifier
        .fillMaxWidth()
        .semantics { testTagsAsResourceId = true }
        .background(
            color = colorResource(id = R.color.white),
            shape = RoundedCornerShape(
                topStart = dimensionResource(id = R.dimen.margin_large),
                topEnd = dimensionResource(id = R.dimen.margin_large)
            )
        )
        .padding(
            start = dimensionResource(id = R.dimen.margin_xLarge),
            end = dimensionResource(id = R.dimen.margin_xLarge),
            bottom = dimensionResource(id = R.dimen.margin_xHuge2))) {

        Row(modifier = Modifier
            .padding(
                top = dimensionResource(id = R.dimen.margin_large),
                bottom = dimensionResource(id = R.dimen.margin_large)
            )
            .fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {

            title?.let{
                MmtComposeTextView(
                    modifier = Modifier
                        .padding(
                            top = dimensionResource(id = R.dimen.margin_tiny),
                            end = dimensionResource(id = R.dimen.margin_large)
                        )
                        .weight(1f)
                        .testTag("heading"),
                    text = it,
                    color = colorResource(id = R.color.black),
                    fontSize = spDimensionResource(id = R.dimen.htl_text_size_xHuge),
                    mmtFontStyle = latoBlack
                )
            }

            if(showCrossIcon) {
                LoadImage(
                    Modifier
                        .width(dimensionResource(id = R.dimen.image_dimen_xmedium))
                        .height(dimensionResource(id = R.dimen.image_dimen_xmedium))
                        .testTag("cross_icon")
                        .mmtClickable(onClick = onDismiss),
                    resourceId = R.drawable.htl_ic_bottomsheet_close,
                    contentScale = ContentScale.Fit
                )
            }
        }

        content()
    }
}