package com.mmt.hotel.base.cache

import android.util.LruCache
import androidx.annotation.StringDef


/**
 * Created by mmt7703 on 14/02/24.
 */
object HotelInMemoryLruCache {


    @StringDef(
        LRU_CACHE_KEYS.HOTEL_DETAIL_CACHE_PREFIX,
        LRU_CACHE_KEYS.HOTEL_LOCUS_POPULAR_CITIES,
        LRU_CACHE_KEYS.HOTEL_LOCUS_POPULAR_LOCATIONS_IN_FILTER,
        LRU_CACHE_KEYS.HOTEL_SEARCH_ROOMS_API,
        LRU_CACHE_KEYS.HOTEL_LISTING_CACHE_PREFIX,
        LRU_CACHE_KEYS.HOTEL_LOCUS_SAVED_LOCATIONS,
        LRU_CACHE_KEYS.HOTEL_AUTO_SUGGEST_POPULAR_CITIES,
        LRU_CACHE_KEYS.HOTEL_AUTO_SUGGEST_POPULAR_LOCATIONS_IN_FILTER,
        LRU_CACHE_KEYS.HOTEL_FILTER_COUNT_CACHE_PREFIX,
        LRU_CACHE_KEYS.HOTEL_EMPERIA_LANDING_PREFIX,
        LRU_CACHE_KEYS.HOTEL_SHORTS_STAYS,
        LRU_CACHE_KEYS.HOTEL_COLLECTION_RESPONSE_CACHE_PREFIX_KEY,
        LRU_CACHE_KEYS.HOTEL_TREEL_RESPONSE_CACHE_PREFIX_KEY
    )
    annotation class LRU_CACHE_KEYS {
        companion object {
            const val HOTEL_DETAIL_CACHE_PREFIX = "htl_det_cache_"
            const val HOTEL_LOCUS_POPULAR_CITIES = "htl_locus_popular_cities_"
            const val HOTEL_LOCUS_POPULAR_LOCATIONS_IN_FILTER = "htl_locus_popular_locations_"
            const val HOTEL_SEARCH_ROOMS_API = "htl_search_room_api_"
            const val HOTEL_LISTING_CACHE_PREFIX = "htl_listing_api_cache_"
            const val HOTEL_LOCUS_SAVED_LOCATIONS = "htl_locus_saved_locations_"
            const val HOTEL_AUTO_SUGGEST_POPULAR_CITIES = "htl_auto_suggest_popular_cities_"
            const val HOTEL_AUTO_SUGGEST_POPULAR_LOCATIONS_IN_FILTER =
                "htl_auto_suggest_popular_locations_"
            const val HOTEL_FILTER_COUNT_CACHE_PREFIX = "htl_filter_count_api_cache_"
            const val HOTEL_EMPERIA_LANDING_PREFIX = "htl_emperia_landing_prefix_"
            const val HOTEL_SHORTS_STAYS = "htl_short_stays_"
            const val HOTEL_COLLECTION_RESPONSE_CACHE_PREFIX_KEY =
                "hotel_collection_response_prefix_key_"
            const val HOTEL_TREEL_RESPONSE_CACHE_PREFIX_KEY = "hotel_treel_response_prefix_key_"
        }
    }


    private val instance = LruCache<String, Any>(20) // No of entries in cache

    fun put(key: String, value: Any) {
        instance.put(key, value)
    }

    fun get(key: String): Any? {
        return instance.get(key)
    }

    fun size(): Int {
        return instance.size()
    }

    fun evictAll() {
        instance.evictAll()
    }

    fun remove(key: String) {
        instance.remove(key)
    }

    fun containsKey(key:String) : Boolean {
        return instance.snapshot().containsKey(key)
    }
}