package com.mmt.hotel.base.adapter.adapterModel

import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.GenericAdapterItemTypes

/**
 * Adapter model to display a loading view within a recyclerView
 *
 * create by <PERSON><PERSON><PERSON> on 26/04/21
 */
class GenericLoadingAdapterModel : AbstractRecyclerItem {

    var isFromPagination = false

    override fun getItemType() = GenericAdapterItemTypes.LOADING_REVIEWS
}