package com.mmt.hotel.base.events

open class HotelEvent(val eventID: String, val data: Any? = null, val eventType: EventType = EventType.CLICK, val extraData: Any? = null) {

    fun copy(eventID: String = this.eventID,data: Any? = this.data,extraData: Any? = this.extraData, eventType : EventType = this.eventType, ): HotelEvent {
        return HotelEvent(eventID, data, eventType, extraData)
    }

}
enum class EventType{
    TRACKING,
    CLICK,
    NAVIGATION,
    BOTTOM_SHEET,
    ACTIVITY_RESULT
}