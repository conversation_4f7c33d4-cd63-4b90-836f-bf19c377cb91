package com.mmt.hotel.base.ui.viewHolder

import android.view.LayoutInflater
import android.view.ViewGroup
import com.mmt.hotel.base.adapter.adapterModel.GenericLoadingAdapterModel
import com.mmt.hotel.databinding.ItemViewProgressBarBinding

/**
 * Generic viewHolder to display a loading view within a RecyclerView
 *
 * create by <PERSON><PERSON><PERSON> on 26/04/21
 */
class GenericLoadingViewHolder (layoutInflater: LayoutInflater, layoutId: Int, parent: ViewGroup)
    : HotelRecyclerViewHolder<ItemViewProgressBarBinding, GenericLoadingAdapterModel>(layoutInflater, layoutId, parent) {
    override fun bindData(data: GenericLoadingAdapterModel, position: Int) {
        with(dataBinding) {
            model = data
        }
    }
}