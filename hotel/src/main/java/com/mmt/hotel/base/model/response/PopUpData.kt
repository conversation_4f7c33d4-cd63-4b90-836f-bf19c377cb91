package com.mmt.hotel.base.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

/**
* Please use this data class as base class for all Pop ups
* (if additional fields are required)
* */
@Parcelize
data class PopUpData(
    val title: String?,
    @SerializedName("subtitle",alternate = ["subTitle"])
    val subTitle: String?,
    val primaryCtaText: String?,
    val secondaryCtaText: String?
) : Parcelable