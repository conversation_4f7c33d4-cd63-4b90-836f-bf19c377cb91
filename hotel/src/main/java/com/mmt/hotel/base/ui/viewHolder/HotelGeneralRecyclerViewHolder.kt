package com.mmt.hotel.base.ui.viewHolder

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.mmt.hotel.BR

/**
 * [HotelGeneralRecyclerViewHolder] class is used for binding variable into layout for recyclerViewHolder
 * variable name defined in xml file must be viewModel
 * @see [com.mmt.travel.app.hotel.selectRoomV2.adapter.TariffSelectionAdapter]
 */
open class HotelGeneralRecyclerViewHolder<T : ViewDataBinding, V>(layoutInflater: LayoutInflater, layoutId: Int,
                                                             parent: ViewGroup, private val bindingId: Int = -1)
    : HotelRecyclerViewHolder<T, V>(layoutInflater,
        layoutId, parent) {
    override fun bindData(data: V, position: Int) {
        if (bindingId != -1) {
            dataBinding.setVariable(bindingId, data) //variable name defined in xml file
        } else {
            dataBinding.setVariable(BR.viewModel, data) //variable name defined in xml file must be viewModel
        }
        dataBinding.executePendingBindings()
    }
}