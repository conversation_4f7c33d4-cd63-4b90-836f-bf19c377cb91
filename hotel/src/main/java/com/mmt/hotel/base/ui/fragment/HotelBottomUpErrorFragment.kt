package com.mmt.hotel.base.ui.fragment

import androidx.annotation.CallSuper
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.FragmentActivity
import com.gommt.logger.LogUtils
import com.mmt.core.util.performIfActivityActive
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelErrorFragmentViewModel

abstract class HotelBottomUpErrorFragment<T : HotelErrorFragmentViewModel, V : ViewDataBinding> : HotelBottomUpFragment<T, V>() {
    override fun onHandleBackPress() {
        try {
            performIfActivityActive(activity) { activity ->
                (activity as FragmentActivity).supportFragmentManager.popBackStackImmediate()
            }
        } catch (e: Exception) {
            LogUtils.error("HotelBottomUpErrorFragment", e)
        }
    }

    @CallSuper
    override fun handleEvents(event: HotelEvent) {
        onHandleBackPress()
    }
}