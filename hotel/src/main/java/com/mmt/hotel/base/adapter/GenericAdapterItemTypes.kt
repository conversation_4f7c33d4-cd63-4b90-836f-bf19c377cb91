package com.mmt.hotel.base.adapter

import androidx.annotation.IntDef
import com.mmt.hotel.base.adapter.GenericAdapterItemTypes.Companion.LOADING_REVIEWS

/**
 * class to define Generic Adapter items
 *
 * create by <PERSON><PERSON><PERSON> on 28/04/21
 */

@Retention(AnnotationRetention.SOURCE)
@IntDef(
        flag = true,
        value = [
            LOADING_REVIEWS
        ]
)
annotation class GenericAdapterItemTypes {
    companion object {
        const val LOADING_REVIEWS = 1111
    }
}