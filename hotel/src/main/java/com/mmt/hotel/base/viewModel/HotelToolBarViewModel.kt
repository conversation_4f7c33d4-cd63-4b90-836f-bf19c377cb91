package com.mmt.hotel.base.viewModel

import androidx.compose.runtime.mutableStateOf
import com.mmt.core.constant.CoreConstants.EMPTY_STRING

abstract class HotelToolBarViewModel : HotelViewModel() {

    val isToolbarBlurred = mutableStateOf(false)

    abstract fun getTitle(): String

    abstract fun onHandleBackPress()

    open fun showCrossIcon() = true

    open fun showSearchIcon() = false

    open fun getSubTitle() = EMPTY_STRING

    open fun showToolBar() = true

    open fun onSearchClicked() {}

    open fun isBottomSheetToolBar() = false

}