package com.mmt.hotel.base.di

import androidx.activity.ComponentActivity
import androidx.annotation.MainThread
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelLazy
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.lifecycle.viewmodel.CreationExtras
import com.mmt.hotel.base.viewModel.HotelViewModelFactory

/**
 * Created by <PERSON><PERSON><PERSON> on 15/06/20.
 */

inline fun <reified T : ViewModel> FragmentActivity.getViewModel(factory: HotelViewModelFactory): T {
    return ViewModelProvider(this, factory)[T::class.java]
}

inline fun <reified T : ViewModel> ComponentActivity.getViewModel(factory: HotelViewModelFactory): T {
    return ViewModelProvider(this, factory)[T::class.java]
}

inline fun <reified T : ViewModel> Fragment.getViewModel(factory: HotelViewModelFactory): T {
    return ViewModelProvider(this, factory)[T::class.java]
}

inline fun <reified T : ViewModel> Fragment.getActivityViewModel(factory: HotelViewModelFactory): T {
    return ViewModelProviders.of(activity!!, factory).get(T::class.java)
}

inline fun <reified T : ViewModel> Fragment.getParentFragmentViewModel(factory: HotelViewModelFactory): T? {
    return parentFragment?.let { ViewModelProviders.of(it, factory).get(T::class.java) }
}

inline fun <reified T : ViewModel> FragmentActivity.getViewModel(): T {
    return ViewModelProvider(this, defaultViewModelProviderFactory)[T::class.java]
}

inline fun <reified T : ViewModel> Fragment.getViewModel(): T {
    return ViewModelProvider(this,defaultViewModelProviderFactory)[T::class.java]
}

inline fun <reified T : ViewModel> Fragment.getActivityViewModel(): T {
    return ViewModelProvider(activity!!.viewModelStore, defaultViewModelProviderFactory).get(T::class.java)
}

inline fun <reified T : ViewModel> Fragment.getParentFragmentViewModel(): T? {
    return parentFragment?.let { ViewModelProviders.of(it, defaultViewModelProviderFactory).get(T::class.java) }
}