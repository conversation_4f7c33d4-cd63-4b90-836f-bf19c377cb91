package com.mmt.hotel.base.ui.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import com.gommt.logger.LogUtils
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.mmt.core.util.Utils.initFabricWithDefault
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getActivityViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import javax.inject.Inject

abstract class HotelComposeBottomSheetFragment<T : HotelViewModel> : BottomSheetDialogFragment() {

    @Inject
    lateinit var factory: HotelViewModelFactory

    protected val activityViewModel by lazy { getActivityViewModel<HotelEventSharedViewModel>(factory) }
    abstract fun initViewModel(): T
    open fun injectDependency() {}
    open fun initAndValidate() {}

    val viewModel: T by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        initViewModel()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        injectDependency()
        initAndValidate()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, styleRes())
        initExtraDataLoggingForFabric()
        LogUtils.debug("HotelComposeBottomSheetFragment","OnCreate()..:"+javaClass.simpleName)

    }

    open fun styleRes() = R.style.HotelBottomSheetCornerRadiusDialogTheme

    final override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
        setContent {
            MainContent()
        }
    }

    @Composable
    abstract fun MainContent()
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel.eventStream.observe(viewLifecycleOwner) {
            handleEvents(it)
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.clearEventStream()
    }
    abstract fun handleEvents(event: HotelEvent)
    protected fun initExtraDataLoggingForFabric() {
        initFabricWithDefault(this.javaClass.simpleName)
    }
    protected fun sendEventToActivity(event: HotelEvent) {
        activityViewModel.updateEventStream(event)
    }
}