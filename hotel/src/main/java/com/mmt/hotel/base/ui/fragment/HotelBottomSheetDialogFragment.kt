package com.mmt.hotel.base.ui.fragment

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.Observer
import com.gommt.logger.LogUtils
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.mmt.core.util.Utils.initFabricWithDefault
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import java.util.UUID

/**
 * Hotel Base class for BottomSheet dialog fragment
 *
 * <AUTHOR> Airon : Dec 13, 2020
 */
abstract class HotelBottomSheetDialogFragment<T : HotelViewModel, V : ViewDataBinding> : BottomSheetDialogFragment() {
    lateinit var viewDataBinding: V
    abstract fun initViewModel(): T
    open fun injectDependency() {}
    open fun initAndValidate() {}
    val viewModel: T by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        initViewModel()
    }

    lateinit var fragmentID: String

    //Do not make public or update this variable, intended to only be used in between onCreate() and onResume()
    private var isRecreating : Boolean = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        injectDependency()
        initAndValidate()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        isRecreating = savedInstanceState != null
        super.onCreate(savedInstanceState)
        fragmentID = savedInstanceState?.getString(FRAGMENT_ID, UUID.randomUUID().toString()) ?: UUID.randomUUID().toString()
        setStyle(STYLE_NORMAL, styleRes())
        initExtraDataLoggingForFabric()
        LogUtils.debug("HotelBottomSheetDialogFragment","OnCreate()..:"+javaClass.simpleName)

    }

    override fun onResume() {
        super.onResume()
        isRecreating = false
    }

    fun isFragmentRecreating() : Boolean {
        return isRecreating
    }

    open fun styleRes() = R.style.HotelBottomSheetCornerRadiusDialogTheme

    final override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        viewDataBinding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false)
        setDataBinding()
        initFragmentView()
        viewDataBinding.root.isClickable = true
        return viewDataBinding.root
    }

    abstract fun setDataBinding()

    abstract fun getLayoutId(): Int

    abstract fun initFragmentView()

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel.eventStream.observe(viewLifecycleOwner, Observer {
            handleEvents(it)
        })
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.clearEventStream()
    }

    abstract fun handleEvents(event: HotelEvent)

    protected fun initExtraDataLoggingForFabric() {
        initFabricWithDefault(this.javaClass.simpleName)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putString(FRAGMENT_ID, fragmentID)
        super.onSaveInstanceState(outState)
    }

    companion object {
        const val FRAGMENT_ID = "FRAGMENT_ID"
    }
}