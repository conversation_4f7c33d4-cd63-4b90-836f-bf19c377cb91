package com.mmt.hotel.base.di

import androidx.lifecycle.ViewModel
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory

import dagger.Module
import dagger.Provides
import javax.inject.Provider

@Module
class HotelModule {
    @Provides
    fun provideViewModelFactory(viewmodel: MutableMap<Class<out ViewModel>, Provider<HotelViewModel>>): HotelViewModelFactory {
        return HotelViewModelFactory(viewmodel)
    }
}