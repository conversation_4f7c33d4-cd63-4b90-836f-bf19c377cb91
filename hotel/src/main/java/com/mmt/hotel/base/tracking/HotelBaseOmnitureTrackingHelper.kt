package com.mmt.hotel.base.tracking

import com.gommt.logger.LogUtils
import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureHelper
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper.TRACK_GCC_EVENT
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.COLON
import com.mmt.core.constant.CoreConstants.PIPE_SEPARATOR
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.util.DateUtil
import com.mmt.core.util.LocaleHelper.getCurrentApiLanguage
import com.mmt.core.util.MailDateFormatter
import com.mmt.core.extensions.addTrackText
import com.mmt.hotel.analytics.HotelOmnitureKeyConstants
import com.mmt.hotel.analytics.pdt.TrackingConstants
import com.mmt.hotel.analytics.pdtMetrics.HotelMetricsTracker
import com.mmt.hotel.analytics.pdtMetrics.helper.HotelPdtV2MetricsHelper
import com.mmt.hotel.base.events.TrackEvent
import com.mmt.hotel.base.model.tracking.HotelBaseTrackingData
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.HOTEL_SEARCH
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil
import java.text.ParseException
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList


abstract class HotelBaseOmnitureTrackingHelper {

    companion object{
        const val DATE_FORMAT = "MMddyyyy"

        const val MOB_DOM_HOTELS = "mob domestic hotels"
        const val MOB_INTL_HOTELS = "mob Intl hotels"
        const val MOB_DOMESTIC_HOMESTAY = "mob domestic homestay"
        const val MOB_INTL_HOMESTAY = "mob Intl homestay"

        const val INTL_CITY_CONSTANT = "mob Intl hotels"
        const val INTL_CITY_HOMESTAY_CONSTANT = "mob Intl homestay"
        const val INTL_CITY_GROUP_CONSTANT = "mob Intl group"
        const val INTL_CITY_DAY_USE_CONSTANT = "mob Intl dayuse"
        const val INTL_CITY_SHORT_STAYS_CONSTANT = "mob Intl shortstays"

        const val DOM_CITY_CONSTANT = "mob domestic hotels"
        const val DOM_CITY_HOMESTAY_CONSTANT = "mob domestic homestay"
        const val DOM_CITY_GROUP_CONSTANT = "mob domestic group"
        const val DOM_CITY_DAY_USE_CONSTANT = "mob domestic dayuse"
        const val DOM_CITY_SHORT_STAYS_CONSTANT = "mob domestic shortstays"

        const val CHANNEL_DOM_HOTEL = "mob domestic hotels funnel"
        const val CHANNEL_DOM_HOMESTAY = "mob domestic homestay funnel"
        const val CHANNEL_DOM_HOTEL_CORP = "mob domestic hotels funnel|corporate"
        const val CHANNEL_INTL_HOTEL = "mob intl hotels funnel"
        const val CHANNEL_INTL_HOMESTAY = "mob intl homestay funnel"
        const val CHANNEL_INTL_HOTEL_CORP = "mob intl hotels funnel|corporate"
        const val CHANNEL_BUDGET = "mob budget hotels funnel|corporate"
    }

    protected val pageExitTrackEvents = CopyOnWriteArrayList<TrackEvent>()

    @Throws(ParseException::class)
    fun commonOmnitureEvent(userSearchData: UserSearchData?): MutableMap<String, Any?> {
        val eventMap: MutableMap<String, Any?> = HashMap()
        userSearchData?.let {
            addOccupancyDataCombined(eventMap, it.occupancyData)
            addOccupancyData(eventMap, it.occupancyData)
            addCheckInDetail(eventMap, it.checkInDate, it.checkOutDate,it.checkInTimeInMills)
            // gcc omniture events
            eventMap[TRACK_GCC_EVENT] = OmnitureHelper.getDomainSbu()
            eventMap[HotelOmnitureKeyConstants.EVAR123] = "currency_" + HotelCurrencyUtil.getSelectedCurrencyCode()
            eventMap[HotelOmnitureKeyConstants.EVAR125] = "|" + userSearchData.countryCode
            eventMap[OmnitureTrackingHelper.OEPK_V_3] = "|" + it.locationName
            addFunnelData(eventMap, it.funnelSrc, it.countryCode)

            eventMap[OmnitureTrackingHelper.OEPK_V_12] = "Rank_" + userSearchData.position
            eventMap[OmnitureTrackingHelper.OEPK_C_19] = getTravellerType(userSearchData.travellerType)
        }
        if (eventMap.contains(OmnitureTrackingHelper.OEPK_V_3) && eventMap[OmnitureTrackingHelper.OEPK_V_3] is String) {
            val string = (eventMap[OmnitureTrackingHelper.OEPK_V_3] as String).trim()
            if (string.isEmpty() || string == "|") {
                HotelMetricsTracker.trackEvarFailure(userSearchData, HotelPdtV2MetricsHelper.MetricsEntityId.EVAR_3_EMPTY)
            }
        } else {
            HotelMetricsTracker.trackEvarFailure(userSearchData, HotelPdtV2MetricsHelper.MetricsEntityId.EVAR_3_NULL)
        }
        return eventMap
    }



    private fun getTravellerType(travellerType: Int): String {
        return when (travellerType) {
            HotelConstants.TRAVELLER_TYPE_LEISURE -> HotelConstants.DEFAULT_TRAVELLER_TYPE_LEISURE
            HotelConstants.TRAVELLER_TYPE_BUSINESS -> HotelConstants.DEFAULT_TRAVELLER_TYPE_BUSINESS
            else -> HotelConstants.DEFAULT_TRAVELLER_TYPE_NONE
        }
    }

    private fun addOccupancyDataCombined(eventMap: MutableMap<String, Any?>, occupancyData: OccupancyData){
        val adultCount = occupancyData.adultCount
        val childCount = occupancyData.childAges.size
        val totalCount = adultCount + childCount
        eventMap[OmnitureTrackingHelper.OEPK_V_27] = adultCount.toString() +
                "|" + childCount.toString() +
                "|" + totalCount.toString()
    }

    private fun getPaxDetails(occupancyData: OccupancyData) :String {
        val paxDetails = StringBuilder()
        val children = occupancyData.childAges.size
        paxDetails.append("Count_$children")
        if (children > 0) {
            paxDetails.append("|Y")
            paxDetails.append(occupancyData.childAges.joinToString("|Y"))
        }
        return paxDetails.toString()
    }

    private fun addCheckInDetail(
        eventMap: MutableMap<String, Any?>,
        checkIn: String,
        checkOut: String,
        checkInTimeInMills: Long?
    ){
        if(checkIn.isEmpty() || checkOut.isEmpty()){
            return
        }
        val checkInDate: Date = DateUtil.convertStringToDate(checkIn, DATE_FORMAT)
        val checkInOmniFormat = MailDateFormatter.getFormattedStringFromDate(checkInDate, MailDateFormatter.FORMAT_DAY_MONTH_YEAR, ":")
        val checkOutDate: Date = DateUtil.convertStringToDate(checkOut, DATE_FORMAT)
        val checkOutOmniFormat = MailDateFormatter.getFormattedStringFromDate(checkOutDate, MailDateFormatter.FORMAT_DAY_MONTH_YEAR, ":")
        eventMap[OmnitureTrackingHelper.OEPK_V_4] = checkInOmniFormat
        eventMap[OmnitureTrackingHelper.OEPK_V_5] = checkOutOmniFormat
        eventMap[OmnitureTrackingHelper.OEPK_V_7] = Integer.toString(DateUtil.getDiffDate(Date(), checkInDate))
        checkInTimeInMills?.let {
            eventMap[OmnitureTrackingHelper.OEPK_m64] = DateUtil.convertLongToString(it,HotelConstants.HH_MM_24HR_TIME_FORMAT)
        }
    }

    private fun addFunnelData(eventMap: MutableMap<String, Any?>, funnelSrc: Int, countryCode: String){
            eventMap[OmnitureTrackingHelper.OEPK_m24] = HotelUtil.getFunnelTrackingName(HotelUtil.isDom(countryCode),funnelSrc)
            eventMap[OmnitureTrackingHelper.OEPK_ch] = getSiteSectionName(HotelUtil.isDom(countryCode), funnelSrc)
    }

    fun track(eventMap: MutableMap<String, Any?>, userSearchData: UserSearchData) {
        OmnitureTrackingHelper.trackAppState(getScreenEvent(userSearchData), eventMap)
    }

    fun trackEvent(variableName:String, eventName:String, userSearchData: UserSearchData, baseTrackingData: HotelBaseTrackingData) {
        val eventMap: MutableMap<String, Any?> = getCommonEvents(userSearchData,baseTrackingData)
        eventMap[variableName] = eventName
        track(eventMap, userSearchData)
    }

    open fun getCommonEvents(userSearchData: UserSearchData, trackingData: HotelBaseTrackingData): MutableMap<String, Any?> {
        val eventMap: MutableMap<String, Any?> = commonOmnitureEvent(userSearchData)
        eventMap[OmnitureTrackingHelper.OEPK_V_73] = HotelUtil.getNights(userSearchData.checkInDate, userSearchData.checkOutDate)

        if (userSearchData.funnelSrc != HotelFunnel.HOMESTAY.funnelValue) {
            eventMap[OmnitureTrackingHelper.OEPK_C_39] = getScreenEvent(userSearchData)
        } else {
            eventMap[OmnitureTrackingHelper.OEPK_C_39] = getScreenEvent(userSearchData) + "|homeStay"
        }
        trackingData.let { data ->
            eventMap[OmnitureTrackingHelper.OEPK_V_9] = data.tripType
            eventMap[OmnitureTrackingHelper.OEPK_c5] = data.stayType + (data.supplierType?.let { "|$it" } ?: CoreConstants.EMPTY_STRING)
        }
        eventMap[OmnitureTrackingHelper.OEPK_REVENUE_Products] = ";" + userSearchData.hotelId
        eventMap[OmnitureTrackingHelper.OEPK_V_42] = trackingData.starRating
        eventMap[OmnitureTrackingHelper.OEPK_C_57] = trackingData.propertyType.orEmpty()
        eventMap[OmnitureTrackingHelper.OEPK_c23] = trackingData.previousPage.orEmpty()
        trackingData.selectedSlotDuration?.let{
            eventMap[OmnitureTrackingHelper.OEPK_V_6] = it
        }
        addPropertyData(eventMap, userSearchData, trackingData)
        return eventMap
    }

    abstract fun getScreenEvent(userSearchData: UserSearchData): String

    fun addOccupancyData(eventMap: MutableMap<String, Any?>, occupancyData: OccupancyData) {
        eventMap[OmnitureTrackingHelper.OEPK_V_20] = occupancyData.roomCount ?: 0 // 0 (roomCount = null) Indicates No explicit bedroom selection by user in AltAcco funnel
        eventMap[OmnitureTrackingHelper.OEPK_V_21] = occupancyData.adultCount
        eventMap[OmnitureTrackingHelper.OEPK_V_23] = getPaxDetails(occupancyData)
    }

    open fun getCommonErrorEvents(userSearchData: UserSearchData): MutableMap<String, Any?> {
        val eventMap: MutableMap<String, Any?> = commonOmnitureEvent(userSearchData)
        eventMap[OmnitureTrackingHelper.OEPK_V_73] = HotelUtil.getNights(userSearchData.checkInDate, userSearchData.checkOutDate)
        return eventMap
    }

    fun addCommonPageLoadEvents(eventMap: MutableMap<String, Any?>, userSearchData: UserSearchData) {
        val funnelName = HotelUtil.getFunnelTrackingName(HotelUtil.isDom(userSearchData.countryCode), userSearchData.funnelSrc)
        val searchType = userSearchData.searchType
        val entityName = userSearchData.displayName
        eventMap[OmnitureTrackingHelper.OEPK_V_55] = "${HOTEL_SEARCH}|$funnelName|$searchType|$entityName"
        eventMap[HotelOmnitureKeyConstants.EVAR125] = "|" +  userSearchData.countryCode
        if (userSearchData.funnelSrc == HotelFunnel.GETAWAYS.funnelValue) {
            eventMap[OmnitureTrackingHelper.OEPK_V_37] = TrackingConstants.FUNNEL_NEARBY
        }
        addOccupancyData(eventMap, userSearchData.occupancyData)
        if (eventMap.contains(OmnitureTrackingHelper.OEPK_V_55) && eventMap[OmnitureTrackingHelper.OEPK_V_55] is String) {
            val string = (eventMap[OmnitureTrackingHelper.OEPK_V_55] as String).trim()
            if (string.isEmpty()) {
                HotelMetricsTracker.trackEvarFailure(userSearchData, HotelPdtV2MetricsHelper.MetricsEntityId.EVAR_55_EMPTY)
            }
        } else {
            HotelMetricsTracker.trackEvarFailure(userSearchData, HotelPdtV2MetricsHelper.MetricsEntityId.EVAR_55_NULL)
        }
    }
    fun getExperimentDataPairs(data:String):Pair<String,String> {
        var part1: String
        var part2 = ""
        if (data.length > 240) {
            part1 = data.substring(0, 240)
            part2 = data.substring(240, data.length)
            val indexOfPipe = part2.indexOf("|")
            if (indexOfPipe != -1) {
                part1 += part2.substring(0, indexOfPipe)
                part2 = part2.substring(indexOfPipe, part2.length)
            } else {
                part1 += part2
                part2 = CoreConstants.EMPTY_STRING
            }
        } else {
            part1 = data
        }
        return Pair(part1,part2)
    }

    fun addPropertyData(eventMap: MutableMap<String, Any?>, userSearchData: UserSearchData, hotelBaseTrackingData: HotelBaseTrackingData) {
        val funnel = HotelUtil.getFunnelSource(userSearchData.funnelSrc)
        val propertyType = hotelBaseTrackingData.propertyType.orEmpty()
        val hotelViewType =  hotelBaseTrackingData.propertyViewType
        hotelViewType?.let {
            eventMap[OmnitureTrackingHelper.EVAR108] = "$funnel$PIPE_SEPARATOR$hotelViewType$PIPE_SEPARATOR$propertyType"
        }
    }

    /**
     * Must be override in case of landing screen and default name will be landing
     * */
    protected open fun getSiteSectionName(isDom: Boolean, funnelSrc: Int): String {
        return if (isDom) {
            when {
                funnelSrc == HotelFunnel.CORP_BUDGET.funnelValue -> CHANNEL_BUDGET
                funnelSrc == HotelFunnel.HOMESTAY.funnelValue -> CHANNEL_DOM_HOMESTAY
                LoginUtil.isCorporateUser() -> CHANNEL_DOM_HOTEL_CORP
                else -> CHANNEL_DOM_HOTEL
            }
        } else {
            when {
                funnelSrc == HotelFunnel.CORP_BUDGET.funnelValue -> CHANNEL_BUDGET
                funnelSrc == HotelFunnel.HOMESTAY.funnelValue -> CHANNEL_INTL_HOMESTAY
                LoginUtil.isCorporateUser() -> CHANNEL_INTL_HOTEL_CORP
                else -> CHANNEL_INTL_HOTEL
            }
        }
    }

    fun appendPageExitEvent(event: TrackEvent){
        pageExitTrackEvents.add(event)
    }

    //Appends an event to pageExit with "_<count>" at the end. count increases based on the number of times the same event gets fired
    fun appendEventToPageExitWithCount(event: TrackEvent) {
        val oldEvent = pageExitTrackEvents.find { it.value == event.value }
        val count = oldEvent?.count ?: 0
        oldEvent?.let { pageExitTrackEvents.remove(it) }
        appendPageExitEvent(event.copy(count = count + 1))
    }

    fun appendPageExitEvent(events: List<TrackEvent>){
        pageExitTrackEvents.addAll(events)
    }

    fun trackPageExitEvents(userSearchData: UserSearchData) {
        if (pageExitTrackEvents.isEmpty()) return
        trackEvents(pageExitTrackEvents,userSearchData)
        pageExitTrackEvents.clear()
    }

    fun trackEvents(events: List<TrackEvent>, userSearchData: UserSearchData,baseTrackingData: HotelBaseTrackingData? = null){
        val trackEvents = baseTrackingData?.let {  getCommonErrorEvents(userSearchData) }?: commonOmnitureEvent(userSearchData)
        events.forEach {
            val value = StringBuilder(it.value)
            if (it.count > 0){
                value.append(CoreConstants.UNDERSCORE).append(it.count.toString())
            }
            trackEvents.putTrackEvent(it.key, value.toString())
        }
        track(trackEvents,userSearchData)
    }

   protected fun MutableMap<String,Any?>.putTrackEvent(key: String,value: String){
       if(key == OmnitureTrackingHelper.OEPK_REVENUE_Products){
              put(key,value)
              return
       }
        put(key,get(key)?.toString()?.addTrackText(value)?:value)
    }

}