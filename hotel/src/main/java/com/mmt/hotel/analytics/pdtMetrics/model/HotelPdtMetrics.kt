package com.mmt.hotel.analytics.pdtMetrics.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.util.LOBS
import com.mmt.core.util.LocaleHelper
import com.mmt.hotel.analytics.pdtMetrics.helper.HotelPdtV2MetricsHelper
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelPdtMetrics(
    @SerializedName("entity_id")
    val entityId: String,
    @SerializedName("render_time")
    val renderTime: Int? = null, // in ms
    @SerializedName("download_time")
    val downloadTime: Int? = null, // in ms
    @SerializedName("download_size")
    val downloadSize: Long? = null, // in kilobytes
    @SerializedName("associated_id")
    val associatedID: String? = null,
    @SerializedName("screen_metrics")
    val screenMetrics: HotelScreenMetrics? = null,
    @SerializedName("entity_format")
    val entityFormat: String? = HotelPdtV2MetricsHelper.EntityFormat.JSON.name, // eg m3u8, mp4, png, json etc
    val url: String? = null,
    @SerializedName("funnel_context")
    val funnelContext: String = HotelPdtV2MetricsHelper.getMetricsFunnelContext().name,
    val type: String = HotelPdtV2MetricsHelper.MetricsType.SCREEN_RENDER.name,
    val language: String = LocaleHelper.getLobWiseLanguage(LOBS.HOTEL.lob),
    val status: String = HotelPdtV2MetricsHelper.MetricsStatus.SUCCESS.name
) : Parcelable