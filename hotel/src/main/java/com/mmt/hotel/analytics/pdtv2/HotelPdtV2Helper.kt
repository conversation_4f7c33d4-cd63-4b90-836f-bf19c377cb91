package com.mmt.hotel.analytics.pdtv2

import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.currency.CurrencyUtil
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.util.DateUtil
import com.mmt.data.model.homepage.searchevent.SearchEventLob
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants.B2B_PDT_V2_TEMPLATE_ID
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants.PDT_V2_TEMPLATE_ID
import com.mmt.hotel.analytics.pdtv2.model.Catalog
import com.mmt.hotel.analytics.pdtv2.model.HotelSearchContext
import com.mmt.hotel.analytics.pdtv2.model.Locus
import com.mmt.hotel.analytics.pdtv2.model.Pax
import com.mmt.hotel.analytics.pdtv2.model.ProductItem
import com.mmt.hotel.analytics.pdtv2.model.To
import com.mmt.hotel.base.model.tracking.HotelBaseTrackingData
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.AREA_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.CITY_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.COLLECTION_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.COUNTRY_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.CUSTOM_LOCATION_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.GPOI_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.GPS_NEARBY_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.HOTEL_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.POI_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.REGION_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.SEARCH_ZONE
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelTrackingUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.dayuse.helper.DayUseRequestCreator
import com.mmt.hotel.listingV2.dataModel.HotelFilterModelV2
import com.mmt.hotel.listingV2.event.SectionOrientation
import com.mmt.hotel.listingV2.model.response.hotels.Hotel
import com.mmt.travelplex.TravelPlexPageContext
import com.pdt.eagleEye.models.Adult
import com.pdt.eagleEye.models.Child
import com.pdt.eagleEye.models.Details
import java.util.concurrent.TimeUnit

object HotelPdtV2Helper {

    fun getCommonEventBuilder(
        eventName: String,
        eventType: String,
        pageName: String,
        userSearchData: UserSearchData,
        requestId: String?,
        baseTrackingData: HotelBaseTrackingData?,
        funnelStep: HotelPdtV2Constants.FunnelStep,
        allRequestIds: List<String>? = null,
        isMetricsEvent: Boolean = false
    ): HotelPdtEventBuilder {
        val topicName : String
        val templateId : Int

        if (isMetricsEvent) {
            topicName = HotelPdtV2Constants.PDT_V2_METRICS_TOPIC_ID
            templateId = HotelPdtV2Constants.METRICS_PDT_V2_TEMPLATE_ID
        } else if(LoginUtil.isCorporateUser()) {
            //For B2B users, we will use B2B topic and template
            topicName =  HotelPdtV2Constants.PDT_V2_B2B_TOPIC_ID
            templateId = B2B_PDT_V2_TEMPLATE_ID
        } else if(CurrencyUtil.shouldUseGlobalDomain()){
            // For global users, we will use Global topic and template
            topicName = HotelPdtV2Constants.PDT_V2_GLOBAL_TOPIC_ID
            templateId = PDT_V2_TEMPLATE_ID
        } else {
            // For B2C users, we will use B2C topic and template
            topicName = HotelPdtV2Constants.PDT_V2_B2C_TOPIC_ID
            templateId = PDT_V2_TEMPLATE_ID
        }
        val commonEventBuilder = HotelPdtEventBuilder(
            eventName = eventName,
            eventType = eventType,
            pageName = pageName,
            journeyId = userSearchData.journeyId ?: HotelUtil.getJourneyId(),
            topicName = topicName,
            templateID = templateId
        )
        setPageContext(commonEventBuilder, userSearchData,funnelStep)
        setEventTrackingContext(commonEventBuilder, userSearchData, baseTrackingData, requestId,allRequestIds)
        return commonEventBuilder
    }

    fun setEventTrackingContext(
        commonEventBuilder: HotelPdtEventBuilder,
        userSearchData: UserSearchData,
        baseTrackingData: HotelBaseTrackingData?,
        requestId: String?,
        allRequestIds: List<String>? = null
    ) {
        requestId?.let { commonEventBuilder.requestId(it) }
        commonEventBuilder.businessUnit(HotelPdtV2Constants.HOTELS_BU)
        commonEventBuilder.funnelEntry(HotelTrackingUtil.getSubLob(userSearchData.funnelSrc))
        commonEventBuilder.trafficSource(baseTrackingData?.cmpId.orEmpty())
        commonEventBuilder.eventTrackingContext.meta_req_id_ls = allRequestIds
        commonEventBuilder.eventTrackingContext.domainCurrency = LoginUtils.getFunnelContextCurrency().name.uppercase()
    }

    fun setPageContext(
        commonEventBuilder: HotelPdtEventBuilder,
        userSearchData: UserSearchData,
        funnelStep: HotelPdtV2Constants.FunnelStep
    ) {
        commonEventBuilder.lobCategory(HotelTrackingUtil.getLobCategory(userSearchData.countryCode))
        commonEventBuilder.subLob(HotelTrackingUtil.getSubLob(userSearchData.funnelSrc))
        commonEventBuilder.funnelStep(funnelStep.name)
    }

    fun createPageContext(userSearchData: UserSearchData,
                          funnelStep: HotelPdtV2Constants.FunnelStep,
                          baseTrackingData: HotelBaseTrackingData): TravelPlexPageContext {
        return TravelPlexPageContext(SearchEventLob.HOTEL.lob).apply {
            setSubLob(HotelTrackingUtil.getSubLob(userSearchData.funnelSrc))
            setLobCategory(HotelTrackingUtil.getLobCategory(userSearchData.countryCode))
            setFunnelStep(funnelStep.name)
            setPageName(funnelStep.name)
            setPreviousPageName(baseTrackingData.prevPageNamePdt.orEmpty())
            setTravelStore(com.mmt.travelplex.TravelStore(storeId = "MMT"))
        }
    }

    fun createProductList(
        hotelLists: List<Hotel>?
    ): List<ProductItem>? {
        return hotelLists?.mapIndexed { index, hotel ->
            createProduct(hotel = hotel, position = index)
        }
    }

    fun createProduct(
        hotel: Hotel,
        clickedIndex: Int? = null,
        position: Int,
        catalog: List<Catalog>? = null,
        @SectionOrientation orientation: String? = SectionOrientation.VERTICAL,
    ): ProductItem {
        return ProductItem(
            hotel,
            clickedIndex = clickedIndex,
            position = position,
            catalog = catalog,
            orientation = orientation
        )
    }

    fun createSearchContext(userSearchData: UserSearchData, filterData: HotelFilterModelV2?=null, hourlySelectedSlot:Int?=null): HotelSearchContext {
        val dayUseSlot = userSearchData.checkInTimeInMills?.let {
            DayUseRequestCreator.getDayUseApiSlot(it, hourlySelectedSlot)
        }
        val fromDateTime = if (userSearchData.funnelSrc == HotelFunnel.DAYUSE.funnelValue) {
            userSearchData.checkInTimeInMills
        } else {
            DateUtil.getTimeinMillisFromDateString(
                userSearchData.checkInDate,
                HotelConstants.SEARCH_DATE_FORMAT
            )
        }
        val toDateTime = if (userSearchData.funnelSrc == HotelFunnel.DAYUSE.funnelValue) {
            userSearchData.checkInTimeInMills?.plus(
                TimeUnit.HOURS.toMillis(
                    dayUseSlot?.duration?.toLong()
                        ?: 0
                )
            )
        } else {
            DateUtil.getTimeinMillisFromDateString(
                userSearchData.checkOutDate,
                HotelConstants.SEARCH_DATE_FORMAT
            )
        }

        val locus = Locus(
            locusId = userSearchData.locationId,
            locusType = userSearchData.locationType,
            country = userSearchData.countryCode
        )
        val adultCount = userSearchData.occupancyData.adultCount
        val adult = Adult(adultCount)
        val totalPax = adultCount + userSearchData.occupancyData.childAges.size
        val child = Child(
            ages = userSearchData.occupancyData.childAges,
            count = userSearchData.occupancyData.childAges.size
        )
        val pax = Pax(
            count = totalPax,
            details = Details(child = child, adult = adult),
            rooms = userSearchData.occupancyData.roomCount
        )
        return HotelSearchContext(
            checkInTime = dayUseSlot?.timeSlot,
            checkInDuration = dayUseSlot?.duration,
            to = To(locus),
            toDateTime = toDateTime,
            persons = pax,
            fromDateTime = fromDateTime,
            hotelId = getHotelId(userSearchData) ?: filterData?.hotels?.first()?.hotelId,
            searchText = userSearchData.displayName,
            searchType = getEventSearchType(userSearchData.searchType),
            trip_type = userSearchData.tripType
        )
    }

    fun getHotelId(userSearchData: UserSearchData): String? {
        return if(userSearchData.searchType == HOTEL_SEARCH){
            userSearchData.id ?: userSearchData.hotelId
        } else null
    }

    private fun getEventSearchType(searchType : String): String {
        return when(searchType){
            COUNTRY_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.country.name
            REGION_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.region.name
            CITY_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.city.name
            AREA_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.area.name
            POI_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.poi.name
            HOTEL_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.product.name
            COLLECTION_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.storefront.name
            CUSTOM_LOCATION_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.google.name
            GPOI_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.google.name
            GPS_NEARBY_SEARCH -> HotelPdtV2Constants.HotelEventSearchType.nearby.name
            SEARCH_ZONE -> HotelPdtV2Constants.HotelEventSearchType.zone.name
            else -> searchType
        }
    }

}