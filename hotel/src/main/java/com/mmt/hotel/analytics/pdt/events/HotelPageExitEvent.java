package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.constant.CommonConstants;
import com.mmt.core.util.StringUtil;
import com.pdt.pdtDataLogging.events.model.Event;
import com.pdt.pdtDataLogging.events.model.PageExitEvent;

public class HotelPageExitEvent extends PageExitEvent {
    private String hotelId;
    private HotelGenericEvent hotelGenericEvent;
    public HotelPageExitEvent(@NonNull String pageName, int eventType, long startTimeStamp,
                              @NonNull String omnitureName,
                              @NonNull String parentScreenName, String prevFunnelStep, String prevPageName) {
        super(PDTAnalyticsKeys.HOTEL_FUNNEL_TOPIC_ID, PDTAnalyticsKeys.HOTEL_FUNNEL_TEMPLATE_ID, pageName,
                CommonConstants.LOB_HOTEL, eventType, startTimeStamp, omnitureName, parentScreenName, prevFunnelStep, prevPageName);
        hotelGenericEvent = new HotelGenericEvent(PAGE_EXIT, pageName, eventType, omnitureName, parentScreenName, false);
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (StringUtil.isNotNullAndEmpty(hotelId)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_ID, hotelId);
        }
        event.getEventParam().putAll(hotelGenericEvent.createPDTEvent().getEventParam());
        return event;
    }

    public String getHotelId() {
        return this.hotelId;
    }

    public HotelGenericEvent getHotelGenericEvent() {
        return this.hotelGenericEvent;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    public void setHotelGenericEvent(HotelGenericEvent hotelGenericEvent) {
        this.hotelGenericEvent = hotelGenericEvent;
    }

    public String toString() {
        return "HotelPageExitEvent(hotelId=" + this.hotelId + ", hotelGenericEvent=" + this.hotelGenericEvent + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelPageExitEvent))
            return false;
        final HotelPageExitEvent other = (HotelPageExitEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$hotelId = this.getHotelId();
        final Object other$hotelId = other.getHotelId();
        if (this$hotelId == null ? other$hotelId != null : !this$hotelId.equals(other$hotelId))
            return false;
        final Object this$hotelGenericEvent = this.getHotelGenericEvent();
        final Object other$hotelGenericEvent = other.getHotelGenericEvent();
        if (this$hotelGenericEvent == null ? other$hotelGenericEvent != null : !this$hotelGenericEvent.equals(other$hotelGenericEvent))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelPageExitEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $hotelId = this.getHotelId();
        result = result * PRIME + ($hotelId == null ? 43 : $hotelId.hashCode());
        final Object $hotelGenericEvent = this.getHotelGenericEvent();
        result = result * PRIME + ($hotelGenericEvent == null ? 43 : $hotelGenericEvent.hashCode());
        return result;
    }
}
