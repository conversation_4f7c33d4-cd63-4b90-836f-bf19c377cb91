package com.mmt.hotel.analytics.pdtv2

import com.google.gson.annotations.SerializedName
import com.mmt.hotel.analytics.pdtv2.model.ContentDetailItem
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtComponent
import com.mmt.hotel.analytics.pdtv2.model.PdtFiltersV2
import com.mmt.hotel.analytics.pdtv2.model.PillItem
import com.mmt.hotel.analytics.pdtv2.model.ProductItem
import com.mmt.hotel.listingV2.model.request.SorterCriteria

class HotelPdtComponentBuilder {

    var id: String? = null
    var filters: PdtFiltersV2? = null

    @SerializedName("content_details")
    var contentDetails: List<ContentDetailItem>? = null

    @SerializedName("component_type")
    var componentType: String? = null

    @SerializedName("sort_by")
    var sortBy: SorterCriteria? = null

    @SerializedName("pills_data")
    var pillsData: List<PillItem>? = null

    @SerializedName("product_list")
    var productList: List<ProductItem>? = null

    fun build(): HotelPdtComponent {
        return HotelPdtComponent(
            id = id,
            filters = filters,
            content_details = contentDetails,
            component_type = componentType,
            sort_by = sortBy,
            pills_data = pillsData,
            product_list = productList
        )
    }
}