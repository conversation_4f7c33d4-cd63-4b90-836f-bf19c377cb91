package com.mmt.hotel.analytics

import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.mmt.core.MMTCore

object FirebaseEventsTracking {

    /**
     * method to log events along with the event Param on Firebase
     */
    fun logEventOnFirebase(eventName: String, paramBundle: Bundle) {
        FirebaseAnalytics.getInstance(MMTCore.mContext).logEvent(eventName, paramBundle)
    }
}