package com.mmt.hotel.analytics.pdtv2.model

import com.google.gson.annotations.SerializedName
import com.pdt.eagleEye.models.ErrorDetailList

data class HotelPdtErrorListItem(
    @SerializedName("code")
    val code: String = "",
    @SerializedName("message")
    val message: String = "",
    @SerializedName("severity")
    val severity: String? = null,
    @SerializedName("source")
    val source: String? = null,
): ErrorDetailList()
