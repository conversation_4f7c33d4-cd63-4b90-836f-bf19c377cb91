package com.mmt.hotel.analytics.pdt.model;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.mmt.core.util.StringUtil;
import com.mmt.hotel.analytics.pdt.HotelPdtKeys;
import com.mmt.hotel.analytics.pdt.events.HotelGenericEvent;
import com.pdt.pdtDataLogging.events.model.Event;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

public class SorterFilterEvent extends HotelGenericEvent {
    private boolean callSuperSF = true;
    private List<PdtFilter> removedFilterList;
    private List<PdtFilter> appliedFilterList;
    private List<String> removedFilterListOld;
    private List<String> appliedFilterListOld;
    //private boolean isBottomSheetFilterApplied;
    private List<String> dynamicFilterList;
    private List<String> amenityList;
    private List<String> matchMakerList;
    private List<String> userRatingList;
    private List<Integer> starRatingList;
    private List<String> propertyFilterList;
    private boolean isClearAllFilter;
    private List<ReviewedHotel> reviewedHotelList;
    protected String userAdId;
    private String sortBy;
    private String sortOrder;
    @Nullable
    private String inlineFilterGroupShown;
    @Nullable
    public List<String> shownFilterPills;
    @Nullable
    public List<String> clickedFilterPills;

    public SorterFilterEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                             @NonNull String omnitureName,
                             @NonNull String parentScreenName) {
        super(eventName, pageName, eventType, omnitureName, parentScreenName);
    }

    public SorterFilterEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                             @NonNull String omnitureName,
                             @NonNull String parentScreenName, boolean callSuper) {
        this(eventName, pageName, eventType, omnitureName, parentScreenName);
        this.callSuperSF = callSuper;
    }

    @Override
    protected Event createPDTEvent() {
        Event event;
        if (callSuperSF) {
            event = super.createPDTEvent();
        } else {
            event = Event.createEvent(getEventName(), new HashMap<>());
        }

        if (CollectionUtil.isNotEmptyCollection(removedFilterList)) {
            event.getEventParam().put(HotelPdtKeys.FILTER_REMOVED_LIST, removedFilterList);
        }
        if (CollectionUtil.isNotEmptyCollection(appliedFilterList)) {
            event.getEventParam().put(HotelPdtKeys.FILTER_APPLIED_LIST, appliedFilterList);
        }
        if(isClearAllFilter) {
            event.getEventParam().put(PDTAnalyticsKeys.IS_FILTERS_CLEARED, isClearAllFilter);
        }
        if(StringUtil.isNotNullAndEmpty(inlineFilterGroupShown)) {
            event.getEventParam().put(HotelPdtKeys.INLINE_FILTER_CATEGORY_SHOWN,inlineFilterGroupShown);
        }
        if (CollectionUtil.isNotEmptyCollection(shownFilterPills)) {
            event.getEventParam().put(HotelPdtKeys.PILLS_SHOWN, shownFilterPills);
        }
        if (CollectionUtil.isNotEmptyCollection(clickedFilterPills)) {
            event.getEventParam().put(HotelPdtKeys.PILLS_CLICKED, clickedFilterPills);
        }
        if (CollectionUtil.isNotEmptyCollection(removedFilterListOld)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_RMVD, removedFilterListOld);
        }
        if (CollectionUtil.isNotEmptyCollection(appliedFilterListOld)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_APPLD, appliedFilterListOld);
        }
        if (CollectionUtil.isNotEmptyCollection(dynamicFilterList)) {
            event.getEventParam().put(PDTAnalyticsKeys.DYNAMIC_FLTR_APPLD, dynamicFilterList);
        }
        if (CollectionUtil.isNotEmptyCollection(amenityList)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_AMINITIES, amenityList);
        }
        if (CollectionUtil.isNotEmptyCollection(matchMakerList)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_LOC, matchMakerList);
        }
        if (CollectionUtil.isNotEmptyCollection(userRatingList)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_USR_RATING, userRatingList);
        }
        if (CollectionUtil.isNotEmptyCollection(starRatingList)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_STAR, starRatingList);
        }
        if (CollectionUtil.isNotEmptyCollection(propertyFilterList)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_PROP_TYP, propertyFilterList);
        }
        if (CollectionUtil.isNotEmptyCollection(reviewedHotelList)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_DTL_DISP, reviewedHotelList);
        }

        if (StringUtil.isNotNullAndEmpty(sortBy)) {
            event.getEventParam().put(PDTAnalyticsKeys.SRT_BY, sortBy);
        }

        if (StringUtil.isNotNullAndEmpty(userAdId)) {
            event.getEventParam().put(PDTAnalyticsKeys.USER_AD_ID, userAdId);
        }

        if (StringUtil.isNotNullAndEmpty(sortOrder)) {
            event.getEventParam().put(PDTAnalyticsKeys.SRT_ORDR, sortOrder);
        }
        return event;
    }

    @Nullable
    public String getInlineFilterGroupShown() {
        return inlineFilterGroupShown;
    }

    public void setInlineFilterGroupShown(@Nullable String inlineFilterGroupShown) {
        this.inlineFilterGroupShown = inlineFilterGroupShown;
    }

    public boolean isCallSuperSF() {
        return this.callSuperSF;
    }

    public List<PdtFilter> getRemovedFilterList() {
        return this.removedFilterList;
    }

    public List<PdtFilter> getAppliedFilterList() {
        return this.appliedFilterList;
    }

    public List<String> getRemovedFilterListOld() {
        return removedFilterListOld;
    }

    public void setRemovedFilterListOld(List<String> removedFilterListOld) {
        this.removedFilterListOld = removedFilterListOld;
    }

    public List<String> getAppliedFilterListOld() {
        return appliedFilterListOld;
    }

    public void setAppliedFilterListOld(List<String> appliedFilterListOld) {
        this.appliedFilterListOld = appliedFilterListOld;
    }

    public List<String> getDynamicFilterList() {
        return this.dynamicFilterList;
    }

    public List<String> getAmenityList() {
        return this.amenityList;
    }

    public List<String> getMatchMakerList() {
        return this.matchMakerList;
    }

    public List<String> getUserRatingList() {
        return this.userRatingList;
    }

    public List<Integer> getStarRatingList() {
        return this.starRatingList;
    }

    public List<String> getPropertyFilterList() {
        return this.propertyFilterList;
    }

    public boolean isClearAllFilter() {
        return this.isClearAllFilter;
    }

    public List<ReviewedHotel> getReviewedHotelList() {
        return this.reviewedHotelList;
    }

    public String getSortBy() {
        return this.sortBy;
    }

    public String getSortOrder() {
        return this.sortOrder;
    }

    public void setCallSuperSF(boolean callSuperSF) {
        this.callSuperSF = callSuperSF;
    }

    public String getUserAdId() {
        return userAdId;
    }

    public void setUserAdId(String userAdId) {
        this.userAdId = userAdId;
    }

    public void setRemovedFilterList(List<PdtFilter> removedFilterList) {
        this.removedFilterList = removedFilterList;
    }

    public void setAppliedFilterList(List<PdtFilter> appliedFilterList) {
        this.appliedFilterList = appliedFilterList;
    }

    public void setDynamicFilterList(List<String> dynamicFilterList) {
        this.dynamicFilterList = dynamicFilterList;
    }

    public void setAmenityList(List<String> amenityList) {
        this.amenityList = amenityList;
    }

    public void setMatchMakerList(List<String> matchMakerList) {
        this.matchMakerList = matchMakerList;
    }

    public void setUserRatingList(List<String> userRatingList) {
        this.userRatingList = userRatingList;
    }

    public void setStarRatingList(List<Integer> starRatingList) {
        this.starRatingList = starRatingList;
    }

    public void setPropertyFilterList(List<String> propertyFilterList) {
        this.propertyFilterList = propertyFilterList;
    }

    public void setClearAllFilter(boolean isClearAllFilter) {
        this.isClearAllFilter = isClearAllFilter;
    }

    public void setReviewedHotelList(List<ReviewedHotel> reviewedHotelList) {
        this.reviewedHotelList = reviewedHotelList;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public String toString() {
        return "SorterFilterEvent{" +
                "callSuperSF=" + callSuperSF +
                ", removedFilterList=" + removedFilterList +
                ", appliedFilterList=" + appliedFilterList +
                ", removedFilterListOld=" + removedFilterListOld +
                ", appliedFilterListOld=" + appliedFilterListOld +
                ", dynamicFilterList=" + dynamicFilterList +
                ", amenityList=" + amenityList +
                ", matchMakerList=" + matchMakerList +
                ", userRatingList=" + userRatingList +
                ", starRatingList=" + starRatingList +
                ", propertyFilterList=" + propertyFilterList +
                ", isClearAllFilter=" + isClearAllFilter +
                ", reviewedHotelList=" + reviewedHotelList +
                ", userAdId='" + userAdId + '\'' +
                ", sortBy='" + sortBy + '\'' +
                ", sortOrder='" + sortOrder + '\'' +
                '}';
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof SorterFilterEvent))
            return false;
        final SorterFilterEvent other = (SorterFilterEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        if (this.isCallSuperSF() != other.isCallSuperSF()) return false;
        final Object this$removedFilterList = this.getRemovedFilterList();
        final Object other$removedFilterList = other.getRemovedFilterList();
        if (this$removedFilterList == null ? other$removedFilterList != null : !this$removedFilterList.equals(other$removedFilterList))
            return false;
        final Object this$appliedFilterList = this.getAppliedFilterList();
        final Object other$appliedFilterList = other.getAppliedFilterList();
        if (this$appliedFilterList == null ? other$appliedFilterList != null : !this$appliedFilterList.equals(other$appliedFilterList))
            return false;
        final Object this$dynamicFilterList = this.getDynamicFilterList();
        final Object other$dynamicFilterList = other.getDynamicFilterList();
        if (this$dynamicFilterList == null ? other$dynamicFilterList != null : !this$dynamicFilterList.equals(other$dynamicFilterList))
            return false;
        final Object this$amenityList = this.getAmenityList();
        final Object other$amenityList = other.getAmenityList();
        if (this$amenityList == null ? other$amenityList != null : !this$amenityList.equals(other$amenityList))
            return false;
        final Object this$matchMakerList = this.getMatchMakerList();
        final Object other$matchMakerList = other.getMatchMakerList();
        if (this$matchMakerList == null ? other$matchMakerList != null : !this$matchMakerList.equals(other$matchMakerList))
            return false;
        final Object this$userRatingList = this.getUserRatingList();
        final Object other$userRatingList = other.getUserRatingList();
        if (this$userRatingList == null ? other$userRatingList != null : !this$userRatingList.equals(other$userRatingList))
            return false;
        final Object this$starRatingList = this.getStarRatingList();
        final Object other$starRatingList = other.getStarRatingList();
        if (this$starRatingList == null ? other$starRatingList != null : !this$starRatingList.equals(other$starRatingList))
            return false;
        final Object this$propertyFilterList = this.getPropertyFilterList();
        final Object other$propertyFilterList = other.getPropertyFilterList();
        if (this$propertyFilterList == null ? other$propertyFilterList != null : !this$propertyFilterList.equals(other$propertyFilterList))
            return false;
        if (this.isClearAllFilter() != other.isClearAllFilter()) return false;
        final Object this$reviewedHotelList = this.getReviewedHotelList();
        final Object other$reviewedHotelList = other.getReviewedHotelList();
        if (this$reviewedHotelList == null ? other$reviewedHotelList != null : !this$reviewedHotelList.equals(other$reviewedHotelList))
            return false;
        final Object this$sortBy = this.getSortBy();
        final Object other$sortBy = other.getSortBy();
        if (this$sortBy == null ? other$sortBy != null : !this$sortBy.equals(other$sortBy))
            return false;
        if (!Objects.equals(((SorterFilterEvent) o).userAdId, this.userAdId)) return false;
        final Object this$sortOrder = this.getSortOrder();
        final Object other$sortOrder = other.getSortOrder();
        if (this$sortOrder == null ? other$sortOrder != null : !this$sortOrder.equals(other$sortOrder))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof SorterFilterEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        result = result * PRIME + (this.isCallSuperSF() ? 79 : 97);
        final Object $removedFilterList = this.getRemovedFilterList();
        result = result * PRIME + ($removedFilterList == null ? 43 : $removedFilterList.hashCode());
        final Object $appliedFilterList = this.getAppliedFilterList();
        result = result * PRIME + ($appliedFilterList == null ? 43 : $appliedFilterList.hashCode());
        final Object $dynamicFilterList = this.getDynamicFilterList();
        result = result * PRIME + ($dynamicFilterList == null ? 43 : $dynamicFilterList.hashCode());
        final Object $amenityList = this.getAmenityList();
        result = result * PRIME + ($amenityList == null ? 43 : $amenityList.hashCode());
        final Object $matchMakerList = this.getMatchMakerList();
        result = result * PRIME + ($matchMakerList == null ? 43 : $matchMakerList.hashCode());
        final Object $userRatingList = this.getUserRatingList();
        result = result * PRIME + ($userRatingList == null ? 43 : $userRatingList.hashCode());
        final Object $starRatingList = this.getStarRatingList();
        result = result * PRIME + ($starRatingList == null ? 43 : $starRatingList.hashCode());
        final Object $propertyFilterList = this.getPropertyFilterList();
        result = result * PRIME + ($propertyFilterList == null ? 43 : $propertyFilterList.hashCode());
        result = result * PRIME + (this.isClearAllFilter() ? 79 : 97);
        final Object $reviewedHotelList = this.getReviewedHotelList();
        result = result * PRIME + ($reviewedHotelList == null ? 43 : $reviewedHotelList.hashCode());
        final Object $sortBy = this.getSortBy();
        result = result * PRIME + ($sortBy == null ? 43 : $sortBy.hashCode());
        final Object $userAdId = this.getUserAdId();
        result = result * PRIME + ($userAdId == null ? 43 : $userAdId.hashCode());
        final Object $sortOrder = this.getSortOrder();
        result = result * PRIME + ($sortOrder == null ? 43 : $sortOrder.hashCode());
        return result;
    }
}
