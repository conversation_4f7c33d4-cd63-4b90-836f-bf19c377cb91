package com.mmt.hotel.analytics.pdt.events;

import static com.mmt.analytics.omnitureclient.OmnitureHelper.SEND_DATE_FORMAT;
import static com.mmt.analytics.pdtclient.PDTAnalyticsKeys.POKUS_EXPERIMENT_DATA_V2;

import androidx.annotation.NonNull;

import com.mmt.analytics.omnitureclient.OmnitureHelper;
import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.mmt.core.util.DateUtil;
import com.mmt.core.util.StringUtil;
import com.mmt.data.model.util.SharedPreferenceUtils;
import com.mmt.hotel.base.tracking.HotelBasePdtTrackingHelper;
import com.mmt.hotel.common.constants.HotelFunnel;
import com.mmt.hotel.common.constants.SharedPrefKeys;
import com.mmt.hotel.common.helper.LocusTrackingHelperKt;
import com.mmt.hotel.common.model.UserSearchData;
import com.mmt.hotel.common.util.HotelUtil;
import com.mmt.hotel.filterV2.model.response.FilterV2;
import com.mmt.hotel.landingV3.model.request.SearchRequest;
import com.mmt.hotel.listingV2.dataModel.HotelFilterModelV2;
import com.mmt.hotel.listingV2.dataModel.LocationFiltersV2;
import com.gommt.logger.LogUtils;
import com.pdt.pdtDataLogging.events.model.Event;
import com.pdt.pdtDataLogging.events.model.ExperimentEventModel;
import com.pdt.pdtDataLogging.events.model.TrackingDataWrapper;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
* This class will not extend HotelGenericEvent class because as call
* are in use as member field in most of class.
* */
public class HotelLandingPageEvent extends HotelLocusDataGenericEvent {

    private static String TAG = "HotelLandingPageEvent";
    public static final String DELIMITER = "~";
    private String checkInDate;
    private String checkoutDate;
    private int adult;
    private int child;
    private int totalGuest;
    private int totalRoom;
    private String travelPurpose;
    private String cityString;
    private String countryString;
    private String areaString;
    private int stayLength;
    private int numberOfNight;
    private String roomStayQualifier;
    private String searchType;
    private List<ExperimentEventModel> experimentEventModelList;
    private String funnelSource = HotelFunnel.HOTEL.getFunnelName();

    private boolean callSuperHlp = true;

    public HotelLandingPageEvent(@NonNull String eventName,
                                 @NonNull String pageName,
                                 int eventType,
                                 @NonNull String omnitureName,
                                 @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(eventName,pageName, eventType, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
    }

    protected Event createOmnitureEvent() {
        return Event.createEvent(getEventName(),super.createOmnitureEvent().getEventParam());
    }

    protected Event createPDTEvent() {
        Event event;
        if (callSuperHlp) {
            event = super.createPDTEvent();
        } else {
            event = Event.createEvent(getEventName(), new HashMap<>());
        }

        Map<String, Object> eventParam = event.getEventParam();

        appendLocusParamsInEvent(event);

        if (StringUtil.isNotNullAndEmpty(getCheckInDate())) {
            eventParam.put(PDTAnalyticsKeys.SRCH_CHECKIN_DT, getCheckInDate());
        }
        if (StringUtil.isNotNullAndEmpty(getCheckoutDate())) {
            eventParam.put(PDTAnalyticsKeys.SRCH_CHECKOUT_DT, getCheckoutDate());
        }

        eventParam.put(PDTAnalyticsKeys.SRCH_GUEST_ADULT, getAdult());
        eventParam.put(PDTAnalyticsKeys.SRCH_GUEST_CHILD, getChild());
        eventParam.put(PDTAnalyticsKeys.SRCH_RM_TOT, getTotalRoom());

        String travelingPurpose = SharedPreferenceUtils.getInstance().getString(SharedPrefKeys.KEY_TRAVELING_PURPOSE);
        if (StringUtil.isNotNullAndEmpty(travelingPurpose)) {
            eventParam.put(PDTAnalyticsKeys.SRCH_TRVL_PURP, travelingPurpose);
        }
        eventParam.put(PDTAnalyticsKeys.SRCH_TRVL_PURP_OPTD,
                SharedPreferenceUtils.getInstance().getBoolean(SharedPrefKeys.KEY_TRAVELING_PURPOSE_OPTED));


        eventParam.put(PDTAnalyticsKeys.SRCH_GUEST_TOT, getTotalGuest());
        if (getStayLength() != 0) {
            eventParam.put(PDTAnalyticsKeys.SRCH_LOS, getStayLength());
        }
        if (numberOfNight != 0) {
            eventParam.put(PDTAnalyticsKeys.SRCH_RM_NGHTS, getNumberOfNight());
        }
        if (StringUtil.isNotNullAndEmpty(getRoomStayQualifier())) {
            eventParam.put(PDTAnalyticsKeys.SRCH_RSQ, getRoomStayQualifier());
        }

        int advancePurchase = getAdvancePurchase();
        if (advancePurchase != -1) {
            eventParam.put(PDTAnalyticsKeys.SRCH_AP, advancePurchase);
        }
        experimentEventModelList = HotelUtil.getExperimentsForPdtTracking(null);
        if (CollectionUtil.isNotEmptyCollection(experimentEventModelList)) {
            eventParam.put(PDTAnalyticsKeys.EXP_IDS_STATUS, experimentEventModelList);
        }

        List<Object> pokusExpData = HotelBasePdtTrackingHelper.INSTANCE.getPokusPdtTrackingData();

        if (CollectionUtil.isNotEmptyCollection(pokusExpData)) {
            event.getEventParam().put(PDTAnalyticsKeys.POKUS_EXPERIMENT_DATA, pokusExpData);
        }

        TrackingDataWrapper trackingData = HotelBasePdtTrackingHelper.INSTANCE.getPokusPdtTrackingDataV2();
        if (trackingData.hasEnoughData()){
            event.getEventParam().put(POKUS_EXPERIMENT_DATA_V2, trackingData);
        }


        event.getEventParam().put(PDTAnalyticsKeys.FUNNEL_SOURCE, funnelSource);

        return Event.createEvent(getEventName(), eventParam);
    }

    public void setFunnelSource(String funnelSource) {
        this.funnelSource = funnelSource;
    }

    private int getAdvancePurchase() {
        Date currentDate = new Date();
        DateFormat inputFormat = new SimpleDateFormat(SEND_DATE_FORMAT);
        Date checkInDate;
        try {
            checkInDate = inputFormat.parse(getCheckInDate());
        } catch (Exception e) {
            LogUtils.error(TAG, e.getMessage());
            return -1;
        }
        return DateUtil.getDiffDate(currentDate, checkInDate);
    }

    public static int calculateStayLength(String checkInRowDate, String checkOutRowdate) {
        Date checkoutDate;
        DateFormat inputFormat = new SimpleDateFormat(SEND_DATE_FORMAT);
        Date checkInDate;
        try {
            checkInDate = inputFormat.parse(checkInRowDate);
            checkoutDate = inputFormat.parse(checkOutRowdate);
        } catch (Exception e) {
            LogUtils.error(TAG, e.getMessage());
            return 0;
        }
        return DateUtil.getDiffDate(checkoutDate,checkInDate);
    }

    public static void bindEventParams(HotelLandingPageEvent event, SearchRequest request) {
        if (event == null || request == null || request.getUserSearchData() == null) {
            return;
        }
        UserSearchData userSearchData = request.getUserSearchData();
        int adult = userSearchData.getOccupancyData().getAdultCount();
        int roomCount = 1;
        if (userSearchData.getOccupancyData().getRoomCount() != null) {
            roomCount = userSearchData.getOccupancyData().getRoomCount();
        }
        event.setAdult(adult);
        int child = userSearchData.getOccupancyData().getChildAges().size();
        event.setChild(child);
        String checkInDate = OmnitureHelper.getStringDate(userSearchData.getCheckInDate());
        String checkOutDate = OmnitureHelper.getStringDate(userSearchData.getCheckOutDate());
        event.setCheckInDate(checkInDate);
        event.setCheckoutDate(checkOutDate);
        event.setCityString(userSearchData.getCityCode());
        event.setCountryString(userSearchData.getCountryCode());
        event.setTotalRoom(roomCount);
        event.setTotalGuest(adult + child );
        int stayLength = calculateStayLength(checkInDate, checkOutDate);
        event.setStayLength(stayLength);
        event.setNumberOfNight(event.getTotalRoom() * stayLength);
        event.setRoomStayQualifier(HotelUtil.INSTANCE.getRoomStayCandidateWithDelimiter(request.getRoomStayCandidate(), DELIMITER));
            event.setSearchType(userSearchData.getSearchType());
            if(request.getListingSearchData() != null) {
                List<FilterV2> appliedFilters = request.getListingSearchData().getAppliedFilterList();
                if(appliedFilters == null) {
                    appliedFilters = new ArrayList<>();
                }
                LocationFiltersV2 locationFiltersV2 = request.getListingSearchData().getLocationFilters();
                if(locationFiltersV2 == null) {
                    locationFiltersV2 = new LocationFiltersV2(null,null,null, null, null);
                }
                HotelFilterModelV2 filterModel = new HotelFilterModelV2(appliedFilters, null,locationFiltersV2
                        , request.getListingSearchData().getHotelTags(), null, null);
                event.initLocusTrackingData(LocusTrackingHelperKt.getLocusTrackingData(userSearchData, filterModel));
            }
        event.funnelSource = userSearchData.getFunnelSrc() == HotelFunnel.HOTEL.getFunnelValue() ? HotelFunnel.HOTEL.getFunnelName() : HotelFunnel.HOMESTAY.getFunnelName();
    }

    public String getCheckInDate() {
        return this.checkInDate;
    }

    public String getCheckoutDate() {
        return this.checkoutDate;
    }

    public int getAdult() {
        return this.adult;
    }

    public int getChild() {
        return this.child;
    }

    public int getTotalGuest() {
        return this.totalGuest;
    }

    public int getTotalRoom() {
        return this.totalRoom;
    }

    public String getTravelPurpose() {
        return this.travelPurpose;
    }

    public String getCityString() {
        return this.cityString;
    }

    public String getCountryString() {
        return this.countryString;
    }

    public String getAreaString() {
        return this.areaString;
    }

    public int getStayLength() {
        return this.stayLength;
    }

    public int getNumberOfNight() {
        return this.numberOfNight;
    }

    public String getRoomStayQualifier() {
        return this.roomStayQualifier;
    }

    public String getSearchType() {
        return this.searchType;
    }

    public List<ExperimentEventModel> getExperimentEventModelList() {
        return this.experimentEventModelList;
    }

    public boolean isCallSuperHlp() {
        return this.callSuperHlp;
    }

    public void setCheckInDate(String checkInDate) {
        this.checkInDate = checkInDate;
    }

    public void setCheckoutDate(String checkoutDate) {
        this.checkoutDate = checkoutDate;
    }

    public void setAdult(int adult) {
        this.adult = adult;
    }

    public void setChild(int child) {
        this.child = child;
    }

    public void setTotalGuest(int totalGuest) {
        this.totalGuest = totalGuest;
    }

    public void setTotalRoom(int totalRoom) {
        this.totalRoom = totalRoom;
    }

    public void setTravelPurpose(String travelPurpose) {
        this.travelPurpose = travelPurpose;
    }

    public void setCityString(String cityString) {
        this.cityString = cityString;
    }

    public void setCountryString(String countryString) {
        this.countryString = countryString;
    }

    public void setAreaString(String areaString) {
        this.areaString = areaString;
    }

    public void setStayLength(int stayLength) {
        this.stayLength = stayLength;
    }

    public void setNumberOfNight(int numberOfNight) {
        this.numberOfNight = numberOfNight;
    }

    public void setRoomStayQualifier(String roomStayQualifier) {
        this.roomStayQualifier = roomStayQualifier;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public void setExperimentEventModelList(List<ExperimentEventModel> experimentEventModelList) {
        this.experimentEventModelList = experimentEventModelList;
    }

    public void setCallSuperHlp(boolean callSuperHlp) {
        this.callSuperHlp = callSuperHlp;
    }

    public String toString() {
        return "HotelLandingPageEvent(checkInDate=" + this.checkInDate + ", checkoutDate=" + this.checkoutDate + ", adult=" + this.adult + ", child=" + this.child + ", totalGuest=" + this.totalGuest + ", totalRoom=" + this.totalRoom + ", travelPurpose=" + this.travelPurpose + ", cityString=" + this.cityString + ", countryString=" + this.countryString + ", areaString=" + this.areaString + ", stayLength=" + this.stayLength + ", numberOfNight=" + this.numberOfNight + ", roomStayQualifier=" + this.roomStayQualifier + ", searchType=" + this.searchType + ", experimentEventModelList=" + this.experimentEventModelList + ", callSuperHlp=" + this.callSuperHlp + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelLandingPageEvent))
            return false;
        final HotelLandingPageEvent other = (HotelLandingPageEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$checkInDate = this.getCheckInDate();
        final Object other$checkInDate = other.getCheckInDate();
        if (this$checkInDate == null ? other$checkInDate != null : !this$checkInDate.equals(other$checkInDate))
            return false;
        final Object this$checkoutDate = this.getCheckoutDate();
        final Object other$checkoutDate = other.getCheckoutDate();
        if (this$checkoutDate == null ? other$checkoutDate != null : !this$checkoutDate.equals(other$checkoutDate))
            return false;
        if (this.getAdult() != other.getAdult()) return false;
        if (this.getChild() != other.getChild()) return false;
        if (this.getTotalGuest() != other.getTotalGuest()) return false;
        if (this.getTotalRoom() != other.getTotalRoom()) return false;
        final Object this$travelPurpose = this.getTravelPurpose();
        final Object other$travelPurpose = other.getTravelPurpose();
        if (this$travelPurpose == null ? other$travelPurpose != null : !this$travelPurpose.equals(other$travelPurpose))
            return false;
        final Object this$cityString = this.getCityString();
        final Object other$cityString = other.getCityString();
        if (this$cityString == null ? other$cityString != null : !this$cityString.equals(other$cityString))
            return false;
        final Object this$countryString = this.getCountryString();
        final Object other$countryString = other.getCountryString();
        if (this$countryString == null ? other$countryString != null : !this$countryString.equals(other$countryString))
            return false;
        final Object this$areaString = this.getAreaString();
        final Object other$areaString = other.getAreaString();
        if (this$areaString == null ? other$areaString != null : !this$areaString.equals(other$areaString))
            return false;
        if (this.getStayLength() != other.getStayLength()) return false;
        if (this.getNumberOfNight() != other.getNumberOfNight()) return false;
        final Object this$roomStayQualifier = this.getRoomStayQualifier();
        final Object other$roomStayQualifier = other.getRoomStayQualifier();
        if (this$roomStayQualifier == null ? other$roomStayQualifier != null : !this$roomStayQualifier.equals(other$roomStayQualifier))
            return false;
        final Object this$searchType = this.getSearchType();
        final Object other$searchType = other.getSearchType();
        if (this$searchType == null ? other$searchType != null : !this$searchType.equals(other$searchType))
            return false;
        final Object this$experimentEventModelList = this.getExperimentEventModelList();
        final Object other$experimentEventModelList = other.getExperimentEventModelList();
        if (this$experimentEventModelList == null ? other$experimentEventModelList != null : !this$experimentEventModelList.equals(other$experimentEventModelList))
            return false;
        if (this.isCallSuperHlp() != other.isCallSuperHlp()) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelLandingPageEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $checkInDate = this.getCheckInDate();
        result = result * PRIME + ($checkInDate == null ? 43 : $checkInDate.hashCode());
        final Object $checkoutDate = this.getCheckoutDate();
        result = result * PRIME + ($checkoutDate == null ? 43 : $checkoutDate.hashCode());
        result = result * PRIME + this.getAdult();
        result = result * PRIME + this.getChild();
        result = result * PRIME + this.getTotalGuest();
        result = result * PRIME + this.getTotalRoom();
        final Object $travelPurpose = this.getTravelPurpose();
        result = result * PRIME + ($travelPurpose == null ? 43 : $travelPurpose.hashCode());
        final Object $cityString = this.getCityString();
        result = result * PRIME + ($cityString == null ? 43 : $cityString.hashCode());
        final Object $countryString = this.getCountryString();
        result = result * PRIME + ($countryString == null ? 43 : $countryString.hashCode());
        final Object $areaString = this.getAreaString();
        result = result * PRIME + ($areaString == null ? 43 : $areaString.hashCode());
        result = result * PRIME + this.getStayLength();
        result = result * PRIME + this.getNumberOfNight();
        final Object $roomStayQualifier = this.getRoomStayQualifier();
        result = result * PRIME + ($roomStayQualifier == null ? 43 : $roomStayQualifier.hashCode());
        final Object $searchType = this.getSearchType();
        result = result * PRIME + ($searchType == null ? 43 : $searchType.hashCode());
        final Object $experimentEventModelList = this.getExperimentEventModelList();
        result = result * PRIME + ($experimentEventModelList == null ? 43 : $experimentEventModelList.hashCode());
        result = result * PRIME + (this.isCallSuperHlp() ? 79 : 97);
        return result;
    }
}
