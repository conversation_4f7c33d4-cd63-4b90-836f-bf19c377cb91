package com.mmt.hotel.analytics.pdtMetrics

import com.mmt.analytics.AnalyticsSDK
import com.mmt.hotel.analytics.pdtMetrics.helper.HotelPdtV2MetricsHelper
import com.mmt.hotel.analytics.pdtMetrics.helper.PerformanceMetricsKeys
import com.mmt.hotel.analytics.pdtMetrics.model.HotelPdtMetrics
import com.mmt.hotel.analytics.pdtMetrics.model.HotelScreenMetrics
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Helper
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtEvent
import com.mmt.hotel.common.model.UserSearchData
import com.pdt.eagleEye.constants.EventName
import com.pdt.eagleEye.constants.EventType
import javax.inject.Inject

open class HotelScreenMetricsTracker @Inject constructor() {

    //Make this protected whenever needed so that it can be accessed by the child classes
    private val metricsTimestampMap = mutableMapOf<PerformanceMetricsKeys, Long>()

    private val apiStartTimestampStack: ArrayDeque<Long> = ArrayDeque()
    private val apiEndTimestampStack: ArrayDeque<Long> = ArrayDeque()
    private var url: String? = null

    var isMetricsCollected = false

    private var userSearchData: UserSearchData? = null
    private var pageName: String? = null
    private var funnelStep: HotelPdtV2Constants.FunnelStep? = null


    fun initTracker(
        userSearchData: UserSearchData,
        pageName: String,
        funnelStep: HotelPdtV2Constants.FunnelStep
    ) {
        this.userSearchData = userSearchData
        this.pageName = pageName
        this.funnelStep = funnelStep
    }

    fun startScreenTracing() {
        if (metricsTimestampMap[PerformanceMetricsKeys.SCREEN_START_TIMESTAMP] == null) {
            metricsTimestampMap[PerformanceMetricsKeys.SCREEN_START_TIMESTAMP] = System.currentTimeMillis()
        }
    }

    fun startApiTracing(url: String? = null) {
        this.url = url
        apiStartTimestampStack.addLast(System.currentTimeMillis())
    }

    fun endApiTracing() {
        apiEndTimestampStack.addLast(System.currentTimeMillis())
    }

    fun traceScreenDisplayed() {
        if (metricsTimestampMap[PerformanceMetricsKeys.SCREEN_DISPLAY_TIMESTAMP] == null) {
            metricsTimestampMap[PerformanceMetricsKeys.SCREEN_DISPLAY_TIMESTAMP] = System.currentTimeMillis()
        }
    }

    fun traceScreenRendered() {
        if (metricsTimestampMap[PerformanceMetricsKeys.SCREEN_RENDER_TIMESTAMP] == null) {
            metricsTimestampMap[PerformanceMetricsKeys.SCREEN_RENDER_TIMESTAMP] = System.currentTimeMillis()
        }
    }

    fun endTracing(
        entityId: HotelPdtV2MetricsHelper.MetricsEntityId,
        entityFormat: String? = HotelPdtV2MetricsHelper.EntityFormat.JSON.name,
        associatedID: String? = null,
        status: HotelPdtV2MetricsHelper.MetricsStatus = HotelPdtV2MetricsHelper.MetricsStatus.SUCCESS,
        type: HotelPdtV2MetricsHelper.MetricsType = HotelPdtV2MetricsHelper.MetricsType.SCREEN_RENDER
    ) {
        if (isMetricsCollected || pageName.isNullOrEmpty()) { return }

        userSearchData?.let { searchData ->
            funnelStep?.let { step ->
                val metricsEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
                    eventName = EventName.PAGE_RENDER,
                    eventType = EventType.LIFECYCLE,
                    pageName = pageName.orEmpty(),
                    userSearchData = searchData,
                    requestId = null,
                    baseTrackingData = null,
                    funnelStep = step,
                    isMetricsEvent = true
                )
                metricsEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(searchData))

                val pdtMetrics = createPdtMetrics(entityId, entityFormat, associatedID, status, type)

                metricsEventBuilder.metrics(pdtMetrics)
                isMetricsCollected = true

                val event = metricsEventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }
        }
    }

    //Can make open when needed
    private fun createPdtMetrics(
        entityId: HotelPdtV2MetricsHelper.MetricsEntityId,
        entityFormat: String?,
        associatedID: String?,
        status: HotelPdtV2MetricsHelper.MetricsStatus,
        type: HotelPdtV2MetricsHelper.MetricsType
    ): HotelPdtMetrics {

        val screenMetrics = createScreenMetrics()

        val renderTime = screenMetrics?.let {
            it.screenRenderTimeStamp - it.screenStartTimeStamp
        }

        val downloadTime = if (
            screenMetrics?.apiStartTimeStamp != null
            && screenMetrics.apiEndTimeStamp != null
        ) {
            screenMetrics.apiEndTimeStamp - screenMetrics.apiStartTimeStamp
        } else null

        return HotelPdtMetrics(
            entityId = entityId.name,
            renderTime = renderTime?.toInt(),
            downloadTime = downloadTime?.toInt(),
            associatedID = associatedID,
            screenMetrics = screenMetrics,
            entityFormat = entityFormat,
            url = url,
            type = type.name,
            status = status.name,
        )
    }

    //Can make open when needed
    private fun createScreenMetrics(): HotelScreenMetrics? {

        val screenMetrics = metricsTimestampMap[PerformanceMetricsKeys.SCREEN_START_TIMESTAMP]?.let {
            HotelScreenMetrics(
                screenStartTimeStamp = it,
                screenDisplayTimeStamp = metricsTimestampMap[PerformanceMetricsKeys.SCREEN_DISPLAY_TIMESTAMP],
                apiStartTimeStamp = apiStartTimestampStack.removeLastOrNull(),
                apiEndTimeStamp = apiEndTimestampStack.removeLastOrNull(),
                screenRenderTimeStamp = metricsTimestampMap[PerformanceMetricsKeys.SCREEN_RENDER_TIMESTAMP] ?: System.currentTimeMillis()
            )
        }

        return screenMetrics
    }

}