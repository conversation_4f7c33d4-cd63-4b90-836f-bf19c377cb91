package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class ContentDetailItem(
    private var id: String,
    val type: String,
    val name: String? = null,
    val duration: Long? = null,
    val resolution: String? = null,
    val wishlisted: Boolean? =null,
    @SerializedName("category_id")
    val categoryId: String? = null,
    @SerializedName("category")
    val categoryName: String? = null,
    val position: Position? = null

) : Parcelable