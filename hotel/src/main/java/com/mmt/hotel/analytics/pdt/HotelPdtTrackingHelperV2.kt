package com.mmt.hotel.analytics.pdt


import com.mmt.analytics.ActivityTypeEvent
import com.mmt.analytics.AnalyticsSDK
import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureHelper
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.DateUtil
import com.mmt.hotel.analytics.pdt.events.HotelGenericEvent
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomCriteriaV2
import com.mmt.hotel.common.model.tracking.LocusTrackingData
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.landingV3.tracking.HotelLandingBaseTrackerUtils
import com.gommt.logger.LogUtils
import com.mmt.uikit.util.isNotNullAndEmpty
import com.pdt.pdtDataLogging.events.model.BaseGenericEvent
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*

open class HotelPdtTrackingHelperV2() {

     fun putAllBasicHotelInfo(event: HotelGenericEvent, trackingMap: Map<String, Any?>) {
        try {
            val userSearchData = trackingMap[TrackingConstants.KEY_USER_SEARCH_DATA] as UserSearchData?
                    ?: return
            val locusTrackingData = trackingMap[TrackingConstants.KEY_LOCUS_TRACKING_DATA] as LocusTrackingData?
            val propertyType = trackingMap[TrackingConstants.KEY_PROPERTY_TYPE] as String?
            val totalAmount = trackingMap[TrackingConstants.KEY_TOTAL_PRICE_TO_PAY] as Float?
            val originalPrice = trackingMap[TrackingConstants.KEY_ORIGINAL_PRICE] as Float?
            val roomCriteria = trackingMap[TrackingConstants.KEY_ROOM_CRITERIA] as List<RoomCriteriaV2>?
            val starRating = trackingMap[TrackingConstants.KEY_STAR_RATING] as Int?
            val userRating = trackingMap[TrackingConstants.KEY_USER_RATING] as Float?
            val headerImageUrl = trackingMap[TrackingConstants.KEY_HEADER_IMAGE] as String?
            val isHotelShortListed = trackingMap[TrackingConstants.KEY_IS_HOTEL_SHORTLISTED] as Boolean?
            val dayUseCheckInTime = trackingMap[TrackingConstants.KEY_DAY_USE_CHECK_IN_TIME] as String?
            val dayUseSlotDuration = trackingMap[TrackingConstants.KEY_DAY_USE_SLOT_DURATION] as String?

            event.apply {
                bindEventParams(this, userSearchData, locusTrackingData, roomCriteria)
                hotelId = userSearchData.hotelId
                countryCode = userSearchData.countryCode
                starRating?.let {
                    startRating = it
                }
                setPropertyType(propertyType)
                putCorrelationKey(event, correlationKey)
                initLocusTrackingData(locusTrackingData)
                if (totalAmount != null && totalAmount > 0) {
                    discountedPrice = totalAmount.toFloat()
                }
                if (originalPrice != null && originalPrice > 0) {
                    this.originalPrice = originalPrice
                }
                userRating?.let {
                    this.userRating = it
                }
                if(headerImageUrl.isNotNullAndEmpty()){
                    topImgUrl = headerImageUrl
                }
                isHotelShortListed?.let {
                    this.isHotelShortListed = it
                }
                dayUseCheckInTime?.let {
                    this.dayUseCheckInTime = it
                }
                dayUseSlotDuration?.let {
                    this.slotDuration = it
                }
            }
        } catch (exception: Exception) {
            exception.printStackTrace()
        }
    }

    fun putCorrelationKey(event: BaseGenericEvent, correlationKey: String?) {
        if (correlationKey.isNotNullAndEmpty()) {
            event.correlationKey = correlationKey
        }
    }

    fun bindEventParams(event: HotelGenericEvent, userSearchData: UserSearchData,
                        locusTrackingData: LocusTrackingData?, roomCriteria: List<RoomCriteriaV2>?) {

        val checkinDate = OmnitureHelper.getStringDate(userSearchData.checkInDate)
        val checkOutDate = OmnitureHelper.getStringDate(userSearchData.checkOutDate)
        val stay = calculateStayLength(checkinDate, checkOutDate)
        val numNights = HotelUtil.getNights(userSearchData.checkInDate, userSearchData.checkOutDate)

        event.apply {
            adult = userSearchData.occupancyData.adultCount
            child = userSearchData.occupancyData.childAges.size
            checkInDate = checkinDate
            checkoutDate = checkOutDate
            countryString = userSearchData.countryCode
            totalRoom = userSearchData.occupancyData.roomCount ?: if (userSearchData.isAltAccoFunnel()) 0 else 1
            totalGuest = (userSearchData.occupancyData.adultCount + userSearchData.occupancyData.childAges.size)
            stayLength = stay
            travelPurpose = userSearchData.tripType
            numberOfNight = (numNights * (userSearchData.occupancyData.roomCount ?: 1))
            roomCriteria?.let {
                roomStayQualifier = HotelUtil.getRoomStayQualifierV2WithDelimiter(roomCriteria, CoreConstants.DELIMITER)
            }
            searchType = userSearchData.searchType
            locusTrackingData?.let {
                initLocusTrackingData(it)
            }
            funnelSource = HotelUtil.getFunnelSource(userSearchData.funnelSrc)
        }
    }

    private fun calculateStayLength(checkInRowDate: String?, checkOutRowDate: String?): Int {
        val checkoutDate: Date
        val inputFormat: DateFormat = SimpleDateFormat(OmnitureHelper.SEND_DATE_FORMAT, Locale.getDefault())
        val checkInDate: Date
        try {
            checkInDate = inputFormat.parse(checkInRowDate)
            checkoutDate = inputFormat.parse(checkOutRowDate)
        } catch (e: Exception) {
            return 0
        }
        return DateUtil.getDiffDate(checkoutDate, checkInDate)
    }

    fun track(pdtEventName: String, eventsPageName: Events, activityTypeEvent: ActivityTypeEvent) {
        try {
            val events = AnalyticsSDK.instance.getCommonGenericEvent(pdtEventName, eventsPageName.value)
            AnalyticsSDK.instance.trackEvent(events, 1, activityTypeEvent)
        } catch (e: Exception) {
            LogUtils.error(HotelLandingBaseTrackerUtils.TAG, e)
        }
    }
}