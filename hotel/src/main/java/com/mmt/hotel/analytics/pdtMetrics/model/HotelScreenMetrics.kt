package com.mmt.hotel.analytics.pdtMetrics.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelScreenMetrics(
    @SerializedName("screen_start_timestamp")
    val screenStartTimeStamp: Long, //When the activity gets initialized
    @SerializedName("screen_display_timestamp")
    val screenDisplayTimeStamp: Long? = null, //When the first ui becomes visible to user (could be a loader or a shimmer screen etc)
    @SerializedName("api_start_timestamp")
    val apiStartTimeStamp: Long? = null, //start timestamp of the primary api call of page which is used to render the screen
    @SerializedName("api_end_timestamp")
    val apiEndTimeStamp: Long? = null, //end timestamp of the primary api call of page which is used to render the screen
    @SerializedName("screen_render_timestamp")
    val screenRenderTimeStamp: Long, //When the screen gets rendered and user can interact with it (after successful api response)
    @SerializedName("total_frames")
    val totalFrames: Int? = null,
    @SerializedName("slow_frames")
    val slowFrames: Int? = null,
    @SerializedName("frozen_frames")
    val frozenFrames: Int? = null
) : Parcelable