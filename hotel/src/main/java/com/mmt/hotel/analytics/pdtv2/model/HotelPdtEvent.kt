package com.mmt.hotel.analytics.pdtv2.model

import com.google.gson.annotations.SerializedName
import com.mmt.hotel.analytics.pdtMetrics.model.HotelPdtMetrics

import com.pdt.eagleEye.models.DeviceContext
import com.pdt.eagleEye.models.Event
import com.pdt.eagleEye.models.EventTrackingContext
import com.pdt.eagleEye.models.ExperimentDetails
import com.pdt.eagleEye.models.PageContext
import com.pdt.eagleEye.models.TrackingInfo
import com.pdt.eagleEye.models.UserContext
import kotlinx.parcelize.Parcelize

@Parcelize
class HotelPdtEvent(
    @Transient
    override val pageContext: PageContext,
    @Transient
    override val eventTrackingContext: EventTrackingContext,
    @Transient
    override val errorDetailList: List<HotelPdtErrorListItem>? = null,
    @Transient
    override val userContext: UserContext,
    @Transient
    override val deviceContext: DeviceContext,
    @Transient
    override val experimentDetails: ExperimentDetails? = null,
    @Transient
    override val trackingInfo: TrackingInfo? = null,
    @Transient
    override val eventDetails: HotelPdtEventDetails,
    @SerializedName("addon_details")
    val addOnDetails: List<AddOnData>? = null,
    @SerializedName("traveller_info")
    val travellersList: List<TravellerInfo>? = null,
    @SerializedName("search_context")
    val searchContext: HotelSearchContext,
    @SerializedName("metrics")
    val metrics: HotelPdtMetrics? = null
) : Event(
    deviceContext = deviceContext,
    userContext = userContext,
    pageContext = pageContext,
    eventTrackingContext = eventTrackingContext,
    eventDetails = eventDetails,
    experimentDetails = experimentDetails,
    trackingInfo = trackingInfo,
    errorDetailList = errorDetailList,
) {
    override fun toString(): String {
        return "HotelPdtEvent(eventDetail=$eventDetails, searchContext=$searchContext) ${super.toString()}"
    }
}
