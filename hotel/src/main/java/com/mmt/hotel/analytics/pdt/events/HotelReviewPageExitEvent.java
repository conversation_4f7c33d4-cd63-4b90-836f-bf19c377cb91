package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.mmt.core.util.StringUtil;
import com.mmt.hotel.analytics.pdt.HotelPdtKeys;
import com.mmt.hotel.analytics.pdt.model.RoomSelectionModel;
import com.mmt.hotel.analytics.pdt.model.TravelerDetail;
import com.mmt.hotel.bookingreview.model.AddOnTrackingModel;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.List;
import java.util.Map;

public class HotelReviewPageExitEvent extends HotelPageExitEvent {

    private TravelerDetail travelerDetail;
    private String couponPreApplied;
    private String couponCode;
    private float couponAmt;
    private String bookingTransactionKey;
    private boolean bnplSelected;
    private float bnplAmtCharged;
    private float bnplAmtPending;
    @Nullable
    private List<AddOnTrackingModel> selectedAddOnList;
    private List<RoomSelectionModel> roomSelectionModel;
    private String payMode;
    private String navigation;
    private float charityAmt;
    private boolean charityOpted;
    private boolean isBookingForMySelf;



    public HotelReviewPageExitEvent(@NonNull String pageName, int eventType, long startTimeStamp,
                                    @NonNull String omnitureName,
                                    @NonNull String parentScreenName, @NonNull String prevFunnelStepPdt, @NonNull String prevPageNamePdt) {
        super(pageName, eventType, startTimeStamp, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
        travelerDetail = new TravelerDetail();
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        Map<String, Object> eventParam = event.getEventParam();
        eventParam.putAll(travelerDetail.getEventParams());
        if (StringUtil.isNotNullAndEmpty(couponPreApplied)) {
            eventParam.put(PDTAnalyticsKeys.CPN_PRE_APPLD, couponPreApplied);
        }
        if (StringUtil.isNotNullAndEmpty(couponCode)) {
            eventParam.put(PDTAnalyticsKeys.CPN_CODE, couponCode);
        }
        if (StringUtil.isNotNullAndEmpty(couponPreApplied)) {
            eventParam.put(HotelPdtKeys.REVIEW_PAGE_DEFAULT_OFFER, couponPreApplied);
        }
        if (StringUtil.isNotNullAndEmpty(couponCode)) {
            eventParam.put(HotelPdtKeys.REVIEW_PAGE_APPLIED_OFFER, couponCode);
        }
        if (couponAmt != 0) {
            eventParam.put(PDTAnalyticsKeys.PRC_CPN_AMT, couponAmt);
        }
        if (StringUtil.isNotNullAndEmpty(bookingTransactionKey)) {
            eventParam.put(PDTAnalyticsKeys.BKG_TXN_ID, bookingTransactionKey);
        }
        if (CollectionUtil.isNotEmptyCollection(roomSelectionModel)) {
            eventParam.put(PDTAnalyticsKeys.PD_HTL_RMS_VWD_LS, roomSelectionModel);
        }
        eventParam.put(PDTAnalyticsKeys.PRC_BNPL_APPLD, isBnplSelected());
        if (bnplAmtCharged != 0) {
            eventParam.put(PDTAnalyticsKeys.PRC_AMT_CHRGD, bnplAmtCharged);
        }
        if (bnplAmtPending != 0) {
            eventParam.put(PDTAnalyticsKeys.PRC_AMT_PNDNG, bnplAmtPending);
        }
        if (charityAmt != 0) {
            eventParam.put(HotelPdtKeys.DETAIL_CHARITY_AMOUNT, charityAmt);
        }
        eventParam.put(HotelPdtKeys.DETAIL_CHARITY_OPTED, charityOpted);
        if(selectedAddOnList != null) {
            eventParam.put(PDTAnalyticsKeys.ADD_ON_DATA, selectedAddOnList);
        }
        eventParam.put(PDTAnalyticsKeys.PAYMENT_MODE, payMode);
        eventParam.put(PDTAnalyticsKeys.NAVIGATION, navigation);
        eventParam.put(HotelPdtKeys.IS_SELF_BOOKING,isBookingForMySelf);
        return event;
    }

    public void setTravelerFName(String travelerFName) {
        travelerDetail.setTravelerFName(travelerFName);
    }

    public void setTravelerLName(String travelerLName) {
        travelerDetail.setTravelerLName(travelerLName);
    }

    public void setTravelerTitle(String travelerTitle) {
        travelerDetail.setTravelerTitle(travelerTitle);
    }

    public void setGstOpted(boolean gstOpted) {
        travelerDetail.setGstOpted(gstOpted);
    }

    public void setTravelerCmpAddress(String travelerCmpAddress) {
        travelerDetail.setTravelerCmpAddress(travelerCmpAddress);
    }

    public void setTravelerCmpName(String travelerCmpName) {
        travelerDetail.setTravelerCmpName(travelerCmpName);
    }

    public void setTravelerMobileNo(String travelerMobileNo) {
        travelerDetail.setTravelerMobileNo(travelerMobileNo);
    }

    public void setTravelerEmailId(String travelerEmailId) {
        travelerDetail.setTravelerEmailId(travelerEmailId);
    }

    public void setTravelerGSTNo(String travelerGSTNo) {
        travelerDetail.setTravelerGSTNo(travelerGSTNo);
    }

    public void setTravelerCtyMobileCode(String travelerCtyMobileCode) {
        travelerDetail.setTravelerCtyMobileCode(travelerCtyMobileCode);
    }

    public TravelerDetail getTravelerDetail() {
        return this.travelerDetail;
    }

    public String getCouponPreApplied() {
        return this.couponPreApplied;
    }

    public String getCouponCode() {
        return this.couponCode;
    }

    public float getCouponAmt() {
        return this.couponAmt;
    }

    public String getBookingTransactionKey() {
        return this.bookingTransactionKey;
    }

    public boolean isBnplSelected() {
        return this.bnplSelected;
    }

    public float getBnplAmtCharged() {
        return this.bnplAmtCharged;
    }

    public float getBnplAmtPending() {
        return this.bnplAmtPending;
    }

    public List<AddOnTrackingModel> getSelectedAddOnList() {
        return this.selectedAddOnList;
    }

    public List<RoomSelectionModel> getRoomSelectionModel() {
        return this.roomSelectionModel;
    }

    public float getCharityAmt() {
        return this.charityAmt;
    }

    public boolean isCharityOpted() {
        return this.charityOpted;
    }

    public void setTravelerDetail(TravelerDetail travelerDetail) {
        this.travelerDetail = travelerDetail;
    }

    public void setCouponPreApplied(String couponPreApplied) {
        this.couponPreApplied = couponPreApplied;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public void setCouponAmt(float couponAmt) {
        this.couponAmt = couponAmt;
    }

    public void setBookingTransactionKey(String bookingTransactionKey) {
        this.bookingTransactionKey = bookingTransactionKey;
    }

    public void setBnplSelected(boolean bnplSelected) {
        this.bnplSelected = bnplSelected;
    }

    public void setBnplAmtCharged(float bnplAmtCharged) {
        this.bnplAmtCharged = bnplAmtCharged;
    }

    public void setBnplAmtPending(float bnplAmtPending) {
        this.bnplAmtPending = bnplAmtPending;
    }

    public void setSelectedAddOnList(@Nullable List<AddOnTrackingModel> selectedAddOnList) {
        this.selectedAddOnList = selectedAddOnList;
    }

    public void setRoomSelectionModel(List<RoomSelectionModel> roomSelectionModel) {
        this.roomSelectionModel = roomSelectionModel;
    }

    public void setCharityAmt(float charityAmt) {
        this.charityAmt = charityAmt;
    }

    public void setCharityOpted(boolean charityOpted) {
        this.charityOpted = charityOpted;
    }

    @Override
    public String toString() {
        return "HotelReviewPageExitEvent{" +
                "travelerDetail=" + travelerDetail +
                ", couponPreApplied='" + couponPreApplied + '\'' +
                ", couponCode='" + couponCode + '\'' +
                ", couponAmt=" + couponAmt +
                ", bookingTransactionKey='" + bookingTransactionKey + '\'' +
                ", bnplSelected=" + bnplSelected +
                ", bnplAmtCharged=" + bnplAmtCharged +
                ", bnplAmtPending=" + bnplAmtPending +
                ", selectedAddOnList=" + selectedAddOnList +
                ", roomSelectionModel=" + roomSelectionModel +
                ", payMode='" + payMode + '\'' +
                ", navigation='" + navigation + '\'' +
                ", charityAmt=" + charityAmt +
                ", charityOpted=" + charityOpted +
                '}';
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelReviewPageExitEvent))
            return false;
        final HotelReviewPageExitEvent other = (HotelReviewPageExitEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$travelerDetail = this.getTravelerDetail();
        final Object other$travelerDetail = other.getTravelerDetail();
        if (this$travelerDetail == null ? other$travelerDetail != null : !this$travelerDetail.equals(other$travelerDetail))
            return false;
        final Object this$couponPreApplied = this.getCouponPreApplied();
        final Object other$couponPreApplied = other.getCouponPreApplied();
        if (this$couponPreApplied == null ? other$couponPreApplied != null : !this$couponPreApplied.equals(other$couponPreApplied))
            return false;
        final Object this$couponCode = this.getCouponCode();
        final Object other$couponCode = other.getCouponCode();
        if (this$couponCode == null ? other$couponCode != null : !this$couponCode.equals(other$couponCode))
            return false;
        if (Float.compare(this.getCouponAmt(), other.getCouponAmt()) != 0) return false;
        final Object this$bookingTransactionKey = this.getBookingTransactionKey();
        final Object other$bookingTransactionKey = other.getBookingTransactionKey();
        if (this$bookingTransactionKey == null ? other$bookingTransactionKey != null : !this$bookingTransactionKey.equals(other$bookingTransactionKey))
            return false;
        if (this.isBnplSelected() != other.isBnplSelected()) return false;
        if (Float.compare(this.getBnplAmtCharged(), other.getBnplAmtCharged()) != 0) return false;
        if (Float.compare(this.getBnplAmtPending(), other.getBnplAmtPending()) != 0) return false;
        final Object this$selectedAddOnList = this.getSelectedAddOnList();
        final Object other$selectedAddOnList = other.getSelectedAddOnList();
        if (this$selectedAddOnList == null ? other$selectedAddOnList != null : !this$selectedAddOnList.equals(other$selectedAddOnList))
            return false;
        final Object this$roomSelectionModel = this.getRoomSelectionModel();
        final Object other$roomSelectionModel = other.getRoomSelectionModel();
        if (this$roomSelectionModel == null ? other$roomSelectionModel != null : !this$roomSelectionModel.equals(other$roomSelectionModel))
            return false;
        if (Float.compare(this.getCharityAmt(), other.getCharityAmt()) != 0) return false;
        if (this.isCharityOpted() != other.isCharityOpted()) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelReviewPageExitEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $travelerDetail = this.getTravelerDetail();
        result = result * PRIME + ($travelerDetail == null ? 43 : $travelerDetail.hashCode());
        final Object $couponPreApplied = this.getCouponPreApplied();
        result = result * PRIME + ($couponPreApplied == null ? 43 : $couponPreApplied.hashCode());
        final Object $couponCode = this.getCouponCode();
        result = result * PRIME + ($couponCode == null ? 43 : $couponCode.hashCode());
        result = result * PRIME + Float.floatToIntBits(this.getCouponAmt());
        final Object $bookingTransactionKey = this.getBookingTransactionKey();
        result = result * PRIME + ($bookingTransactionKey == null ? 43 : $bookingTransactionKey.hashCode());
        result = result * PRIME + (this.isBnplSelected() ? 79 : 97);
        result = result * PRIME + Float.floatToIntBits(this.getBnplAmtCharged());
        result = result * PRIME + Float.floatToIntBits(this.getBnplAmtPending());
        final Object $selectedAddOnList = this.getSelectedAddOnList();
        result = result * PRIME + ($selectedAddOnList == null ? 43 : $selectedAddOnList.hashCode());
        final Object $roomSelectionModel = this.getRoomSelectionModel();
        result = result * PRIME + ($roomSelectionModel == null ? 43 : $roomSelectionModel.hashCode());
        result = result * PRIME + Float.floatToIntBits(this.getCharityAmt());
        result = result * PRIME + (this.isCharityOpted() ? 79 : 97);
        return result;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getNavigation() {
        return navigation;
    }

    public void setNavigation(String navigation) {
        this.navigation = navigation;
    }

    public boolean isBookingForMySelf() {
        return isBookingForMySelf;
    }

    public void setBookingForMySelf(boolean bookingForMySelf) {
        isBookingForMySelf = bookingForMySelf;
    }
}
