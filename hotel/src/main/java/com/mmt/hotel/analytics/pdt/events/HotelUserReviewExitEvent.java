package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.mmt.hotel.analytics.pdt.model.RewardsAndRatingEventModel;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.List;

public class HotelUserReviewExitEvent extends HotelPageExitEvent {

    private List<String> appliedCategory;
    private List<String> reviewIds;
    private RewardsAndRatingEventModel rewardsAndRatingEventModel;

    public HotelUserReviewExitEvent(@NonNull String pageName, int eventType, long startTimeStamp,
                                    @NonNull String omnitureName,
                                    @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(pageName, eventType, startTimeStamp, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (CollectionUtil.isNotEmptyCollection(appliedCategory)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_HTL_RVW_APPLD, appliedCategory);
        }
        if (CollectionUtil.isNotEmptyCollection(reviewIds)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RVW_IDS_DISP, reviewIds);
        }
        if(rewardsAndRatingEventModel != null) {
            event.getEventParam().put(PDTAnalyticsKeys.REWARDS_AND_RATINGS_PAGE_DETAILS,rewardsAndRatingEventModel);
        }
        return event;
    }

    public List<String> getAppliedCategory() {
        return this.appliedCategory;
    }

    public List<String> getReviewIds() {
        return this.reviewIds;
    }

    public void setAppliedCategory(List<String> appliedCategory) {
        this.appliedCategory = appliedCategory;
    }

    public void setReviewIds(List<String> reviewIds) {
        this.reviewIds = reviewIds;
    }

    public void setRewardsAndRatingEventModel(RewardsAndRatingEventModel rewardsAndRatingEventModel) {
        this.rewardsAndRatingEventModel = rewardsAndRatingEventModel;
    }

    public String toString() {
        return "HotelUserReviewExitEvent(appliedCategory=" + this.appliedCategory + ", reviewIds=" + this.reviewIds + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelUserReviewExitEvent))
            return false;
        final HotelUserReviewExitEvent other = (HotelUserReviewExitEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$appliedCategory = this.getAppliedCategory();
        final Object other$appliedCategory = other.getAppliedCategory();
        if (this$appliedCategory == null ? other$appliedCategory != null : !this$appliedCategory.equals(other$appliedCategory))
            return false;
        final Object this$reviewIds = this.getReviewIds();
        final Object other$reviewIds = other.getReviewIds();
        if (this$reviewIds == null ? other$reviewIds != null : !this$reviewIds.equals(other$reviewIds))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelUserReviewExitEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $appliedCategory = this.getAppliedCategory();
        result = result * PRIME + ($appliedCategory == null ? 43 : $appliedCategory.hashCode());
        final Object $reviewIds = this.getReviewIds();
        result = result * PRIME + ($reviewIds == null ? 43 : $reviewIds.hashCode());
        return result;
    }
}
