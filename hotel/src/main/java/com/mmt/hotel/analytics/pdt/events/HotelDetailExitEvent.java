package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.mmt.auth.login.model.Employee;
import com.mmt.hotel.analytics.pdt.HotelPdtKeys;
import com.mmt.hotel.analytics.pdt.TrackingConstants;
import com.mmt.hotel.analytics.pdt.model.SimilarHotelEventModel;
import com.mmt.hotel.analytics.pdt.model.TravelerDetail;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.HashMap;
import java.util.List;


public class HotelDetailExitEvent extends HotelPageExitEvent {
    private List<String> visibleCardList;
    private List<SimilarHotelEventModel> viewedSimilarHotelIds;
    private List<String> topReviewsShown;
    private List<String> topReviewsScrolled;
    private TravelerDetail travelerDetail;
    private String couponPreApplied;
    private String couponCode;

    private HashMap<String,String>  allClickedInfo = new HashMap<>();

    public HotelDetailExitEvent(@NonNull String hotelId, int eventType, long startTimeStamp,
                                @NonNull String omnitureName,
                                @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(TrackingConstants.HOTEL_DETAIL, eventType, startTimeStamp, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
        setHotelId(hotelId);
        travelerDetail = new TravelerDetail();
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (CollectionUtil.isNotEmptyCollection(visibleCardList)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_DTL_SCTNS_VWD, visibleCardList);
        }
        if (CollectionUtil.isNotEmptyCollection(viewedSimilarHotelIds)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_CRD_DISP, viewedSimilarHotelIds);
        }
        if(CollectionUtil.isNotEmptyCollection(topReviewsScrolled)) {
            event.getEventParam().put(PDTAnalyticsKeys.TOP_REVIEWS_SCROLLED,topReviewsScrolled);
        }
        if(CollectionUtil.isNotEmptyCollection(topReviewsShown)) {
            event.getEventParam().put(PDTAnalyticsKeys.TOP_REVIEWS_SHOWN,topReviewsShown);
        }
        event.getEventParam().putAll(travelerDetail.getEventParams());
        event.getEventParam().putAll(allClickedInfo);
        event.getEventParam().put(HotelPdtKeys.DETAIL_PAGE_DEFAULT_OFFER,couponPreApplied);
        event.getEventParam().put(HotelPdtKeys.DETAIL_PAGE_APPLIED_OFFER,couponCode);

        return event;
    }

    public void fillTravelerDetail(Employee traveller) {
        String[] name = traveller.getName().split(" ");
        travelerDetail.setTravelerFName(name[0]);
        if(name.length > 1) {
            travelerDetail.setTravelerLName(name[name.length-1]);
        }
        travelerDetail.setTravelerTitle(traveller.getTitle());
        travelerDetail.setTravelerMobileNo(traveller.getPhoneNumber());
        travelerDetail.setTravelerEmailId(traveller.getBusinessEmailId());
    }

    public List<String> getVisibleCardList() {
        return this.visibleCardList;
    }

    public List<SimilarHotelEventModel> getViewedSimilarHotelIds() {
        return this.viewedSimilarHotelIds;
    }

    public void setVisibleCardList(List<String> visibleCardList) {
        this.visibleCardList = visibleCardList;
    }

    public void setViewedSimilarHotelIds(List<SimilarHotelEventModel> viewedSimilarHotelIds) {
        this.viewedSimilarHotelIds = viewedSimilarHotelIds;
    }

    public void setTopReviewsShown(List<String> topReviewsShown) {
        this.topReviewsShown = topReviewsShown;
    }
    public void setTopReviewsScrolled(List<String> topReviewsScrolled) {
        this.topReviewsScrolled = topReviewsScrolled;
    }

    public TravelerDetail getTravelerDetail() {
        return this.travelerDetail;
    }

    public String toString() {
        return "HotelDetailExitEvent(visibleCardList=" + this.visibleCardList + ", viewedSimilarHotelIds=" + this.viewedSimilarHotelIds + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelDetailExitEvent))
            return false;
        final HotelDetailExitEvent other = (HotelDetailExitEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$visibleCardList = this.getVisibleCardList();
        final Object other$visibleCardList = other.getVisibleCardList();
        if (this$visibleCardList == null ? other$visibleCardList != null : !this$visibleCardList.equals(other$visibleCardList))
            return false;
        final Object this$viewedSimilarHotelIds = this.getViewedSimilarHotelIds();
        final Object other$viewedSimilarHotelIds = other.getViewedSimilarHotelIds();
        if (this$viewedSimilarHotelIds == null ? other$viewedSimilarHotelIds != null : !this$viewedSimilarHotelIds.equals(other$viewedSimilarHotelIds))
            return false;
        final Object this$travelerDetail = this.getTravelerDetail();
        final Object other$travelerDetail = other.getTravelerDetail();
        if (this$travelerDetail == null ? other$travelerDetail != null : !this$travelerDetail.equals(other$travelerDetail))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelDetailExitEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $visibleCardList = this.getVisibleCardList();
        result = result * PRIME + ($visibleCardList == null ? 43 : $visibleCardList.hashCode());
        final Object $viewedSimilarHotelIds = this.getViewedSimilarHotelIds();
        result = result * PRIME + ($viewedSimilarHotelIds == null ? 43 : $viewedSimilarHotelIds.hashCode());
        final Object $travelerDetail = this.getTravelerDetail();
        result = result * PRIME + ($travelerDetail == null ? 43 : $travelerDetail.hashCode());
        return result;
    }

    public HashMap<String, String> getAllClickedInfo() {
        return allClickedInfo;
    }

    public void setAllClickedInfo(HashMap<String, String> allClickedInfo) {
        this.allClickedInfo = allClickedInfo;
    }

    public String getCouponPreApplied() {
        return couponPreApplied;
    }

    public void setCouponPreApplied(String couponPreApplied) {
        this.couponPreApplied = couponPreApplied;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }
}
