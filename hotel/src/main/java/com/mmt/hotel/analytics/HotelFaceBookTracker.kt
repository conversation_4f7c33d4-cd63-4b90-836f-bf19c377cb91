package com.mmt.hotel.analytics

import android.os.Bundle
import com.facebook.appevents.AppEventsConstants
import com.gommt.logger.LogUtils
import com.gommt.logger.Severity
import com.gommt.pan.utility.EMPTY_STRING
import com.mmt.core.MMTCore
import com.mmt.core.constant.CommonConstants
import com.mmt.core.util.CollectionUtil
import com.mmt.core.util.DateUtil
import com.mmt.core.util.GsonUtils
import com.mmt.core.util.StringUtil
import com.mmt.data.model.util.CommonMigrationHelper
import com.mmt.data.model.util.MapResourceParserUtil
import com.mmt.hotel.R
import com.mmt.hotel.bookingreview.model.response.price.PriceItem
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomCriteriaV2
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.model.tracking.PriceTrackingData
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.KEY_TOTAL_AMOUNT
import com.mmt.hotel.detail.dataModel.HotelDetailData
import com.mmt.travel.app.hotel.thankyou.model.response.bookingDetails.BookingDetails
import com.mmt.travel.app.hotel.thankyou.model.uiModel.HotelDetailUiModel
import com.mmt.travel.app.hotel.thankyou.model.uiModel.RoomInfoUIModel
import com.mmt.travel.app.hotel.thankyou.ui.FragmentHotelThankYou.Companion.TAG
import com.mmt.uikit.util.isNotNullAndEmpty
import java.text.SimpleDateFormat
import java.util.Currency
import java.util.Date
import java.util.Locale


private const val CONTENT_TYPES = "hotel"
private const val CONTENT_TYPES_SHORTSTAY = "hotel_dsc"
private const val CONTENT_TYPES_HOMESTAY = "homestay"
private const val CHECK_IN_DATE = "fb_checkin_date"
private const val CHECK_OUT_DATE = "fb_checkout_date"
private const val CITY_NAME = "fb_destination"
private const val COUNTRY_CODE = "fb_country"
private const val ADULTS = "fb_num_adults"
private const val CHILDREN = "fb_num_children"
private const val INFANTS = "fb_num_infants"
private const val PURCHASE_VALUE = "fb_purchase_value"
private const val PURCHASE_CURRENCY = "fb_purchase_currency"
private const val DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd"
private val map: Map<String, String> = MapResourceParserUtil.getHashMapResource(MMTCore.mContext, R.xml.premium_hotel_cities)


/**
 * method to log Hotel Listing Search event on FB
 *
 * <AUTHOR> Airon : Jan 27, 2021
 */

object FacebookEventsTracking {
    fun logFBSearchEvent(
        userSearchData: UserSearchData,
        roomCriteriaV2List: List<RoomStayCandidatesV2>
    ) {
        val paramBundle = Bundle()
        fillInCommonParams(userSearchData, paramBundle)
        val adultChildCountPair = getAdultChildCountPair(roomCriteriaV2List)
        paramBundle.putString(ADULTS, adultChildCountPair.first.toString())
        paramBundle.putString(CHILDREN, adultChildCountPair.second.toString())
        logEventOnFacebook(
            AppEventsConstants.EVENT_NAME_SEARCHED,
            paramBundle
        )
    }

    /**
     * method to log Hotel Detail Viewed event on FB
     *
     * <AUTHOR> Airon : Jan 25, 2021
     */
    fun logFBContentViewEvent(data: HotelDetailData, discountedPrice: Double) {
        val paramBundle = Bundle()
        val userSearchData = data.userData
        fillInCommonParams(userSearchData, paramBundle)
        val adultChildCountPair = getAdultChildCountPair(data.roomStayCandidate)
        paramBundle.putString(ADULTS, adultChildCountPair.first.toString())
        paramBundle.putString(CHILDREN, adultChildCountPair.second.toString())
        paramBundle.putString(
            PURCHASE_VALUE,
            StringUtil.getCommaSeparatedRoundOfPrice(discountedPrice)
        )
        logEventOnFacebook(
            AppEventsConstants.EVENT_NAME_VIEWED_CONTENT,
            paramBundle
        )
    }

    /**
     * method to log Review Hotel Booking event on FB
     *
     * <AUTHOR> Airon : Jan 25, 2021
     */
    fun logFBBookingReviewEvent(
        userSearchData: UserSearchData,
        roomCriteriaV2List: List<RoomCriteriaV2>,
        priceItemList: List<PriceItem>?
    ) {
        val paramBundle = Bundle()
        fillInCommonParams(userSearchData, paramBundle)
        val adultChildCountPair = getAdultChildCountPairForRooms(roomCriteriaV2List)
        paramBundle.putString(ADULTS, adultChildCountPair.first.toString())
        paramBundle.putString(CHILDREN, adultChildCountPair.second.toString())
        paramBundle.putString(
            PURCHASE_VALUE,
            StringUtil.getCommaSeparatedRoundOfPrice(getTotalPrice(priceItemList))
        )
        logEventOnFacebook(
            AppEventsConstants.EVENT_NAME_INITIATED_CHECKOUT,
            paramBundle
        )
    }

    fun logFBPurchaseEvent(
        countryCode: String,
        currencyCode: String,
        hotelDetailUiModel: HotelDetailUiModel,
        roomInfoUIModel: RoomInfoUIModel,
        priceTrackingData: PriceTrackingData?,
        responseCheckinDate: String,
        responseCheckoutDate: String
    ) {
        val paramBundle = Bundle()
        try {
            val checkInDate = BookingDetails.SERVER_DATE_FORMATTER.parse(responseCheckinDate)
            val checkOutDate = BookingDetails.SERVER_DATE_FORMATTER.parse(responseCheckoutDate)
            val startDate: String =
                checkInDate?.let {
                    SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD, Locale.ENGLISH).format(
                        it
                    )
                } ?: EMPTY_STRING
            val endDate: String =
                checkOutDate?.let {
                    SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD, Locale.ENGLISH).format(
                        it
                    )
                } ?: EMPTY_STRING
            paramBundle.putString(CHECK_IN_DATE, startDate)
            paramBundle.putString(CHECK_OUT_DATE, endDate)
        } catch (ex: Exception) {
            LogUtils.error(
                lob = CommonConstants.LOB_HOTEL,
                tag = TAG,
                severity = Severity.CRITICAL,
                message = "FB purchase tracking",
                cause = ex
            )
        }

        paramBundle.putString(AppEventsConstants.EVENT_PARAM_CONTENT_ID, hotelDetailUiModel.hotelId)
        paramBundle.putString(
            AppEventsConstants.EVENT_PARAM_CONTENT_TYPE,
            getContentType(altAcco = hotelDetailUiModel.altAcco)
        )
        paramBundle.putString(AppEventsConstants.EVENT_PARAM_CURRENCY, currencyCode)

        paramBundle.putString(CITY_NAME, hotelDetailUiModel.cityName)
        paramBundle.putString(COUNTRY_CODE, countryCode)
        paramBundle.putString(ADULTS, roomInfoUIModel.totalAdultCount.toString())
        paramBundle.putString(CHILDREN, roomInfoUIModel.totalChildCount.toString())
        paramBundle.putString(INFANTS, 0.toString())
        paramBundle.putString(PURCHASE_CURRENCY, currencyCode)
        val amountInDouble = priceTrackingData?.totalAmount?.toDouble() ?: 0.0
        val amount = StringUtil.getCommaSeparatedRoundOfPrice(amountInDouble)
        paramBundle.putString(PURCHASE_VALUE, amount)
        HotelMigratorHelper.instance.logPurchaseEvent(
            amountInDouble.toBigDecimal(),
            Currency.getInstance(currencyCode),
            paramBundle
        )
    }

    /**
     * method to get total Price from a given list of [PriceItem]
     *
     * <AUTHOR> Airon : Jan 25, 2021
     */
    private fun getTotalPrice(priceItemList: List<PriceItem>?): Double {
        return priceItemList?.firstOrNull { it.key == KEY_TOTAL_AMOUNT }?.amount ?: 0.0
    }

/**
 * method to fill in common params in a given [Bundle]
 */
private fun fillInCommonParams(userSearchData: UserSearchData, paramBundle: Bundle) {
    val currencyCode = HotelCurrencyUtil.getSelectedCurrencyCode()
    val startDate: String = HotelUtil.convertCheckInDateFormat(userSearchData.checkInDate, HotelConstants.CURRENT_DATE_FORMAT, DATE_FORMAT_YYYY_MM_DD)
    val endDate: String = HotelUtil.convertCheckOutDateFormat(userSearchData.checkOutDate, HotelConstants.CURRENT_DATE_FORMAT, DATE_FORMAT_YYYY_MM_DD, userSearchData.checkInDate)
    val hotelID = userSearchData.hotelId
    if (hotelID.isNotNullAndEmpty()) {
        val premiumHotelId = isAppendPremiumHotelId(userSearchData.locationId, userSearchData.countryCode)
        premiumHotelId?.let {
            paramBundle.putString(AppEventsConstants.EVENT_PARAM_CONTENT_ID, appendToHotelID(hotelId = hotelID, premiumHotelId = it))
        } ?: paramBundle.putString(AppEventsConstants.EVENT_PARAM_CONTENT_ID, hotelID)
    }
    paramBundle.putString(
        AppEventsConstants.EVENT_PARAM_CONTENT_TYPE,
        getContentType(userSearchData)
    )
    paramBundle.putString(AppEventsConstants.EVENT_PARAM_CURRENCY, currencyCode)
    paramBundle.putString(CHECK_IN_DATE, startDate)
    paramBundle.putString(CHECK_OUT_DATE, endDate)
    paramBundle.putString(CITY_NAME, userSearchData.locationName)
    paramBundle.putString(COUNTRY_CODE, userSearchData.countryCode)
    paramBundle.putString(INFANTS, 0.toString())
    paramBundle.putString(PURCHASE_CURRENCY, currencyCode)
}

    private fun getContentType(
        userSearchData: UserSearchData? = null,
        altAcco: Boolean = false
    ): String {
        return if (HotelFunnel.SHORT_STAYS.funnelValue == userSearchData?.funnelSrc) {
            CONTENT_TYPES_SHORTSTAY
        } else if (HotelFunnel.HOMESTAY.funnelValue == userSearchData?.funnelSrc || altAcco) {
            CONTENT_TYPES_HOMESTAY
        } else {
            CONTENT_TYPES
        }
    }

    /**
     * method to get premium hotelId for a given locusId and the country
     */
    private fun isAppendPremiumHotelId(locusId: String, countryCode: String): String? {
        return if (HotelConstants.COUNTRY_CODE_INDIA == countryCode && CollectionUtil.isNotEmptyMap(
                map
            )
        ) {
            map[locusId]
        } else null
    }

    /**
     * method to append hotelId with the premiumHotelId
     */
    private fun appendToHotelID(hotelId: String, premiumHotelId: String): String {
        return GsonUtils.getInstance().serializeToJson(listOf(hotelId, premiumHotelId))
    }

    /**
     * method to get a [Pair<Int, Int>] from a given list of [RoomStayCandidatesV2]
     * where first param in the pair would represent the total adultCount and second param would represent the total childCount
     */
    private fun getAdultChildCountPair(roomStayCandidatesV2List: List<RoomStayCandidatesV2>): Pair<Int, Int> {
        var adultCount = 0
        var childCount = 0
        roomStayCandidatesV2List.forEach {
            adultCount += it.adultCount
            childCount += it.childAges?.size ?: 0
        }
        return Pair(adultCount, childCount)
    }

    /**
     * method to get a [Pair<Int, Int>] from a given list of [RoomCriteriaV2]
     * where first param in the pair would represent the total adultCount and second param would represent the total childCount
     */
    private fun getAdultChildCountPairForRooms(roomCriteriaV2List: List<RoomCriteriaV2>): Pair<Int, Int> {
        var adultCount = 0
        var childCount = 0
        roomCriteriaV2List.forEach { roomCriteria ->
            roomCriteria.roomStayCandidates?.forEach {
                adultCount += it.adultCount
                childCount += it.childAges?.size ?: 0
            }
        }
        return Pair(adultCount, childCount)
    }

    fun logEventOnFacebook(eventName: String, paramBundle: Bundle) {
        CommonMigrationHelper.instance.logFBEvent(eventName, paramBundle)
    }

}
