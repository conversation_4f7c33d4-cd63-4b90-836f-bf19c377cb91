package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class HotelPdtBookingInfo (
    val currency: String,
    val price: Double,
    val booking_transaction_id: String?,
    val tax: Double,
    val applied_coupon: String?,
    val from_date_time: Long,
    val to_date_time: Long,
    val number_of_rooms: Int,
    val origin: String?,
    val destination: String,
    val travel_class: String?,
    val trip_type: String?,
    val booking_id: String,
    val booking_parent_id: String,
    val booking_date: String,
    val is_self_booking: Boolean,
    val status: String,
    val payment_type: String?
) : Parcelable
