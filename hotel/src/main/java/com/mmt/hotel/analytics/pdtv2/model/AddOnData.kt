package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class AddOnData(
    val id:String,
    @SerializedName("addontype")
    val addOnType:String,
    @SerializedName("includedunits")
    val includedUnits:Int,
    @SerializedName("unitprice")
    val unitPrice:Double,
    @SerializedName("unittype")
    val unitType:String,
    @SerializedName("heading")
    val heading:String = CoreConstants.EMPTY_STRING,
    val selected:Boolean,
    val available:Boolean,
    @SerializedName("is_preselected")
    val isPreselected:Boolean
) : Parcelable{
}