package com.mmt.hotel.analytics.pdt.model;

import com.google.gson.annotations.SerializedName;

public class SimilarHotelEventModel {
    @SerializedName("pd_htl_crd_id")
    private String hotelId;

    public SimilarHotelEventModel() {
    }

    public String getHotelId() {
        return this.hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof SimilarHotelEventModel))
            return false;
        final SimilarHotelEventModel other = (SimilarHotelEventModel) o;
        if (!other.canEqual(this)) return false;
        final Object this$hotelId = this.hotelId;
        final Object other$hotelId = other.hotelId;
        if (this$hotelId == null ? other$hotelId != null : !this$hotelId.equals(other$hotelId))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof SimilarHotelEventModel;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        final Object $hotelId = this.hotelId;
        result = result * PRIME + ($hotelId == null ? 43 : $hotelId.hashCode());
        return result;
    }

    public String toString() {
        return "SimilarHotelEventModel(hotelId=" + this.hotelId + ")";
    }
}
