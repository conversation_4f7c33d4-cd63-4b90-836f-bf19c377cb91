package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.hotel.analytics.pdt.model.TravelerDetail;
import com.pdt.pdtDataLogging.events.model.Event;

public class HotelReviewEntryEvent extends HotelPageEntryEvent {
    private TravelerDetail travelerDetail;

    public HotelReviewEntryEvent(@NonNull String pageName, int eventType, long sessionStartTime,
                                 @NonNull String omnitureName,
                                 @NonNull String parentScreenName , @NonNull String prevFunnelStep, @NonNull String prevPageName) {
        super(pageName, eventType, sessionStartTime, omnitureName, parentScreenName, prevFunnelStep, prevPageName);
        travelerDetail = new TravelerDetail();
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        event.getEventParam().putAll(travelerDetail.getEventParams());
        return event;
    }

    public void setTravelerFName(String travelerFName) {
        travelerDetail.setTravelerFName(travelerFName);
    }

    public void setTravelerLName(String travelerLName) {
        travelerDetail.setTravelerLName(travelerLName);
    }

    public void setTravelerTitle(String travelerTitle) {
        travelerDetail.setTravelerTitle(travelerTitle);
    }

    public void setGstOpted(boolean gstOpted) {
        travelerDetail.setGstOpted(gstOpted);
    }

    public void setTravelerCmpAddress(String travelerCmpAddress) {
        travelerDetail.setTravelerCmpAddress(travelerCmpAddress);
    }

    public void setTravelerCmpName(String travelerCmpName) {
        travelerDetail.setTravelerCmpName(travelerCmpName);
    }

    public void setTravelerMobileNo(String travelerMobileNo) {
        travelerDetail.setTravelerMobileNo(travelerMobileNo);
    }

    public void setTravelerEmailId(String travelerEmailId) {
        travelerDetail.setTravelerEmailId(travelerEmailId);
    }

    public void setTravelerGSTNo(String travelerGSTNo) {
        travelerDetail.setTravelerGSTNo(travelerGSTNo);
    }

    public void setTravelerCtyMobileCode(String travelerCtyMobileCode) {
        travelerDetail.setTravelerCtyMobileCode(travelerCtyMobileCode);
    }

    public TravelerDetail getTravelerDetail() {
        return this.travelerDetail;
    }

    public String toString() {
        return "HotelReviewEntryEvent()";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelReviewEntryEvent))
            return false;
        final HotelReviewEntryEvent other = (HotelReviewEntryEvent) o;
        if (!other.canEqual(this)) return false;
        final Object this$travelerDetail = this.getTravelerDetail();
        final Object other$travelerDetail = other.getTravelerDetail();
        if (this$travelerDetail == null ? other$travelerDetail != null : !this$travelerDetail.equals(other$travelerDetail))
            return false;
        if (!super.equals(o)) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelReviewEntryEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $travelerDetail = this.getTravelerDetail();
        result = result * PRIME + ($travelerDetail == null ? 43 : $travelerDetail.hashCode());
        return result;
    }
}
