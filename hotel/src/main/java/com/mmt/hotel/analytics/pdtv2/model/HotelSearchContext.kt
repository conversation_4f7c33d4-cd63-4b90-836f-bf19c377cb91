package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.pdt.eagleEye.models.Details
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelSearchContext(
    @SerializedName("checkin_time")
    val checkInTime: Int?,
    @SerializedName("checkin_duration")
    val checkInDuration: Int?,
    @SerializedName("to")
    val to: To?,
    @SerializedName("to_date_time")
    val toDateTime: Long?,
    @SerializedName("pax")
    val persons: Pax?,
    @SerializedName("from_date_time")
    val fromDateTime: Long?,
    @SerializedName("product_id")
    val hotelId:String?,
    @SerializedName("search_text")
    val searchText: String?=null,
    @SerializedName("search_type")
    val searchType: String?,
    @SerializedName("trip_type")
    val trip_type: String?,
) : Parcelable

@Parcelize
data class To(val locus:Locus) : Parcelable

@Parcelize
data class Locus(
    @SerializedName("locus_id")
    val locusId:String?,
    @SerializedName("locus_type")
    val locusType:String?,
    val country:String?,
    val lat:Double? = null,
    val long:Double? = null
    ) : Parcelable


@Parcelize
data class Pax(
    @SerializedName("count")
    val count: Int,
    @SerializedName("details")
    val details: Details,
    @SerializedName("rooms")
    val rooms:Int?
) : Parcelable