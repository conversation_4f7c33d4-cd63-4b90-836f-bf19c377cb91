package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.old.hotelreview.model.request.checkout.SpecialCheckoutRequest
import kotlinx.parcelize.Parcelize

@Parcelize
data class SpecialRequest(
    val categories:List<Category>
) : Parcelable

@Parcelize
data class Category(
    val code:String,
    val name:String,
    val value: String?=null,
    @SerializedName("sub_categories")
    val subCategories:List<SubCatagory>
) : Parcelable

@Parcelize
data class SubCatagory(
    val code:String,
    val name:String,
    val value:String
) : Parcelable
