package com.mmt.hotel.analytics.pdt.events;

import static com.mmt.analytics.omnitureclient.OmnitureHelper.SEND_DATE_FORMAT;
import static com.mmt.analytics.pdtclient.PDTAnalyticsKeys.POKUS_EXPERIMENT_DATA_V2;

import androidx.annotation.NonNull;

import com.mmt.analytics.AnalyticsSDK;
import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.auth.login.util.LoginUtils;
import com.mmt.core.constant.CoreConstants;
import com.mmt.hotel.base.tracking.HotelBasePdtTrackingHelper;
import com.gommt.logger.LogUtils;
import com.mmt.core.util.CollectionUtil;
import com.mmt.core.util.DateUtil;
import com.mmt.core.util.StringUtil;
import com.mmt.hotel.common.HotelSharedPrefUtil;
import com.mmt.hotel.common.constants.HotelConstants;
import com.mmt.hotel.common.constants.HotelFunnel;
import com.mmt.hotel.common.constants.SharedPrefKeys;
import com.mmt.hotel.common.util.HotelMigratorHelper;
import com.mmt.hotel.common.util.HotelUtil;
import com.pdt.pdtDataLogging.events.model.Event;
import com.pdt.pdtDataLogging.events.model.ExperimentEventModel;
import com.pdt.pdtDataLogging.events.model.TrackingDataWrapper;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HotelGenericEvent extends HotelLocusDataGenericEvent {

    private String TAG = "HotelGenericEvent";
    private String extraContent;
    private String topImgUrl;
    private String bnplPersuasion;
    private float originalPrice;
    private float discountedPrice;
    private int startRating;
    private float userRating;
    private String ratePlanCode;
    private boolean isHotelShortListed;
    private String cityCode;
    private String countryCode;
    private List<ExperimentEventModel> experimentEventModelList;
    private boolean callSuperHg = true;
    private String propertyType = HotelConstants.PROPERTY_TYPE_HOTEL.toLowerCase();

    private String checkInDate;
    private String checkoutDate;
    private int adult;
    private int child;
    private int totalGuest;
    private int totalRoom;
    private String travelPurpose;
    private String cityString;
    private String countryString;
    private String areaString;
    private int stayLength;
    private int numberOfNight;
    private String roomStayQualifier;
    private String searchType;
    private String funnelSource = HotelFunnel.HOTEL.getFunnelName();
    // day use params
    private String slotDuration;
    private String dayUseCheckInTime;

    public HotelGenericEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                             @NonNull String omnitureName,
                             @NonNull String parentScreenName) {
        super(eventName, pageName, eventType, omnitureName, parentScreenName, CoreConstants.EMPTY_STRING, CoreConstants.EMPTY_STRING);
    }

    public HotelGenericEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                             @NonNull String omnitureName,
                             @NonNull String parentScreenName, boolean callSuper) {
        this(eventName, pageName, eventType, omnitureName, parentScreenName);
        this.callSuperHg = callSuper;

    }



    public String getBnplPersuasion() {
        return bnplPersuasion;
    }

    public void setBnplPersuasion(String bnplPersuasion) {
        this.bnplPersuasion = bnplPersuasion;
    }

    @Override
    protected Event createPDTEvent() {
        Event event;
        if (callSuperHg) {
            event = super.createPDTEvent();
        } else {
            event = Event.createEvent(getEventName(), new HashMap<>());
        }
        appendLocusParamsInEvent(event);


        if(StringUtil.isNotNullAndEmpty(bnplPersuasion)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_BNPL, bnplPersuasion);
        }
        if (StringUtil.isNotNullAndEmpty(extraContent)) {
            event.getEventParam().put(PDTAnalyticsKeys.META_ACT_CNTNT, extraContent);
        }
        if (StringUtil.isNotNullAndEmpty(topImgUrl)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_TOP_IMG, topImgUrl);
        }

        if (originalPrice != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_DISP_SELL_AMT, originalPrice);
        }
        if (discountedPrice != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_DISP_SELL_AMT_DIS, discountedPrice);
        }

        event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_STR, startRating);

        if (userRating != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RT_DISP, userRating);
        }
        if (StringUtil.isNotNullAndEmpty(getHotelId())) {//This check is to identify  event is for some hotel or not
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_SHRTLSTD, isHotelShortListed);
        }
        event.getEventParam().putAll(createHotelBasePDTEvent().getEventParam());
        experimentEventModelList = HotelUtil.getExperimentsForPdtTracking(null);
        if (CollectionUtil.isNotEmptyCollection(experimentEventModelList)) {
            event.getEventParam().put(PDTAnalyticsKeys.EXP_IDS_STATUS, experimentEventModelList);
        }

        List<Object> pokusExpData = HotelBasePdtTrackingHelper.INSTANCE.getPokusPdtTrackingData();

        if (CollectionUtil.isNotEmptyCollection(pokusExpData)) {
            event.getEventParam().put(PDTAnalyticsKeys.POKUS_EXPERIMENT_DATA, pokusExpData);
        }

        TrackingDataWrapper trackingData = HotelBasePdtTrackingHelper.INSTANCE.getPokusPdtTrackingDataV2();

        if (trackingData.hasEnoughData()){
            event.getEventParam().put(POKUS_EXPERIMENT_DATA_V2, trackingData);
        }

        event.getEventParam().put(PDTAnalyticsKeys.SESS_MMT_ID, AnalyticsSDK.Companion.getSessionId());

        if(LoginUtils.INSTANCE.isCorporateUser() && StringUtil.isNotNullAndEmpty(LoginUtils.INSTANCE.getOrgId())){
            event.getEventParam().put(PDTAnalyticsKeys.USR_CORP_ORG_ID,LoginUtils.INSTANCE.getOrgId());
        }
        if (StringUtil.isNotNullAndEmpty(ratePlanCode)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RT_PLAN_CD, ratePlanCode);
        }
        if(HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ORDR, null) != null){
            event.getEventParam().put(PDTAnalyticsKeys.EXP_RNK_ORDR, HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ORDR, null));
        }
        if(HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ALGO_VER, null) != null){
            event.getEventParam().put(PDTAnalyticsKeys.EXP_RNK_ORDR, HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ALGO_VER, null));
        }
        event.getEventParam().put(PDTAnalyticsKeys.PROPERTY_TYPE, propertyType);
        return event;
    }

    public Event createHotelBasePDTEvent() {
        Event event = Event.createEvent(getEventName(), new HashMap<>());

        Map<String, Object> eventParam = event.getEventParam();

        appendLocusParamsInEvent(event);

        if (StringUtil.isNotNullAndEmpty(getCheckInDate())) {
            eventParam.put(PDTAnalyticsKeys.SRCH_CHECKIN_DT, getCheckInDate());
        }
        if (StringUtil.isNotNullAndEmpty(getCheckoutDate())) {
            eventParam.put(PDTAnalyticsKeys.SRCH_CHECKOUT_DT, getCheckoutDate());
        }

        eventParam.put(PDTAnalyticsKeys.SRCH_GUEST_ADULT, getAdult());
        eventParam.put(PDTAnalyticsKeys.SRCH_GUEST_CHILD, getChild());
        eventParam.put(PDTAnalyticsKeys.SRCH_RM_TOT, getTotalRoom());
        eventParam.put(PDTAnalyticsKeys.SRCH_TRVL_PURP, getTravelPurpose());


        eventParam.put(PDTAnalyticsKeys.SRCH_TRVL_PURP_OPTD, HotelSharedPrefUtil.Companion.getInstance().getBoolean(SharedPrefKeys.KEY_TRAVELING_PURPOSE_OPTED));

        // day use params
        if (StringUtil.isNotNullAndEmpty(getDayUseCheckInTime())) {
            eventParam.put(PDTAnalyticsKeys.DAY_USE_CHECKIN_TIME, getDayUseCheckInTime());
        }
        if (StringUtil.isNotNullAndEmpty(getSlotDuration())) {
            eventParam.put(PDTAnalyticsKeys.DAY_USE_STAY_DURATION, getSlotDuration());
        }

        eventParam.put(PDTAnalyticsKeys.SRCH_GUEST_TOT, getTotalGuest());
        if (getStayLength() != 0) {
            eventParam.put(PDTAnalyticsKeys.SRCH_LOS, getStayLength());
        }
        if (numberOfNight != 0) {
            eventParam.put(PDTAnalyticsKeys.SRCH_RM_NGHTS, getNumberOfNight());
        }
        if (StringUtil.isNotNullAndEmpty(getRoomStayQualifier())) {
            eventParam.put(PDTAnalyticsKeys.SRCH_RSQ, getRoomStayQualifier());
        }

        int advancePurchase = getAdvancePurchase();
        if (advancePurchase != -1) {
            eventParam.put(PDTAnalyticsKeys.SRCH_AP, advancePurchase);
        }

        event.getEventParam().put(PDTAnalyticsKeys.FUNNEL_SOURCE, funnelSource);

        return Event.createEvent(getEventName(), eventParam);
    }

    private int getAdvancePurchase() {
        Date currentDate = new Date();
        DateFormat inputFormat = new SimpleDateFormat(SEND_DATE_FORMAT);
        Date checkInDate;
        try {
            checkInDate = inputFormat.parse(getCheckInDate());
        } catch (Exception e) {
            LogUtils.error(TAG, e.getMessage());
            return -1;
        }
        return DateUtil.getDiffDate(currentDate, checkInDate);
    }

    public String getExtraContent() {
        return this.extraContent;
    }

    public String getTopImgUrl() {
        return this.topImgUrl;
    }

    public float getOriginalPrice() {
        return this.originalPrice;
    }

    public float getDiscountedPrice() {
        return this.discountedPrice;
    }

    public int getStartRating() {
        return this.startRating;
    }

    public float getUserRating() {
        return this.userRating;
    }

    public String getRatePlanCode() {
        return this.ratePlanCode;
    }

    public boolean isHotelShortListed() {
        return this.isHotelShortListed;
    }

    public String getCityCode() {
        return this.cityCode;
    }

    public String getCountryCode() {
        return this.countryCode;
    }

    public List<ExperimentEventModel> getExperimentEventModelList() {
        return this.experimentEventModelList;
    }

    public boolean isCallSuperHg() {
        return this.callSuperHg;
    }

    public void setExtraContent(String extraContent) {
        this.extraContent = extraContent;
    }

    public void setTopImgUrl(String topImgUrl) {
        this.topImgUrl = topImgUrl;
    }

    public void setOriginalPrice(float originalPrice) {
        this.originalPrice = originalPrice;
    }

    public void setDiscountedPrice(float discountedPrice) {
        this.discountedPrice = discountedPrice;
    }

    public void setStartRating(int startRating) {
        this.startRating = startRating;
    }

    public void setUserRating(float userRating) {
        this.userRating = userRating;
    }

    public void setRatePlanCode(String ratePlanCode) {
        this.ratePlanCode = ratePlanCode;
    }

    public void setHotelShortListed(boolean isHotelShortListed) {
        this.isHotelShortListed = isHotelShortListed;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public void setExperimentEventModelList(List<ExperimentEventModel> experimentEventModelList) {
        this.experimentEventModelList = experimentEventModelList;
    }

    public void setCallSuperHg(boolean callSuperHg) {
        this.callSuperHg = callSuperHg;
    }

    public void setPropertyType(String propertyType) {
        this.propertyType = propertyType;
    }


    public String getCheckInDate() {
        return checkInDate;
    }

    public void setCheckInDate(String checkInDate) {
        this.checkInDate = checkInDate;
    }

    public String getCheckoutDate() {
        return checkoutDate;
    }

    public void setCheckoutDate(String checkoutDate) {
        this.checkoutDate = checkoutDate;
    }

    public int getAdult() {
        return adult;
    }

    public void setAdult(int adult) {
        this.adult = adult;
    }

    public int getChild() {
        return child;
    }

    public void setChild(int child) {
        this.child = child;
    }

    public int getTotalGuest() {
        return totalGuest;
    }

    public void setTotalGuest(int totalGuest) {
        this.totalGuest = totalGuest;
    }

    public int getTotalRoom() {
        return totalRoom;
    }

    public void setTotalRoom(int totalRoom) {
        this.totalRoom = totalRoom;
    }

    public String getTravelPurpose() {
        return travelPurpose;
    }

    public void setTravelPurpose(String travelPurpose) {
        this.travelPurpose = travelPurpose;
    }

    public String getCityString() {
        return cityString;
    }

    public void setCityString(String cityString) {
        this.cityString = cityString;
    }

    public String getCountryString() {
        return countryString;
    }

    public void setCountryString(String countryString) {
        this.countryString = countryString;
    }

    public String getAreaString() {
        return areaString;
    }

    public void setAreaString(String areaString) {
        this.areaString = areaString;
    }

    public int getStayLength() {
        return stayLength;
    }

    public void setStayLength(int stayLength) {
        this.stayLength = stayLength;
    }

    public int getNumberOfNight() {
        return numberOfNight;
    }

    public void setNumberOfNight(int numberOfNight) {
        this.numberOfNight = numberOfNight;
    }

    public String getRoomStayQualifier() {
        return roomStayQualifier;
    }

    public void setRoomStayQualifier(String roomStayQualifier) {
        this.roomStayQualifier = roomStayQualifier;
    }

    public String getSlotDuration() {
        return slotDuration;
    }

    public void setSlotDuration(String slotDuration) {
        this.slotDuration = slotDuration;
    }

    public String getDayUseCheckInTime() {
        return dayUseCheckInTime;
    }

    public void setDayUseCheckInTime(String dayUseCheckInTime) {
        this.dayUseCheckInTime = dayUseCheckInTime;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public String getFunnelSource() {
        return funnelSource;
    }

    public void setFunnelSource(String funnelSource) {
        this.funnelSource = funnelSource;
    }


    public String toString() {
        return "HotelGenericEvent(hotelId=" + this.getHotelId() + ", extraContent=" + this.extraContent + ", topImgUrl=" + this.topImgUrl + ", originalPrice=" + this.originalPrice + ", discountedPrice=" + this.discountedPrice + ", startRating=" + this.startRating + ", userRating=" + this.userRating + ", ratePlanCode=" + this.ratePlanCode + ", isHotelShortListed=" + this.isHotelShortListed + ", cityCode=" + this.cityCode + ", countryCode=" + this.countryCode + ", experimentEventModelList=" + this.experimentEventModelList + ", callSuperHg=" + this.callSuperHg + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelGenericEvent))
            return false;
        final HotelGenericEvent other = (HotelGenericEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$hotelId = this.getHotelId();
        final Object other$hotelId = other.getHotelId();
        if (this$hotelId == null ? other$hotelId != null : !this$hotelId.equals(other$hotelId))
            return false;
        final Object this$extraContent = this.getExtraContent();
        final Object other$extraContent = other.getExtraContent();
        if (this$extraContent == null ? other$extraContent != null : !this$extraContent.equals(other$extraContent))
            return false;
        final Object this$topImgUrl = this.getTopImgUrl();
        final Object other$topImgUrl = other.getTopImgUrl();
        if (this$topImgUrl == null ? other$topImgUrl != null : !this$topImgUrl.equals(other$topImgUrl))
            return false;
        if (Float.compare(this.getOriginalPrice(), other.getOriginalPrice()) != 0) return false;
        if (Float.compare(this.getDiscountedPrice(), other.getDiscountedPrice()) != 0) return false;
        if (this.getStartRating() != other.getStartRating()) return false;
        if (Float.compare(this.getUserRating(), other.getUserRating()) != 0) return false;
        final Object this$ratePlanCode = this.getRatePlanCode();
        final Object other$ratePlanCode = other.getRatePlanCode();
        if (this$ratePlanCode == null ? other$ratePlanCode != null : !this$ratePlanCode.equals(other$ratePlanCode))
            return false;
        if (this.isHotelShortListed() != other.isHotelShortListed()) return false;
        final Object this$cityCode = this.getCityCode();
        final Object other$cityCode = other.getCityCode();
        if (this$cityCode == null ? other$cityCode != null : !this$cityCode.equals(other$cityCode))
            return false;
        final Object this$countryCode = this.getCountryCode();
        final Object other$countryCode = other.getCountryCode();
        if (this$countryCode == null ? other$countryCode != null : !this$countryCode.equals(other$countryCode))
            return false;
        final Object this$experimentEventModelList = this.getExperimentEventModelList();
        final Object other$experimentEventModelList = other.getExperimentEventModelList();
        if (this$experimentEventModelList == null ? other$experimentEventModelList != null : !this$experimentEventModelList.equals(other$experimentEventModelList))
            return false;
        if (this.isCallSuperHg() != other.isCallSuperHg()) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelGenericEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $hotelId = this.getHotelId();
        result = result * PRIME + ($hotelId == null ? 43 : $hotelId.hashCode());
        final Object $extraContent = this.getExtraContent();
        result = result * PRIME + ($extraContent == null ? 43 : $extraContent.hashCode());
        final Object $topImgUrl = this.getTopImgUrl();
        result = result * PRIME + ($topImgUrl == null ? 43 : $topImgUrl.hashCode());
        result = result * PRIME + Float.floatToIntBits(this.getOriginalPrice());
        result = result * PRIME + Float.floatToIntBits(this.getDiscountedPrice());
        result = result * PRIME + this.getStartRating();
        result = result * PRIME + Float.floatToIntBits(this.getUserRating());
        final Object $ratePlanCode = this.getRatePlanCode();
        result = result * PRIME + ($ratePlanCode == null ? 43 : $ratePlanCode.hashCode());
        result = result * PRIME + (this.isHotelShortListed() ? 79 : 97);
        final Object $cityCode = this.getCityCode();
        result = result * PRIME + ($cityCode == null ? 43 : $cityCode.hashCode());
        final Object $countryCode = this.getCountryCode();
        result = result * PRIME + ($countryCode == null ? 43 : $countryCode.hashCode());
        final Object $experimentEventModelList = this.getExperimentEventModelList();
        result = result * PRIME + ($experimentEventModelList == null ? 43 : $experimentEventModelList.hashCode());
        result = result * PRIME + (this.isCallSuperHg() ? 79 : 97);
        return result;
    }
}
