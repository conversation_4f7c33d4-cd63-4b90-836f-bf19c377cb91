package com.mmt.hotel.analytics.pdtMetrics.helper

import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.currency.CurrencyUtil

object HotelPdtV2MetricsHelper {

    enum class MetricsStatus {
        SUCCESS, ERROR, INITIATED
    }

    enum class MetricsType {
        LOAD_TIME, SCREEN_RENDER, API_LOGGING, EVAR_FAILURE
    }

    enum class EntityFormat {
        M3U8, MP4, PNG, JSON
    }

    enum class MetricsEntityId {
        LISTING_SCREEN_RENDER,
        HOTEL_LANDING_SCREEN_RENDER,
        ALTACCO_LANDING_SCREEN_RENDER,
        DETAIL_SCREEN_RENDER,
        SELECT_ROOM_SCREEN_RENDER,
        BOOKING_REVIEW_SCREEN_RENDER,
        CORP_APPROVAL_SCREEN_RENDER,
        TREEL_SCREEN_RENDER,
        DAYUSE_SCREEN_RENDER,
        GST_API_LOGGING,
        EVAR_3_EMPTY,
        EVAR_3_NULL,
        EVAR_55_EMPTY,
        EVAR_55_NULL,
        PAGE_LOAD_EXCEPTION
    }

    enum class MetricsFunnelContext {
        B2B, B2C, GCC, KSA, A2A
    }

    fun getMetricsFunnelContext(): MetricsFunnelContext {
        return when {
            LoginUtils.isCorporateUser -> MetricsFunnelContext.B2B
            LoginUtils.getPreferredRegion().isUAERegion() -> MetricsFunnelContext.GCC
            LoginUtils.getPreferredRegion().isKSARegion() -> MetricsFunnelContext.KSA
            CurrencyUtil.shouldUseGlobalDomain() -> MetricsFunnelContext.A2A
            else -> MetricsFunnelContext.B2C
        }
    }
}