package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.mmt.core.util.StringUtil;
import com.mmt.hotel.analytics.pdt.TrackingConstants;
import com.mmt.hotel.analytics.pdt.model.ReviewedHotel;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.ArrayList;
import java.util.List;


public class HotelDetailClickEvent extends HotelGenericEvent {
    private List<ReviewedHotel> reviewedHotelList;
    private String imageUrl;
    private int WalletAmount;
    private String couponCode;
    private float couponAmt;

    public HotelDetailClickEvent(@NonNull String hotelId, @NonNull String eventName, int eventType,
                                 @NonNull String omnitureName,
                                 @NonNull String parentScreenName) {
        super(eventName, TrackingConstants.HOTEL_DETAIL, eventType, omnitureName, parentScreenName);
        setHotelId(hotelId);
        reviewedHotelList  = new ArrayList();
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (CollectionUtil.isNotEmptyCollection(reviewedHotelList)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_DTL_DISP, reviewedHotelList);
        }
        if (StringUtil.isNotNullAndEmpty(imageUrl)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_TOP_IMG, imageUrl);
        }
        if (WalletAmount != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PAY_WAL_AMT, WalletAmount);
        }
        if (StringUtil.isNotNullAndEmpty(couponCode)) {
            event.getEventParam().put(PDTAnalyticsKeys.CPN_CODE, couponCode);
        }
        if (couponAmt != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PRC_CPN_AMT, couponAmt);
        }

        return event;
    }

    public List<ReviewedHotel> getReviewedHotelList() {
        return this.reviewedHotelList;
    }

    public String getImageUrl() {
        return this.imageUrl;
    }

    public int getWalletAmount() {
        return this.WalletAmount;
    }

    public String getCouponCode() {
        return this.couponCode;
    }

    public float getCouponAmt() {
        return this.couponAmt;
    }

    public void setReviewedHotelList(List<ReviewedHotel> reviewedHotelList) {
        this.reviewedHotelList = reviewedHotelList;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public void setWalletAmount(int WalletAmount) {
        this.WalletAmount = WalletAmount;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public void setCouponAmt(float couponAmt) {
        this.couponAmt = couponAmt;
    }

    public String toString() {
        return "HotelDetailClickEvent(reviewedHotelList=" + this.reviewedHotelList + ", imageUrl=" + this.imageUrl +", WalletAmount=" + this.WalletAmount + ", couponCode=" + this.couponCode + ", couponAmt=" + this.couponAmt + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelDetailClickEvent))
            return false;
        final HotelDetailClickEvent other = (HotelDetailClickEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$reviewedHotelList = this.getReviewedHotelList();
        final Object other$reviewedHotelList = other.getReviewedHotelList();
        if (this$reviewedHotelList == null ? other$reviewedHotelList != null : !this$reviewedHotelList.equals(other$reviewedHotelList))
            return false;
        final Object this$imageUrl = this.getImageUrl();
        final Object other$imageUrl = other.getImageUrl();
        if (this$imageUrl == null ? other$imageUrl != null : !this$imageUrl.equals(other$imageUrl))
            return false;
        if (this.getWalletAmount() != other.getWalletAmount()) return false;
        final Object this$couponCode = this.getCouponCode();
        final Object other$couponCode = other.getCouponCode();
        if (this$couponCode == null ? other$couponCode != null : !this$couponCode.equals(other$couponCode))
            return false;
        if (Float.compare(this.getCouponAmt(), other.getCouponAmt()) != 0) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelDetailClickEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $reviewedHotelList = this.getReviewedHotelList();
        result = result * PRIME + ($reviewedHotelList == null ? 43 : $reviewedHotelList.hashCode());
        final Object $imageUrl = this.getImageUrl();
        result = result * PRIME + ($imageUrl == null ? 43 : $imageUrl.hashCode());
        result = result * PRIME + this.getWalletAmount();
        final Object $couponCode = this.getCouponCode();
        result = result * PRIME + ($couponCode == null ? 43 : $couponCode.hashCode());
        result = result * PRIME + Float.floatToIntBits(this.getCouponAmt());
        return result;
    }
}
