package com.mmt.hotel.analytics.pdtv2

import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.analytics.pdtMetrics.model.HotelPdtMetrics
import com.mmt.hotel.analytics.pdtv2.model.AddOnData
import com.mmt.hotel.analytics.pdtv2.model.ContentDetailItem
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtBookingInfo
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtErrorListItem
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtEvent
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtEventDetails
import com.mmt.hotel.analytics.pdtv2.model.HotelSearchContext
import com.mmt.hotel.analytics.pdtv2.model.PdtFiltersV2
import com.mmt.hotel.analytics.pdtv2.model.PillItem
import com.mmt.hotel.analytics.pdtv2.model.ProductItem
import com.mmt.hotel.analytics.pdtv2.model.TravellerInfo
import com.mmt.hotel.base.tracking.HotelBasePdtTrackingHelper
import com.mmt.hotel.listingV2.model.request.SorterCriteria
import com.pdt.eagleEye.constants.Lobs
import com.pdt.eagleEye.creators.AbstractBuilder
import com.pdt.eagleEye.models.ErrorDetailList
import com.pdt.eagleEye.models.ExperimentDetails

class HotelPdtEventBuilder(
    val eventName: String,
    val eventType: String,
    pageName: String? = null,
    journeyId: String,
    topicName: String,
    templateID: Int
) : AbstractBuilder<HotelPdtEventBuilder>(
    lob = Lobs.HOTEL,
    eventName = eventName,
    eventType = eventType,
    pageName = pageName,
    journeyId = journeyId,
    topicName = topicName,
    templateID = templateID
) {

    init {
        val pdtData = HotelBasePdtTrackingHelper.getPdtV2TrackingData()
        experimentDetails = ExperimentDetails(
            validExpList = pdtData.second,
            variantKeys = pdtData.first,
            honouredExpList = emptyList()
        )
    }

    @SerializedName("search_context")
    lateinit var searchContext: HotelSearchContext

    var eventValue: String? = null
    var autoSuggestSearchId: String? = null


    @SerializedName("addon_details")
    var addOnDetails: List<AddOnData>? = null

    @SerializedName("traveller_info")
    var travellersList: List<TravellerInfo>? = null

    var bookingInfo: HotelPdtBookingInfo? = null

    private var metrics: HotelPdtMetrics? = null
    private var hotelErrorDetailList: List<HotelPdtErrorListItem>? = null

    override fun getThis(): HotelPdtEventBuilder {
        return this
    }

    val componentBuilder = HotelPdtComponentBuilder()

    fun componentId(id: String): HotelPdtEventBuilder {
        componentBuilder.id = id
        return this
    }

    fun filters(filters: PdtFiltersV2): HotelPdtEventBuilder {
        componentBuilder.filters = filters
        return this
    }

    fun contentDetails(contentList: List<ContentDetailItem>?): HotelPdtEventBuilder {
        componentBuilder.contentDetails = contentList
        return this
    }

    fun componentType(componentType: String): HotelPdtEventBuilder {
        componentBuilder.componentType = componentType
        return this
    }

    fun sortBy(sortCriteria: SorterCriteria): HotelPdtEventBuilder {
        componentBuilder.sortBy = sortCriteria
        return this
    }

    fun pillsData(pillsData: List<PillItem>?): HotelPdtEventBuilder {
        componentBuilder.pillsData = pillsData
        return this
    }

    fun productList(productList: List<ProductItem>?): HotelPdtEventBuilder {
        componentBuilder.productList = productList
        return this
    }

    fun bookingInfo(bookingId: String, status: String, currency: String, price: Double): HotelPdtEventBuilder {
        // Can convert to builder pattern if required in future but many fields are non null hence this is done.
        this.bookingInfo = HotelPdtBookingInfo(
            currency = currency,
            price = price,
            booking_transaction_id = null,
            tax = -1.0,
            applied_coupon = null,
            from_date_time = -1,
            to_date_time = -1,
            number_of_rooms = -1,
            origin = null,
            destination = CoreConstants.EMPTY_STRING,
            travel_class = null,
            trip_type = null,
            booking_id = bookingId,
            booking_parent_id = CoreConstants.EMPTY_STRING,
            booking_date = CoreConstants.EMPTY_STRING,
            is_self_booking = false,
            status = status,
            payment_type = null
        )
        return this
    }

    override fun eventValue(eventValue: String): HotelPdtEventBuilder {
        this.eventValue = eventValue
        return this
    }


    override fun build(): HotelPdtEvent {
        val component = componentBuilder.build()
        val eventDetails = HotelPdtEventDetails(
            eventName = eventName,
            eventType = eventType,
            eventValue = eventValue,
            components = component,
            bookingInfo = bookingInfo,
            autoSuggestSearchId = autoSuggestSearchId,
        )
        return HotelPdtEvent(
            pageContext = pageContext,
            eventTrackingContext = eventTrackingContext,
            errorDetailList = hotelErrorDetailList,
            userContext = userContext,
            deviceContext = deviceContext,
            experimentDetails = experimentDetails,
            trackingInfo = trackingInfo,
            eventDetails = eventDetails,
            searchContext = searchContext,
            addOnDetails = addOnDetails,
            travellersList = travellersList,
            metrics = metrics
        )
    }


    fun searchContext(searchContext: HotelSearchContext) {
        this.searchContext = searchContext
    }

    fun travellerList(travellersList: List<TravellerInfo>) {
        this.travellersList = travellersList
    }

    fun metrics(metrics: HotelPdtMetrics) {
        this.metrics = metrics
    }

    fun hotelErrorDetailList(errorDetailList: List<HotelPdtErrorListItem>) {
        this.hotelErrorDetailList = errorDetailList
    }
}
