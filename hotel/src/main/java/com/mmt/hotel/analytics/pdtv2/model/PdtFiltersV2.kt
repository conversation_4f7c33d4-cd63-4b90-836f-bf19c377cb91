package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class PdtFiltersV2(
    val applied: List<PdtFilterItemV2>?,
    val removed: List<PdtFilterItemV2>?,
    val seen: List<PdtFilterItemV2>? = null,
    @SerializedName("clear_all_clicked")
    val clearAllClicked: Boolean
) : Parcelable