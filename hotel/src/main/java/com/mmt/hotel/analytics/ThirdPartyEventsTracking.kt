package com.mmt.hotel.analytics

import android.os.Bundle
import androidx.annotation.VisibleForTesting
import com.gommt.logger.LogUtils
import com.gommt.logger.Severity
import com.google.firebase.analytics.FirebaseAnalytics
import com.mmt.core.constant.CommonConstants
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.DateUtil
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.tracking.PriceTrackingData
import com.mmt.hotel.common.util.HotelDateUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.travel.app.hotel.thankyou.model.response.bookingDetails.BookingDetails
import com.mmt.travel.app.hotel.thankyou.model.uiModel.HotelDetailUiModel
import com.mmt.travel.app.hotel.thankyou.model.uiModel.RoomInfoUIModel
import com.mmt.uikit.util.fragment.put
import java.util.*
import com.mmt.auth.login.util.LoginUtils

private const val PAGE_LISTING = "LISTING_"
private const val PAGE_DETAIL = "DETAIL_"
private const val PAGE_REVIEW = "REVIEW_"
private const val EVENT_PARAM_REGION = "REGION"
private const val EVENT_PARAM_DAYS_SPENT = "DAYS_SPENT"
private const val DATE_FORMATTER = "MMddyyyy"
private const val LOGGING_DATE_FORMAT = "dd/MM/yyyy"
private const val VALUE_REGION_INDIA = "India"

private const val PAGE_THANK_YOU = "TRANSACTION_"
private const val EVENT_PARAM_DISCOUNT = "DISCOUNT"
private const val FB_TRACKING_KEY_STAYCATION = "SC_"
private const val DESTINATION_COUNTRY = "destCountry"

/**
 * singleton class to provide utility method to log events on
 * third party platforms [currently fireBase and facebook] along with the event parameters.
 * Utility methods are for LISTING/DETAIL/REVIEW and THANKYOU screens
 *
 * <AUTHOR> Airon : Jan 08, 2021
 *
 */
object ThirdPartyEventsTracking {

    const val TAG = "ThirdPartyEventsTracking"

    /**
     * method to log Listing related events and params
     */

    private const val Analytics_COUNTRY_NAME = "destCountry"

    fun trackHotelListEvent(userSearchData: UserSearchData) {
        val eventName = getEventName(pageName = PAGE_LISTING, countryCode = userSearchData.countryCode, funnelSrc = userSearchData.funnelSrc)
        logEvent(eventName = eventName, paramBundle = getEventParamBundle(userSearchData))
    }

    /**
     * method to log Detail related events and params
     */
    fun trackHotelDetailEvent(userSearchData: UserSearchData) {
        val eventName = getEventName(pageName = PAGE_DETAIL, countryCode = userSearchData.countryCode, funnelSrc = userSearchData.funnelSrc)
        logEvent(eventName = eventName, paramBundle = getEventParamBundle(userSearchData))
    }


    /**
     * method to log ThankYou screen related events and params
     */
    fun trackHotelThankYouEvent(
        bookingId: String?,
        funnelSrc: Int,
        countryCode: String,
        currencyCode: String,
        roomInfoUIModel: RoomInfoUIModel,
        hotelDetailUiModel: HotelDetailUiModel,
        priceTrackingData: PriceTrackingData?,
        country: String,
        responseCheckinDate: String,
        responseCheckoutDate: String
    ) {
        val paramBundle = Bundle()
        paramBundle.put(FirebaseAnalytics.Param.TRANSACTION_ID, bookingId)
        paramBundle.put(FirebaseAnalytics.Param.ITEM_ID, hotelDetailUiModel.hotelId)
        paramBundle.put(FirebaseAnalytics.Param.DESTINATION, hotelDetailUiModel.cityName)
        try {
            val checkInDate = BookingDetails.SERVER_DATE_FORMATTER.parse(responseCheckinDate)
            val checkOutDate = BookingDetails.SERVER_DATE_FORMATTER.parse(responseCheckoutDate)
            paramBundle.put(FirebaseAnalytics.Param.START_DATE, HotelDateUtil.convertDateToString(checkInDate, LOGGING_DATE_FORMAT))
            paramBundle.put(FirebaseAnalytics.Param.END_DATE, HotelDateUtil.convertDateToString(checkOutDate, LOGGING_DATE_FORMAT))
        }catch (e:Exception){
            LogUtils.error(lob = CommonConstants.LOB_HOTEL, tag = TAG, severity = Severity.CRITICAL, message = "Firebase error while parsing dates", cause =  e)
        }

        paramBundle.put(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, roomInfoUIModel.totalGuestCount)
        paramBundle.put(FirebaseAnalytics.Param.NUMBER_OF_NIGHTS, hotelDetailUiModel.nights)
        paramBundle.put(DESTINATION_COUNTRY, country)
        priceTrackingData?.let {
            paramBundle.put(FirebaseAnalytics.Param.TAX, it.taxAmount.toDoubleOrNull() ?: 0.0f)
            paramBundle.put(EVENT_PARAM_DISCOUNT, it.discountAmount.toDoubleOrNull() ?: 0.0f)
            paramBundle.put(FirebaseAnalytics.Param.VALUE, it.totalAmount.toDoubleOrNull() ?: 0.0f)
        }
        paramBundle.put(FirebaseAnalytics.Param.CURRENCY, currencyCode)
        fillInCommonParams(paramBundle)
        getThankyouPageEventNames(countryCode, funnelSrc).forEach {
                logEvent(
                    eventName = it,
                    paramBundle = paramBundle
                )
        }
        LogUtils.error(lob = CommonConstants.LOB_HOTEL, tag = TAG, severity = Severity.CRITICAL, message = "Firebase event sent successfully...$bookingId")

    }


    // ============================= NON Thankyou Pages specific FUNCTIONS =========================


    /**
     * method to get Event name for the given PAGE NAME depending upon the countryCode and the funnelSource
     */
    private fun getEventName(pageName: String, countryCode: String, funnelSrc: Int): String {
        val prefix = if (HotelUtil.isDom(countryCode)) {
            when (funnelSrc) {
                HotelFunnel.HOTEL.funnelValue -> {
                    "DH_"
                }
                HotelFunnel.SHORT_STAYS.funnelValue -> {
                    "DSC_"
                }
                else -> {
                    "DAA_"
                }
            }
        } else {
            if (funnelSrc == HotelFunnel.HOTEL.funnelValue) "IH_" else "IAA_"
        }
        return "$prefix$pageName${getEventNamePostFix()}"
    }

    /**
     * method to get event name postfixes (for pages other than thankyou)
     * based on days spent value since app install day
     */
    private fun getEventNamePostFix(): Int {
        return when (CoreUtil.getDifferenceBetweenAppInstallAndCurrentDate().toInt()) {
            0 -> 0
            in 1..6 -> 7
            in 7..29 -> 30
            else -> 31
        }
    }

    /**
     * method to create and return event parameter bundle
     */
    private fun getEventParamBundle(userSearchData: UserSearchData): Bundle {
        val checkInDate = DateUtil.convertStringToDate(userSearchData.checkInDate, DATE_FORMATTER)
        val checkOutDate = DateUtil.convertStringToDate(userSearchData.checkOutDate, DATE_FORMATTER)
        val paramBundle = Bundle()
        paramBundle.put(FirebaseAnalytics.Param.ITEM_ID, userSearchData.hotelId)
        paramBundle.put(FirebaseAnalytics.Param.DESTINATION, userSearchData.locationName)
        paramBundle.put(FirebaseAnalytics.Param.START_DATE, HotelDateUtil.convertDateToString(checkInDate, LOGGING_DATE_FORMAT))
        paramBundle.put(FirebaseAnalytics.Param.END_DATE, HotelDateUtil.convertDateToString(checkOutDate, LOGGING_DATE_FORMAT))
        paramBundle.put(FirebaseAnalytics.Param.NUMBER_OF_PASSENGERS, getPAX(userSearchData.occupancyData))
        getLOS(checkOutDate, checkInDate)?.let {
            paramBundle.put(FirebaseAnalytics.Param.NUMBER_OF_NIGHTS, it)
        }
        paramBundle.put(Analytics_COUNTRY_NAME, userSearchData.country)
        fillInCommonParams(paramBundle)
        return paramBundle
    }

    /**
     * method to get total guest count (includes both adults and children count)
     */
    private fun getPAX(occupancyData: OccupancyData): Int {
        return occupancyData.adultCount + occupancyData.childAges.size
    }

    /**
     * method to get length of stay
     */
    private fun getLOS(checkInDate: Date, checkOutDate: Date): Int? {
        return DateUtil.getDaysDiffBetweenTwoDate(checkInDate, checkOutDate)
    }


    // ============================= Thankyou Page specific FUNCTIONS ==============================


    /**
     * method to get Event name for Thankyou Page depending upon the countryCode and the funnelSource
     */
    @VisibleForTesting
    fun getThankyouPageEventNames(countryCode: String, funnelSrc: Int): List<String> {
        val eventNamePrefix = getThankyouPageEventNamePrefix(countryCode, funnelSrc)
        return getThankyouPageEventNamePostFixes().map {
            "$eventNamePrefix$PAGE_THANK_YOU$it"
        }
    }

    private fun getThankyouPageEventNamePrefix(countryCode: String, funnelSrc: Int): String{
        var prefix = if (HotelUtil.isDom(countryCode)) "D" else "I"

        prefix += when (funnelSrc) {
            HotelFunnel.HOMESTAY.funnelValue -> "AA_"
            HotelFunnel.DAYUSE.funnelValue -> "DU_"
            HotelFunnel.GROUP_BOOKING.funnelValue -> "GB_"
            HotelFunnel.SHORT_STAYS.funnelValue -> {
                FB_TRACKING_KEY_STAYCATION
            }
            else -> "H_"
        }
        return prefix
    }

    /**
     * method to get event name postfixes (for thankyou page)
     * based on days spent value since app install day
     */
    private fun getThankyouPageEventNamePostFixes(): List<Int> {
        return when(CoreUtil.getDifferenceBetweenAppInstallAndCurrentDate().toInt()){
            0 -> listOf(0,7,30)
            in 1..7 -> listOf(7,30)
            in 8..30 -> listOf(30)
            else -> listOf(31)
        }
    }


    // ============================= COMMON FUNCTIONS ==============================================


    /**
     * method to fill in common parameter to [Bundle]
     */
    private fun fillInCommonParams(paramBundle: Bundle) {
        paramBundle.put(EVENT_PARAM_REGION, funnelRegion())
        paramBundle.put(EVENT_PARAM_DAYS_SPENT, getEventNamePostFix())
    }

    /**
     * method to get funnel region
     */
    private fun funnelRegion() = if (LoginUtils.getPreferredRegion().isIndiaRegion()) VALUE_REGION_INDIA else LoginUtils.getPreferredRegion().code

    /**
     * method to log events along with the event Param on Firebase and Facebook
     */
    private fun logEvent(eventName: String, paramBundle: Bundle) {
        FirebaseEventsTracking.logEventOnFirebase(eventName, paramBundle)
        FacebookEventsTracking.logEventOnFacebook(eventName, paramBundle)
    }
}