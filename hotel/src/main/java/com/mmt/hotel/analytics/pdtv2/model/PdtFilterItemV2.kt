package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.analytics.pdt.model.PdtFilter
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants.SOURCE_AUTOSUGGEST
import com.mmt.hotel.common.model.response.TagSelectionForListingV2
import com.mmt.hotel.filterV2.model.response.FilterV2
import com.mmt.hotel.old.model.hotelListingRequest.advancedfilters.FilterConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class PdtFilterItemV2(
    val range: Range? = null,
    val group: String,
    val value: String?=null,
    @SerializedName("range_filter")
    val rangeFilter: Boolean = false,
    val category: String?,
    val source: String?
) : Parcelable {
    companion object{

        private const val SELECT_ROOM_FILTER_GROUP = "select_room"

        fun getPdtFilterItem(filter: FilterV2): PdtFilterItemV2 {
            val filterRange = filter.filterRange
            return if (filterRange != null) {
                val range = Range(start = filterRange.minValue, end = filterRange.maxValue)
                PdtFilterItemV2(
                    group = filter.filterGroup,
                    rangeFilter = true,
                    range = range,
                    category = filter.filterUiCategory,
                    source = filter.source
                )
            } else if (filter.filterGroup == FilterConstants.LOCATION_FILTER_GROUP) {
                PdtFilterItemV2(
                    group = filter.filterGroup,
                    value = filter.filterValue,
                    category = filter.filterUiCategory,
                    source = filter.source
                )
            } else {
                PdtFilterItemV2(
                    group = filter.filterGroup,
                    value = filter.filterValue,
                    category = filter.filterUiCategory,
                    source = filter.source
                )
            }
        }

        fun getPdtFilterItem(tag: TagSelectionForListingV2): PdtFilterItemV2 {
            return PdtFilterItemV2(
                group = FilterConstants.LOCATION_FILTER_GROUP, value = tag.tagAreaId,
                category = tag.filterUiCategory, source = if(tag.isPrimary == true) SOURCE_AUTOSUGGEST else tag.trackSource
            )
        }

        fun getPdtFilterItem(filterCode: String): PdtFilterItemV2 {
            return PdtFilterItemV2(
                group = SELECT_ROOM_FILTER_GROUP, value = filterCode,
                category = null, source = null
            )
        }
     }
}

@Parcelize
data class Range(val start: Int, val end: Int) : Parcelable