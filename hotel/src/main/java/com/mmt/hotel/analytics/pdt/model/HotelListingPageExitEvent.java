package com.mmt.hotel.analytics.pdt.model;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.mmt.auth.login.model.Employee;
import com.mmt.hotel.analytics.pdt.HotelPdtKeys;
import com.mmt.hotel.analytics.pdt.events.HotelPageExitEvent;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.ArrayList;
import java.util.Objects;

public class HotelListingPageExitEvent extends HotelPageExitEvent {
    private SorterFilterEvent sorterFilterEvent;
    private TravelerDetail travelerDetail;

    private boolean searchClicked = false;

    private boolean searchContextChanged = false;

    private boolean isMapViewClicked = false;

    private String listingSearchText;

    @Nullable
    private ArrayList<ListingPropertyCountModel> listingPropertyCount;

    private boolean easySelectionBSDismissed;

    public HotelListingPageExitEvent(@NonNull String pageName,
                                     int eventType,
                                     long sessionStartTime,
                                     @NonNull String omnitureName,
                                     @NonNull String parentScreenName, String userAdId, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(pageName, eventType, sessionStartTime, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
        sorterFilterEvent = new SorterFilterEvent("", pageName, eventType, omnitureName,
                parentScreenName, false);
        sorterFilterEvent.setUserAdId(userAdId);
        travelerDetail = new TravelerDetail();
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        event.getEventParam().putAll(sorterFilterEvent.createPDTEvent().getEventParam());
        event.getEventParam().putAll(travelerDetail.getEventParams());
        event.getEventParam().put(HotelPdtKeys.SEARCH_CLICKED,searchClicked);
        event.getEventParam().put(HotelPdtKeys.LISTING_SEARCH_CONTEXT_MODIFIED,searchContextChanged);
        event.getEventParam().put(HotelPdtKeys.MAP_VIEW_CLICKED,isMapViewClicked);
        event.getEventParam().put(HotelPdtKeys.LISTING_SEARCH_TEXT,listingSearchText);
        event.getEventParam().put(HotelPdtKeys.LISTING_PROPERTY_COUNT, listingPropertyCount);
        event.getEventParam().put(HotelPdtKeys.EASY_SELECTION_BS_DISMISSED, easySelectionBSDismissed);
        return event;
    }

    public void fillTravelerDetail(Employee traveller) {
        String[] name = traveller.getName().split(" ");
        travelerDetail.setTravelerFName(name[0]);
        if(name.length > 1) {
            travelerDetail.setTravelerLName(name[name.length-1]);
        }
        travelerDetail.setTravelerTitle(traveller.getTitle());
        travelerDetail.setTravelerMobileNo(traveller.getPhoneNumber());
        travelerDetail.setTravelerEmailId(traveller.getBusinessEmailId());
    }

    public SorterFilterEvent getSorterFilterEvent() {
        return this.sorterFilterEvent;
    }

    public void setSorterFilterEvent(SorterFilterEvent sorterFilterEvent) {
        this.sorterFilterEvent = sorterFilterEvent;
    }

    public TravelerDetail getTravelerDetail() {
        return this.travelerDetail;
    }

    public String toString() {
        return "HotelListingPageExitEvent(sorterFilterEvent=" + this.sorterFilterEvent + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelListingPageExitEvent))
            return false;
        final HotelListingPageExitEvent other = (HotelListingPageExitEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$sorterFilterEvent = this.getSorterFilterEvent();
        final Object other$sorterFilterEvent = other.getSorterFilterEvent();
        if (!Objects.equals(this$sorterFilterEvent, other$sorterFilterEvent))
            return false;
        final Object this$travelerDetail = this.getTravelerDetail();
        final Object other$travelerDetail = other.getTravelerDetail();
        if (!Objects.equals(this$travelerDetail, other$travelerDetail))
            return false;
        final Object this$listingPropertyCount = this.getListingPropertyCount();
        final Object other$listingPropertyCount = other.getListingPropertyCount();
        if (!Objects.equals(this$listingPropertyCount, other$listingPropertyCount))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelListingPageExitEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $sorterFilterEvent = this.getSorterFilterEvent();
        result = result * PRIME + ($sorterFilterEvent == null ? 43 : $sorterFilterEvent.hashCode());
        final Object $travelerDetail = this.getTravelerDetail();
        result = result * PRIME + ($travelerDetail == null ? 43 : $travelerDetail.hashCode());
        final Object $listingPropertyCount = this.getListingPropertyCount();
        result = result * PRIME + ($listingPropertyCount == null ? 43 : $listingPropertyCount.hashCode());
        return result;
    }

    public boolean isSearchClicked() {
        return searchClicked;
    }

    public void setSearchClicked(boolean searchClicked) {
        this.searchClicked = searchClicked;
    }

    public boolean isSearchContextChanged() {
        return searchContextChanged;
    }

    public void setSearchContextChanged(boolean searchContextChanged) {
        this.searchContextChanged = searchContextChanged;
    }

    public boolean isMapViewClicked() {
        return isMapViewClicked;
    }

    public void setMapViewClicked(boolean mapViewClicked) {
        isMapViewClicked = mapViewClicked;
    }

    public String getListingSearchText() {
        return listingSearchText;
    }

    public void setListingSearchText(String listingSearchText) {
        this.listingSearchText = listingSearchText;
    }

    @Nullable
    public ArrayList<ListingPropertyCountModel> getListingPropertyCount() {
        return listingPropertyCount;
    }

    public void setListingPropertyCount(@Nullable ArrayList<ListingPropertyCountModel> listingPropertyCount) {
        this.listingPropertyCount = listingPropertyCount;
    }

    public boolean isEasySelectionBSDismissed() {
        return easySelectionBSDismissed;
    }

    public void setEasySelectionBSDismissed(boolean easySelectionBSDismissed) {
        this.easySelectionBSDismissed = easySelectionBSDismissed;
    }
}
