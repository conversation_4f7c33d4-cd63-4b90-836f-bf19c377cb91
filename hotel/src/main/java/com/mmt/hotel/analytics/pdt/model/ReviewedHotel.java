package com.mmt.hotel.analytics.pdt.model;

import com.google.gson.annotations.SerializedName;
import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.hotel.analytics.pdt.HotelPdtKeys;

import java.util.List;
import java.util.Objects;

public class ReviewedHotel {
    @SerializedName(PDTAnalyticsKeys.PD_HTL_ID)
    private String hotelId;
    @SerializedName(PDTAnalyticsKeys.PD_DISP_SELL_AMT)
    private float originalPrice;
    @SerializedName(PDTAnalyticsKeys.PD_DISP_SELL_AMT_DIS)
    private float discountedPrice;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RNK_DISP)
    private int positionInList;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_LISTING_PERSONALISATION_SECTION)
    private String sectionName;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_SOLD_OUT)
    private boolean isHotelSoldOut;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_SHRTLSTD)
    private boolean isHotelShortListed;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_STR)
    private int hotelStartRating;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_PAH)
    private boolean isPAHAvailable;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_IMG_DISP)
    private List<String> viewedImages;
    @SerializedName(HotelPdtKeys.PD_HTL_IMG_SENT)
    private List<HotelImage> hotelImages;
    @SerializedName(HotelPdtKeys.IMG_SCROLL_IDX)
    private int imageScrolledIndex;
    @SerializedName(HotelPdtKeys.IMG_CLICK_IDX)
    private int clickedHotelImageIndex = -1;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_AD_REQ_ID)
    private String pdAdRequestID;
    @SerializedName(PDTAnalyticsKeys.PD_AD_CAMPAIGN_ID)
    private String pdAdCampaignId;
    @SerializedName(PDTAnalyticsKeys.PD_AD_ADV_ID)
    private String pdAdvertiserID;
    @SerializedName(PDTAnalyticsKeys.PD_AD_ID)
    private String pdAdID;
    @SerializedName(PDTAnalyticsKeys.USR_AD_ID)
    private String userAdID;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_FC)
    private boolean isFreeCancellationAvailable;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_CAT_LS)
    private List<String> hotelCategoryList;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_MMT_ASSRD)
    private boolean isMMTAssured;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_LOC_MTCH_PRCNT)
    private float matchMakerScore;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RT_DISP)
    private float usrRating;
    @SerializedName(PDTAnalyticsKeys.SER_HTL_PERS_VWD_LS)
    private List<Persuasion> persuasionList;
    @SerializedName(PDTAnalyticsKeys.CPN_CODE)
    private String cpnCode;
    @SerializedName(PDTAnalyticsKeys.PRC_CPN_AMT)
    private float cpnAmt;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_BNPL)
    private String bnpnPersuastion;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_PERSUASION_COUNT)
    private String persuasionCount;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_PERSUASION_LIST)
    private List<List<String>> hotelPersuasionsList;

    public ReviewedHotel() {
    }

    public String getHotelId() {
        return this.hotelId;
    }

    public float getOriginalPrice() {
        return this.originalPrice;
    }

    public float getDiscountedPrice() {
        return this.discountedPrice;
    }

    public String getBnpnPersuastion() {
        return bnpnPersuastion;
    }

    public void setBnpnPersuastion(String bnpnPersuastion) {
        this.bnpnPersuastion = bnpnPersuastion;
    }

    public List<HotelImage> getHotelImages() {
        return hotelImages;
    }

    public void setHotelImages(List<HotelImage> hotelImages) {
        this.hotelImages = hotelImages;
    }

    public int getImageScrolledIndex() {
        return imageScrolledIndex;
    }

    public void setImageScrolledIndex(int imageScrolledIndex) {
        this.imageScrolledIndex = imageScrolledIndex;
    }

    public int getClickedHotelImageIndex() {
        return clickedHotelImageIndex;
    }

    public void setClickedHotelImageIndex(int clickedHotelImageIndex) {
        this.clickedHotelImageIndex = clickedHotelImageIndex;
    }

    public int getPositionInList() {
        return this.positionInList;
    }

    public String getSectionName() {
        return this.sectionName;
    }

    public boolean isHotelSoldOut() {
        return this.isHotelSoldOut;
    }

    public boolean isHotelShortListed() {
        return this.isHotelShortListed;
    }

    public int getHotelStartRating() {
        return this.hotelStartRating;
    }

    public boolean isPAHAvailable() {
        return this.isPAHAvailable;
    }

    public List<String> getViewedImages() {
        return this.viewedImages;
    }

    public boolean isFreeCancellationAvailable() {
        return this.isFreeCancellationAvailable;
    }

    public List<String> getHotelCategoryList() {
        return this.hotelCategoryList;
    }

    public boolean isMMTAssured() {
        return this.isMMTAssured;
    }

    public float getMatchMakerScore() {
        return this.matchMakerScore;
    }

    public float getUsrRating() {
        return this.usrRating;
    }

    public List<Persuasion> getPersuasionList() {
        return this.persuasionList;
    }

    public String getCpnCode() {
        return this.cpnCode;
    }

    public float getCpnAmt() {
        return this.cpnAmt;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    public void setOriginalPrice(float originalPrice) {
        this.originalPrice = originalPrice;
    }

    public void setDiscountedPrice(float discountedPrice) {
        this.discountedPrice = discountedPrice;
    }

    public void setPositionInList(int positionInList) {
        this.positionInList = positionInList;
    }

    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }

    public void setHotelSoldOut(boolean isHotelSoldOut) {
        this.isHotelSoldOut = isHotelSoldOut;
    }

    public void setHotelStartRating(int hotelStartRating) {
        this.hotelStartRating = hotelStartRating;
    }

    public void setPAHAvailable(boolean isPAHAvailable) {
        this.isPAHAvailable = isPAHAvailable;
    }

    public void setViewedImages(List<String> viewedImages) {
        this.viewedImages = viewedImages;
    }

    public String getPdAdRequestID() {
        return pdAdRequestID;
    }

    public void setPdAdRequestID(String pdAdRequestID) {
        this.pdAdRequestID = pdAdRequestID;
    }

    public String getPdAdCampaignId() {
        return pdAdCampaignId;
    }

    public void setPdAdCampaignId(String pdAdCampaignId) {
        this.pdAdCampaignId = pdAdCampaignId;
    }

    public String getPdAdvertiserID() {
        return pdAdvertiserID;
    }

    public void setPdAdvertiserID(String pdAdvertiserID) {
        this.pdAdvertiserID = pdAdvertiserID;
    }

    public String getPdAdID() {
        return pdAdID;
    }

    public void setPdAdID(String pdAdID) {
        this.pdAdID = pdAdID;
    }

    public String getUserAdID() {
        return userAdID;
    }

    public void setUserAdID(String userAdID) {
        this.userAdID = userAdID;
    }

    public void setFreeCancellationAvailable(boolean isFreeCancellationAvailable) {
        this.isFreeCancellationAvailable = isFreeCancellationAvailable;
    }

    public void setHotelCategoryList(List<String> hotelCategoryList) {
        this.hotelCategoryList = hotelCategoryList;
    }

    public void setMMTAssured(boolean isMMTAssured) {
        this.isMMTAssured = isMMTAssured;
    }

    public void setMatchMakerScore(float matchMakerScore) {
        this.matchMakerScore = matchMakerScore;
    }

    public void setUsrRating(float usrRating) {
        this.usrRating = usrRating;
    }

    public void setPersuasionList(List<Persuasion> persuasionList) {
        this.persuasionList = persuasionList;
    }

    public void setCpnCode(String cpnCode) {
        this.cpnCode = cpnCode;
    }

    public void setCpnAmt(float cpnAmt) {
        this.cpnAmt = cpnAmt;
    }

    public String getPersuasionCount() {
        return persuasionCount;
    }

    public void setPersuasionCount(String persuasionCount) {
        this.persuasionCount = persuasionCount;
    }

    public List<List<String>> getHotelPersuasionsList() {
        return hotelPersuasionsList;
    }

    public void setHotelPersuasionsList(List<List<String>> hotelPersuasionsList) {
        this.hotelPersuasionsList = hotelPersuasionsList;
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof ReviewedHotel)) return false;
        final ReviewedHotel other = (ReviewedHotel) o;
        if (!other.canEqual(this)) return false;
        final Object this$hotelId = this.hotelId;
        final Object other$hotelId = other.hotelId;
        if (this$hotelId == null ? other$hotelId != null : !this$hotelId.equals(other$hotelId))
            return false;
        if (Float.compare(this.originalPrice, other.originalPrice) != 0) return false;
        if (Float.compare(this.discountedPrice, other.discountedPrice) != 0) return false;
        if (this.positionInList != other.positionInList) return false;
        final Object this$sectionName = this.sectionName;
        final Object other$sectionName = other.sectionName;
        if (this$sectionName == null ? other$sectionName != null : !this$sectionName.equals(other$sectionName))
            return false;
        if (this.isHotelSoldOut != other.isHotelSoldOut) return false;
        if (this.isHotelShortListed != other.isHotelShortListed) return false;
        if (this.hotelStartRating != other.hotelStartRating) return false;
        if (this.isPAHAvailable != other.isPAHAvailable) return false;
        final Object this$viewedImages = this.viewedImages;
        final Object other$viewedImages = other.viewedImages;
        if (this$viewedImages == null ? other$viewedImages != null : !this$viewedImages.equals(other$viewedImages))
            return false;
        if (this.isFreeCancellationAvailable != other.isFreeCancellationAvailable) return false;
        if(!Objects.equals(this.bnpnPersuastion,other.bnpnPersuastion)) return false;
        if(!Objects.equals(this.pdAdRequestID,other.pdAdRequestID)) return false;
        if(!Objects.equals(this.pdAdCampaignId,other.pdAdCampaignId)) return false;
        if(!Objects.equals(this.pdAdvertiserID,other.pdAdvertiserID)) return false;
        if(!Objects.equals(this.pdAdID,other.pdAdID)) return false;
        final Object this$hotelCategoryList = this.hotelCategoryList;
        final Object other$hotelCategoryList = other.hotelCategoryList;
        if (this$hotelCategoryList == null ? other$hotelCategoryList != null : !this$hotelCategoryList.equals(other$hotelCategoryList))
            return false;
        if (this.isMMTAssured != other.isMMTAssured) return false;
        if (Float.compare(this.matchMakerScore, other.matchMakerScore) != 0) return false;
        if (Float.compare(this.usrRating, other.usrRating) != 0) return false;
        final Object this$persuasionList = this.persuasionList;
        final Object other$persuasionList = other.persuasionList;
        if (this$persuasionList == null ? other$persuasionList != null : !this$persuasionList.equals(other$persuasionList))
            return false;
        final Object this$cpnCode = this.cpnCode;
        final Object other$cpnCode = other.cpnCode;
        if (this$cpnCode == null ? other$cpnCode != null : !this$cpnCode.equals(other$cpnCode))
            return false;
        if (Float.compare(this.cpnAmt, other.cpnAmt) != 0) return false;
        if (!Objects.equals(this.persuasionCount, other.persuasionCount)) return false;
        if (!Objects.equals(this.hotelPersuasionsList, other.hotelPersuasionsList)) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof ReviewedHotel;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        final Object $hotelId = this.hotelId;
        result = result * PRIME + ($hotelId == null ? 43 : $hotelId.hashCode());
        result = result * PRIME + Float.floatToIntBits(this.originalPrice);
        result = result * PRIME + Float.floatToIntBits(this.discountedPrice);
        result = result * PRIME + this.positionInList;
        final Object $sectionName = this.sectionName;
        result = result * PRIME + ($sectionName == null ? 43 : $sectionName.hashCode());
        final Object $apdAdRequestID = this.pdAdRequestID;
        result = result * PRIME + ($apdAdRequestID == null ? 43 : $apdAdRequestID.hashCode());
        final Object $pdAdvertiserID = this.pdAdvertiserID;
        result = result * PRIME + ($pdAdvertiserID == null ? 43 : $pdAdvertiserID.hashCode());
        final Object $pdAdCampaignId = this.pdAdCampaignId;
        result = result * PRIME + ($pdAdCampaignId == null ? 43 : $pdAdCampaignId.hashCode());
        final Object $pdAdID = this.pdAdID;
        result = result * PRIME + ($pdAdID == null ? 43 : $pdAdID.hashCode());
        result = result * PRIME + (this.isHotelSoldOut ? 79 : 97);
        result = result * PRIME + (this.isHotelShortListed ? 79 : 97);
        result = result * PRIME + this.hotelStartRating;
        result = result * PRIME + (this.isPAHAvailable ? 79 : 97);
        final Object $viewedImages = this.viewedImages;
        result = result * PRIME + ($viewedImages == null ? 43 : $viewedImages.hashCode());
        result = result * PRIME + (this.isFreeCancellationAvailable ? 79 : 97);
        result = result * PRIME + (this.bnpnPersuastion == null ? 43 : this.bnpnPersuastion.hashCode());
        final Object $hotelCategoryList = this.hotelCategoryList;
        result = result * PRIME + ($hotelCategoryList == null ? 43 : $hotelCategoryList.hashCode());
        result = result * PRIME + (this.isMMTAssured ? 79 : 97);
        result = result * PRIME + Float.floatToIntBits(this.matchMakerScore);
        result = result * PRIME + Float.floatToIntBits(this.usrRating);
        final Object $persuasionList = this.persuasionList;
        result = result * PRIME + ($persuasionList == null ? 43 : $persuasionList.hashCode());
        final Object $cpnCode = this.cpnCode;
        result = result * PRIME + ($cpnCode == null ? 43 : $cpnCode.hashCode());
        result = result * PRIME + Float.floatToIntBits(this.cpnAmt);
        result = result * PRIME + (this.persuasionCount == null ? 43 : this.persuasionCount.hashCode());
        result = result * PRIME + (this.hotelPersuasionsList == null ? 43 : this.hotelPersuasionsList.hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "ReviewedHotel{" +
                "hotelId='" + hotelId + '\'' +
                ", originalPrice=" + originalPrice +
                ", discountedPrice=" + discountedPrice +
                ", positionInList=" + positionInList +
                ", sectionName='" + sectionName + '\'' +
                ", isHotelSoldOut=" + isHotelSoldOut +
                ", isHotelShortListed=" + isHotelShortListed +
                ", hotelStartRating=" + hotelStartRating +
                ", isPAHAvailable=" + isPAHAvailable +
                ", viewedImages=" + viewedImages +
                ", hotelImages=" + hotelImages +
                ", imageScrolledIndex=" + imageScrolledIndex +
                ", clickedHotelImageIndex=" + clickedHotelImageIndex +
                ", pdAdRequestID='" + pdAdRequestID + '\'' +
                ", pdAdCampaignId='" + pdAdCampaignId + '\'' +
                ", pdAdvertiserID='" + pdAdvertiserID + '\'' +
                ", pdAdID='" + pdAdID + '\'' +
                ", userAdID='" + userAdID + '\'' +
                ", isFreeCancellationAvailable=" + isFreeCancellationAvailable +
                ", hotelCategoryList=" + hotelCategoryList +
                ", isMMTAssured=" + isMMTAssured +
                ", matchMakerScore=" + matchMakerScore +
                ", usrRating=" + usrRating +
                ", persuasionList=" + persuasionList +
                ", cpnCode='" + cpnCode + '\'' +
                ", cpnAmt=" + cpnAmt +
                ", bnpnPersuastion='" + bnpnPersuastion + '\'' +
                ", persuasionCount='" + persuasionCount + '\'' +
                ", hotelPersuasionsList=" + hotelPersuasionsList +
                '}';
    }

    public static class Persuasion {
        @SerializedName(PDTAnalyticsKeys.SER_PERS_ID)
        String persuasionId;
        @SerializedName(PDTAnalyticsKeys.SER_PERS_TITLE)
        String persuasionText;

        public Persuasion() {
        }

        public String getPersuasionId() {
            return this.persuasionId;
        }

        public String getPersuasionText() {
            return this.persuasionText;
        }

        public void setPersuasionId(String persuasionId) {
            this.persuasionId = persuasionId;
        }

        public void setPersuasionText(String persuasionText) {
            this.persuasionText = persuasionText;
        }

        public boolean equals(final Object o) {
            if (o == this) return true;
            if (!(o instanceof Persuasion))
                return false;
            final Persuasion other = (Persuasion) o;
            if (!other.canEqual(this)) return false;
            final Object this$persuasionId = this.persuasionId;
            final Object other$persuasionId = other.persuasionId;
            if (this$persuasionId == null ? other$persuasionId != null : !this$persuasionId.equals(other$persuasionId))
                return false;
            final Object this$persuasionText = this.persuasionText;
            final Object other$persuasionText = other.persuasionText;
            if (this$persuasionText == null ? other$persuasionText != null : !this$persuasionText.equals(other$persuasionText))
                return false;
            return true;
        }

        protected boolean canEqual(final Object other) {
            return other instanceof Persuasion;
        }

        public int hashCode() {
            final int PRIME = 59;
            int result = 1;
            final Object $persuasionId = this.persuasionId;
            result = result * PRIME + ($persuasionId == null ? 43 : $persuasionId.hashCode());
            final Object $persuasionText = this.persuasionText;
            result = result * PRIME + ($persuasionText == null ? 43 : $persuasionText.hashCode());
            return result;
        }

        public String toString() {
            return "ReviewedHotel.Persuasion(persuasionId=" + this.persuasionId + ", persuasionText=" + this.persuasionText + ")";
        }
    }
}
