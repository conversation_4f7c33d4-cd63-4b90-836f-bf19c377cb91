package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.hotel.analytics.pdt.model.RewardsAndRatingEventModel;
import com.pdt.pdtDataLogging.events.model.Event;

public class HotelUserReviewEntryEvent extends HotelPageEntryEvent {
    private float hotelRating;
    private float hotelFacilitiesRating;
    private float cleanlinessRating;
    private float hotelLocationRating;
    private RewardsAndRatingEventModel rewardsAndRatingEventModel;

    public HotelUserReviewEntryEvent(@NonNull String pageName, int eventType, long sessionStartTime,
                                     @NonNull String omnitureName,
                                     @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(pageName, eventType, sessionStartTime, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (hotelRating != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RT_DISP, hotelRating);
        }
        if (hotelFacilitiesRating != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_FCLTS_RT, hotelFacilitiesRating);
        }
        if (cleanlinessRating != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_CLNLNSS_RT, cleanlinessRating);
        }
        if (hotelLocationRating != 0) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_LOC_RT, hotelLocationRating);
        }
        if(rewardsAndRatingEventModel != null) {
            event.getEventParam().put(PDTAnalyticsKeys.REWARDS_AND_RATINGS_PAGE_DETAILS, rewardsAndRatingEventModel);
        }

        return event;
    }

    public float getHotelRating() {
        return this.hotelRating;
    }

    public float getHotelFacilitiesRating() {
        return this.hotelFacilitiesRating;
    }

    public float getCleanlinessRating() {
        return this.cleanlinessRating;
    }

    public float getHotelLocationRating() {
        return this.hotelLocationRating;
    }

    public void setHotelRating(float hotelRating) {
        this.hotelRating = hotelRating;
    }

    public void setHotelFacilitiesRating(float hotelFacilitiesRating) {
        this.hotelFacilitiesRating = hotelFacilitiesRating;
    }

    public void setRewardsAndRatingEventModel(RewardsAndRatingEventModel rewardsAndRatingEventModel) {
        this.rewardsAndRatingEventModel = rewardsAndRatingEventModel;
    }

    public void setCleanlinessRating(float cleanlinessRating) {
        this.cleanlinessRating = cleanlinessRating;
    }

    public void setHotelLocationRating(float hotelLocationRating) {
        this.hotelLocationRating = hotelLocationRating;
    }

    public String toString() {
        return "HotelUserReviewEntryEvent(hotelRating=" + this.hotelRating + ", hotelFacilitiesRating=" + this.hotelFacilitiesRating + ", cleanlinessRating=" + this.cleanlinessRating + ", hotelLocationRating=" + this.hotelLocationRating + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelUserReviewEntryEvent))
            return false;
        final HotelUserReviewEntryEvent other = (HotelUserReviewEntryEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        if (Float.compare(this.getHotelRating(), other.getHotelRating()) != 0) return false;
        if (Float.compare(this.getHotelFacilitiesRating(), other.getHotelFacilitiesRating()) != 0)
            return false;
        if (Float.compare(this.getCleanlinessRating(), other.getCleanlinessRating()) != 0)
            return false;
        if (Float.compare(this.getHotelLocationRating(), other.getHotelLocationRating()) != 0)
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelUserReviewEntryEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        result = result * PRIME + Float.floatToIntBits(this.getHotelRating());
        result = result * PRIME + Float.floatToIntBits(this.getHotelFacilitiesRating());
        result = result * PRIME + Float.floatToIntBits(this.getCleanlinessRating());
        result = result * PRIME + Float.floatToIntBits(this.getHotelLocationRating());
        return result;
    }
}
