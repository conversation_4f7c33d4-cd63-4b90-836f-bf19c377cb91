package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.listingV2.event.SectionOrientation
import com.mmt.hotel.listingV2.model.response.hotels.Hotel
import kotlinx.parcelize.Parcelize

@Parcelize
data class ProductItem(
    private var id: CharSequence,
    @SerializedName("is_whislisted")
    val wishlisted: Boolean? = null,
    val images: ProductImages? = null,
    @SerializedName("image_url")
    val imageUrl: CharSequence? = null,
    @SerializedName("section_type")
    val sectionType: CharSequence? = null,
    val position: Position? = null,
    @SerializedName("request_id")
    val requestId: CharSequence? = null,
    @SerializedName("price")
    val price:Price?=null,
    @SerializedName("catalog")
    val catalog:List<Catalog>?=null,
    @SerializedName("special_request")
    val specialRequestData:SpecialRequest?=null
) : Parcelable {
    constructor(
        hotel: Hotel,
        clickedIndex: Int? = null,
        position: Int? = null,
        @SectionOrientation orientation: String? = SectionOrientation.VERTICAL,
        catalog: List<Catalog>? = null
    ) :
            this(
                id = hotel.id,
                wishlisted = hotel.isWishListed,
                images = calculateImages(hotel.imageScrollIndex, clickedIndex),
                imageUrl = hotel.heroImage,
                sectionType = hotel.sectionName,
                requestId = hotel.requestId,
                position = calculatePosition(position, orientation),
                catalog = catalog
            )

    companion object {
        fun calculateImages(imageScrollIndex: Int?, clickedIndex: Int?): ProductImages? {
            if (imageScrollIndex == null && clickedIndex == null) {
                return null
            }
            return ProductImages(viewed_index = imageScrollIndex, clicked_index = clickedIndex)
        }

        fun calculatePosition(
            position: Int?,
            @SectionOrientation orientation: String? = SectionOrientation.VERTICAL
        ): Position? {
            return position?.let {
                if (orientation == SectionOrientation.HORIZONTAL) {
                    Position(h = position)
                } else {
                    Position(v = position)
                }
            }
        }
    }
}