package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.pdt.pdtDataLogging.events.model.Event;

public class HotelPhotoEntryEvent extends HotelPageEntryEvent {
    private HotelDetailVwdPhotosEvent photosEvent;
    public HotelPhotoEntryEvent(@NonNull String pageName, int eventType, long sessionStartTime,
                                @NonNull String omnitureName, @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(pageName, eventType, sessionStartTime, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
        photosEvent = new HotelDetailVwdPhotosEvent(PAGE_ENTRY, pageName, eventType, omnitureName, parentScreenName, false);
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        event.getEventParam().putAll(photosEvent.createPDTEvent().getEventParam());
        return event;
    }

    public HotelDetailVwdPhotosEvent getPhotosEvent() {
        return this.photosEvent;
    }

    public void setPhotosEvent(HotelDetailVwdPhotosEvent photosEvent) {
        this.photosEvent = photosEvent;
    }

    public String toString() {
        return "HotelPhotoEntryEvent(photosEvent=" + this.photosEvent + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelPhotoEntryEvent))
            return false;
        final HotelPhotoEntryEvent other = (HotelPhotoEntryEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$photosEvent = this.getPhotosEvent();
        final Object other$photosEvent = other.getPhotosEvent();
        if (this$photosEvent == null ? other$photosEvent != null : !this$photosEvent.equals(other$photosEvent))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelPhotoEntryEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $photosEvent = this.getPhotosEvent();
        result = result * PRIME + ($photosEvent == null ? 43 : $photosEvent.hashCode());
        return result;
    }
}
