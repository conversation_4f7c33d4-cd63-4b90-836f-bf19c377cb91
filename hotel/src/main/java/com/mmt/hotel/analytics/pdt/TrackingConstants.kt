package com.mmt.hotel.analytics.pdt

object TrackingConstants {
    const val HOTEL_LISTING = "Listing"
    const val HOTEL_DETAIL = "Detail"
    const val ROOM_SELECTION = "Hotel Room Selection"
    const val HOTEL_REVIEW_PAGE = "Review"
    const val HOTEL_ACCO_INTERSTITIAL = "AltAccoInterstitial"
    const val MMT_VALUE_STAYS_TAG = "valueStays"
    const val FUNNEL_NEARBY = "FNL_NEARBY"

    const val RTB = "RTB"
    const val ABSO = "ABSO"
    const val NOT_LISTED = "notlisted_open"

    const val FUNNEL_HOTEL_SELECT_ROOMS = "Room Selection"
    const val FUNNEL_HOTEL_LISTING = "Hotel Listing"

    const val TOPIC_ID_SELECT_ROOM = "1022"
    const val TOPIC_ID_LISTING = "1026"

    const val TEMPLATE_ID_SELECTROOM = "771"
    const val TEMPLATE_ID_LISTING = "774"
    const val TEMPLATE_ID_DETAILS = "777"

    const val TRAVELLER = "Traveller"

    const val TREELS_ENTRY_POINT_SHOWN = "treels_entrypoint_shown"


    const val SPONSORED_SHOWN = "Sponsored_shown"
    const val SPONSORED_CLICKED = "Sponsored_clicked"

    const val KEY_PROPERTY_TYPE = "KEY_PROPERTY_TYPE"
    const val KEY_LOCUS_TRACKING_DATA = "KEY_LOCUS_TRACKING_DATA"
    const val KEY_USER_SEARCH_DATA = "KEY_USER_SEARCH_DATA"
    const val KEY_TOTAL_PRICE_TO_PAY = "KEY_TOTAL_PRICE_TO_PAY"
    const val KEY_ORIGINAL_PRICE = "KEY_ORIGINAL_PRICE"
    const val KEY_STAR_RATING = "KEY_STAR_RATING"
    const val KEY_USER_RATING = "KEY_USER_RATING"
    const val KEY_ROOM_CRITERIA = "KEY_ROOM_CRITERIA"
    const val KEY_IS_HOTEL_SHORTLISTED = "KEY_IS_HOTEL_SHORTLISTED"
    const val KEY_HEADER_IMAGE = "KEY_HEADER_IMAGE"
    const val KEY_CORRELATION = "KEY_CORRELATION"

    const val UPSELL_SUCCESS = "upsell_success"
    const val BNPL_BOOKING_COMPLETED = "bnpl_booking_completed"
    const val UPSELL_SHOWN = "upsell_shown"
    const val UPSELL_INTERACT = "upsell_interact"
    const val UPSELL = "upsell"

    const val KEY_DAY_USE_CHECK_IN_TIME = "day_use_checkin_time"
    const val KEY_DAY_USE_SLOT_DURATION = "day_use_slot_duration"
    const val NAVIGATION_FORWARD = "forward"
    const val NAVIGATION_BACKWARD = "backward"

    const val NEED_HELP_VIEW_SHOWN = "hvc_shown"
    const val NEED_HELP_VIEW_OPENED = "hvc_opened"
    const val NEED_HELP_VIEW_FORM = "hvc_form"
    const val NEED_HELP_VIEW_DIRECTCALL = "hvc_directcall"

    const val LOVED_BY_INDIANS = "LovedbyIndians"
    const val LOVED_BY_DEVOTEES = "LovedbyDevotees"
    const val LOVED_BY_INDIANS_CLICKED = "LovedbyIndians_clicked"

    const val SEARCH_REQUEST_MODIFIED = "search_request_modified"
    const val DEFAULT_INFO_NOT_CHANGED = "Default_Info_Not_Changed"

    const val LANDING_BENEFIT_CARD_CLICKED = "XSellCard_Landingpage_Clicked"
    const val LISTING_BENEFIT_CARD_CLICKED = "XSellCard_Listingingpage_Clicked"
    const val REVIEW_PAGE_BLOCKER_SHOWN = "Review_page_blocker_shown"

    const val FIRST_5_OFFER = "1st5offer"

    const val EXTERNAL_INFO_ICON_CLICKED = "%s_more_info_clicked"
    const val OTHER_PROPS_BY_HOST_LOADED = "other_props_by_host_loaded"
    const val OTHER_PROPS_BY_HOST_CLICKED = "other_props_by_host_clicked"
    const val HOST_IMPRESSIONS_LOADED = "host_impressions_loaded"
    const val OTHER_PROPS_BY_HOST_SECTION_LOADED = "other_props_by_host_section_loaded"
}

object BookingReviewTrackingConstants {
    const val ALT_ACCO_RTB_REVIEW_INFO_ICON = "Alt_Acco_RTB_Review_info_icon"
    const val ALT_ACCO_RTB_MULTIBOOK_WARNING = "Alt_Acco_RTB_MultiBook_warning"
    const val ALT_ACCO_RTB_MULTIBOOK_BLOCK = "Alt_Acco_RTB_MultiBook_block"
    const val ALT_ACCO_RTB_REVIEW_TEXTBOX = "Alt_Acco_RTB_Review_textBox"
    const val ALT_ACCO_RTB_PAYMENT_ZPN = "Alt_Acco_RTB_Payment_ZPN"
    const val ALT_ACCO_RTB_PAYMENT_RESERVECC = "Alt_Acco_RTB_Payment_ReserveCC"
    const val ALT_ACCO_RTB_PAYMENT_COMPLETE = "Alt_Acco_RTB_Payment_complete"

    const val SCROLL_BUTTON_SHOWN = "scroll_down_cta_shown"
    const val SCROLL_BUTTON_CLICKED = "scroll_down_cta_clicked"
    const val TRIP_MONEY_BNPL_CTA_CLIKCED = "BNPL_Check_Eligibility"
    const val TRIP_MONEY_BNPL_VALIDATION_SUCCESS = "BNPL_success_banner_shown"
    const val TRIP_MONEY_BNPL_VALIDATION_FAILURE = "BNPL_failure_banner_shown"
    const val BNPL_LOADER_SHOWN = "bnpl_loader_shown"
    const val TRIP_MONEY_BNPL_RETRY_CLICKED = "BNPL_Retry_Eligibility"
    const val TRIP_MONEY_BNPL_CARD_SHOWN = "BNPL_Eligiblity_banner_shown"
    const val TRIP_MONEY_BNPL_CACHE_VALIDATION_SUCCESS = "BNPL_success_cache_banner_shown"

    const val FREE_CANCELLATION = "free_cancellation"
    const val PART_PAYMENT_SHOWN = "part_payment_shown"
    const val BNPL_AVAILABLE = "bnpl_available"
    const val BNPL_NOT_AVAILABLE = "bnpl_not_available"
    const val PART_PAYMENT_SELECTED = "part_payment_selected"
    const val FULL_PAYMENT_SELECTED = "full_payment_selected"
    const val CANCELLATION_TYPE_PREFIX = "canType:"
    const val NON_REFUNDABLE = "non_ref"

    const val REVIEW_FOOTER_CTA_CLICKED = "Review_CTA_Clicked"
    const val REVIEW_BNPL_FOOTER_CTA_CLICKED = "Review_BNPL_CTA_Clicked"
    const val REVIEW_FOOTER_SHOWN = "Review_Footer_shown"

    const val BPG_CARD_SHOWN = "bpg_card_shown"
    const val BNPL_SELECTED = "BNPL_Selected"
    const val SKIP_APPROVAL_SHOWN_TRACKING = "skip_approval_shown"
    const val APPROVAL_CTA_SHOWN_TRACKING = "%s_shown"
    const val REVIEW_CHARITY_V2_SHOWN = "charity_shown"
    const val REVIEW_CHARITY_V2_ITEM_SELECTED = "charity_selected_%s"
    const val REVIEW_CHARITY_V2_ITEM_UNSELECTED = "charity_unselected_%s"

}

object DetailTrackingConstants {
    const val BUNDLE_UPDATED = "bundle_data_update"
    const val PRICE_UPDATED = "price_data_update"
    const val GALLERY_DATA_UPDATED = "gallery_data_update"
    const val USER_REVIEW_PAGE = "User Review Page"
    const val INSTAGRAM_GRID_SWIPED = "Grid swiped"
    const val INSTAGRAM_GRID_IMAGE_CLICKED = "Grid image clicked"
    const val INSTAGRAM_GRID_ICON_CLICKED = "Grid icon clicked"
    const val INSTAGRAM_POST_TAPPED = "Post tapped"
    const val INSTAGRAM_POST_SWIPED = "Post swiped"
    const val INSTAGRAM_MEDIA_TRACKING_COUNT = "Insta_Card_Shown_%sI_%sV"
    const val INSTAGRAM_TAB_IMAGE_TAPPED = "Insta Image tapped"
    const val INSTAGRAM_RESPONSE_RECEIVED = "Instagram Response Received"
}