package com.mmt.hotel.analytics.pdt.events

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys
import com.mmt.core.util.StringUtil
import com.mmt.hotel.analytics.pdt.TrackingConstants
import com.pdt.pdtDataLogging.events.model.Event

class HotelDetailDefaultSelectedRatePlanEvent(hotelId: String,
                                              eventName: String,
                                              eventType: Int,
                                              omnitureName: String,
                                              parentScreenName: String) :
        HotelGenericEvent(eventName, TrackingConstants.HOTEL_DETAIL, eventType, omnitureName, parentScreenName) {

    init {
        this.hotelId = hotelId
    }

    var mmtRoomCode: String? = null
    var supplierRoomCode: String? = null
    var supplierCode: String? = null
    var noRoomImg = -1
    var noRoomAmenities = -1
    var noRoomReviews = -1

    override fun createPDTEvent(): Event {
        val pdtEvent = super.createPDTEvent()
        val eventParam = pdtEvent.eventParam
        if (StringUtil.isNotNullAndEmpty(hotelId)) {
            eventParam[PDTAnalyticsKeys.PD_HTL_ID] = hotelId
        }

        if (StringUtil.isNotNullAndEmpty(mmtRoomCode)) {
            eventParam[PDTAnalyticsKeys.PD_HTL_RM_CD] = mmtRoomCode
        }

        if (StringUtil.isNotNullAndEmpty(supplierRoomCode)) {
            eventParam[PDTAnalyticsKeys.PD_SUPPLIER_ROOM_CODE] = supplierRoomCode
        }

        if (StringUtil.isNotNullAndEmpty(supplierCode)) {
            eventParam[PDTAnalyticsKeys.PD_SUPPLIER_CODE] = supplierCode
        }

        if (noRoomImg != -1) {
            eventParam[PDTAnalyticsKeys.PD_IMAGE_COUNT] = noRoomImg
        }

        if (noRoomAmenities != -1) {
            eventParam[PDTAnalyticsKeys.PD_AMENITY_COUNT] = noRoomAmenities
        }

        if (noRoomReviews != -1) {
            eventParam[PDTAnalyticsKeys.PD_ROOM_LEVEL_REVIEW_COUNT] = noRoomReviews
        }
        return pdtEvent
    }

}