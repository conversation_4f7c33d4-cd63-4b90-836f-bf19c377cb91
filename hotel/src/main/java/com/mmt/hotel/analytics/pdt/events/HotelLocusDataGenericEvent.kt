package com.mmt.hotel.analytics.pdt.events

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys
import com.mmt.core.constant.CommonConstants
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.common.model.tracking.LocusPDTPoiData
import com.mmt.hotel.common.model.tracking.LocusTrackingData
import com.mmt.uikit.util.isNotNullAndEmpty
import com.pdt.pdtDataLogging.events.model.BaseGenericEvent
import com.pdt.pdtDataLogging.events.model.Event


open class HotelLocusDataGenericEvent(eventName: String, pageName: String, eventType: Int,
                                      omnitureName: String, parentScreenName: String,
                                      prevFunnelStepPdt: String = CoreConstants.EMPTY_STRING, prevPageName:String = CoreConstants.EMPTY_STRING)
    : BaseGenericEvent(
    PDTAnalyticsKeys.HOTEL_FUNNEL_TOPIC_ID, PDTAnalyticsKeys.HOTEL_FUNNEL_TEMPLATE_ID,
        eventName, pageName, CommonConstants.LOB_HOTEL, eventType, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageName) {

    var locusRegionCode: String? = null
    var locusType: String? = null
    var locusCountryCode: String? = null
    var isLocus = false
    var locusSearchText: String? = null
    var locusCityCode: String? = null
    var locusSearchedAreas: List<String>? = null
    var locusPoiData: List<LocusPDTPoiData>? = null
    var hotelId: String? = null
    var locusMMApplied = false

    fun initLocusTrackingData(locusTrackingData: LocusTrackingData?) {
        if (locusTrackingData == null) {
            return
        }
        isLocus = true
        locusCountryCode = locusTrackingData.locusCountryCode
        locusRegionCode = locusTrackingData.locusRegionCode
        locusCityCode = locusTrackingData.locusCityCode
        locusType = locusTrackingData.locusType
        hotelId = locusTrackingData.hotelId
        locusMMApplied = locusTrackingData.locusMMApplied
        locusSearchText = locusTrackingData.locusSearchText
        locusSearchedAreas = locusTrackingData.locusSearchedAreas
        locusPoiData = locusTrackingData.locusPoiData
    }

    fun appendLocusParamsInEvent(event: Event) {
        val paramsMap = event.eventParam
        if (hotelId.isNotNullAndEmpty()) {
            paramsMap[PDTAnalyticsKeys.PD_LOC_HTL_CD] = hotelId
        }

        if (locusRegionCode.isNotNullAndEmpty()) {
            paramsMap[PDTAnalyticsKeys.PD_LOC_REG_CDS] = listOf(locusRegionCode)
        }

        if (locusCountryCode.isNotNullAndEmpty()) {
            paramsMap[PDTAnalyticsKeys.PD_LOC_CTRY_ID] = locusCountryCode
        }

        if (locusType.isNotNullAndEmpty()) {
            paramsMap[PDTAnalyticsKeys.SRCH_TYP_LOC] = locusType
        }

        if (locusSearchText.isNotNullAndEmpty()) {
            paramsMap[PDTAnalyticsKeys.SRCH_TXT] = locusSearchText
        }
        paramsMap[PDTAnalyticsKeys.PD_MM_APPLIED_LISTING] = locusMMApplied

        if (locusCityCode.isNotNullAndEmpty()) {
            paramsMap[PDTAnalyticsKeys.PD_LOC_CITY_CD] = locusCityCode
        }

        if (locusSearchedAreas?.isNotEmpty() == true) {
            val areaSearch = StringBuilder()
            for (index in locusSearchedAreas!!.indices) {
                areaSearch.append(locusSearchedAreas!![index])
                if (index < locusSearchedAreas!!.size - 1) {
                    areaSearch.append(CoreConstants.COMMA)
                }
            }
            paramsMap[PDTAnalyticsKeys.PD_LOC_AREA_CDS] =  listOf(areaSearch.toString())
        }

        if (locusPoiData?.isNotEmpty() == true) {
            paramsMap[PDTAnalyticsKeys.POI_LS] = locusPoiData
        }

        paramsMap[PDTAnalyticsKeys.PD_IS_LOCUS] = true
    }

}

