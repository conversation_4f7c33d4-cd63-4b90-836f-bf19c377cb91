package com.mmt.hotel.analytics.pdtMetrics

import com.mmt.analytics.AnalyticsSDK
import com.mmt.hotel.analytics.pdtMetrics.helper.HotelPdtV2MetricsHelper
import com.mmt.hotel.analytics.pdtMetrics.model.HotelPdtMetrics
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Helper
import com.mmt.hotel.common.model.UserSearchData
import com.pdt.eagleEye.constants.EventName
import com.pdt.eagleEye.constants.EventType

class HotelGstApiTracker(
    private val userSearchData: UserSearchData?,
    private val txnKey: String?
) {

    fun trackApiInitiated() {
        trackGstApiMetrics(HotelPdtV2MetricsHelper.MetricsStatus.INITIATED)
    }

    fun trackSuccess() {
        trackGstApiMetrics(HotelPdtV2MetricsHelper.MetricsStatus.SUCCESS)
    }

    fun trackError() {
        trackGstApiMetrics(HotelPdtV2MetricsHelper.MetricsStatus.ERROR)
    }

    private fun trackGstApiMetrics(status: HotelPdtV2MetricsHelper.MetricsStatus) {

        val searchData = userSearchData ?: return

        val pdtMetrics = HotelPdtMetrics(
            entityId = HotelPdtV2MetricsHelper.MetricsEntityId.GST_API_LOGGING.name,
            associatedID = txnKey,
            type = HotelPdtV2MetricsHelper.MetricsType.API_LOGGING.name,
            status = status.name
        )

        val metricsEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
            eventName = EventName.API_STATUS,
            eventType = EventType.ACTION,
            pageName = HotelPdtV2Constants.PageName.review.name,
            userSearchData = searchData,
            requestId = null,
            baseTrackingData = null,
            funnelStep = HotelPdtV2Constants.FunnelStep.review,
            isMetricsEvent = true
        )

        metricsEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(searchData))

        metricsEventBuilder.metrics(pdtMetrics)

        val event = metricsEventBuilder.build()
        AnalyticsSDK.instance.trackEvent(event)
    }
}