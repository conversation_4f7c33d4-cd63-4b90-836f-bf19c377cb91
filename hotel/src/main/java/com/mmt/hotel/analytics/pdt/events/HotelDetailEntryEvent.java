package com.mmt.hotel.analytics.pdt.events;

import static com.mmt.analytics.pdtclient.PDTAnalyticsKeys.POKUS_EXPERIMENT_DATA_V2;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.auth.login.model.Employee;
import com.mmt.hotel.analytics.pdt.TrackingConstants;
import com.mmt.hotel.analytics.pdt.model.TravelerDetail;
import com.mmt.hotel.common.constants.ExperimentsHotel;
import com.mmt.hotel.common.model.SponsoredDetailTrackingInfo;
import com.mmt.hotel.common.model.SponsoredTrackingInfoModel;
import com.mmt.pokus.LOB;
import com.mmt.pokus.PokusV2Helper;
import com.mmt.pokus.model.PokusConfigData;
import com.pdt.pdtDataLogging.events.model.Event;
import com.pdt.pdtDataLogging.events.model.TrackingDataWrapper;

import java.util.Map;
import java.util.Objects;

public class HotelDetailEntryEvent extends HotelPageEntryEvent {
    private SponsoredDetailTrackingInfo sponsoredDetailTrackingInfo;
    private TravelerDetail travelerDetail;

    private Boolean isFromMapView;

    public HotelDetailEntryEvent(@NonNull String hotelId, int eventType, long sessionStartTime,
                                 @NonNull String omnitureName,
                                 @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(TrackingConstants.HOTEL_DETAIL,eventType, sessionStartTime, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
        setHotelId(hotelId);
        travelerDetail = new TravelerDetail();
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (sponsoredDetailTrackingInfo != null && sponsoredDetailTrackingInfo.getSponsoredTrackingInfoModel() != null) {
            SponsoredTrackingInfoModel trackingInfo = sponsoredDetailTrackingInfo.getSponsoredTrackingInfoModel();
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_AD_REQ_ID, trackingInfo.getAdRequestID());
            event.getEventParam().put(PDTAnalyticsKeys.PD_AD_CAMPAIGN_ID, trackingInfo.getCampaignID());
            event.getEventParam().put(PDTAnalyticsKeys.PD_AD_ADV_ID, trackingInfo.getAdvertiserID());
            event.getEventParam().put(PDTAnalyticsKeys.PD_AD_ID, trackingInfo.getPdAdID());
            event.getEventParam().put(PDTAnalyticsKeys.USR_AD_ID, sponsoredDetailTrackingInfo.getUserAdId());
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RANK, sponsoredDetailTrackingInfo.getRank());
        }
        event.getEventParam().putAll(travelerDetail.getEventParams());
        updateExperimentDataWithHonouredEvents(event);
        return event;
    }

    private void updateExperimentDataWithHonouredEvents(Event pdtEvent){
        if(pdtEvent.getEventParam() != null && pdtEvent.getEventParam().containsKey(POKUS_EXPERIMENT_DATA_V2)){
            TrackingDataWrapper trackingDataWrapper = (TrackingDataWrapper) pdtEvent.getEventParam().get(POKUS_EXPERIMENT_DATA_V2);
            if(trackingDataWrapper != null){
                if(PokusV2Helper.getLobValue(LOB.HOTEL.getLobName()) !=null && PokusV2Helper.getLobValue(LOB.HOTEL.getLobName()).getMetadataValues() != null){
                    Map<String, PokusConfigData> mapOfMetaValues=  PokusV2Helper.getLobValue(LOB.HOTEL.getLobName()).getMetadataValues();
                    String packageMaxInclusionKey = ExperimentsHotel.INSTANCE.getPackageDealMaxInclusions().getKey();
                    if(mapOfMetaValues.get(packageMaxInclusionKey) != null && !TextUtils.isEmpty(mapOfMetaValues.get(packageMaxInclusionKey).getExpDetails())){
                        trackingDataWrapper.addToHonouredExpList(mapOfMetaValues.get(packageMaxInclusionKey).getExpDetails());
                    }
                }
            }
        }
    }

    public void fillTravelerDetail(Employee traveller) {
        String[] name = traveller.getName().split(" ");
        travelerDetail.setTravelerFName(name[0]);
        if(name.length > 1) {
            travelerDetail.setTravelerLName(name[name.length-1]);
        }
        travelerDetail.setTravelerTitle(traveller.getTitle());
        travelerDetail.setTravelerMobileNo(traveller.getPhoneNumber());
        travelerDetail.setTravelerEmailId(traveller.getBusinessEmailId());
    }

    public SponsoredDetailTrackingInfo getSponsoredDetailTrackingInfo() {
        return sponsoredDetailTrackingInfo;
    }

    public void setSponsoredDetailTrackingInfo(SponsoredDetailTrackingInfo sponsoredDetailTrackingInfo) {
        this.sponsoredDetailTrackingInfo = sponsoredDetailTrackingInfo;
    }

    public TravelerDetail getTravelerDetail() {
        return this.travelerDetail;
    }

    public String toString() {
        return "HotelDetailEntryEvent()";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelDetailEntryEvent))
            return false;
        final HotelDetailEntryEvent other = (HotelDetailEntryEvent) o;
        if (!other.canEqual(this)) return false;
        final Object this$sponsoredDetailTrackingInfo = this.getSponsoredDetailTrackingInfo();
        final Object other$sponsoredDetailTrackingInfo = other.getSponsoredDetailTrackingInfo();
        if (!Objects.equals(this$sponsoredDetailTrackingInfo, other$sponsoredDetailTrackingInfo))
            return false;
        final Object this$travelerDetail = this.getTravelerDetail();
        final Object other$travelerDetail = other.getTravelerDetail();
        if (this$travelerDetail == null ? other$travelerDetail != null : !this$travelerDetail.equals(other$travelerDetail))
            return false;
        if (!super.equals(o)) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelDetailEntryEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $sponsoredDetailTrackingInfo = this.getSponsoredDetailTrackingInfo();
        result = result * PRIME + ($sponsoredDetailTrackingInfo == null ? 43 : $sponsoredDetailTrackingInfo.hashCode());
        final Object $travelerDetail = this.getTravelerDetail();
        result = result * PRIME + ($travelerDetail == null ? 43 : $travelerDetail.hashCode());
        return result;
    }

    public Boolean getFromMapView() {
        return isFromMapView;
    }

    public void setFromMapView(Boolean fromMapView) {
        isFromMapView = fromMapView;
    }
}
