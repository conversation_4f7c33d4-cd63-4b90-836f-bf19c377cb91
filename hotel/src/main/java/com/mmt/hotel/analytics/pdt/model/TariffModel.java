package com.mmt.hotel.analytics.pdt.model;

import com.google.gson.annotations.SerializedName;
import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;

public class TariffModel {
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RT_PLAN_CD)
    private String ratePlanCode;

    @SerializedName(PDTAnalyticsKeys.PD_HTL_FCZP)
    private boolean isFreeCancelZeroPayment;

    @SerializedName(PDTAnalyticsKeys.PD_HTL_RM_QTY)
    private int rmQty;
    @SerializedName(PDTAnalyticsKeys.PD_DISP_SELL_AMT_DIS)
    private float sellAmt;
    @SerializedName(PDTAnalyticsKeys.PD_DISP_SELL_AMT)
    private float originalAmt;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_MEAL_PLAN)
    private String mealCode;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_CANC_POL)
    private String cancelPolicy;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RM_RSQ)
    private String rmRSQ;
    @SerializedName(PDTAnalyticsKeys.PRC_PAY_MOD)
    private String payMode;

    public TariffModel() {
    }

    public String getRatePlanCode() {
        return this.ratePlanCode;
    }

    public int getRmQty() {
        return this.rmQty;
    }

    public float getSellAmt() {
        return this.sellAmt;
    }

    public float getOriginalAmt() {
        return this.originalAmt;
    }

    public String getMealCode() {
        return this.mealCode;
    }

    public String getCancelPolicy() {
        return this.cancelPolicy;
    }

    public String getRmRSQ() {
        return this.rmRSQ;
    }

    public String getPayMode() {
        return this.payMode;
    }

    public void setRatePlanCode(String ratePlanCode) {
        this.ratePlanCode = ratePlanCode;
    }

    public void setRmQty(int rmQty) {
        this.rmQty = rmQty;
    }

    public void setSellAmt(float sellAmt) {
        this.sellAmt = sellAmt;
    }

    public void setOriginalAmt(float originalAmt) {
        this.originalAmt = originalAmt;
    }

    public void setMealCode(String mealCode) {
        this.mealCode = mealCode;
    }

    public void setCancelPolicy(String cancelPolicy) {
        this.cancelPolicy = cancelPolicy;
    }

    public void setRmRSQ(String rmRSQ) {
        this.rmRSQ = rmRSQ;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof TariffModel)) return false;
        final TariffModel other = (TariffModel) o;
        if (!other.canEqual(this)) return false;
        final Object this$ratePlanCode = this.ratePlanCode;
        final Object other$ratePlanCode = other.ratePlanCode;
        if (this$ratePlanCode == null ? other$ratePlanCode != null : !this$ratePlanCode.equals(other$ratePlanCode))
            return false;
        if(this.isFreeCancelZeroPayment != other.isFreeCancelZeroPayment) {
            return false;
        }
        if (this.rmQty != other.rmQty) return false;
        if (Float.compare(this.sellAmt, other.sellAmt) != 0) return false;
        if (Float.compare(this.originalAmt, other.originalAmt) != 0) return false;
        final Object this$mealCode = this.mealCode;
        final Object other$mealCode = other.mealCode;
        if (this$mealCode == null ? other$mealCode != null : !this$mealCode.equals(other$mealCode))
            return false;
        final Object this$cancelPolicy = this.cancelPolicy;
        final Object other$cancelPolicy = other.cancelPolicy;
        if (this$cancelPolicy == null ? other$cancelPolicy != null : !this$cancelPolicy.equals(other$cancelPolicy))
            return false;
        final Object this$rmRSQ = this.rmRSQ;
        final Object other$rmRSQ = other.rmRSQ;
        if (this$rmRSQ == null ? other$rmRSQ != null : !this$rmRSQ.equals(other$rmRSQ))
            return false;
        final Object this$payMode = this.payMode;
        final Object other$payMode = other.payMode;
        if (this$payMode == null ? other$payMode != null : !this$payMode.equals(other$payMode))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof TariffModel;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        final Object $ratePlanCode = this.ratePlanCode;
        result = result * PRIME + ($ratePlanCode == null ? 43 : $ratePlanCode.hashCode());
        result = result * PRIME + this.rmQty;
        result = result * PRIME + Float.floatToIntBits(this.sellAmt);
        result = result * PRIME + Float.floatToIntBits(this.originalAmt);
        final Object $mealCode = this.mealCode;
        result = result * PRIME + ($mealCode == null ? 43 : $mealCode.hashCode());
        final Object $cancelPolicy = this.cancelPolicy;
        result = result * PRIME + ($cancelPolicy == null ? 43 : $cancelPolicy.hashCode());
        final Object $rmRSQ = this.rmRSQ;
        result = result * PRIME + ($rmRSQ == null ? 43 : $rmRSQ.hashCode());
        final Object $payMode = this.payMode;
        result = result * PRIME + ($payMode == null ? 43 : $payMode.hashCode());
        result = result * PRIME + (isFreeCancelZeroPayment ? 43 : 47);
        return result;
    }

    public boolean isFreeCancelZeroPayment() {
        return isFreeCancelZeroPayment;
    }

    public void setFreeCancelZeroPayment(boolean freeCancelZeroPayment) {
        isFreeCancelZeroPayment = freeCancelZeroPayment;
    }

    @Override
    public String toString() {
        return "TariffModel{" +
                "ratePlanCode='" + ratePlanCode + '\'' +
                ", isFreeCancelZeroPayment=" + isFreeCancelZeroPayment +
                ", rmQty=" + rmQty +
                ", sellAmt=" + sellAmt +
                ", originalAmt=" + originalAmt +
                ", mealCode='" + mealCode + '\'' +
                ", cancelPolicy='" + cancelPolicy + '\'' +
                ", rmRSQ='" + rmRSQ + '\'' +
                ", payMode='" + payMode + '\'' +
                '}';
    }
}
