package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.analytics.pdtMetrics.model.HotelPdtMetrics
import com.pdt.eagleEye.models.EventDetails
import kotlinx.parcelize.Parcelize
import java.util.UUID

@Parcelize
data class HotelPdtEventDetails(
    @Transient
    override val eventName: String,
    @Transient
    override val eventType: String,
    @Transient
    override var eventValue: String? = null,
    @SerializedName("autosuggest_search_id")
    var autoSuggestSearchId: String? = null,
    val components: HotelPdtComponent,
    @SerializedName("booking_info")
    val bookingInfo: HotelPdtBookingInfo? = null,
) : EventDetails(eventName = eventName, eventType = eventType, eventValue = eventValue)