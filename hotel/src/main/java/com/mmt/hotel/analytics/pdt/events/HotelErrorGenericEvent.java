package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.auth.login.util.LoginUtils;
import com.mmt.core.util.StringUtil;
import com.mmt.hotel.common.HotelSharedPrefUtil;
import com.mmt.hotel.common.constants.SharedPrefKeys;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.HashMap;

public class HotelErrorGenericEvent extends HotelLocusDataGenericEvent {

    private String cityCode;
    private String countryCode;
    private String errorCode;
    private String errorMessage;
    private String extraContent;
    private HotelGenericEvent hotelGenericEvent;
    private boolean callSuperHg = true;

    public HotelErrorGenericEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                                  @NonNull String omnitureName,
                                  @NonNull String parentScreenName, String prevFunnelStepPdt, String previousPage) {
        super(eventName, pageName, eventType, omnitureName, parentScreenName, prevFunnelStepPdt, previousPage);
        hotelGenericEvent = new HotelGenericEvent(eventName, pageName, eventType, omnitureName, parentScreenName, false);
    }

    public HotelErrorGenericEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                             @NonNull String omnitureName,
                             @NonNull String parentScreenName, boolean callSuper,String prevFunnelStepPdt, String previousPage ) {
        this(eventName, pageName, eventType, omnitureName, parentScreenName, prevFunnelStepPdt, previousPage);
        hotelGenericEvent = new HotelGenericEvent(eventName, pageName, eventType, omnitureName, parentScreenName, false);
        this.callSuperHg = callSuper;

    }

    public HotelGenericEvent getHotelGenericEvent() {
        return this.hotelGenericEvent;
    }

    public void setHotelGenericEvent(HotelGenericEvent hotelGenericEvent) {
        this.hotelGenericEvent = hotelGenericEvent;
    }

    @Override
    protected Event createPDTEvent() {
        Event event;
        if (callSuperHg) {
            event = super.createPDTEvent();
        } else {
            event = Event.createEvent(getEventName(), new HashMap<>());
        }

        if (isLocus()) {
            appendLocusParamsInEvent(event);
        } else{
            String hotelId = getHotelId();
            if (StringUtil.isNotNullAndEmpty(hotelId)) {
                event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_ID, hotelId);
            }

            if (StringUtil.isNotNullAndEmpty(cityCode)) {
                event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_CITY, cityCode);
            }
            if (StringUtil.isNotNullAndEmpty(countryCode)) {
                event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_CTY, countryCode);
            }

            event.getEventParam().put(PDTAnalyticsKeys.PD_IS_LOCUS, false);
        }

        if (StringUtil.isNotNullAndEmpty(extraContent)) {
            event.getEventParam().put(PDTAnalyticsKeys.META_ACT_CNTNT, extraContent);
        }

        if (StringUtil.isNotNullAndEmpty(errorCode)) {
            event.getEventParam().put(PDTAnalyticsKeys.META_ERR_CD, errorCode);
        }

        if (StringUtil.isNotNullAndEmpty(errorMessage)) {
            event.getEventParam().put(PDTAnalyticsKeys.META_ERR_MSG, errorMessage);
        }

        event.getEventParam().putAll(hotelGenericEvent.createHotelBasePDTEvent().getEventParam());

        if(LoginUtils.INSTANCE.isCorporateUser() && StringUtil.isNotNullAndEmpty(LoginUtils.INSTANCE.getOrgId())){
            event.getEventParam().put(PDTAnalyticsKeys.USR_CORP_ORG_ID,LoginUtils.INSTANCE.getOrgId());
        }

        if(HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ORDR, null) != null){
            event.getEventParam().put(PDTAnalyticsKeys.EXP_RNK_ORDR,  HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ORDR, null));
        }
        if(HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ALGO_VER, null) != null){
            event.getEventParam().put(PDTAnalyticsKeys.EXP_RNK_ORDR,  HotelSharedPrefUtil.Companion.getInstance().getString(SharedPrefKeys.EXP_RNK_ALGO_VER, null));
        }
        return event;
    }

    public String getCityCode() {
        return this.cityCode;
    }

    public String getCountryCode() {
        return this.countryCode;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public String getExtraContent() {
        return this.extraContent;
    }

    public boolean isCallSuperHg() {
        return this.callSuperHg;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public void setExtraContent(String extraContent) {
        this.extraContent = extraContent;
    }

    public void setCallSuperHg(boolean callSuperHg) {
        this.callSuperHg = callSuperHg;
    }

    public String toString() {
        return "HotelErrorGenericEvent(locusDetails=" + super.toString() + ", cityCode=" + this.cityCode + ", countryCode=" + this.countryCode + ", errorCode=" + this.errorCode + ", errorMessage=" + this.errorMessage + ", extraContent=" + this.extraContent + ", hotelGenericEvent=" + this.hotelGenericEvent + ", callSuperHg=" + this.callSuperHg + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelErrorGenericEvent))
            return false;
        final HotelErrorGenericEvent other = (HotelErrorGenericEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$hotelId = this.getHotelId();
        final Object other$hotelId = other.getHotelId();
        if (this$hotelId == null ? other$hotelId != null : !this$hotelId.equals(other$hotelId))
            return false;
        final Object this$cityCode = this.getCityCode();
        final Object other$cityCode = other.getCityCode();
        if (this$cityCode == null ? other$cityCode != null : !this$cityCode.equals(other$cityCode))
            return false;
        final Object this$countryCode = this.getCountryCode();
        final Object other$countryCode = other.getCountryCode();
        if (this$countryCode == null ? other$countryCode != null : !this$countryCode.equals(other$countryCode))
            return false;
        final Object this$errorCode = this.getErrorCode();
        final Object other$errorCode = other.getErrorCode();
        if (this$errorCode == null ? other$errorCode != null : !this$errorCode.equals(other$errorCode))
            return false;
        final Object this$errorMessage = this.getErrorMessage();
        final Object other$errorMessage = other.getErrorMessage();
        if (this$errorMessage == null ? other$errorMessage != null : !this$errorMessage.equals(other$errorMessage))
            return false;
        final Object this$extraContent = this.getExtraContent();
        final Object other$extraContent = other.getExtraContent();
        if (this$extraContent == null ? other$extraContent != null : !this$extraContent.equals(other$extraContent))
            return false;
        final Object this$hotelGenericEvent = this.getHotelGenericEvent();
        final Object other$hotelGenericEvent = other.getHotelGenericEvent();
        if (this$hotelGenericEvent == null ? other$hotelGenericEvent != null : !this$hotelGenericEvent.equals(other$hotelGenericEvent))
            return false;
        if (this.isCallSuperHg() != other.isCallSuperHg()) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelErrorGenericEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $hotelId = this.getHotelId();
        result = result * PRIME + ($hotelId == null ? 43 : $hotelId.hashCode());
        final Object $cityCode = this.getCityCode();
        result = result * PRIME + ($cityCode == null ? 43 : $cityCode.hashCode());
        final Object $countryCode = this.getCountryCode();
        result = result * PRIME + ($countryCode == null ? 43 : $countryCode.hashCode());
        final Object $errorCode = this.getErrorCode();
        result = result * PRIME + ($errorCode == null ? 43 : $errorCode.hashCode());
        final Object $errorMessage = this.getErrorMessage();
        result = result * PRIME + ($errorMessage == null ? 43 : $errorMessage.hashCode());
        final Object $extraContent = this.getExtraContent();
        result = result * PRIME + ($extraContent == null ? 43 : $extraContent.hashCode());
        final Object $hotelGenericEvent = this.getHotelGenericEvent();
        result = result * PRIME + ($hotelGenericEvent == null ? 43 : $hotelGenericEvent.hashCode());
        result = result * PRIME + (this.isCallSuperHg() ? 79 : 97);
        return result;
    }
}
