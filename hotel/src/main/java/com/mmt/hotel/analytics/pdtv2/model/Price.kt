package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Price(
    val currency: String?,
    @SerializedName("display_price")
    val displayPrice: Double?,
    @SerializedName("base_price")
    val basePrice: Double?,
    @SerializedName("total_tax")
    val totalTax: Double?,
    @SerializedName("selling_price")
    val sellingPrice: Double?,
    @SerializedName("total_discount")
    val totalDiscount: Double,
    @SerializedName("discounts")
    val discounts: Discounts? = null
) : Parcelable

@Parcelize
data class Discounts(
    @SerializedName("applicable_coupons")
    val appliedCoupons: List<Coupon>? = null
) : Parcelable

@Parcelize
data class Coupon(
    val code: String,
    val discount: Double? = null,
    @SerializedName("is_applied")
    val isApplied: Boolean? = null,
    @SerializedName("is_preapplied")
    val isPreApplied: Boolean? = null
) : Parcelable
