package com.mmt.hotel.common.model

import android.os.Parcelable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * model classes to contain tracking info for Sponsored hotels
 *
 * <AUTHOR> : Aug 19, 2020
 */
@Parcelize
data class SponsoredTrackingInfoModel(
        @SerializedName("ad_id")
        @Expose
        val pdAdID: String,
        @SerializedName("campaign_id")
        @Expose
        val campaignID: String,
        @SerializedName("advertiser_id")
        @Expose
        val advertiserID: String,
        @SerializedName("request_id")
        @Expose
        val adRequestID: String,
        @SerializedName("tracking_node")
        @Expose
        val trackingNode: TrackingNode?,
        val metadata: TrackingMetaData? = null,

        var impressionTracked : Boolean = false
) : Parcelable

@Parcelize
data class TrackingMetaData(val tagType: String? = null) : Parcelable

@Parcelize
data class TrackingNode(
        @SerializedName("click_url")
        @Expose
        val clickUrl: String,
        @SerializedName("view_url")
        @Expose
        val viewUrl: String
) : Parcelable