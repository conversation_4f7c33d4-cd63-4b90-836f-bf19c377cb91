package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.mmt.hotel.analytics.pdt.model.RoomSelectionModel;
import com.mmt.hotel.selectRoom.tracking.ReccomRoomSelectionModel;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.HashMap;
import java.util.List;

public class SelectRoomClickEvent extends HotelGenericEvent {

    private boolean callSuperSr = true;
    private List<RoomSelectionModel> roomSelectionModel;
    private List<ReccomRoomSelectionModel> recommRoomSelectionModel;
    private List<String> selectedTags;

    public SelectRoomClickEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                                @NonNull String omnitureName,
                                @NonNull String parentScreenName) {
        super(eventName, pageName, eventType, omnitureName, parentScreenName);
    }

    public SelectRoomClickEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                                @NonNull String omnitureName,
                                @NonNull String parentScreenName, boolean callSuper) {
        this(eventName, pageName, eventType, omnitureName, parentScreenName);
        this.callSuperSr = callSuper;
    }

    @Override
    protected Event createPDTEvent() {

        Event event;
        if (callSuperSr) {
            event = super.createPDTEvent();
        } else {
            event = Event.createEvent(getEventName(), new HashMap<>());
        }

        if (CollectionUtil.isNotEmptyCollection(roomSelectionModel)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RMS_VWD_LS, roomSelectionModel);
        }
        if (CollectionUtil.isNotEmptyCollection(recommRoomSelectionModel)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RMS_RCMMND_LS, recommRoomSelectionModel);
        }
        if (CollectionUtil.isNotEmptyCollection(selectedTags)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_HTL_RM_SLCTNS, selectedTags);
        }
        return event;
    }

    public boolean isCallSuperSr() {
        return this.callSuperSr;
    }

    public List<RoomSelectionModel> getRoomSelectionModel() {
        return this.roomSelectionModel;
    }

    public List<ReccomRoomSelectionModel> getRecommRoomSelectionModel() {
        return this.recommRoomSelectionModel;
    }

    public List<String> getSelectedTags() {
        return this.selectedTags;
    }

    public void setCallSuperSr(boolean callSuperSr) {
        this.callSuperSr = callSuperSr;
    }

    public void setRoomSelectionModel(List<RoomSelectionModel> roomSelectionModel) {
        this.roomSelectionModel = roomSelectionModel;
    }

    public void setRecommRoomSelectionModel(List<ReccomRoomSelectionModel> recommRoomSelectionModel) {
        this.recommRoomSelectionModel = recommRoomSelectionModel;
    }

    public void setSelectedTags(List<String> selectedTags) {
        this.selectedTags = selectedTags;
    }

    public String toString() {
        return "SelectRoomClickEvent(callSuperSr=" + this.callSuperSr + ", roomSelectionModel=" + this.roomSelectionModel + ", recommRoomSelectionModel=" + this.recommRoomSelectionModel + ", selectedTags=" + this.selectedTags + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof SelectRoomClickEvent))
            return false;
        final SelectRoomClickEvent other = (SelectRoomClickEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        if (this.isCallSuperSr() != other.isCallSuperSr()) return false;
        final Object this$roomSelectionModel = this.getRoomSelectionModel();
        final Object other$roomSelectionModel = other.getRoomSelectionModel();
        if (this$roomSelectionModel == null ? other$roomSelectionModel != null : !this$roomSelectionModel.equals(other$roomSelectionModel))
            return false;
        final Object this$recommRoomSelectionModel = this.getRecommRoomSelectionModel();
        final Object other$recommRoomSelectionModel = other.getRecommRoomSelectionModel();
        if (this$recommRoomSelectionModel == null ? other$recommRoomSelectionModel != null : !this$recommRoomSelectionModel.equals(other$recommRoomSelectionModel))
            return false;
        final Object this$selectedTags = this.getSelectedTags();
        final Object other$selectedTags = other.getSelectedTags();
        if (this$selectedTags == null ? other$selectedTags != null : !this$selectedTags.equals(other$selectedTags))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof SelectRoomClickEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        result = result * PRIME + (this.isCallSuperSr() ? 79 : 97);
        final Object $roomSelectionModel = this.getRoomSelectionModel();
        result = result * PRIME + ($roomSelectionModel == null ? 43 : $roomSelectionModel.hashCode());
        final Object $recommRoomSelectionModel = this.getRecommRoomSelectionModel();
        result = result * PRIME + ($recommRoomSelectionModel == null ? 43 : $recommRoomSelectionModel.hashCode());
        final Object $selectedTags = this.getSelectedTags();
        result = result * PRIME + ($selectedTags == null ? 43 : $selectedTags.hashCode());
        return result;
    }
}
