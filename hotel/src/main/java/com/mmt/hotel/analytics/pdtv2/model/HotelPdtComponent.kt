package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.mmt.hotel.listingV2.model.request.SorterCriteria
import kotlinx.parcelize.Parcelize

@Parcelize
data class HotelPdtComponent(
    val id: String?,
    val filters: PdtFiltersV2?=null,
    val content_details: List<ContentDetailItem>?=null,
    val component_type: String?=null,
    val sort_by: SorterCriteria?=null,
    val pills_data: List<PillItem>?=null,
    val product_list: List<ProductItem>?=null
) : Parcelable