package com.mmt.hotel.analytics.pdtMetrics.helper

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.withFrameMillis
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.Lifecycle
import com.mmt.hotel.analytics.pdtMetrics.HotelScreenMetricsTracker
import com.mmt.hotel.base.getActivity
import com.mmt.hotel.common.di.HotelActivityEntryPoint
import com.mmt.hotel.compose.resources.OnLifecycleEvent
import dagger.hilt.android.EntryPointAccessors

// Helper function to ensure we wait for one frame render
suspend fun awaitFrameRender(onFrameRendered: () -> Unit) {
    // Wait for a frame to be rendered before calling onFrameRendered
    withFrameMillis {
        onFrameRendered()
    }
}

@Composable
fun AwaitFirstFrameRenderEffect(onFrameRendered: () -> Unit) {
    LaunchedEffect(Unit) {
        awaitFrameRender {
            onFrameRendered()
        }
    }
}

@Composable
fun rememberMetricsTrackerForActivity(): HotelScreenMetricsTracker? {
    val activity = LocalContext.current.getActivity()
    activity?.let {
        val tracker = remember { EntryPointAccessors.fromActivity(it, HotelActivityEntryPoint::class.java).createHotelScreenMetricsTracker() }
        return tracker
    }
    return null
}

@Composable
fun TraceMetricsOnLifecycleStop(
    entityId: HotelPdtV2MetricsHelper.MetricsEntityId,
    preCondition: () -> Unit = {}
) {
    val activityMetricsTracker = rememberMetricsTrackerForActivity()
    OnLifecycleEvent { _, event ->
        if (event == Lifecycle.Event.ON_STOP && activityMetricsTracker?.isMetricsCollected != true) {
            //To Update userSearchData in tracker as initially it is null on some pages like landing and corpApproval
            preCondition()
            activityMetricsTracker?.endTracing(entityId)
        }
    }
}

@Composable
fun EndTracingOnNextFrame(entityId: HotelPdtV2MetricsHelper.MetricsEntityId) {
    val activityMetricsTracker = rememberMetricsTrackerForActivity()
    AwaitFirstFrameRenderEffect { activityMetricsTracker?.endTracing(entityId) }
}

@Composable
fun TraceScreenDisplayedOnNextFrame() {
    val activityMetricsTracker = rememberMetricsTrackerForActivity()
    AwaitFirstFrameRenderEffect { activityMetricsTracker?.traceScreenDisplayed() }
}

@Composable
fun TraceScreenRenderedOnNextFrame() {
    val activityMetricsTracker = rememberMetricsTrackerForActivity()
    AwaitFirstFrameRenderEffect { activityMetricsTracker?.traceScreenRendered() }
}