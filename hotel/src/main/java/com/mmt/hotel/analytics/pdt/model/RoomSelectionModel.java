package com.mmt.hotel.analytics.pdt.model;

import com.google.gson.annotations.SerializedName;
import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;

import java.util.List;

public class RoomSelectionModel {
    @SerializedName(PDTAnalyticsKeys.PD_HTL_TARIFF_LS)
    private List<TariffModel> tariffList;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RM_CD)
    private String roomCode;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RMS_LEFT)
    private int roomLeft;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RM_IMG_VWD)
    private List<String> vwdImg;
    @SerializedName(PDTAnalyticsKeys.PD_HTL_RM_PERS_LS)
    private List<String> persuasionList;

    public RoomSelectionModel() {
    }

    public List<TariffModel> getTariffList() {
        return this.tariffList;
    }

    public String getRoomCode() {
        return this.roomCode;
    }

    public int getRoomLeft() {
        return this.roomLeft;
    }

    public List<String> getVwdImg() {
        return this.vwdImg;
    }

    public List<String> getPersuasionList() {
        return this.persuasionList;
    }

    public void setTariffList(List<TariffModel> tariffList) {
        this.tariffList = tariffList;
    }

    public void setRoomCode(String roomCode) {
        this.roomCode = roomCode;
    }

    public void setRoomLeft(int roomLeft) {
        this.roomLeft = roomLeft;
    }

    public void setVwdImg(List<String> vwdImg) {
        this.vwdImg = vwdImg;
    }

    public void setPersuasionList(List<String> persuasionList) {
        this.persuasionList = persuasionList;
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof RoomSelectionModel))
            return false;
        final RoomSelectionModel other = (RoomSelectionModel) o;
        if (!other.canEqual(this)) return false;
        final Object this$tariffList = this.tariffList;
        final Object other$tariffList = other.tariffList;
        if (this$tariffList == null ? other$tariffList != null : !this$tariffList.equals(other$tariffList))
            return false;
        final Object this$roomCode = this.roomCode;
        final Object other$roomCode = other.roomCode;
        if (this$roomCode == null ? other$roomCode != null : !this$roomCode.equals(other$roomCode))
            return false;
        if (this.roomLeft != other.roomLeft) return false;
        final Object this$vwdImg = this.vwdImg;
        final Object other$vwdImg = other.vwdImg;
        if (this$vwdImg == null ? other$vwdImg != null : !this$vwdImg.equals(other$vwdImg))
            return false;
        final Object this$persuasionList = this.persuasionList;
        final Object other$persuasionList = other.persuasionList;
        if (this$persuasionList == null ? other$persuasionList != null : !this$persuasionList.equals(other$persuasionList))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof RoomSelectionModel;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        final Object $tariffList = this.tariffList;
        result = result * PRIME + ($tariffList == null ? 43 : $tariffList.hashCode());
        final Object $roomCode = this.roomCode;
        result = result * PRIME + ($roomCode == null ? 43 : $roomCode.hashCode());
        result = result * PRIME + this.roomLeft;
        final Object $vwdImg = this.vwdImg;
        result = result * PRIME + ($vwdImg == null ? 43 : $vwdImg.hashCode());
        final Object $persuasionList = this.persuasionList;
        result = result * PRIME + ($persuasionList == null ? 43 : $persuasionList.hashCode());
        return result;
    }

    public String toString() {
        return "RoomSelectionModel(tariffList=" + this.tariffList + ", roomCode=" + this.roomCode + ", roomLeft=" + this.roomLeft + ", vwdImg=" + this.vwdImg + ", persuasionList=" + this.persuasionList + ")";
    }
}
