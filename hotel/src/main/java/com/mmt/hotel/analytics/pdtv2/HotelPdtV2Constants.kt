package com.mmt.hotel.analytics.pdtv2

import androidx.annotation.StringDef
import com.mmt.hotel.analytics.pdtv2.HotelEventType.Companion.ERROR

object HotelPdtV2Constants {

    const val PDT_V2_TEMPLATE_ID = 128385 // for B2C and global
    const val B2B_PDT_V2_TEMPLATE_ID = 92373
    const val METRICS_PDT_V2_TEMPLATE_ID = 133669
    const val PDT_V2_B2C_TOPIC_ID = "mmt_hotel_b2c_pdt_client_logging"
    const val PDT_V2_B2B_TOPIC_ID = "mmt_hotel_mybiz_pdt_client_logging"
    const val PDT_V2_METRICS_TOPIC_ID = "mmt_hotel_all_metric_client_logging"
    const val PDT_V2_GCC_TOPIC_ID = "global_hotels_b2c_pdt_client_logging"
    const val PDT_V2_GLOBAL_TOPIC_ID = "global_hotels_b2c_pdt_client_logging"
    const val LOB_CATEGORY_DH = "dh"
    const val LOB_CATEGORY_IH = "ih"
    const val HOTELS_BU = "hotels"

    const val PILL_SHOWN = "shown"
    const val PILL_CLICKED = "clicked"
    const val SORT_PILL_TYPE = "sort"
    const val FILTER_PILL_TYPE = "filter"

    const val SOURCE_AUTOSUGGEST = "autosuggest"

    enum class SponsoredType {
        SPONSORED_LISTING,BANNER
    }

    enum class SubLob {
        homestay,
        staycation,
        getaway,
        hotel,
        group,
        hourly,
        shortstays,
        treels
    }

    enum class PageName {
        landing,
        listing,
        review,
        thank_you,
        detail,
        select_room,
        treels,
        listing_map,
        detail_map,
        corp_approval
    }

    enum class SubPageNames {
        filter, gallery, property_photos, traveller_photos, instagram_photos, mmt_reviews, ta_reviews, external_reviews, amenities, about_property, property_policies, search, story_view, add_to_compare, location_map, property_host, food_and_dining, questions_and_answers, rate_plan, calendar
    }

    enum class FunnelStep {
        landing, listing, detail, review, select_room, thank_you, corp_approval, treels
    }

    enum class HotelEventValues {
        footer_cta_click, // detail, select room - footer cta click
        auto_suggest_item_clicked, // autosuggest - item clicked
        htl_not_interested, // listing - not interested clicked from pop up menu
        // api.name - error
        share, // treel - hotel share
        back, // treel - back press
        filter, // treel - filter bottomsheet opened
        book, // treel - book now click
        hotel_name, // treel - hotel name click
        wishlisted, // treel - wishlist icon click to wishlist
        unwishlisted, // treel - wishlist icon click to unwishlist
        audio_allowed, // treel - toggle audio
        audio_blocked, // treel - toggle audio
        onboarding_video_played, // treel - onboarding video completed
        upsell_clicked, // listing - upsell clicked
        upsell_rateplan_clicked, // listing - upsell rateplan clicked
        request_call_back_clicked_1, // listing - request call back clicked version 1
        request_call_back_clicked_2, // listing - request call back clicked version 2
        intl_roaming_consent_given, // review - intl roaming consent given
        intl_roaming_consent_removed // review - intl roaming consent removed
    }

    enum class BackendApis {
        thankYou, availRooms,validateCoupon,totalPricing, checkout, staticDetail, searchRooms, searchHotels, mobLanding, filterCount, fetchCollections, updatePrice
    }

    enum class HotelEventSearchType() {
        city, poi, area, product, region, location, nearby, google, zone, storefront, country
    }

}
@Retention(AnnotationRetention.SOURCE)
@StringDef(value = [ERROR])
annotation class HotelEventType {
    companion object {
        const val ERROR = "error"
    }
}
