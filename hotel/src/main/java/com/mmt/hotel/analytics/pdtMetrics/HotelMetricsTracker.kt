package com.mmt.hotel.analytics.pdtMetrics

import com.mmt.analytics.AnalyticsSDK
import com.mmt.hotel.analytics.pdtMetrics.helper.HotelPdtV2MetricsHelper
import com.mmt.hotel.analytics.pdtMetrics.model.HotelPdtMetrics
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Helper
import com.mmt.hotel.common.model.UserSearchData
import com.pdt.eagleEye.constants.EventName
import com.pdt.eagleEye.constants.EventType

object HotelMetricsTracker {

    fun trackEvarFailure(
        userSearchData: UserSearchData?,
        entityId: HotelPdtV2MetricsHelper.MetricsEntityId
    ) {

        val searchData = userSearchData ?: return

        val pdtMetrics = HotelPdtMetrics(
            entityId = entityId.name,
            type = HotelPdtV2MetricsHelper.MetricsType.EVAR_FAILURE.name,
            associatedID = searchData.toString(),
        )

        val metricsEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
            eventName = EventName.VALUE_SELECTED,
            eventType = EventType.ACTION,
            pageName = HotelPdtV2Constants.PageName.listing.name,
            userSearchData = searchData,
            requestId = null,
            baseTrackingData = null,
            funnelStep = HotelPdtV2Constants.FunnelStep.listing,
            isMetricsEvent = true
        )

        metricsEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(searchData))

        metricsEventBuilder.metrics(pdtMetrics)

        val event = metricsEventBuilder.build()
        AnalyticsSDK.instance.trackEvent(event)
    }

}