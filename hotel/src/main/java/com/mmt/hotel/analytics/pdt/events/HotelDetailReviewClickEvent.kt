package com.mmt.hotel.analytics.pdt.events

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys
import com.mmt.hotel.analytics.pdt.TrackingConstants
import com.mmt.hotel.analytics.pdt.model.RewardsAndRatingEventModel
import com.pdt.pdtDataLogging.events.model.Event

class HotelDetailReviewClickEvent(hotelId: String,
                                  eventName: String,
                                  eventType: Int,
                                  omnitureName: String,
                                  parentScreenName: String) :
        HotelGenericEvent(eventName, TrackingConstants.HOTEL_DETAIL, eventType, omnitureName, parentScreenName) {

    init {
        this.hotelId = hotelId
    }

    var wgsUrl: String? = null
    var wgsShown: List<String>? = null
    var topReviewUrl: String? = null
    var topReviewsShown: List<String>? = null
    var topReviewsScrolled: List<String>? = null
    var travellerPhotoClicked:String? = null
    var travellerPhotosShown:List<String>? = null
    var rewardsAndRatingEventModel: RewardsAndRatingEventModel?= null

    override fun createPDTEvent(): Event {
        val pdtEvent = super.createPDTEvent()
        val eventParam = pdtEvent.eventParam
        if (hotelId != null) {
            eventParam[PDTAnalyticsKeys.PD_HTL_ID] = hotelId
        }
        if (!wgsShown.isNullOrEmpty()) {
            eventParam[PDTAnalyticsKeys.WGS_SHOWN] = wgsShown
        }
        if (wgsUrl != null) {
            eventParam[PDTAnalyticsKeys.WGS_CLICKED] = wgsUrl
        }
        if (!topReviewsShown.isNullOrEmpty()) {
            eventParam[PDTAnalyticsKeys.TOP_REVIEWS_SHOWN] = topReviewsShown
        }
        if (!topReviewsScrolled.isNullOrEmpty()) {
            eventParam[PDTAnalyticsKeys.TOP_REVIEWS_SCROLLED] = topReviewsScrolled
        }
        if (topReviewUrl != null) {
            eventParam[PDTAnalyticsKeys.TOP_REVIEW_CLICKED] = topReviewUrl
        }
        if(!travellerPhotosShown.isNullOrEmpty()) {
            eventParam[PDTAnalyticsKeys.REVIEW_PHOTO_DISPLAYED] = travellerPhotosShown
        }
        if (travellerPhotoClicked != null) {
            eventParam[PDTAnalyticsKeys.REVIEW_PHOTO_CLICKED_URL] = travellerPhotoClicked
        }
        if(rewardsAndRatingEventModel != null) {
            eventParam[PDTAnalyticsKeys.REWARDS_AND_RATINGS_PAGE_DETAILS] = rewardsAndRatingEventModel
        }
        return pdtEvent
    }

}