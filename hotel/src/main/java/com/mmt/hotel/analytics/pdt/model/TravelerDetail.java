package com.mmt.hotel.analytics.pdt.model;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.StringUtil;

import java.util.HashMap;
import java.util.Map;

public class TravelerDetail {
    private String travelerFName;
    private String travelerLName;
    private String travelerTitle;
    private boolean gstOpted;
    private String travelerCmpAddress;
    private String travelerCmpName;
    private String travelerMobileNo;
    private String travelerEmailId;
    private String travelerGSTNo;
    private String travelerCtyMobileCode;

    public TravelerDetail() {
    }

    public Map<String, Object> getEventParams() {
        Map<String, Object> eventParam = new HashMap<>();
        if (StringUtil.isNotNullAndEmpty(travelerFName)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_FNAME, travelerFName);
        }
        if (StringUtil.isNotNullAndEmpty(travelerLName)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_LNAME, travelerLName);
        }
        if (StringUtil.isNotNullAndEmpty(travelerTitle)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_TITLE, travelerTitle);
        }
        if (StringUtil.isNotNullAndEmpty(travelerCmpAddress)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_CMPNY_ADDR, travelerCmpAddress);
        }
        if (StringUtil.isNotNullAndEmpty(travelerCmpName)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_CMPNY, travelerCmpName);
        }
        if (StringUtil.isNotNullAndEmpty(travelerGSTNo)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_GST_NO, travelerGSTNo);
        }
        if (StringUtil.isNotNullAndEmpty(travelerCtyMobileCode)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_T_M_CTY_CD, travelerCtyMobileCode);
        }
        if (StringUtil.isNotNullAndEmpty(travelerEmailId)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_T_E_COM_ID, travelerEmailId);
        }
        if (StringUtil.isNotNullAndEmpty(travelerMobileNo)) {
            eventParam.put(PDTAnalyticsKeys.TRVLR_T_M_COM_ID, travelerMobileNo);
        }
        eventParam.put(PDTAnalyticsKeys.TRVLR_GST_OPTD, gstOpted);

        return eventParam;
    }

    public void setTravelerFName(String travelerFName) {
        this.travelerFName = travelerFName;
    }

    public void setTravelerLName(String travelerLName) {
        this.travelerLName = travelerLName;
    }

    public void setTravelerTitle(String travelerTitle) {
        this.travelerTitle = travelerTitle;
    }

    public void setGstOpted(boolean gstOpted) {
        this.gstOpted = gstOpted;
    }

    public void setTravelerCmpAddress(String travelerCmpAddress) {
        this.travelerCmpAddress = travelerCmpAddress;
    }

    public void setTravelerCmpName(String travelerCmpName) {
        this.travelerCmpName = travelerCmpName;
    }

    public void setTravelerMobileNo(String travelerMobileNo) {
        this.travelerMobileNo = travelerMobileNo;
    }

    public void setTravelerEmailId(String travelerEmailId) {
        this.travelerEmailId = travelerEmailId;
    }

    public void setTravelerGSTNo(String travelerGSTNo) {
        this.travelerGSTNo = travelerGSTNo;
    }

    public void setTravelerCtyMobileCode(String travelerCtyMobileCode) {
        this.travelerCtyMobileCode = travelerCtyMobileCode;
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof TravelerDetail)) return false;
        final TravelerDetail other = (TravelerDetail) o;
        if (!other.canEqual(this)) return false;
        final Object this$travelerFName = this.travelerFName;
        final Object other$travelerFName = other.travelerFName;
        if (this$travelerFName == null ? other$travelerFName != null : !this$travelerFName.equals(other$travelerFName))
            return false;
        final Object this$travelerLName = this.travelerLName;
        final Object other$travelerLName = other.travelerLName;
        if (this$travelerLName == null ? other$travelerLName != null : !this$travelerLName.equals(other$travelerLName))
            return false;
        final Object this$travelerTitle = this.travelerTitle;
        final Object other$travelerTitle = other.travelerTitle;
        if (this$travelerTitle == null ? other$travelerTitle != null : !this$travelerTitle.equals(other$travelerTitle))
            return false;
        if (this.gstOpted != other.gstOpted) return false;
        final Object this$travelerCmpAddress = this.travelerCmpAddress;
        final Object other$travelerCmpAddress = other.travelerCmpAddress;
        if (this$travelerCmpAddress == null ? other$travelerCmpAddress != null : !this$travelerCmpAddress.equals(other$travelerCmpAddress))
            return false;
        final Object this$travelerCmpName = this.travelerCmpName;
        final Object other$travelerCmpName = other.travelerCmpName;
        if (this$travelerCmpName == null ? other$travelerCmpName != null : !this$travelerCmpName.equals(other$travelerCmpName))
            return false;
        final Object this$travelerMobileNo = this.travelerMobileNo;
        final Object other$travelerMobileNo = other.travelerMobileNo;
        if (this$travelerMobileNo == null ? other$travelerMobileNo != null : !this$travelerMobileNo.equals(other$travelerMobileNo))
            return false;
        final Object this$travelerEmailId = this.travelerEmailId;
        final Object other$travelerEmailId = other.travelerEmailId;
        if (this$travelerEmailId == null ? other$travelerEmailId != null : !this$travelerEmailId.equals(other$travelerEmailId))
            return false;
        final Object this$travelerGSTNo = this.travelerGSTNo;
        final Object other$travelerGSTNo = other.travelerGSTNo;
        if (this$travelerGSTNo == null ? other$travelerGSTNo != null : !this$travelerGSTNo.equals(other$travelerGSTNo))
            return false;
        final Object this$travelerCtyMobileCode = this.travelerCtyMobileCode;
        final Object other$travelerCtyMobileCode = other.travelerCtyMobileCode;
        if (this$travelerCtyMobileCode == null ? other$travelerCtyMobileCode != null : !this$travelerCtyMobileCode.equals(other$travelerCtyMobileCode))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof TravelerDetail;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = 1;
        final Object $travelerFName = this.travelerFName;
        result = result * PRIME + ($travelerFName == null ? 43 : $travelerFName.hashCode());
        final Object $travelerLName = this.travelerLName;
        result = result * PRIME + ($travelerLName == null ? 43 : $travelerLName.hashCode());
        final Object $travelerTitle = this.travelerTitle;
        result = result * PRIME + ($travelerTitle == null ? 43 : $travelerTitle.hashCode());
        result = result * PRIME + (this.gstOpted ? 79 : 97);
        final Object $travelerCmpAddress = this.travelerCmpAddress;
        result = result * PRIME + ($travelerCmpAddress == null ? 43 : $travelerCmpAddress.hashCode());
        final Object $travelerCmpName = this.travelerCmpName;
        result = result * PRIME + ($travelerCmpName == null ? 43 : $travelerCmpName.hashCode());
        final Object $travelerMobileNo = this.travelerMobileNo;
        result = result * PRIME + ($travelerMobileNo == null ? 43 : $travelerMobileNo.hashCode());
        final Object $travelerEmailId = this.travelerEmailId;
        result = result * PRIME + ($travelerEmailId == null ? 43 : $travelerEmailId.hashCode());
        final Object $travelerGSTNo = this.travelerGSTNo;
        result = result * PRIME + ($travelerGSTNo == null ? 43 : $travelerGSTNo.hashCode());
        final Object $travelerCtyMobileCode = this.travelerCtyMobileCode;
        result = result * PRIME + ($travelerCtyMobileCode == null ? 43 : $travelerCtyMobileCode.hashCode());
        return result;
    }

    public String toString() {
        return "TravelerDetail(travelerFName=" + this.travelerFName + ", travelerLName=" + this.travelerLName + ", travelerTitle=" + this.travelerTitle + ", gstOpted=" + this.gstOpted + ", travelerCmpAddress=" + this.travelerCmpAddress + ", travelerCmpName=" + this.travelerCmpName + ", travelerMobileNo=" + this.travelerMobileNo + ", travelerEmailId=" + this.travelerEmailId + ", travelerGSTNo=" + this.travelerGSTNo + ", travelerCtyMobileCode=" + this.travelerCtyMobileCode + ")";
    }
}
