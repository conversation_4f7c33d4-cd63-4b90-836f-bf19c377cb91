package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.core.constant.CoreConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class TravellerInfo(
    @SerializedName("is_primary")
    val isPrimary: <PERSON><PERSON><PERSON>,
    @SerializedName("email_id")
    val emailId: String? = null,
    val fname: String,
    val lname: String,
    val title: String,
    @SerializedName("gst_optd")
    val gstOpted: <PERSON><PERSON><PERSON>,
    @SerializedName("mobile_com_id")
    val mobileComId: String = CoreConstants.EMPTY_STRING,
    @SerializedName("email_com_id")
    val emailComId: String = CoreConstants.EMPTY_STRING,
    @SerializedName("mob_id")
    val mobileNo: String,
    val gender: String
) : Parcelable