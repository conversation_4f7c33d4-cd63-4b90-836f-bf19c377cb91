package com.mmt.hotel.analytics.pdt

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys

object HotelPdtKeys {


    const val PD_HTL_IMG_SENT = "pd_htl_img_sent"
    const val IMG_SCROLL_IDX = "img_scroll_idx"
    const val IMG_CLICK_IDX = "img_click_idx"
    const val FILTER_APPLIED_LIST = "fltr_appld_list"
    const val FILTER_REMOVED_LIST = "fltr_removed_list"

    //Hotel Listing
    const val SEARCH_CLICKED = "search_clicked"
    const val MAP_VIEW_CLICKED = "pd_htl_map_vw_optd"
    const val IS_BOTTOM_SHEET_FILTER_APPLIED = "is_bottom_sheet_filter_applied"
    const val ASP_BUCKET = "asp_bucket"
    const val TRAFFIC_SOURCE = "traffic_source"
    const val LISTING_SEARCH_TEXT = "listing_search_text"
    const val LISTING_SEARCH_CONTEXT_MODIFIED = "listing_serch_context_modified"
    const val LISTING_PROPERTY_COUNT = "pd_listing_prop_count_section"

    //Hotel Details

    const val TOP_IMAGE_CLICKED = "topImageClicked"
    const val REVIEW_SEARCH_CLICKED = "reviewSearchClicked"

    const val FAQ_CLICKED = "faq_clicked"
    const val OFFER_APPLIED = "offerApplied"
    const val OFFER_SECTION_CLICKED = "detail_offer_section_clicked"
    const val LUX_IMAGE_CARD_CLICK = "luxe_image_clicked"

    const val MODIFY_SEARCH_CLICKED = "modify_search_clicked"
    const val LOCATION_TAB_CLICKED = "location_tab_clicked"
    const val LOCATION_ITEM_CLICKED = "location_tab_selected_category"
    const val REVIEW_TAB_CUSTOMER_PHOTO_CLICKED = "review_tab_customer_photo_cliecked"
    const val REVIEW_TAB_CLICKED = "review_tab_clicked"
    const val INSTA_SECTION_INTERACTED = "insta_section_interacted"
    const val VIEW_ALL_REVIEW_CLICKED = "review_tab_view_all_review_clicked"
    const val MORE_AMENITIES_CLICKED = "more_amenties_clicked"
    const val DETAIL_PAGE_DEFAULT_OFFER = "detail_page_default_offer"
    const val DETAIL_PAGE_APPLIED_OFFER = "detail_page_applied_offer"
    const val DETAIL_CHARITY_AMOUNT = PDTAnalyticsKeys.PRC_CHRTY_AMT
    const val DETAIL_CHARITY_OPTED =  PDTAnalyticsKeys.PRC_CHRTY_OPTD


    const val DETAIL_SEARCHED_TEXT = "detail_search_text"
    const val TAB_CLICKED = "tab_clicked"
    const val MODIFY_DATE_CLICKED = "modify_date_clicked"

    //Select Room Page
    const val CLICK_ON_ROOM_PHOTOS_IMAGES = "clickOnRoomPhotosImages"
    const val HOTEL_ROOM_TYPE= "pd_htl_rm_typ"
    const val HOTEL_ROOM_NAME= "pd_htl_rm_nm"

    //Review Page.
    const val IS_SELF_BOOKING= "is_self_booking"
    const val REVIEW_PAGE_DEFAULT_OFFER = "review_page_default_offer"
    const val REVIEW_PAGE_APPLIED_OFFER = "review_page_applied_offer"
    const val INLINE_FILTER_CATEGORY_SHOWN = "inline_filter_category_shown"
    const val PILLS_SHOWN = "pills_shown"
    const val PILLS_CLICKED = "pills_clicked"
    const val EASY_SELECTION_BS_DISMISSED = "easy_select_bs_dismiss"
}