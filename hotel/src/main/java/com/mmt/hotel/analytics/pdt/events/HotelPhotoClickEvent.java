package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.ArrayList;
import java.util.List;

public class HotelPhotoClickEvent extends HotelGenericEvent {

    private List<String> imageUrl;
    private final List<String> selectedCategories =  new ArrayList<>();

    public HotelPhotoClickEvent(@NonNull String eventName,
                                @NonNull String pageName, int eventType,
                                @NonNull String hotelId,@NonNull String imageUrl,
                                @NonNull String omnitureName,
                                @NonNull String parentScreenName) {
        super(eventName, pageName, eventType, omnitureName, parentScreenName);
        setHotelId(hotelId);
        this.imageUrl = new ArrayList<>();
        this.imageUrl.add(imageUrl);
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (CollectionUtil.isNotEmptyCollection(imageUrl)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RM_IMG_VWD, imageUrl);
        }
        if (CollectionUtil.isNotEmptyCollection(selectedCategories)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_HTL_IMG_TAG_CLCKD, selectedCategories);
        }
        return event;
    }

    public void setCurrentCategory(String category) {
        selectedCategories.add(category);
    }

    public List<String> getImageUrl() {
        return this.imageUrl;
    }

    public List<String> getSelectedCategories() {
        return this.selectedCategories;
    }

    public void setImageUrl(List<String> imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String toString() {
        return "HotelPhotoClickEvent(imageUrl=" + this.imageUrl + ", selectedCategories=" + this.selectedCategories + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelPhotoClickEvent))
            return false;
        final HotelPhotoClickEvent other = (HotelPhotoClickEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$imageUrl = this.getImageUrl();
        final Object other$imageUrl = other.getImageUrl();
        if (this$imageUrl == null ? other$imageUrl != null : !this$imageUrl.equals(other$imageUrl))
            return false;
        final Object this$selectedCategories = this.getSelectedCategories();
        final Object other$selectedCategories = other.getSelectedCategories();
        if (this$selectedCategories == null ? other$selectedCategories != null : !this$selectedCategories.equals(other$selectedCategories))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelPhotoClickEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $imageUrl = this.getImageUrl();
        result = result * PRIME + ($imageUrl == null ? 43 : $imageUrl.hashCode());
        final Object $selectedCategories = this.getSelectedCategories();
        result = result * PRIME + ($selectedCategories == null ? 43 : $selectedCategories.hashCode());
        return result;
    }
}

