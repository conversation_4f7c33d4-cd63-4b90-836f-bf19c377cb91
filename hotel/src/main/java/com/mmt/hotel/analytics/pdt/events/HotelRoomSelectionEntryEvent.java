package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

public class HotelRoomSelectionEntryEvent extends HotelPageEntryEvent {
    public HotelRoomSelectionEntryEvent(@NonNull String pageName, int eventType, long sessionStartTime,
                                        @NonNull String omnitureName,
                                        @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(pageName, eventType, sessionStartTime, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
    }

    public String toString() {
        return "HotelRoomSelectionEntryEvent()";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelRoomSelectionEntryEvent))
            return false;
        final HotelRoomSelectionEntryEvent other = (HotelRoomSelectionEntryEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelRoomSelectionEntryEvent;
    }

    public int hashCode() {
        int result = super.hashCode();
        return result;
    }
}
