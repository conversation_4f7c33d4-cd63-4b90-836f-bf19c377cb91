package com.mmt.hotel.analytics.pdtv2.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.bookingreview.model.response.room.RoomTariff
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.selectRoom.model.response.room.ratePlan.TariffOccupancy
import kotlinx.parcelize.Parcelize

@Parcelize
data class Catalog(
    val id: String,
    @SerializedName("rate_plan_code") val ratePlanCode: String,
    @SerializedName("combo_id") val comboId: String? = null,
    @SerializedName("room_code") val roomCode: String,
    @SerializedName("tariff_occupancy") val tariffOccupancy: List<Occupancy>,
    @SerializedName("selected") val selected: Boolean = true
) : Parcelable

@Parcelize
data class Occupancy(
    val adult: Int,
    val children: Int? = null,
    @SerializedName("room_count") val roomCount: Int? = null
) : Parcelable {
    constructor(roomTariff: RoomTariff) : this(
        adult = roomTariff.numberOfAdults,
        children = roomTariff.numberOfChildren,
        roomCount = roomTariff.roomCount
    )

    constructor(occupancy: TariffOccupancy): this(
        adult = occupancy.numberOfAdults,
        children = occupancy.numberOfChildren,
        roomCount = occupancy.roomCount
    )

    constructor(occupancy: RoomStayCandidatesV2): this(
        adult = occupancy.adultCount,
        children = occupancy.childAges?.size ?: 0,
        roomCount = occupancy.rooms ?: 1
    )
}