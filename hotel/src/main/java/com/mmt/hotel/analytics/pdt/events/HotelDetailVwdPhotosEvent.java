package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.CollectionUtil;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class HotelDetailVwdPhotosEvent extends HotelGenericEvent {

    private boolean callSuperHdp = true;
    private List<String> imageUrl;
    private final List<String> selectedCategories =  new ArrayList<>();

    public HotelDetailVwdPhotosEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                                     @NonNull String omnitureName,
                                     @NonNull String parentScreenName) {
        super(eventName, pageName, eventType, omnitureName, parentScreenName);
    }

    public HotelDetailVwdPhotosEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                                     @NonNull String omnitureName,
                                     @NonNull String parentScreenName, boolean callSuper) {
        this(eventName, pageName, eventType, omnitureName, parentScreenName);
        this.callSuperHdp = callSuper;
    }

    @Override
    protected Event createPDTEvent() {
        Event event;
        if (callSuperHdp) {
            event = super.createPDTEvent();
        } else {
            event = Event.createEvent(getEventName(), new HashMap<>());
        }

        if (CollectionUtil.isNotEmptyCollection(imageUrl)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RM_IMG_VWD, imageUrl);
        }
        if (CollectionUtil.isNotEmptyCollection(selectedCategories)) {
            event.getEventParam().put(PDTAnalyticsKeys.FLTR_HTL_IMG_TAG_CLCKD, selectedCategories);
        }
        return event;
    }

    public void setSelectedCategories(String category) {
        selectedCategories.clear();
        selectedCategories.add(category);
    }

    public boolean isCallSuperHdp() {
        return this.callSuperHdp;
    }

    public List<String> getImageUrl() {
        return this.imageUrl;
    }

    public List<String> getSelectedCategories() {
        return this.selectedCategories;
    }

    public void setCallSuperHdp(boolean callSuperHdp) {
        this.callSuperHdp = callSuperHdp;
    }

    public void setImageUrl(List<String> imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String toString() {
        return "HotelDetailVwdPhotosEvent(callSuperHdp=" + this.callSuperHdp + ", imageUrl=" + this.imageUrl + ", selectedCategories=" + this.selectedCategories + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelDetailVwdPhotosEvent))
            return false;
        final HotelDetailVwdPhotosEvent other = (HotelDetailVwdPhotosEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        if (this.isCallSuperHdp() != other.isCallSuperHdp()) return false;
        final Object this$imageUrl = this.getImageUrl();
        final Object other$imageUrl = other.getImageUrl();
        if (this$imageUrl == null ? other$imageUrl != null : !this$imageUrl.equals(other$imageUrl))
            return false;
        final Object this$selectedCategories = this.getSelectedCategories();
        final Object other$selectedCategories = other.getSelectedCategories();
        if (this$selectedCategories == null ? other$selectedCategories != null : !this$selectedCategories.equals(other$selectedCategories))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelDetailVwdPhotosEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        result = result * PRIME + (this.isCallSuperHdp() ? 79 : 97);
        final Object $imageUrl = this.getImageUrl();
        result = result * PRIME + ($imageUrl == null ? 43 : $imageUrl.hashCode());
        final Object $selectedCategories = this.getSelectedCategories();
        result = result * PRIME + ($selectedCategories == null ? 43 : $selectedCategories.hashCode());
        return result;
    }
}
