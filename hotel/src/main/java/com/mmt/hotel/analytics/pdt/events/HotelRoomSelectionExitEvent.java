package com.mmt.hotel.analytics.pdt.events;

import androidx.annotation.NonNull;

import com.pdt.pdtDataLogging.events.model.Event;

import java.util.HashMap;

public class HotelRoomSelectionExitEvent extends HotelPageExitEvent {

    private SelectRoomClickEvent selectRoomClickEvent;

    private RoomTrackingInfo roomInfo;

    private HashMap<String,String> allClickedInfo;

    public HotelRoomSelectionExitEvent(@NonNull String pageName, int eventType, long startTimeStamp,
                                       @NonNull String omnitureName,
                                       @NonNull String parentScreenName, String prevFunnelStepPdt, String prevPageNamePdt) {
        super(pageName, eventType, startTimeStamp, omnitureName, parentScreenName, prevFunnelStepPdt, prevPageNamePdt);
        selectRoomClickEvent = new SelectRoomClickEvent(PAGE_EXIT, pageName,eventType, omnitureName, parentScreenName, false);
        allClickedInfo = new HashMap<>();
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        event.getEventParam().putAll(selectRoomClickEvent.createPDTEvent().getEventParam());
        return event;
    }

    public SelectRoomClickEvent getSelectRoomClickEvent() {
        return this.selectRoomClickEvent;
    }

    public void setSelectRoomClickEvent(SelectRoomClickEvent selectRoomClickEvent) {
        this.selectRoomClickEvent = selectRoomClickEvent;
    }

    public String toString() {
        return "HotelRoomSelectionExitEvent(selectRoomClickEvent=" + this.selectRoomClickEvent + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelRoomSelectionExitEvent))
            return false;
        final HotelRoomSelectionExitEvent other = (HotelRoomSelectionExitEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$selectRoomClickEvent = this.getSelectRoomClickEvent();
        final Object other$selectRoomClickEvent = other.getSelectRoomClickEvent();
        if (this$selectRoomClickEvent == null ? other$selectRoomClickEvent != null : !this$selectRoomClickEvent.equals(other$selectRoomClickEvent))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelRoomSelectionExitEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $selectRoomClickEvent = this.getSelectRoomClickEvent();
        result = result * PRIME + ($selectRoomClickEvent == null ? 43 : $selectRoomClickEvent.hashCode());
        return result;
    }

    public RoomTrackingInfo getRoomInfo() {
        return roomInfo;
    }

    public void setRoomInfo(RoomTrackingInfo roomInfo) {
        this.roomInfo = roomInfo;
    }

    public HashMap<String, String> getAllClickedInfo() {
        return allClickedInfo;
    }

    public void setAllClickedInfo(HashMap<String, String> allClickedInfo) {
        this.allClickedInfo = allClickedInfo;
    }
}
