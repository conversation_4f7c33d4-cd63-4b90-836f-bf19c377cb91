package com.mmt.hotel.analytics.pdt.model

import com.google.gson.annotations.SerializedName
import com.mmt.analytics.pdtclient.PDTAnalyticsKeys

class RewardsAndRatingEventModel {
    @SerializedName(PDTAnalyticsKeys.CUSTOMER_COHORTS_SELECTED)
    var cohortSelected: String? = null

    @SerializedName(PDTAnalyticsKeys.CUSTOMER_COHORTS_SHOWN)
    var tabList: List<String>? = null

    @SerializedName(PDTAnalyticsKeys.SEEK_TAGS_SHOWN)
    var seekTagsShown:List<String>? = null

    @SerializedName(PDTAnalyticsKeys.SEEK_TAG_CLICKED)
    var seekTagClicked:String? = null

    @SerializedName(PDTAnalyticsKeys.SORTING_TYPE)
    var sortingType:String? = null

    @SerializedName(PDTAnalyticsKeys.REVIEWS_SEEN)
    var seenReviewsList: List<String>? = null

    @SerializedName(PDTAnalyticsKeys.REVIEW_PHOTO_CLICKED_URL)
    var travellerPhotoClicked:String? = null

    @SerializedName(PDTAnalyticsKeys.REVIEW_PHOTO_DISPLAYED)
    var travellerPhotosShown:List<String>? = null

    @SerializedName(PDTAnalyticsKeys.PHOTO_CLICKED_REVIEW_ID)
    var photoReviewId:String? = null

    @SerializedName(PDTAnalyticsKeys.HELPFUL_CLICKED)
    var helpfulClickedList: List<String>? = null

    @SerializedName(PDTAnalyticsKeys.SHOW_MORE_CLICKED)
    var showMoreClickedList: List<String?>? = null
}