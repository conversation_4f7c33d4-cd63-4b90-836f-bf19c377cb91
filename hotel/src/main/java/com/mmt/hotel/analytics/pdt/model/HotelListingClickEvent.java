package com.mmt.hotel.analytics.pdt.model;

import androidx.annotation.NonNull;

import com.mmt.analytics.pdtclient.PDTAnalyticsKeys;
import com.mmt.core.util.StringUtil;
import com.mmt.hotel.analytics.pdt.HotelPdtKeys;
import com.mmt.hotel.common.model.SponsoredTrackingInfoModel;
import com.pdt.pdtDataLogging.events.model.Event;

import java.util.Map;
import java.util.Objects;

public class HotelListingClickEvent extends SorterFilterEvent {
    private SponsoredTrackingInfoModel sponsoredTrackingInfoModel;
    private int hotelRank;
    private String aspBucket;

    private String trafficSource;

    public void setAspBucket(String aspBucket) {
        this.aspBucket = aspBucket;
    }

    public String getAspBucket() {
        return aspBucket;
    }

    public int getHotelRank() {
        return hotelRank;
    }

    public void setHotelRank(int hotelRank) {
        this.hotelRank = hotelRank;
    }

    public void setTrafficSource(String trafficSource) {
        this.trafficSource = trafficSource;
    }

    public String getTrafficSource() {
        return trafficSource;
    }

    public HotelListingClickEvent(@NonNull String eventName, @NonNull String pageName, int eventType,
                                  @NonNull String omnitureName,
                                  @NonNull String parentScreenName) {
        super(eventName, pageName, eventType, omnitureName, parentScreenName);
    }

    @Override
    protected Event createPDTEvent() {
        Event event = super.createPDTEvent();
        if (sponsoredTrackingInfoModel != null && userAdId != null) {
            Map<String, Object> eventMap = event.getEventParam();
            eventMap.put(PDTAnalyticsKeys.PD_HTL_AD_REQ_ID, sponsoredTrackingInfoModel.getAdRequestID());
            eventMap.put(PDTAnalyticsKeys.PD_AD_CAMPAIGN_ID, sponsoredTrackingInfoModel.getCampaignID());
            eventMap.put(PDTAnalyticsKeys.PD_AD_ADV_ID, sponsoredTrackingInfoModel.getAdvertiserID());
            eventMap.put(PDTAnalyticsKeys.PD_AD_ID, sponsoredTrackingInfoModel.getPdAdID());
            eventMap.put(PDTAnalyticsKeys.USR_AD_ID, userAdId);
        }


        event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_RANK, hotelRank);
        event.getEventParam().put(HotelPdtKeys.ASP_BUCKET, aspBucket);
        event.getEventParam().put(HotelPdtKeys.TRAFFIC_SOURCE, trafficSource);
        String hotelId = getHotelId();
        if (StringUtil.isNotNullAndEmpty(hotelId)) {
            event.getEventParam().put(PDTAnalyticsKeys.PD_HTL_ID, hotelId);
        }

        return event;
    }

    public void setSponsoredHotelClickTrackingInfo(SponsoredTrackingInfoModel sponsoredTrackingInfoModel, String userAdId, int rank) {
        this.sponsoredTrackingInfoModel = sponsoredTrackingInfoModel;
        this.userAdId = userAdId;
        this.hotelRank = rank;
    }

    public String toString() {
        return "HotelListingClickEvent()";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelListingClickEvent))
            return false;
        final HotelListingClickEvent other = (HotelListingClickEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;

        final Object this$sponsoredTrackingInfoModel = this.sponsoredTrackingInfoModel;
        final Object other$sponsoredTrackingInfoModel = other.sponsoredTrackingInfoModel;
        if (!Objects.equals(this$sponsoredTrackingInfoModel, other$sponsoredTrackingInfoModel))
            return false;

        if (!Objects.equals(this.userAdId, other.userAdId)) {
            return false;
        }

        if (this.hotelRank != other.hotelRank) {
            return false;
        }

        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelListingClickEvent;
    }

    public int hashCode() {
        int result = super.hashCode();
        return result;
    }
}
