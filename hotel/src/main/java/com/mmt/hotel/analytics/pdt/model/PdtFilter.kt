package com.mmt.hotel.analytics.pdt.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.filterV2.model.response.FilterV2
import com.mmt.hotel.old.model.hotelListingRequest.advancedfilters.FilterConstants
import kotlinx.parcelize.Parcelize

@Parcelize
data class PdtFilter(
    @SerializedName("filter_group")
    val filterGroup: String,
    @SerializedName("filter_value")
    val filterValue: String? = null,
    @Transient
    val description: String? = null, //this is used in location filter tracking
    @SerializedName("range_filter")
    val rangeFilter: Boolean? = null,
    val start: Long? = null,
    val end: Long? = null,
    @SerializedName("filter_category")
    val filterCategory: String? = null,
    @SerializedName("filter_source")
    val source: String? = null
) : Parcelable {


    companion object {
        fun getPdtFilter(filter: FilterV2): PdtFilter {
            val filterRange = filter.filterRange
            return if (filterRange != null) {
                PdtFilter(filterGroup = filter.filterGroup,
                        rangeFilter = true,
                        start = filterRange.minValue.toLong(),
                        end = filterRange.maxValue.toLong(),
                filterCategory = filter.filterUiCategory, source = filter.source)
            } else if(filter.filterGroup == FilterConstants.LOCATION_FILTER_GROUP){
                PdtFilter(filter.filterGroup, filter.filterValue,filter.filterValue, filterCategory = filter.filterUiCategory, source = filter.source)
            } else {
                PdtFilter(filter.filterGroup, filter.filterValue, filterCategory = filter.filterUiCategory, source = filter.source)
            }
        }
    }

}