package com.mmt.hotel.analytics.pdt.model;

import static com.mmt.analytics.pdtclient.PDTAnalyticsKeys.POKUS_EXPERIMENT_DATA_V2;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.mmt.auth.login.model.Employee;
import com.mmt.hotel.analytics.pdt.events.HotelPageEntryEvent;
import com.mmt.hotel.common.constants.ExperimentsHotel;
import com.mmt.pokus.LOB;
import com.mmt.pokus.PokusV2Helper;
import com.mmt.pokus.model.PokusConfigData;
import com.pdt.pdtDataLogging.events.model.Event;
import com.pdt.pdtDataLogging.events.model.TrackingDataWrapper;

import java.util.Map;

public class HotelListingEntryEvent extends HotelPageEntryEvent {

    private SorterFilterEvent sorterFilterEvent;
    private TravelerDetail travelerDetail;

    public HotelListingEntryEvent(@NonNull String pageName, int eventType, long sessionStartTime,
                                  @NonNull String omnitureName,
                                  @NonNull String parentScreenName,
                                  String prevFunnelStep, String prevPageName) {
        super(pageName, eventType, sessionStartTime, omnitureName, parentScreenName,prevFunnelStep,prevPageName);
        sorterFilterEvent = new SorterFilterEvent("", pageName, eventType, omnitureName, parentScreenName, false);
        travelerDetail = new TravelerDetail();
    }

    @Override
    protected Event createPDTEvent() {
        Event pdtEvent = super.createPDTEvent();
        pdtEvent.getEventParam().putAll(sorterFilterEvent.createPDTEvent().getEventParam());
        pdtEvent.getEventParam().putAll(travelerDetail.getEventParams());
        updateExperimentDataWithHonouredEvents(pdtEvent);
        return pdtEvent;
    }

    private void updateExperimentDataWithHonouredEvents(Event pdtEvent){
        if(pdtEvent.getEventParam() != null && pdtEvent.getEventParam().containsKey(POKUS_EXPERIMENT_DATA_V2)){
            TrackingDataWrapper trackingDataWrapper = (TrackingDataWrapper) pdtEvent.getEventParam().get(POKUS_EXPERIMENT_DATA_V2);
            if(trackingDataWrapper != null){
                if(PokusV2Helper.getLobValue(LOB.HOTEL.getLobName()) !=null && PokusV2Helper.getLobValue(LOB.HOTEL.getLobName()).getMetadataValues() != null){
                    Map<String, PokusConfigData> mapOfMetaValues=  PokusV2Helper.getLobValue(LOB.HOTEL.getLobName()).getMetadataValues();
                    String filterBottomSheetKey = ExperimentsHotel.INSTANCE.getFilterBottomSheet().getKey();
                    String htlPricingExpNameKey = ExperimentsHotel.INSTANCE.getHtlPricingExpName().getKey();
                    if(mapOfMetaValues.get(filterBottomSheetKey) != null && !TextUtils.isEmpty(mapOfMetaValues.get(filterBottomSheetKey).getExpDetails())){
                        trackingDataWrapper.addToHonouredExpList(mapOfMetaValues.get(filterBottomSheetKey).getExpDetails());
                    }
                    if(mapOfMetaValues.get(htlPricingExpNameKey) != null && !TextUtils.isEmpty(mapOfMetaValues.get(htlPricingExpNameKey).getExpDetails())){
                        trackingDataWrapper.addToHonouredExpList(mapOfMetaValues.get(htlPricingExpNameKey).getExpDetails());
                    }
                }

            }
        }
    }


    public void fillTravelerDetail(Employee traveller) {
        String[] name = traveller.getName().split(" ");
        travelerDetail.setTravelerFName(name[0]);
        if(name.length > 1) {
            travelerDetail.setTravelerLName(name[name.length-1]);
        }
        travelerDetail.setTravelerTitle(traveller.getTitle());
        travelerDetail.setTravelerMobileNo(traveller.getPhoneNumber());
        travelerDetail.setTravelerEmailId(traveller.getBusinessEmailId());
    }

    public SorterFilterEvent getSorterFilterEvent() {
        return this.sorterFilterEvent;
    }

    public void setSorterFilterEvent(SorterFilterEvent sorterFilterEvent) {
        this.sorterFilterEvent = sorterFilterEvent;
    }

    public TravelerDetail getTravelerDetail() {
        return this.travelerDetail;
    }

    public String toString() {
        return "HotelListingEntryEvent(sorterFilterEvent=" + this.sorterFilterEvent + ")";
    }

    public boolean equals(final Object o) {
        if (o == this) return true;
        if (!(o instanceof HotelListingEntryEvent))
            return false;
        final HotelListingEntryEvent other = (HotelListingEntryEvent) o;
        if (!other.canEqual(this)) return false;
        if (!super.equals(o)) return false;
        final Object this$sorterFilterEvent = this.getSorterFilterEvent();
        final Object other$sorterFilterEvent = other.getSorterFilterEvent();
        if (this$sorterFilterEvent == null ? other$sorterFilterEvent != null : !this$sorterFilterEvent.equals(other$sorterFilterEvent))
            return false;
        final Object this$travelerDetail = this.getTravelerDetail();
        final Object other$travelerDetail = other.getTravelerDetail();
        if (this$travelerDetail == null ? other$travelerDetail != null : !this$travelerDetail.equals(other$travelerDetail))
            return false;
        return true;
    }

    protected boolean canEqual(final Object other) {
        return other instanceof HotelListingEntryEvent;
    }

    public int hashCode() {
        final int PRIME = 59;
        int result = super.hashCode();
        final Object $sorterFilterEvent = this.getSorterFilterEvent();
        result = result * PRIME + ($sorterFilterEvent == null ? 43 : $sorterFilterEvent.hashCode());
        final Object $travelerDetail = this.getTravelerDetail();
        result = result * PRIME + ($travelerDetail == null ? 43 : $travelerDetail.hashCode());
        return result;
    }
}

