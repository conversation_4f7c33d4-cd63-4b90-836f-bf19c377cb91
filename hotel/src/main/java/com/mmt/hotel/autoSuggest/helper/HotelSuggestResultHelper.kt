package com.mmt.hotel.autoSuggest.helper

import com.mmt.data.model.hotel.hotellocationpicker.response.SuggestResult
import com.mmt.core.util.isNotNullAndEmptyString
import com.mmt.core.constant.CoreConstants

object HotelSuggestResultHelper {

    @JvmStatic
    fun getTagAreaID(suggestResult: SuggestResult?): String? {
        var tagAreaID: String? = null
        isNotNullAndEmptyString(suggestResult?.id) { id ->
            val lastIndex = id.lastIndexOf(CoreConstants.UNDERSCORE)
            tagAreaID = if (lastIndex != -1 && lastIndex + 1 < id.length) {
                id.substring(lastIndex + 1)
            } else {
                id
            }
        }
        return tagAreaID
    }

}