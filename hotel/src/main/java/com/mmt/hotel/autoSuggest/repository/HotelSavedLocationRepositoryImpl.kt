package com.mmt.hotel.autoSuggest.repository

import com.mmt.core.constant.CoreHotelConstants.CORP_HTL_SAVED_LOCATION_CACHE_KEY
import com.mmt.core.util.LruCacheUtil
import com.mmt.data.model.network.NetworkConstants
import com.mmt.hotel.autoSuggest.helper.requestGenerator.CorpHotelAutoSuggestRequestGeneratorImpl.Companion.CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_URL
import com.mmt.hotel.autoSuggest.model.response.CorpAutoSuggestLocationResponse
import com.mmt.hotel.autoSuggest.model.response.CorpAutoSuggestResponse
import com.mmt.hotel.base.repository.HotelLRUCacheRepository
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.CONTENT_TYPE_JSON
import com.mmt.hotel.base.repository.HotelInMemCacheRepository
import com.mmt.network.NetworkHelper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class HotelSavedLocationRepositoryImpl @Inject constructor(): HotelSavedLocationRepository, HotelInMemCacheRepository() {

    private val lruCacheUtil = LruCacheUtil.getInstance()

    override fun fetchSavedLocationsData(): Flow<List<CorpAutoSuggestLocationResponse>> {
        val url = String.format(CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_URL, "\"\"", "\"\"", "\"\"")
        return makeGetRequestWithCaching<CorpAutoSuggestResponse>(
            url = url,
            countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN,
            prefixKey = LruCacheUtil.LRU_CACHE_KEYS.HOTEL_LOCUS_SAVED_LOCATIONS,
            cacheKey = CORP_HTL_SAVED_LOCATION_CACHE_KEY,
            headers = mutableMapOf(Pair(NetworkConstants.HEADER_CONTENT_TYPE, CONTENT_TYPE_JSON)))
            .map {
               it.networkResponse?.locationResponse ?: emptyList()
            }
    }


    override fun clearAutoSuggestCache() {
        lruCacheUtil.deleteEntryFromCacheAsync(LruCacheUtil.LRU_CACHE_KEYS.HOTEL_LOCUS_SAVED_LOCATIONS, CORP_HTL_SAVED_LOCATION_CACHE_KEY)
    }

}