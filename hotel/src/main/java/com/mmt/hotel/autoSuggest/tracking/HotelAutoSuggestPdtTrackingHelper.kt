package com.mmt.hotel.autoSuggest.tracking

import android.text.TextUtils
import com.mmt.analytics.ActivityTypeEvent
import com.mmt.analytics.AnalyticsSDK
import com.mmt.analytics.omnitureclient.Events
import com.mmt.hotel.analytics.pdt.HotelPdtKeys.LISTING_SEARCH_TEXT
import javax.inject.Inject

class HotelAutoSuggestPdtTrackingHelper @Inject constructor() {

    fun trackEvent(pdtEventName: String, eventPageName: Events,activityTypeEvent: ActivityTypeEvent) {
        val events = AnalyticsSDK.instance.getCommonGenericEvent(pdtEventName, eventPageName.value)
        AnalyticsSDK.instance.trackEvent(events, 1, activityTypeEvent)
    }

}