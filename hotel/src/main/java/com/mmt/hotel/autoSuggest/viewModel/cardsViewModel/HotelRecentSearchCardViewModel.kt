package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import android.graphics.drawable.Drawable
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.POPULAR_LOCATION_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.SAVED_LOCATION_CARD
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardsOrder.SAVED_LOCATON_CARD_ORDER
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardsOrder.RECENT_SEARCH_CARD_ORDER
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardsOrder.TRENDING_HOTEL_CARD_ORDER
import com.mmt.hotel.base.AbstractRecyclerItem

class HotelRecentSearchCardViewModel(val title: String, val recentItems: List<AbstractRecyclerItem>, private val viewType: Int): AutoSuggestStaticCardItem {

    fun getDrawable(): Drawable? {
        return when(viewType) {
            SAVED_LOCATION_CARD -> ResourceProvider.instance.getDrawable(R.drawable.popular_ic)
            POPULAR_LOCATION_CARD -> ResourceProvider.instance.getDrawable(R.drawable.trending_ic)
            else -> ResourceProvider.instance.getDrawable(R.drawable.recent_ic)
        }
    }

    override fun cardPriority(): Int {
        return when (viewType) {
            SAVED_LOCATION_CARD -> SAVED_LOCATON_CARD_ORDER.ordinal
            POPULAR_LOCATION_CARD -> TRENDING_HOTEL_CARD_ORDER.ordinal
            else -> RECENT_SEARCH_CARD_ORDER.ordinal
        }
    }

    override fun getItemType(): Int {
        return viewType
    }
}