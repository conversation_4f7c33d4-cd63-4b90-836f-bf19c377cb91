package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.DESTINATION_NOT_FOUND_ITEM
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.DESTINATION_NOT_FOUND
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent

class HotelDestinationNotFoundViewModel(
    val searchedQuery: String,
    val showDivider: Boolean,
    val eventStream: MutableLiveData<HotelEvent>
) : AbstractRecyclerItem, BaseObservable() {

    val isSelected = ObservableBoolean(false)
    val locationNotfoundClicked = MutableLiveData(false)

    fun onClicked() {
        eventStream.value = HotelEvent(DESTINATION_NOT_FOUND, searchedQuery)
        isSelected.set(true)
        locationNotfoundClicked.value = true
    }

    @Bindable("isSelected")
    fun getDisplayText(): String {
        return if(isSelected.get()) {
            ResourceProvider.instance.getString(R.string.htl_destination_not_found_selected_message, searchedQuery)
        } else {
            ResourceProvider.instance.getString(R.string.htl_destination_not_found_unselected_message)
        }
    }

    override fun getItemType(): Int {
        return DESTINATION_NOT_FOUND_ITEM
    }
}