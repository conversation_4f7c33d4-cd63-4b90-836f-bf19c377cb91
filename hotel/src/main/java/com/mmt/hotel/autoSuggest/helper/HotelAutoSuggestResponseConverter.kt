package com.mmt.hotel.autoSuggest.helper

import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.data.model.hotel.hotellocationpicker.response.SuggestResult
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.OTHER
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.POPULAR_LOCATION_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.RECENT_SEARCH_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.SAVED_LOCATION_CARD
import com.mmt.hotel.autoSuggest.constants.LocusResultType.LOCUS_RESULT_TYPE_NEARBY
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.model.response.CorpAutoSuggestLocationResponse
import com.mmt.hotel.autoSuggest.model.response.HotelAutoSuggestResponseItem
import com.mmt.hotel.autoSuggest.model.response.HotelPlaceIdResponse
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.RECENT_SEARCH_CLICKED
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.TRENDING_SEARCH_CLICKED
import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.*
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants.NEAR_ME_DISPLAY_TEXT
import com.mmt.hotel.common.constants.HotelsSearchRequestType
import com.mmt.hotel.common.model.response.TagSelectionForListingV2
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.filterV2.location.savelocation.constants.CorpSaveLocationType
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.shortStays.locationSelection.viewmodel.cardsViewModel.ShortStaysLocationSelectionCardViewModel
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class HotelAutoSuggestResponseConverter @Inject constructor(private val dataConverter: HotelAutoSuggestDataConverter) {

    fun createAutoSuggestCards(
        funnelType: FunnelType,
        queryString: String,
        items: List<HotelAutoSuggestResponseItem>,
        eventStream: MutableLiveData<HotelEvent>
    ): List<AbstractRecyclerItem> {
        val childItems = getGroupedChildItemsMap(funnelType, queryString, items, eventStream)
        val itemList = mutableListOf<AbstractRecyclerItem>()
        items.forEach { item ->
            val itemId = if (item.type.isNotNullAndEmpty()) {
                item.id
            } else EMPTY_STRING
            val key = item.groupID ?: itemId ?: item.placeId ?: EMPTY_STRING
            if (item.groupID.isNullOrEmpty()) {
                val dataWrapper = dataConverter.convertToDataWrapper(funnelType, queryString, item,itemList.size)
                childItems[key]?.let { childItems ->
                    itemList.add(createAutoSuggestCard(dataWrapper, childItems, eventStream))
                } ?: kotlin.run {
                    itemList.add(createAutoSuggestGroupedCard(dataWrapper, eventStream))
                }
            }
        }
        return itemList
    }

    fun createDestinationNotFoundCard(
        queryString: String,
        showDivider: Boolean,
        eventStream: MutableLiveData<HotelEvent>
    ): AbstractRecyclerItem {
        return HotelDestinationNotFoundViewModel(queryString, showDivider, eventStream)
    }

    fun convertToTagSelectionV2(response: HotelPlaceIdResponse, displayText: String): TagSelectionForListingV2 {
        return dataConverter.convertToTagSelectionV2(response, displayText)
    }

    fun convertToLocusDataWrapper(
        response: HotelPlaceIdResponse,
        isNearMe: Boolean,
        previousCreatedWrapper: LocusAutoSuggestDataWrapper?
    ): LocusAutoSuggestDataWrapper {
        return dataConverter.convertToDataWrapper(response).also {
            if (isNearMe) {
                appendNearMeData(it.suggestResult)
                it.displayText = NEAR_ME_DISPLAY_TEXT
                it.suggestResult?.sethType(ResourceProvider.instance.getString(R.string.htl_CURRENT_LOCATION))
            } else {
                it.displayText = if (previousCreatedWrapper?.displayText?.isNotNullAndEmpty() == true) {
                    previousCreatedWrapper.displayText
                } else {
                    it.suggestResult?.cityName.orEmpty()
                }
                it.subtext = previousCreatedWrapper?.subtext.orEmpty()
                it.suggestResult?.type = HotelsSearchRequestType.CUSTOM_LOCATION_SEARCH
                previousCreatedWrapper?.suggestResult?.gethType()?.let { htype ->
                    it.suggestResult?.sethType(htype)
                }
            }
            it.googlePlaceID = previousCreatedWrapper?.googlePlaceID
        }
    }

    fun convertToRecentSearchCard(
        items: List<LocusAutoSuggestDataWrapper>,
        eventStream: MutableLiveData<HotelEvent>
    ): List<AutoSuggestStaticCardItem> {
        val recentItems = items.map {
            HotelRecentSearchCardItemViewModel(it, RECENT_SEARCH_CLICKED, RECENT_SEARCH_CARD, eventStream)
        }
        val title = ResourceProvider.instance.getString(R.string.htl_IDS_STR_RECENT_SEARCHES_TEXT)
        return if (recentItems.isEmpty()) {
            emptyList()
        } else {
            listOf(HotelRecentSearchCardViewModel(title, recentItems, RECENT_SEARCH_CARD))
        }
    }

    fun convertToRecentSearchCardV2(
        items: List<SearchRequest>,
        eventStream: MutableLiveData<HotelEvent>
    ): List<AutoSuggestStaticCardItem> {
        val recentItems = items.map {
            HotelRecentSearchCardItemViewModelV2(it, RECENT_SEARCH_CLICKED, RECENT_SEARCH_CARD, eventStream)
        }
        val title = ResourceProvider.instance.getString(R.string.htl_IDS_STR_RECENT_SEARCHES_TEXT)
        return if (recentItems.isEmpty()) {
            emptyList()
        } else {
            listOf(HotelRecentSearchCardViewModel(title, recentItems, RECENT_SEARCH_CARD))
        }
    }

   suspend fun convertToSavedLocationCard(
       funnelType: FunnelType,
       items: List<CorpAutoSuggestLocationResponse>,
       eventStream: MutableLiveData<HotelEvent>
   ): List<AutoSuggestStaticCardItem> = withContext(Dispatchers.IO) {
         items.mapNotNull { autoSuggestLocationResponse->
            val cardItems = autoSuggestLocationResponse.location?.map {
                HotelRecentSearchCardItemViewModel(
                    dataConverter.convertToDataWrapper(funnelType, EMPTY_STRING, it),
                    TRENDING_SEARCH_CLICKED, getViewType(autoSuggestLocationResponse.groupId), eventStream)
            } ?: emptyList()
            if (cardItems.isEmpty()) {
                null
            } else {
                HotelRecentSearchCardViewModel(autoSuggestLocationResponse.groupName.orEmpty(), cardItems, SAVED_LOCATION_CARD)
            }
        }
    }

    private fun getViewType(groupId : String) : Int{
        return when(groupId){
            CorpSaveLocationType.SAVED_FOR_COMPANY, CorpSaveLocationType.SAVED_BY_USER -> SAVED_LOCATION_CARD
            else->  OTHER
        }
    }

   suspend fun convertToPopularLocationCard(
       funnelType: FunnelType,
       isAreaSearch: Boolean,
       locationName: String,
       items: List<HotelAutoSuggestResponseItem>,
       eventStream: MutableLiveData<HotelEvent>
   ): List<AutoSuggestStaticCardItem> = withContext(Dispatchers.IO){
       if (isAreaSearch) {
            createPopularLocationCardsForAreaSearch(funnelType, locationName, items, eventStream)
        } else {
            createPopularLocationStaticCards(funnelType, items, eventStream)
        }
    }

    fun createNearMeCard(eventStream: MutableLiveData<HotelEvent>): AutoSuggestStaticCardItem {
        return NearMeCardViewModel(eventStream)
    }

    fun createStaycationNearMeCard(eventStream: MutableLiveData<HotelEvent>): AutoSuggestStaticCardItem {
        return HotelUtil.getHtlPremiumNearMeCardInfo()?.let {
            StaycationNearMeCardViewModel(it, eventStream)
        } ?: createNearMeCard(eventStream)
    }

    private fun appendNearMeData(suggestResult: SuggestResult?) {
        suggestResult?.apply {
            id = LOCUS_RESULT_TYPE_NEARBY
            type = LOCUS_RESULT_TYPE_NEARBY
        }
    }

    private fun createPopularLocationCardsForAreaSearch(
        funnelType: FunnelType,
        locationName: String,
        items: List<HotelAutoSuggestResponseItem>,
        eventStream: MutableLiveData<HotelEvent>
    ): List<AutoSuggestStaticCardItem> {
        val cards = mutableListOf<AutoSuggestStaticCardItem>()
        if (locationName.isNotEmpty() && items.isNotEmpty()) {
            cards.add(PopularLocationHeaderViewModel(ResourceProvider.instance.getString(R.string.htl_popular_text, locationName)))
        }
        cards.addAll(items.map {
            val data = dataConverter.convertToDataWrapper(funnelType, EMPTY_STRING, it)
            HotelAutoSuggestGroupedItemViewModel(data, eventStream)
        })
        return cards
    }

    private fun createPopularLocationStaticCards(
        funnelType: FunnelType,
        items: List<HotelAutoSuggestResponseItem>,
        eventStream: MutableLiveData<HotelEvent>
    ): List<AutoSuggestStaticCardItem> {
        val cardItems = items.map {
            val data = dataConverter.convertToDataWrapper(funnelType, EMPTY_STRING, it)
            HotelRecentSearchCardItemViewModel(data, TRENDING_SEARCH_CLICKED, POPULAR_LOCATION_CARD, eventStream)
        }
        val title: String = ResourceProvider.instance.getString(R.string.htl_trending_locations)
        return if (cardItems.isEmpty()) {
            emptyList()
        } else {
            listOf(HotelRecentSearchCardViewModel(title, cardItems, POPULAR_LOCATION_CARD))
        }
    }

    /**
     * grouping cards based on groupId
     */
    private fun getGroupedChildItemsMap(
        funnelType: FunnelType,
        queryString: String,
        items: List<HotelAutoSuggestResponseItem>,
        eventStream: MutableLiveData<HotelEvent>
    ): LinkedHashMap<String, MutableList<AbstractRecyclerItem>> {
        val childItems = LinkedHashMap<String, MutableList<AbstractRecyclerItem>>()
        items.forEach {
            if (it.groupID.isNotNullAndEmpty()) {
                val children = childItems[it.groupID] ?: mutableListOf()
                val dataWrapper = dataConverter.convertToDataWrapper(funnelType, queryString, it, children.size)
                children.add(createAutoSuggestGroupedCard(dataWrapper, eventStream))
                childItems[it.groupID.orEmpty()] = children
            }
        }
        return childItems
    }

    private fun createAutoSuggestCard(
        data: LocusAutoSuggestDataWrapper,
        childItems: List<AbstractRecyclerItem>,
        eventStream: MutableLiveData<HotelEvent>
    ): HotelAutoSuggestCardViewModel {
        return HotelAutoSuggestCardViewModel(data, childItems, eventStream)
    }

    private fun createAutoSuggestGroupedCard(
        data: LocusAutoSuggestDataWrapper,
        eventStream: MutableLiveData<HotelEvent>
    ): HotelAutoSuggestGroupedItemViewModel {
        return HotelAutoSuggestGroupedItemViewModel(data, eventStream)
    }

    fun createLocationSelectionCards(
        funnelType: FunnelType,
        items: List<HotelAutoSuggestResponseItem>?,
        eventStream: MutableLiveData<HotelEvent>
    ): List<AbstractRecyclerItem> {
        val cardItems = items?.map {
            val data = dataConverter.convertToDataWrapper(funnelType, EMPTY_STRING, it)
            ShortStaysLocationSelectionCardViewModel(data, eventStream)
        }
        return cardItems?: emptyList()
    }

}