package com.mmt.hotel.autoSuggest.repository

import com.mmt.core.util.LruCacheUtil
import com.mmt.hotel.autoSuggest.db.HotelAutoSuggestRecentSearchRepository
import com.mmt.hotel.autoSuggest.model.response.HotelAutoSuggestResponseItem
import com.mmt.hotel.autoSuggest.model.response.HotelCustomLocationResponse
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.AUTO_SUGGEST_RECENT_SEARCH_MAX_ITEMS
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.base.repository.HotelInMemCacheRepository
import com.mmt.hotel.base.repository.HotelLRUCacheRepository
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.database.HotelLandingRecentSearchRepo
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.shortStays.locationSelection.models.response.ShortStaysLocationSelectionResponse
import com.mmt.hotel.shortStays.locationSelection.repo.ShortStaysLocationSelectionRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

open class HotelAutoSuggestRepositoryImpl @Inject constructor(): HotelInMemCacheRepository(), HotelAutoSuggestRepository, ShortStaysLocationSelectionRepository {

    companion object {
        val SHORT_STAYS_LOCATION_SELECTION_URL = LOCUS_DOMAIN_URL + "locations/web/v1/all/%s/details?type=%s"
    }

    override fun fetchAutoSuggestData(url: String): Flow<List<HotelAutoSuggestResponseItem>> {
        return if (url.isEmpty()) {
            emptyFlow()
        } else {
            makeGetRequest(url = url, countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN, funnel = if(HotelUtil.isPremiumExperience()) HotelConstants.PREMIUM_FUNNEL else null)
        }
    }

    override fun fetchCustomLocationData(url: String): Flow<HotelCustomLocationResponse> {
        return makeGetRequest(url = url, countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN, funnel = if(HotelUtil.isPremiumExperience()) HotelConstants.PREMIUM_FUNNEL else null)
    }

    override fun fetchRecentSearchData(funnelType: FunnelType): Flow<List<LocusAutoSuggestDataWrapper>> {
        return HotelAutoSuggestRecentSearchRepository.getRecentSearchRequest(AUTO_SUGGEST_RECENT_SEARCH_MAX_ITEMS, funnelType)
    }

    override fun fetchRecentSearchDataWithRoomAndPax(
        limit: Int,
        funnelType: FunnelType,
    ): Flow<List<SearchRequest>> {
        return HotelLandingRecentSearchRepo.getSavedResultBasedOnFunnelAndRegionRecent(limit, funnelType)
    }

    override suspend fun fetchShortStaysLocationSelection(): Flow<ShortStaysLocationSelectionResponse> {
        return makeGetRequestWithCaching<ShortStaysLocationSelectionResponse>(
            url = shortStaysLocationSelectionUrl(),
            countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN,
            prefixKey = LruCacheUtil.LRU_CACHE_KEYS.HOTEL_SHORTS_STAYS,
            cacheKey = HotelConstants.SHORT_STAYS_LOCATION_SELECTION_CACHE_KEY,
            headers = mutableMapOf(HotelConstants.API_KEY to HotelConstants.HOTSTORE_MAPI_API_KEY))
            .map { it.networkResponse?: ShortStaysLocationSelectionResponse() }
    }

    private fun shortStaysLocationSelectionUrl(): String {
        return SHORT_STAYS_LOCATION_SELECTION_URL.format(HotelConstants.SEARCH_ZONE, HotelConstants.ROAD_TRIP)
    }

}