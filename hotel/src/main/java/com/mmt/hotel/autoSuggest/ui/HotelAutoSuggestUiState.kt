package com.mmt.hotel.autoSuggest.ui

import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.HotelDestinationNotFoundViewModel

sealed class HotelAutoSuggestUiState {

    object LoadingState : HotelAutoSuggestUiState()

    data class SuccessState(var successState: HotelAutoSuggestFullScreenState) : HotelAutoSuggestUiState()

    data class ErrorState(var notFoundDestinationCard: HotelDestinationNotFoundViewModel?): HotelAutoSuggestUiState()
}

