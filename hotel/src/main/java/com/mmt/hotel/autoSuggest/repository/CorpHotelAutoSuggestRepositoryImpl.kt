package com.mmt.hotel.autoSuggest.repository

import com.mmt.data.model.network.NetworkConstants
import com.mmt.hotel.autoSuggest.model.response.CorpAutoSuggestResponse
import com.mmt.hotel.autoSuggest.model.response.HotelAutoSuggestResponseItem
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.HotelUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject


class CorpHotelAutoSuggestRepositoryImpl @Inject constructor() : HotelAutoSuggestRepositoryImpl() {

    override fun fetchAutoSuggestData(url: String): Flow<List<HotelAutoSuggestResponseItem>> {
        return if (url.isEmpty()) {
            emptyFlow()
        } else {
            makeGetRequest<CorpAutoSuggestResponse>(
                url = url, countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN,
                headerMap = mutableMapOf(
                    Pair(
                        NetworkConstants.HEADER_CONTENT_TYPE,
                        HotelConstants.CONTENT_TYPE_JSON
                    )
                ),
                funnel = if(HotelUtil.isPremiumExperience()) HotelConstants.PREMIUM_FUNNEL else null
            )
                .map { createExpectedFormat(it) }
        }
    }

    private suspend fun createExpectedFormat(response: CorpAutoSuggestResponse): List<HotelAutoSuggestResponseItem> =
        withContext(Dispatchers.IO) {
            response.locationResponse
                ?.filter { it.location?.isNotEmpty() == true }
                ?.flatMap { it.location!! } ?: emptyList()
        }
}