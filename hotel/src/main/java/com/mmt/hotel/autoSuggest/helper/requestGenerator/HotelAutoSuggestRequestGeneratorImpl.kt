package com.mmt.hotel.autoSuggest.helper.requestGenerator


import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.hotel.autoSuggest.constants.LocusRequestType
import com.mmt.hotel.base.repository.HotelBaseRepository
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.AUTOSUGGEST_SEARCH_GOOGLE_RESULTS_SUFFIX
import com.mmt.hotel.common.constants.HotelConstants.AUTOSUGGEST_COUNTRY_CLIC<PERSON>BLE_SUFFIX
import com.mmt.hotel.common.constants.HotelConstants.AUTOSUGGEST_EXP_SCORE_SUFFIX
import com.mmt.hotel.common.constants.HotelConstants.AUTOSUGGEST_UI_V2
import com.mmt.hotel.common.constants.HotelConstants.AUTOSUGGEST_UI_VERSION_SUFFIX
import com.mmt.hotel.common.constants.HotelConstants.AUTOSUGGEST_HIGHLIGHT_COUNTRY_NAME_SUFFIX
import com.mmt.hotel.common.constants.HotelConstants.AUTOSUGGEST_MASKED_PROPERTY_NAME_SUFFIX
import com.mmt.hotel.common.constants.HotelConstants.HOTEL_LAT_PARAM
import com.mmt.hotel.common.constants.HotelConstants.HOTEL_LONG_PARAM
import com.mmt.hotel.common.constants.HotelConstants.PLACE_ID_PARAM
import com.mmt.hotel.common.constants.HotelConstants.UTF_8
import com.mmt.hotel.common.extensions.toChar
import com.mmt.hotel.common.extensions.toInt
import com.mmt.hotel.common.util.*
import com.mmt.hotel.common.util.ExperimentUtil.maskPropertyName
import com.mmt.uikit.util.isNotNullAndEmpty
import java.net.URLEncoder
import javax.inject.Inject

const val AUTOSUGGEST_HIERARCHY_EXPERIMENT = "4"
open class HotelAutoSuggestRequestGeneratorImpl @Inject constructor(): HotelAutoSuggestRequestGenerator {

    companion object{
        val HOTEL_LOCUS_POPULAR_CITY_URL = HotelBaseRepository.LOCUS_DOMAIN_URL + "autosuggest/v5/search"
        val HOTEL_LOCUS_AUTOSUGGEST_URL = "$HOTEL_LOCUS_POPULAR_CITY_URL?q=%s&searchId=%s&requestId=%s&srcClient=ANDROID"
        val HOTEL_LOCUS_AUTOSUGGEST_HOTEL_SEARCH_URL = "$HOTEL_LOCUS_AUTOSUGGEST_URL&t=hotel&c=%s"
        val HOTEL_LOCUS_AUTOSUGGEST_AREA_SEARCH_URL = "$HOTEL_LOCUS_AUTOSUGGEST_URL&t=area,poi,gpoi&c=%s"
        val HOTEL_LOCUS_AUTOSUGGEST_AREA_SEARCH_URL_NO_CONTEXT = "$HOTEL_LOCUS_AUTOSUGGEST_URL&t=area,poi,gpoi"
        val HOTEL_LOCUS_AUTOSUGGEST_CONTEXT_SEARCH_URL = "$HOTEL_LOCUS_POPULAR_CITY_URL?q=%s&c=%s&searchId=%s&requestId=%s&t=city,area,poi,gpoi,hotel"
        val HOTEL_LOCUS_AUTOSUGGEST_CONTEXT_WITHOUT_CITY_URL = "$HOTEL_LOCUS_POPULAR_CITY_URL?q=%s&c=%s&searchId=%s&requestId=%s&t=area,poi,gpoi,hotel"
        val HOTEL_LOCUS_PLACE_ID_TO_LAT_LNG = HotelBaseRepository.LOCUS_DOMAIN_URL + "autosuggest/v5/get-place-details?"

    }
    lateinit var funnelType: FunnelType

    override fun generateAutoSuggestUrl(
        queryString: String,
        contextID: String,
        funnelType: FunnelType,
        requestType: LocusRequestType,
        searchId: String,
        requestId: String
    ): String {
        this.funnelType = funnelType
        return when (requestType) {
            LocusRequestType.HOTEL_SEARCH -> fetchHotelSearchUrl(queryString, contextID, funnelType,searchId,requestId)
            LocusRequestType.LANDING_SEARCH -> fetchLandingSearchUrl(queryString, funnelType,searchId,requestId)
            LocusRequestType.AREA_POI_SEARCH -> fetchAreaPoiSearchUrl(queryString, contextID, funnelType,searchId,requestId)
            LocusRequestType.AREA_POI_SEARCH_NO_CONTEXT -> fetchAreaPoiSearchUrlNoContext(queryString,searchId,requestId)
            LocusRequestType.GENERIC_SEARCH_WITH_CONTEXT -> fetchGenericSearchUrlWithContext(queryString, contextID,searchId,requestId)
            LocusRequestType.GENERIC_SEARCH_WITH_CONTEXT_WITHOUT_CITY -> fetchGenericSearchUrlWithContextWithoutCity(queryString, contextID,searchId,requestId)
        }
    }

    override fun fetchHotelSearchUrl(queryParam: String, contextID: String, funnelType: FunnelType,searchId: String,requestId: String): String {
        return if (contextID.isEmpty()) CoreConstants.EMPTY_STRING
        else String.format(HOTEL_LOCUS_AUTOSUGGEST_HOTEL_SEARCH_URL, queryParam,searchId,requestId,contextID) + getAltAccoParams(funnelType) + getGetawaysSuffix(funnelType) + getCommonSuffixes() + getAutoSuggestVersionSuffix() + getAutoSuggestVersion() + getAutoSuggestExpKeyAndValue()
    }

    override fun fetchLandingSearchUrl(queryParam: String, funnelType: FunnelType,searchId: String,requestId: String): String {
        return String.format(HOTEL_LOCUS_AUTOSUGGEST_URL, queryParam,searchId,requestId) + getAltAccoParams(funnelType) + getGetawaysSuffix(funnelType) +
                getCollectionSuffix(funnelType) + HotelConstants.EXP_PARAM + AUTOSUGGEST_HIERARCHY_EXPERIMENT + getCommonSuffixes() + getAutoSuggestVersionSuffix() + getAutoSuggestVersion() + getAutoSuggestExpKeyAndValue() + getSemanticSearchRequestParam()
    }

    private fun getAutoSuggestVersionSuffix(): String{
        return AUTOSUGGEST_UI_VERSION_SUFFIX
    }

    private fun getAutoSuggestVersion(): String{
        return AUTOSUGGEST_UI_V2
    }

    private fun getAutoSuggestExpKeyAndValue(): String{
        val expScore = getHtlAutoSuggestExpScore()
        if(expScore.isNotNullAndEmpty()){
            return AUTOSUGGEST_EXP_SCORE_SUFFIX + expScore
        }
        return CoreConstants.EMPTY_STRING
    }

    private fun getSemanticSearchRequestParam(): String {
        return if (ExperimentUtil.isSemanticSearchInAutoSuggest()) {
            HotelConstants.AUTOSUGGEST_SEMANTIC_SEARCH_PARAM
        } else {
            CoreConstants.EMPTY_STRING
        }
    }

    override fun fetchAreaPoiSearchUrl(queryParam: String, contextID: String, funnelType: FunnelType,searchId: String,requestId: String): String {
        return if (contextID.isEmpty()) CoreConstants.EMPTY_STRING
        else String.format(HOTEL_LOCUS_AUTOSUGGEST_AREA_SEARCH_URL, queryParam,searchId,requestId, contextID) + getAltAccoParams(funnelType) + getCommonSuffixes() + getAutoSuggestVersionSuffix() + getAutoSuggestVersion() + getAutoSuggestExpKeyAndValue()
    }

    override fun fetchAreaPoiSearchUrlNoContext(queryParam: String,searchId: String,requestId: String): String {
        return String.format(HOTEL_LOCUS_AUTOSUGGEST_AREA_SEARCH_URL_NO_CONTEXT, queryParam,searchId,requestId) + getCommonSuffixes() + getAutoSuggestVersionSuffix() + getAutoSuggestVersion() + getAutoSuggestExpKeyAndValue()
    }

    override fun fetchGenericSearchUrlWithContext(queryString: String, contextID: String,searchId: String,requestId: String): String {
        return String.format(HOTEL_LOCUS_AUTOSUGGEST_CONTEXT_SEARCH_URL, queryString,contextID,searchId,requestId) + getCommonSuffixes() + getAutoSuggestVersionSuffix() + getAutoSuggestVersion() + getAutoSuggestExpKeyAndValue()
    }

    override fun fetchGenericSearchUrlWithContextWithoutCity(queryString: String, contextID: String,searchId: String,requestId: String): String {
        return String.format(HOTEL_LOCUS_AUTOSUGGEST_CONTEXT_WITHOUT_CITY_URL, queryString, contextID,searchId,requestId) + getCommonSuffixes() + getAutoSuggestVersionSuffix() + getAutoSuggestVersion() + getAutoSuggestExpKeyAndValue()
    }

    override fun fetchCustomLocationUrl(googlePlaceId: String, latitude: String, longitude: String): String {
        return if (googlePlaceId.isEmpty()) {
            HOTEL_LOCUS_PLACE_ID_TO_LAT_LNG + HOTEL_LAT_PARAM + latitude + HOTEL_LONG_PARAM + longitude
        } else {
            HOTEL_LOCUS_PLACE_ID_TO_LAT_LNG + PLACE_ID_PARAM + URLEncoder.encode(googlePlaceId, UTF_8)
        }
    }

    private fun getAltAccoParams(funnelType: FunnelType): String {
        return if (funnelType == FunnelType.ALT_ACCO_FUNNEL) {
            HotelConstants.ALT_ACCO_AUTO_SUGGEST_PARAMS
        } else {
            CoreConstants.EMPTY_STRING
        }
    }


    protected fun getGetawaysSuffix(funnelType: FunnelType): String {
        return HotelConstants.GETAWAYS_SUFFIX_FALSE
    }

    protected fun getCollectionSuffix(funnelType: FunnelType): String {
        return if(funnelType == FunnelType.HOTEL_FUNNEL && !LoginUtil.isCorporateUser()) {
            HotelConstants.AUTOSUGGEST_COLLECTION_SUFFIX_TRUE
        } else {
            CoreConstants.EMPTY_STRING
        }
    }

    private fun getCommonSuffixes(): String {
        return getSGRSuffix() + getCCSuffix() + getHCNSuffix()+ getHCNSuffix() + getMPNSuffix()
    }

    /**
     * remove Google Places results from auto suggest or not
     *
     * value : sgr=t/f
     * */
    protected fun getSGRSuffix(): String {
        return AUTOSUGGEST_SEARCH_GOOGLE_RESULTS_SUFFIX + getAutoSuggestSGRExperiment().toChar()
    }

    /**
     * country should be clickable or not
     *
     * value : cc=1/0
     * */
    private fun getCCSuffix(): String {
        return AUTOSUGGEST_COUNTRY_CLICKABLE_SUFFIX + isCountryClickable().toInt()
    }

    /**
     * change country name to be more highlighted or not
     *
     * value : hcn=1/0
     * */
    private fun getHCNSuffix(): String {
        return AUTOSUGGEST_HIGHLIGHT_COUNTRY_NAME_SUFFIX
    }

    /**
     * property name should be masked or not
     *
     * value : mpn=t/f
     * */
    private fun getMPNSuffix(): String {
        return AUTOSUGGEST_MASKED_PROPERTY_NAME_SUFFIX + maskPropertyName().toChar()
    }

}