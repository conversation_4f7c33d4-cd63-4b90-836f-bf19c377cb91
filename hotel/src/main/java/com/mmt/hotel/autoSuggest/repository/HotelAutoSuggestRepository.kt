package com.mmt.hotel.autoSuggest.repository

import com.mmt.hotel.autoSuggest.model.response.HotelAutoSuggestResponseItem
import com.mmt.hotel.autoSuggest.model.response.HotelCustomLocationResponse
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.landingV3.model.request.SearchRequest
import kotlinx.coroutines.flow.Flow

interface HotelAutoSuggestRepository {
    fun fetchAutoSuggestData(url: String): Flow<List<HotelAutoSuggestResponseItem>>
    fun fetchCustomLocationData(url: String): Flow<HotelCustomLocationResponse>
    fun fetchRecentSearchData(funnelType: FunnelType): Flow<List<LocusAutoSuggestDataWrapper>>
    fun fetchRecentSearchDataWithRoomAndPax(limit: Int, funnelType: FunnelType): Flow<List<SearchRequest>>
}