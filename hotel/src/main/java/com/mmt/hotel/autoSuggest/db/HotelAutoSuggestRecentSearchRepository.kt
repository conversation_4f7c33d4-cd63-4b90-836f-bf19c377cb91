package com.mmt.hotel.autoSuggest.db

import android.annotation.SuppressLint
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.StringUtil
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.database.HotelLandingRecentSearchRepo
import com.mmt.hotel.database.LocusSearchRequestDatabase
import com.mmt.hotel.database.entity.LocusRecentSearchDBEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext

object HotelAutoSuggestRecentSearchRepository {

    private val database = LocusSearchRequestDatabase.getInstance()

    fun getRecentSearchRequest(
        limit: Int,
        funnelType: FunnelType
    ): Flow<List<LocusAutoSuggestDataWrapper>> {
        return flow {
            val savedLists = database.saveRecentRequest().getSavedResultForDestinationPicker(
                limit = limit,
                isCorporateUser = isCorporateUser(),
                funnelType = funnelType.name,
                funnelCountryCode = LoginUtils.getPreferredRegionCode()
            )
           emit(savedLists.mapNotNull { it.locusAutoSuggestDataWrapper })
        }.flowOn(Dispatchers.IO)
    }

    @SuppressLint("CheckResult")
   suspend fun saveRecentSearchInBackground(
        locusAutoSuggestDataWrapper: LocusAutoSuggestDataWrapper,
        funnelType: FunnelType
    ) {
        withContext(Dispatchers.IO) {
            val id = locusAutoSuggestDataWrapper.suggestResult?.id ?: ""
            val originalType = locusAutoSuggestDataWrapper.suggestResult?.originalType
            if (StringUtil.isNotNullAndEmpty(originalType)) {
                locusAutoSuggestDataWrapper.suggestResult?.type = originalType
            }
            if (StringUtil.isNullOrEmpty(id)) {
                return@withContext
            }
            val locusRecentSearchEntity = LocusRecentSearchDBEntity(
                itemID = id,
                createdAt = System.currentTimeMillis(),
                locusAutoSuggestDataWrapper = locusAutoSuggestDataWrapper,
                isCorporateUser = isCorporateUser(),
                searchType = locusAutoSuggestDataWrapper.suggestResult?.type ?: "",
                funnelType = funnelType.name,
                funnelContextValue = LoginUtils.getPreferredRegionCode()
            )
            database.saveRecentRequest().getRecentRequest(
                id = id,
                isCorporateUser = isCorporateUser(),
                funnelType = funnelType.name,
                funnelCountryCode = LoginUtils.getPreferredRegionCode()
            )?.let {
                database.saveRecentRequest().update(
                    id = id,
                    createdAt = System.currentTimeMillis(),
                    isCorporateUser = isCorporateUser(),
                    funnelType = funnelType.name,
                    funnelCountryCode = LoginUtils.getPreferredRegionCode()
                )
            } ?: database.saveRecentRequest().saveRecentSearchRequest(locusRecentSearchEntity)
        }
    }

    private fun isCorporateUser():Int {
        return if (LoginUtil.isCorporateUser()) 1 else 0
    }

}