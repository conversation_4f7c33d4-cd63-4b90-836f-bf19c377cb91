package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.ITEM_TYPE_AUTO_SUGGEST_CARD
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.AUTO_SUGGEST_CARD_CLICK
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestHelper
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.AUTO_SUGGEST_ITEM_CLICK
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper

class HotelAutoSuggestCardViewModel(val data: LocusAutoSuggestDataWrapper,
                                    val childItems: List<AbstractRecyclerItem>,
                                    private val eventStream: MutableLiveData<HotelEvent>)
    : AbstractRecyclerItem {
    val hotelAutoSuggestIconHelper = HotelAutoSuggestHelper()

    fun onItemClick(rank: Int?=null) {
        if (!data.isClickable) {
            return
        }
        eventStream.value = HotelEvent(AUTO_SUGGEST_CARD_CLICK, Triple(data, rank, AUTO_SUGGEST_ITEM_CLICK))
    }

    override fun getItemType(): Int {
        return ITEM_TYPE_AUTO_SUGGEST_CARD
    }

    fun getIconDrawable(): Int {
        return hotelAutoSuggestIconHelper.getAutoSuggestIconDrawable(data)
    }

    fun getAutoSuggestIconDrawableV2(): String?{
        return hotelAutoSuggestIconHelper.getAutoSuggestIconDrawableV2(data)
    }
}