package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.RECENT_SEARCH_CARD_ITEM_V2
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.landingV3.model.request.SearchRequest

class HotelRecentSearchCardItemViewModelV2(val data: SearchRequest,
                                           private val trackText: String,
                                           private val viewType: Int,
                                           private val eventStream: MutableLiveData<HotelEvent>)
    : AbstractRecyclerItem {
    override fun getItemType(): Int {
        return RECENT_SEARCH_CARD_ITEM_V2
    }

    fun onRecentSearchItemClick(searchRequest: SearchRequest) {
        eventStream.postValue(HotelEvent(HotelAutoSuggestCardEvents.RECENT_SEARCH_CARD_CLICK, searchRequest))
    }
}