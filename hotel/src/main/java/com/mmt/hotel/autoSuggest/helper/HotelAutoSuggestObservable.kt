package com.mmt.hotel.autoSuggest.helper

import androidx.databinding.*
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.constants.LocusRequestType
import com.mmt.hotel.autoSuggest.dataModel.AutoSuggestBundleData
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.ON_BACK_PRESSED
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.HANDLE_BACK_CLICK
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.HANDLE_CROSS_ICON_CLICK
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.HANDLE_SEARCH_AGAIN_CLICK
import com.mmt.hotel.autoSuggest.ui.HotelAutoSuggestFullScreenState
import com.mmt.hotel.autoSuggest.ui.ListSearchBoxState
import com.mmt.hotel.autoSuggest.ui.HotelAutoSuggestUiState
import com.mmt.hotel.autoSuggest.viewModel.HotelAutoSuggestFragmentViewModel
import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.HotelAutoSuggestGroupedItemViewModel
import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.HotelRecentSearchCardViewModel
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.util.isLandingUIV2
import javax.inject.Inject

class HotelAutoSuggestObservable @Inject constructor(private val data: AutoSuggestBundleData): BaseObservable() {

    private val resource = ResourceProvider.instance
    val liveAutoSuggestCards = MutableLiveData<ArrayList<Pair<Int, AbstractRecyclerItem>>> (arrayListOf())
    var staticCardIdx: Int = 0

    val staticCards = ObservableArrayList<AutoSuggestStaticCardItem>()

    val searchedQuery = ObservableField(EMPTY_STRING)
    val eventStream = MutableLiveData<HotelEvent>()
    val liveSearchedQuery = MutableLiveData<String>(EMPTY_STRING)
    private val loaderState = MutableLiveData(false)
    val liveStaticCards = MutableLiveData<ArrayList<Pair<Int, AutoSuggestStaticCardItem>>> (arrayListOf())

    private val searchBoxState =  MutableLiveData(ListSearchBoxState(liveSearchedQuery, loaderState, ResourceProvider.instance.getString(R.string.htl_autosuggest_search_box_hint), funnelType = data.funnelType))
    val viewState = MutableLiveData<HotelAutoSuggestUiState> (HotelAutoSuggestUiState.SuccessState(successState = getSuccessState()))

    private fun getSuccessState(): HotelAutoSuggestFullScreenState{
        return HotelAutoSuggestFullScreenState( staticCardsList = liveStaticCards, recommendedCardsList = liveAutoSuggestCards)
    }


    fun setLoadingViewState(isloading: Boolean, loaderType: HotelAutoSuggestFragmentViewModel.AutoSuggestLoaderType){
        if(isloading && loaderType == HotelAutoSuggestFragmentViewModel.AutoSuggestLoaderType.SHIMMER_LOADER){
            viewState.value = HotelAutoSuggestUiState.LoadingState
        }else if(loaderType == HotelAutoSuggestFragmentViewModel.AutoSuggestLoaderType.PROGRESS_BAR_LOADER){
            loaderState.value = isloading
        } else {
            viewState.value = HotelAutoSuggestUiState.SuccessState(successState = HotelAutoSuggestFullScreenState(
                staticCardsList = liveStaticCards, recommendedCardsList = liveAutoSuggestCards))
        }
    }

    fun getSearchBoxState(): LiveData<ListSearchBoxState>?{
        return searchBoxState
    }

    fun getSearchHint(): String {
        return when (data.requestType) {
            LocusRequestType.HOTEL_SEARCH -> resource.getString(R.string.htl_HOTEL_ONLY_PICKER_HINT)
            LocusRequestType.LANDING_SEARCH -> getSearchHintForLanding()
            LocusRequestType.AREA_POI_SEARCH, LocusRequestType.AREA_POI_SEARCH_NO_CONTEXT -> resource.getString(R.string.htl_HOTEL_AREA_PICKER_HINT)
            LocusRequestType.GENERIC_SEARCH_WITH_CONTEXT-> resource.getString(R.string.htl_HOTEL_LANDMARK_HOTEL_HINT)
            LocusRequestType.GENERIC_SEARCH_WITH_CONTEXT_WITHOUT_CITY -> resource.getString(R.string.htl_autosuggest_search_property_hint)
        }
    }

    fun getTitle(): String{
        val stringRes = when(data.requestType){
            LocusRequestType.HOTEL_SEARCH -> resource.getString(R.string.htl_title_property_name)
            LocusRequestType.LANDING_SEARCH -> getTitleForLanding()
            LocusRequestType.AREA_POI_SEARCH, LocusRequestType.AREA_POI_SEARCH_NO_CONTEXT -> resource.getString(R.string.htl_title_area_landmark_address)
            LocusRequestType.GENERIC_SEARCH_WITH_CONTEXT -> resource.getString(R.string.htl_title_landmark_area_property_name)
            LocusRequestType.GENERIC_SEARCH_WITH_CONTEXT_WITHOUT_CITY -> resource.getString(R.string.htl_autosuggest_search_property_title, data.locationName)
        }
        return stringRes
    }

    private fun getSearchHintForLanding(): String {
        return if (isLandingUIV2())
            resource.getString(R.string.htl_HOTEL_DESTINATION_PICKER_HINT_V2)
        else resource.getString(R.string.htl_HOTEL_DESTINATION_PICKER_HINT)
    }

    private fun getTitleForLanding(): String {
        return if (isLandingUIV2())
            resource.getString(R.string.htl_city_area_property_name_v2)
        else resource.getString(R.string.htl_city_area_landmark_property_name)
    }

    fun onClearClicked() {
        searchedQuery.set(EMPTY_STRING)
        liveSearchedQuery.value = EMPTY_STRING
        viewState.value = HotelAutoSuggestUiState.SuccessState(successState = getSuccessState())
    }

    /**
     * function to merge cards based on card priority
     */
    fun updateStaticCards(cards: List<AutoSuggestStaticCardItem>) {
        staticCards.addAll(cards)
        val items = staticCards.sortedBy {
            it.cardPriority()
        }
        staticCards.clear()
        staticCards.addAll(items)
        var staticCardList: ArrayList<Pair<Int, AutoSuggestStaticCardItem>> = arrayListOf()
        staticCardIdx = 0
        items.forEach{
            staticCardList.add(Pair(staticCardIdx, it))
            if(it is HotelRecentSearchCardViewModel){
                staticCardIdx += it.recentItems.size
            }else if(it is HotelAutoSuggestGroupedItemViewModel){
                staticCardIdx += 1
            }
        }
        liveStaticCards.value  = staticCardList
    }

    fun onBackPressed() {
        eventStream.value = HotelEvent(ON_BACK_PRESSED)
    }

    fun handleClickEvent(event: HotelEvent) {
        when(event.eventID){
            HANDLE_BACK_CLICK -> {
                onBackPressed()
            }
            HANDLE_CROSS_ICON_CLICK, HANDLE_SEARCH_AGAIN_CLICK -> {
                onClearClicked()
            }
        }
    }

}