package com.mmt.hotel.autoSuggest.repository

import com.google.gson.reflect.TypeToken
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.LruCacheUtil
import com.mmt.core.util.GsonUtils
import com.mmt.core.util.LOBS
import com.mmt.core.util.LocaleHelper
import com.mmt.hotel.autoSuggest.helper.requestGenerator.HotelAutoSuggestRequestGeneratorImpl
import com.mmt.hotel.autoSuggest.model.response.HotelAutoSuggestResponseItem
import com.mmt.hotel.base.repository.HotelInMemCacheRepository
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.API_KEY
import com.mmt.hotel.common.constants.HotelConstants.COUNTRY_CODE_UNKNOWN
import com.mmt.hotel.common.constants.HotelConstants.HOTSTORE_MAPI_API_KEY
import com.mmt.hotel.common.util.HotelUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import java.util.*
import javax.inject.Inject

class HotelPopularLocationsRepositoryImpl @Inject constructor(): HotelPopularLocationsRepository, HotelInMemCacheRepository() {

    override fun fetchPopularLocationsData(
        isAreaSearch: Boolean, contextID: String, searchId: String, requestId: String
    ): Flow<List<HotelAutoSuggestResponseItem>> {
        val prefixKey = generatePopularLocationsPrefixKey(isAreaSearch)
        val url = fetchPopularLocationsUrl(isAreaSearch, contextID,searchId ,requestId)
        val cacheKey = generateCacheKey(isAreaSearch, contextID)
        val queryParams = HashMap<String, String>()
        if(HotelUtil.isPremiumExperience()) {
            queryParams[PARAM_FUNNEL] = HotelConstants.PREMIUM_FUNNEL
        }
        return makeGetRequestWithCaching<List<HotelAutoSuggestResponseItem>>(url = url,
            cacheKey = cacheKey, prefixKey = prefixKey, countryCode = COUNTRY_CODE_UNKNOWN,
            headers = mutableMapOf(API_KEY to HOTSTORE_MAPI_API_KEY), queryParams = queryParams)
            .map {
                val gson = GsonUtils.getInstance()
                val jsonString = gson.serializeToJson(it.networkResponse)
                gson.deserializeJSON<List<HotelAutoSuggestResponseItem>>(jsonString, TypeToken.getParameterized(List::class.java, HotelAutoSuggestResponseItem::class.java))
            }.flowOn(Dispatchers.Default)
    }

    private fun fetchPopularLocationsUrl(isAreaSearch: Boolean, contextID: String,searchId: String,requestId: String): String {
        return if (isAreaSearch) {
            String.format(
                HotelAutoSuggestRequestGeneratorImpl.HOTEL_LOCUS_AUTOSUGGEST_AREA_SEARCH_URL,
                CoreConstants.EMPTY_STRING,searchId,requestId, contextID.lowercase(Locale.US))
        } else {
            HotelAutoSuggestRequestGeneratorImpl.HOTEL_LOCUS_POPULAR_CITY_URL
        }
    }

    private fun generatePopularLocationsPrefixKey(isAreaSearch: Boolean): String {
        return if (isAreaSearch) {
            LruCacheUtil.LRU_CACHE_KEYS.HOTEL_AUTO_SUGGEST_POPULAR_LOCATIONS_IN_FILTER
        } else {
            LruCacheUtil.LRU_CACHE_KEYS.HOTEL_AUTO_SUGGEST_POPULAR_CITIES
        }
    }

    private fun generateCacheKey(isAreaSearch: Boolean, contextID: String): String {
        return if (isAreaSearch) {
            contextID.lowercase(Locale.US) + LocaleHelper.getLobWiseLanguage(LOBS.HOTEL.lob).lowercase(Locale.US)
        } else {
            LoginUtils.getPreferredRegionCode().lowercase(Locale.US) + LocaleHelper.getLobWiseLanguage(LOBS.HOTEL.lob).lowercase(Locale.US)
        }
    }

}