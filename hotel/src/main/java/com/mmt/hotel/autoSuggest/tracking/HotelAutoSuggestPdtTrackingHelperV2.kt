package com.mmt.hotel.autoSuggest.tracking

import com.mmt.analytics.AnalyticsSDK
import com.mmt.analytics.omnitureclient.Events
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.StringUtil.EMPTY_STRING
import com.mmt.hotel.analytics.pdtv2.HotelPdtEventBuilder
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Helper
import com.mmt.hotel.analytics.pdtv2.model.ContentDetailItem
import com.mmt.hotel.analytics.pdtv2.model.Position
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil
import com.gommt.logger.LogUtils
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants
import com.pdt.eagleEye.constants.EventName
import com.pdt.eagleEye.constants.EventType
import javax.inject.Inject

class HotelAutoSuggestPdtTrackingHelperV2 @Inject constructor() {

    companion object {
        const val TAG = "HotelAutoSuggestPdtTrackingHelperV2"
        const val NEAR_ME_CATEGORY_ID = "near_me"
        const val STAYCATION_NEAR_ME_CATEGORY_ID = "staycation_near_me"
    }
    fun trackClick(
        item: LocusAutoSuggestDataWrapper,
        trackText: String,
        source: String?,
        basePageNameEvent: Events,
        typedText: String? = CoreConstants.EMPTY_STRING,
        rank: Integer? = null,
        requestId: String,
        searchId: String,
        funnelType: FunnelType,
        userSearchData: UserSearchData?,
        funnelStep: HotelPdtV2Constants.FunnelStep

    ) {
        try {
            val commonEventBuilder = createAutoSuggestPageEvent(
                EventName.CONTENT_CLICKED,
                EventType.ACTION,
                requestId =  requestId,
                searchId = searchId,
                funnelType,
                userSearchData,
                funnelStep
            )
           val trackTextValue = trackText.replace("_clicked","")
            commonEventBuilder.eventValue(HotelPdtV2Constants.HotelEventValues.auto_suggest_item_clicked.name)
                 commonEventBuilder.contentDetails(createComponentDetal(item, rank, typedText,basePageNameEvent, source,trackTextValue))
            commonEventBuilder.let { eventBuilder ->
                val event = eventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }

        } catch (e: Exception) {
            LogUtils.error(TAG, "trackClickPDT", e)
            }
    }

    fun trackNearMeClickPDT(
        requestId: String,
        searchId: String,
        funnelType: FunnelType,
        userSearchData: UserSearchData?,
        funnelStep: HotelPdtV2Constants.FunnelStep

    ) {
        try {
            val commonEventBuilder = createAutoSuggestPageEvent(
                EventName.CONTENT_CLICKED,
                EventType.ACTION,
                requestId =  requestId,
                searchId = searchId,
                funnelType,
                userSearchData,
                funnelStep
            )
            commonEventBuilder?.eventValue(HotelPdtV2Constants.HotelEventValues.auto_suggest_item_clicked.name)
            commonEventBuilder?.contentDetails(listOf(ContentDetailItem("","",categoryId= NEAR_ME_CATEGORY_ID))
            )
            commonEventBuilder?.let { eventBuilder ->
                val event = eventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }

        } catch (e: Exception) {
            LogUtils.error(TAG, "trackClickPDT", e)
        }
    }

    fun trackStaycationNearMeClickPDT(
        requestId: String,
        searchId: String,
        funnelType: FunnelType,
        userSearchData: UserSearchData?,
        funnelStep: HotelPdtV2Constants.FunnelStep

    ) {
        try {
            val commonEventBuilder = createAutoSuggestPageEvent(
                EventName.CONTENT_CLICKED,
                EventType.ACTION,
                requestId =  requestId,
                searchId = searchId,
                funnelType,
                userSearchData,
                funnelStep
            )
            commonEventBuilder?.eventValue(HotelPdtV2Constants.HotelEventValues.auto_suggest_item_clicked.name)
            commonEventBuilder?.contentDetails(listOf(ContentDetailItem("","",categoryId= STAYCATION_NEAR_ME_CATEGORY_ID))
            )
            commonEventBuilder?.let { eventBuilder ->
                val event = eventBuilder.build()
                AnalyticsSDK.instance.trackEvent(event)
            }

        } catch (e: Exception) {
            LogUtils.error(TAG, "trackClickPDT", e)
        }
    }

    private fun createComponentDetal(item: LocusAutoSuggestDataWrapper, rank: Integer?, typedText: String?, basePageNameEvent: Events, source: String?, trackText: String): List<ContentDetailItem>? {
        val type = if (item.semanticPayload != null) {
            HotelAutoSuggestTrackingConstants.AUTOSUGGEST_CARD_TYPE_SEMANTIC
        } else {
            item.suggestResult?.type
        }
       val contentDetail= ContentDetailItem(id = item.suggestResult?.id ?: EMPTY_STRING , type =  type ?: EMPTY_STRING , name = item.suggestResult?.name,
            categoryName= trackText,
            categoryId = item.groupID ?: EMPTY_STRING,
            position = Position(v = rank?.toInt())
        )
        return  listOf(contentDetail)
    }


    private fun createAutoSuggestPageEvent(
        eventName: String,
        eventType: String,
        requestId: String? = null,
        searchId: String? = null,
        funnelType: FunnelType,
        userSearchData: UserSearchData?,
        funnelStep: HotelPdtV2Constants.FunnelStep
    ): HotelPdtEventBuilder {
        val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
            eventName = eventName,
            eventType = eventType,
            pageName = HotelConstants.PAGE_NAME_AUTO_SUGGEST,
            userSearchData = userSearchData?: UserSearchData(funnelSrc = HotelUtil.getFunnel(funnelType).funnelValue, id = null),
            baseTrackingData = null,
            requestId= requestId,
            funnelStep = funnelStep,

        )
        commonEventBuilder.autoSuggestSearchId = searchId
        commonEventBuilder.searchContext = HotelPdtV2Helper.createSearchContext(userSearchData?: UserSearchData(funnelSrc = HotelUtil.getFunnel(funnelType).funnelValue, id = null))
        return commonEventBuilder
    }
}