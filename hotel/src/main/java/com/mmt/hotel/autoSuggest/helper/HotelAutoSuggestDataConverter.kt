package com.mmt.hotel.autoSuggest.helper

import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.ifNotNull
import com.mmt.data.model.hotel.LatLngBounds
import com.mmt.data.model.hotel.hotellocationpicker.response.Location
import com.mmt.data.model.hotel.hotellocationpicker.response.SuggestResult
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.constants.LocusResultType
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.model.response.AutoSuggestFilterItem
import com.mmt.hotel.autoSuggest.model.response.HotelAutoSuggestResponseItem
import com.mmt.hotel.autoSuggest.model.response.HotelPlaceIdResponse
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants.AREA_SEARCH_CATEGORY_ID
import com.mmt.hotel.common.constants.HotelConstants.AREA_SEARCH_TAG_ID
import com.mmt.hotel.common.constants.HotelConstants.AREA_SEARCH_TAG_TYPE_ID
import com.mmt.hotel.common.constants.HotelsSearchRequestType
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.AREA_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.CITY_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.CUSTOM_LOCATION_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.GPOI_SEARCH
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.POI_SEARCH
import com.mmt.hotel.common.extensions.orIfNullOrBlank
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.response.TagSelectionForListingV2
import com.mmt.hotel.filterV2.tracking.HotelFilterTrackingConstants
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.listingV2.model.request.HotelTagsV2
import com.mmt.hotel.listingV2.model.response.moblanding.LatLongBoundsV2
import com.mmt.hotel.listingV2.model.response.moblanding.LocationMetaInfo
import com.mmt.hotel.listingV2.model.response.moblanding.MatchMakerTagV2
import com.mmt.uikit.util.isNotNullAndEmpty
import java.util.*
import javax.inject.Inject

class HotelAutoSuggestDataConverter @Inject constructor() {

    fun convertToDataWrapper(
        funnelType: FunnelType,
        queryString: String,
        item: HotelAutoSuggestResponseItem,
        position: Int = 0
    ): LocusAutoSuggestDataWrapper {
        val wrapperItem = LocusAutoSuggestDataWrapper()
        val resultType = item.type.orEmpty()
        wrapperItem.apply {
            displayText = item.semanticPayload?.queryText?:item.displayName.orEmpty()
            subtext = item.subtext.orIfNullOrBlank((if (item.displayName != item.countryName) item.countryName.orEmpty() else CoreConstants.EMPTY_STRING))
            groupName = item.groupName.orEmpty()
            tag = item.tag
            label = item.label
            iconType = item.iconType
            isClickable = item.isClickable ?: true
            groupID = item.groupID
            name = item.name
            this.queryParam = queryString
            cityTagline = item.tagline
            distanceText = item.distanceText
            this.position = position
            wrapperItem.calendarCriteria = item.calendarCriteria
            this.centerLocation = item.centerLocation
            attribute = item.attribute
            hotelAttributes = item.hotelAttributes
            this.timezoneInfo = item.timezoneInfo
            this.filter = item.filters?.firstOrNull()?.copy(
                cityCode = item.cityCode,
                context = item.context
            )
            this.maskedPropertyName = item.maskedPropertyName
            this.semanticPayload = item.semanticPayload
        }

        if (resultType.isNotNullAndEmpty()) {
            wrapperItem.apply {
                locationContext = item.locationType?:item.type
                locationID = item.id.orEmpty()
                itemID = item.id.orEmpty()
                displayType = item.htype
                    ?: item.type?.let {
                        it.substring(0, 1).uppercase(Locale.US) + it.substring(1).lowercase(Locale.US)
                    }.orEmpty()
                item.context?.let {
                    when (resultType.uppercase(Locale.US)) {
                        LocusResultType.LOCUS_RESULT_TYPE_GPOI,
                        LocusResultType.LOCUS_RESULT_TYPE_POI,
                        LocusResultType.LOCUS_RESULT_TYPE_HOTEL,
                        LocusResultType.LOCUS_RESULT_TYPE_AREA -> {
                            locationContext = it.type?.lowercase(Locale.US)
                            locationID = it.id
                        }
                    }
                    if(LocusResultType.LOCUS_RESULT_TYPE_CITY == resultType.uppercase(Locale.US)){
                        wrapperItem.locusContextID = item.id
                        wrapperItem.locusContextType = item.locationType?:item.type
                        locusContextName = item.name
                    } else{
                        wrapperItem.locusContextID = it.id
                        wrapperItem.locusContextType = it.type?.lowercase(Locale.US)
                        locusContextName = it.name
                    }
                }
                suggestResult = convertToSuggestResultModel(item)
                propertyDetails = getPropertyCountString(funnelType, item)
            }
        } else {
            var itemId = item.placeId.orEmpty()
            item.semanticPayload?.let {
                itemId = item.id.orEmpty()
            }
            wrapperItem.googlePlaceID = item.placeId.orEmpty()
            wrapperItem.suggestResult = SuggestResult().also {
                it.id = itemId
                it.type = getSuggestResultType(LocusResultType.LOCUS_RESULT_TYPE_GOOGLE)
                it.name = item.displayName
                it.sethType(item.htype)
            }
        }
        return wrapperItem
    }

    fun convertToDataWrapper(response: HotelPlaceIdResponse): LocusAutoSuggestDataWrapper {
        val data = LocusAutoSuggestDataWrapper()
        data.suggestResult = SuggestResult().also {
            response.details?.search?.let { searchData ->
                data.locationID = searchData.id
                data.locationContext = searchData.type
                data.locusContextID = searchData.id
                data.locusContextType = searchData.type
                data.locusContextName = searchData.name
                it.cityName = searchData.name
            }
            response.details?.country?.let { countryData ->
                it.countryCode = countryData.id
                it.countryName = countryData.name
                it.latitude = response.location?.latitude ?: 0.0
                it.longitude = response.location?.longitude ?: 0.0
            }
            it.id = response.placeid
            it.bounds = response.bounds?.toV1()
        }
        return data
    }

    fun convertToTagSelectionV2(response: HotelPlaceIdResponse, displayText: String): TagSelectionForListingV2 {
        return response.run {
            TagSelectionForListingV2(tagDescription = displayText, tagId = hashCode(), isLocation = true,
                latitude = location?.latitude ?: 0.0, longitude = location?.longitude ?: 0.0,
                bounds = bounds, placeId = placeid, locId = details?.search?.id.orEmpty(),
                locType = details?.search?.type.orEmpty(), autoSuggestType = CUSTOM_LOCATION_SEARCH)
        }
    }

    fun convertToTagSelectionV2(filterItem: AutoSuggestFilterItem, isSelected: Boolean) : TagSelectionForListingV2 {

        val bounds = if (filterItem.northEastLatLng != null && filterItem.southWestLatLng != null) {
            val northEastCords = Location(filterItem.northEastLatLng.lat.toString(), filterItem.northEastLatLng.lng.toString())
            val southWestCords = Location(filterItem.southWestLatLng.lat.toString(), filterItem.southWestLatLng.lng.toString())
            LatLngBounds(northEastCords, southWestCords).toV2()
        } else null

        val showableEntities = mutableSetOf<String>()
        filterItem.cityCode?.let { showableEntities.add(it) }
        filterItem.context?.id?.let { showableEntities.add(it) }

        val areaPoiTag =  TagSelectionForListingV2(
            isPrimary = false, tagDescription = filterItem.name,
            tagId = AREA_SEARCH_TAG_ID, placeId = filterItem.id,
            latitude = filterItem.centreLatLng?.lat ?: 0.0,
            longitude = filterItem.centreLatLng?.lng ?: 0.0,
            bounds = bounds, locId = filterItem.context?.id,
            tagAreaId = filterItem.id, locType = filterItem.context?.type?.lowercase(Locale.US),
            autoSuggestType = getSuggestResultType(filterItem.type.orEmpty()),
            cityCode = filterItem.id, tagTypeId = AREA_SEARCH_TAG_TYPE_ID,
            isSelected = isSelected, isCity = false, showableEntities = showableEntities,
            trackText = HotelFilterTrackingConstants.LANDING_FILTER,
            trackSource = HotelFilterTrackingConstants.LANDING_FILTER,
            persuasionText = filterItem.text
        )

        return if (filterItem.type.equals(POI_SEARCH, true)) {
            areaPoiTag.copy(isLocation = true)
        } else if (filterItem.type.equals(AREA_SEARCH, true)) {
            areaPoiTag.copy(isLocation = false, categoryId = AREA_SEARCH_CATEGORY_ID)
        } else areaPoiTag
    }

    fun convertToTagSelectionV2(data: LocusAutoSuggestDataWrapper): TagSelectionForListingV2 {
        val isLocation = data.suggestResult?.type?.let { suggestType ->
            POI_SEARCH.equals(suggestType, true)
            || CUSTOM_LOCATION_SEARCH.equals(suggestType, true)
            || GPOI_SEARCH.equals(suggestType, true)
        } ?: false
        val id = data.suggestResult?.id
        return data.run {
            TagSelectionForListingV2(tagDescription = name ?: displayText, tagId = AREA_SEARCH_TAG_ID,
                latitude = suggestResult?.latitude ?: 0.0, longitude = suggestResult?.longitude ?: 0.0,
                bounds = suggestResult?.bounds?.toV2(), placeId = id, locId = locationID, tagAreaId = id,
                locType = locationContext, autoSuggestType = suggestResult?.type ?: displayType,
                cityCode = itemID, isLocation = isLocation, tagTypeId = AREA_SEARCH_TAG_TYPE_ID,
                isSelected = true, isCity = suggestResult?.type == CITY_SEARCH, label = label,
                showableEntities = getShowableEntitiesFromAutoSuggest(this).toMutableSet())
        }
    }

    fun isLatLongValid(suggestResult: SuggestResult?): Boolean {
        if(suggestResult == null){
            return false
        }
        return suggestResult.latitude != 0.0 && suggestResult.longitude != 0.0
    }

    /**
     * used in listing map for poi search
     */
    fun getTagSelectionForPoiSearch(item: LocusAutoSuggestDataWrapper): TagSelectionForListingV2? {
        val suggestResult = item.suggestResult ?: return null
        val tagAreaID = HotelSuggestResultHelper.getTagAreaID(suggestResult)
        val showAbleEntities: MutableSet<String> = HashSet()
        item.locusContextID?.let {
            showAbleEntities.add(it)
        }
        return convertToTagSelectionV2(item).copy(
            tagDescription = getTagDesc(suggestResult),
            tagId = tagAreaID?.hashCode() ?: 0,
            placeId = tagAreaID,
            source = suggestResult.source,
            cityCode = item.locationID,
            showableEntities = showAbleEntities,
            poiCategory = item.displayType)
    }

    /**
     * used in listing map for area search
     */
    fun getTagSelectionForAreaSearch(item: LocusAutoSuggestDataWrapper): TagSelectionForListingV2? {
        val suggestResult = item.suggestResult ?: return null
        val showAbleEntities: MutableSet<String> = HashSet()
        item.locationID?.let {
            showAbleEntities.add(it)
        }
        return convertToTagSelectionForAreaSearch(item).copy(
            tagDescription = getTagDesc(suggestResult),
            tagAreaId = HotelSuggestResultHelper.getTagAreaID(suggestResult),
            source = suggestResult.source,
            cityCode = suggestResult.cityCode,
            showableEntities = showAbleEntities
        )
    }


    private fun getTagDesc(suggestResult: SuggestResult): String? {
        return suggestResult.name ?: suggestResult.cityName
    }

    fun convertToTagSelectionForAreaSearch(data: LocusAutoSuggestDataWrapper): TagSelectionForListingV2 {
        return convertToTagSelectionV2(data).copy(categoryId = AREA_SEARCH_CATEGORY_ID,
            tagTypeId = AREA_SEARCH_TAG_TYPE_ID, tagId = AREA_SEARCH_TAG_ID)
    }

    fun LatLngBounds.toV2(): LatLongBoundsV2 {
        return LatLongBoundsV2(ne = com.mmt.hotel.listingV2.model.response.hotels.Location(ne.latitude.toDouble(), ne.longitude.toDouble()),
            sw = com.mmt.hotel.listingV2.model.response.hotels.Location(sw.latitude.toDouble(), sw.longitude.toDouble()))
    }

    fun LatLongBoundsV2.toV1(): LatLngBounds {
        return LatLngBounds(
            Location(ne?.latitude.toString(), ne?.longitude.toString()),
            Location(sw?.latitude.toString(), sw?.longitude.toString())
        )
    }

    /**
     * this method is used to convert LocusAutoSuggestDataWrapper into HotelTagsV2
     */
    fun convertToHotelTag(item: LocusAutoSuggestDataWrapper): HotelTagsV2? {
        item.suggestResult?.let {
            if (HotelsSearchRequestType.HOTEL_SEARCH.equals(it.type, true)) {
                return HotelTagsV2(
                    name = it.displayText,
                    hotelId = it.id, hotLat = it.latitude,
                    hotLng = it.longitude
                )
            }
        }
        return null
    }

    fun convertToMatchMakerTag(item: LocusAutoSuggestDataWrapper): MatchMakerTagV2 {
        val poiCategory = item.suggestResult?.type?.let { type ->
            if (type == POI_SEARCH) item.displayType else null
        }
        val latLongBounds: LatLongBoundsV2? = item.suggestResult?.bounds?.let {
            LatLongBoundsV2(
                ne = com.mmt.hotel.listingV2.model.response.hotels.Location(
                    it.ne.latitude.toDoubleOrNull() ?: 0.0,
                    it.ne.longitude.toDoubleOrNull() ?: 0.0
                ),
                sw = com.mmt.hotel.listingV2.model.response.hotels.Location(
                    it.sw.latitude.toDoubleOrNull() ?: 0.0,
                    it.sw.longitude.toDoubleOrNull() ?: 0.0
                )
            )
        }
        val matchmakerLocation = if (item.suggestResult?.latitude != 0.0 && item.suggestResult?.longitude != 0.0) {
            com.mmt.hotel.listingV2.model.response.hotels.Location(
                item.suggestResult?.latitude ?: 0.0,
                item.suggestResult?.longitude ?: 0.0
            )
        } else null
        return MatchMakerTagV2(
            desc = item.name ?: item.displayText, id = AREA_SEARCH_TAG_ID, typeId = AREA_SEARCH_TAG_TYPE_ID,
            areaIdStr = item.itemID, isPoi = item.suggestResult?.type == POI_SEARCH,
            isCity = CITY_SEARCH == item.suggestResult?.type, isSelected = true, locId = item.locationID,
            locType = item.locationContext, showableEntities = getShowableEntitiesFromAutoSuggest(item),
            poiCategory = poiCategory, filterMetaInfo = LocationMetaInfo(bbox = latLongBounds, centre = matchmakerLocation)
        )
    }

    fun convertToLocusDataWrapper(searchRequest: SearchRequest): LocusAutoSuggestDataWrapper {
        val userSearchData = searchRequest.userSearchData
        val suggestResult = if (userSearchData != null) {
            getSuggestResult(userSearchData, searchRequest.latitude ?: 0.0, searchRequest.longitude ?: 0.0)
        } else null
        return LocusAutoSuggestDataWrapper(
            displayText = userSearchData?.displayName.orEmpty(),
            subtext = userSearchData?.subtext.orIfNullOrBlank(
                (if (userSearchData?.displayName != userSearchData?.country) userSearchData?.country.orEmpty() else CoreConstants.EMPTY_STRING)
            ),
            locationID = userSearchData?.locationId,
            locationContext = userSearchData?.locationType,
            locusContextID = userSearchData?.locationId,
            locusContextType = userSearchData?.locationType,
            locusContextName = userSearchData?.locusLocationName,
            suggestResult = suggestResult,
            itemID = suggestResult?.id,
            timezoneInfo = searchRequest.userSearchData?.timezoneInfo
        )
    }

    fun getSuggestResult(userSearchData: UserSearchData, latitude: Double = 0.0, longitude: Double = 0.0): SuggestResult {
        return SuggestResult().also {
            it.location = Location(latitude.toString(), longitude.toString())
            it.latitude = latitude
            it.longitude = longitude
            it.cityCode = userSearchData.locationId
            it.cityName = userSearchData.locationName
            it.countryCode = userSearchData.countryCode
            it.countryName = userSearchData.country
            it.displayText = userSearchData.displayName
            it.name = userSearchData.displayName
            it.type = userSearchData.searchType
            it.originalCityName = userSearchData.locationName
            it.originalLocusType = userSearchData.locationType
            it.originalType = userSearchData.searchType
            it.sethType(userSearchData.hType)
            it.id = userSearchData.id
        }
    }

    private fun getShowableEntitiesFromAutoSuggest(item: LocusAutoSuggestDataWrapper): List<String> {
        val showableEntities = mutableSetOf<String>()
        item.suggestResult?.cityCode?.let {
            showableEntities.add(it)
        }
        item.locationID?.let {
            showableEntities.add(it)
        }
        return showableEntities.toList()
    }

    private fun convertToSuggestResultModel(item: HotelAutoSuggestResponseItem): SuggestResult {
        val suggestResult = SuggestResult()
        suggestResult.apply {
            id = item.id
            cityName = item.cityName ?: item.name ?: item.displayName
            cityCode = item.cityCode
            countryCode = item.countryCode
            countryName = item.countryName
            displayText = item.displayName
            name = item.displayName
            sethType(item.htype)
        }

        ifNotNull(item.northEastLatLng, item.southWestLatLng) { northEastLatLng, southWestLatLng ->
            val northEastCords = Location(northEastLatLng.lat.toString(), northEastLatLng.lng.toString())
            val southWestCords = Location(southWestLatLng.lat.toString(), southWestLatLng.lng.toString())
            suggestResult.bounds = LatLngBounds(northEastCords, southWestCords)
        }

        ifNotNull(item.centreLatLng) { latlong ->
            suggestResult.location = Location(latlong.lat.toString(), latlong.lng.toString())
            suggestResult.also {
                it.latitude = latlong.lat
                it.longitude = latlong.lng
            }
        }
        suggestResult.type = getSuggestResultType(item.type.orEmpty())
        return suggestResult
    }

    private fun getPropertyCountString(funnelType: FunnelType, item: HotelAutoSuggestResponseItem): String {

        if (item.htlCount > 0) {
            if(funnelType == FunnelType.ALT_ACCO_FUNNEL){
                return ResourceProvider.instance.getString(R.string.htl_Properties, item.altAccoCount)
            }else {
                return ResourceProvider.instance.getString(R.string.htl_Properties, item.htlCount)
            }
        }
        return CoreConstants.EMPTY_STRING
    }

    fun getSuggestResultType(locusSearchType: String): String {
        return when (locusSearchType.uppercase(Locale.US)) {
            LocusResultType.LOCUS_RESULT_TYPE_REGION -> HotelsSearchRequestType.REGION_SEARCH
            LocusResultType.LOCUS_RESULT_TYPE_AREA -> HotelsSearchRequestType.AREA_SEARCH
            LocusResultType.LOCUS_RESULT_TYPE_NEARBY -> HotelsSearchRequestType.GPS_NEARBY_SEARCH
            LocusResultType.LOCUS_RESULT_TYPE_GOOGLE -> CUSTOM_LOCATION_SEARCH
            LocusResultType.LOCUS_RESULT_TYPE_GPOI -> GPOI_SEARCH
            LocusResultType.LOCUS_RESULT_TYPE_POI -> POI_SEARCH
            LocusResultType.LOCUS_RESULT_TYPE_HOTEL -> HotelsSearchRequestType.HOTEL_SEARCH
            LocusResultType.LOCUS_RESULT_TYPE_COUNTRY -> HotelsSearchRequestType.COUNTRY_SEARCH
            else -> CITY_SEARCH
        }
    }

}