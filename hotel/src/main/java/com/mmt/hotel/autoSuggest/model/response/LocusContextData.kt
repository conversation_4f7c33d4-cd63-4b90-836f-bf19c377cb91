package com.mmt.hotel.autoSuggest.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Created by <PERSON><PERSON><PERSON> on 25,May,2021
 */
@Parcelize
data class LocusContextData(
        @SerializedName("type", alternate = ["locusType"])
        var type: String? = null,
        @SerializedName("id", alternate = ["locusId"])
        var id: String? = null,
        @SerializedName("name", alternate = ["locusName"])
        var name: String? = null
) : Parcelable