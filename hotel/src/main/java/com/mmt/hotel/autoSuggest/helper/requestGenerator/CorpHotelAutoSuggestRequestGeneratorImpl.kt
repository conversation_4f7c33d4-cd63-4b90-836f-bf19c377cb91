package com.mmt.hotel.autoSuggest.helper.requestGenerator

import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.base.repository.HotelBaseRepository
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.util.HtlUrlConstants.CORPORATE_CCB_BASE_URL
import javax.inject.Inject

class CorpHotelAutoSuggestRequestGeneratorImpl @Inject constructor(): HotelAutoSuggestRequestGeneratorImpl() {

    companion object {
        const val CORPORATE_LOCATION_BASE_URL = "${CORPORATE_CCB_BASE_URL}/location"
        const val CORPORATE_HOTEL_AUTO_SUGGEST_PATH = "$CORPORATE_LOCATION_BASE_URL/autosuggest"
        const val CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_URL = "$CORPORATE_HOTEL_AUTO_SUGGEST_PATH?q=%s&searchId=%s&requestId=%s&srcClient=ANDROID"
        const val CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_AREA_SEARCH_URL = "$CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_URL&t=area,poi,gpoi&c=%s"
        const val CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_AREA_SEARCH_URL_NO_CONTEXT = "$CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_URL&t=area,poi,gpoi"
        const val CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_CONTEXT_SEARCH_URL = "$CORPORATE_HOTEL_AUTO_SUGGEST_PATH?q=%s&c=%s&searchId=%s&requestId=%s&t=city,area,poi,gpoi,hotel"
        const val CORPORATE_HOTEL_LOCUS_AUTOSUGGEST_CONTEXT_WITHOUT_CITY_URL = "$CORPORATE_HOTEL_AUTO_SUGGEST_PATH?q=%s&c=%s&searchId=%s&requestId=%s&t=area,poi,gpoi,hotel"

    }

    override fun fetchLandingSearchUrl(
        queryParam: String,
        funnelType: FunnelType,
        searchId: String,
        requestId: String
    ): String {
        return String.format(CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_URL, queryParam,searchId,requestId) +
                getGetawaysSuffix(funnelType) + getCollectionSuffix(funnelType)  + HotelConstants.EXP_PARAM + AUTOSUGGEST_HIERARCHY_EXPERIMENT + getSGRSuffix()
    }

    override fun fetchAreaPoiSearchUrl(
        queryParam: String,
        contextID: String,
        funnelType: FunnelType,
        searchId: String,
        requestId: String
    ): String {
        return if (contextID.isEmpty()) CoreConstants.EMPTY_STRING
        else String.format(CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_AREA_SEARCH_URL, queryParam,searchId,requestId, contextID) + getSGRSuffix()
    }

    override fun fetchAreaPoiSearchUrlNoContext(
        queryParam: String,
        searchId: String,
        requestId: String
    ): String {
        return String.format(CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_AREA_SEARCH_URL_NO_CONTEXT, queryParam,searchId,requestId) + getSGRSuffix()
    }

    override fun fetchGenericSearchUrlWithContext(
        queryString: String,
        contextID: String,
        searchId: String,
        requestId: String
    ): String {
        return String.format(CORPORATE_HOTEL_LOCUS_AUTO_SUGGEST_CONTEXT_SEARCH_URL, queryString, contextID,searchId,requestId) + getSGRSuffix()
    }

    override fun fetchGenericSearchUrlWithContextWithoutCity(
        queryString: String,
        contextID: String,
        searchId: String,
        requestId: String
    ): String {
        return String.format(CORPORATE_HOTEL_LOCUS_AUTOSUGGEST_CONTEXT_WITHOUT_CITY_URL, queryString, contextID,searchId,requestId) + getSGRSuffix()
    }

}