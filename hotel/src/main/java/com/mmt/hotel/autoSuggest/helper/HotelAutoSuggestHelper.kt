package com.mmt.hotel.autoSuggest.helper

import com.google.gson.reflect.TypeToken
import com.mmt.core.util.GsonUtils
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.constants.LocusResultType
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelsSearchRequestType
import com.mmt.hotel.common.constants.SharedPrefKeys
import java.util.HashMap

class HotelAutoSuggestHelper {

    val imgUrlHtypeMap = HotelSharedPrefUtil.instance.getString(SharedPrefKeys.KEY_HTYPE_LOCATION_ICON_MAP)
    fun getAutoSuggestIconDrawable(data: LocusAutoSuggestDataWrapper):Int {
        return when (data.suggestResult?.type) {
            HotelsSearchRequestType.HOTEL_SEARCH -> {
                when (data.displayType.uppercase()) {
                    LocusResultType.LOCUS_RESULT_TYPE_HOTEL, LocusResultType.LOCUS_RESULT_TYPE_RESORT -> R.drawable.auto_suggest_hotels_drawable
                    else -> R.drawable.auto_suggest_homestay_drawable
                }
            }
            else -> {
                when (data.displayType.uppercase()) {
                    LocusResultType.LOCUS_RESULT_TYPE_STATE -> R.drawable.auto_suggest_state_drawable
                    LocusResultType.LOCUS_RESULT_TYPE_COUNTRY -> R.drawable.auto_suggest_country_drawable
                    LocusResultType.LOCUS_RESULT_TYPE_CITY -> R.drawable.auto_suggest_city_drawable
                    else -> R.drawable.getaways_icon
                }
            }
        }
    }

    fun getAutoSuggestIconDrawableV2(data: LocusAutoSuggestDataWrapper): String? {
        val type = object : TypeToken<HashMap<String, String>?>() {}.type
        val htypeIconMap = GsonUtils.getInstance().deserializeJSON<HashMap<String, String>>(imgUrlHtypeMap, type)
        val iconMap = htypeIconMap?.entries?.associate { (key, value) -> key.lowercase() to value }
        val imageUrl = (data.iconType?:data.displayType)?.lowercase()?.let { iconMap?.get(it) }
        return imageUrl
    }

}