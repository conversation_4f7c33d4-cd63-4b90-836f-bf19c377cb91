package com.mmt.hotel.autoSuggest.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.common.constants.HotelConstants.INDIA_TIMEZONE_MILLISECONDS
import com.mmt.hotel.common.constants.HotelConstants.INDIA_TIMEZONE_SECONDS
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.old.model.hotelListingResponse.HotelCalendarCriteria
import com.mmt.hotel.semantic.payload.SemanticPayload
import kotlinx.parcelize.Parcelize
import java.util.TimeZone

@Parcelize
data class HotelAutoSuggestResponseItem(
    @SerializedName("id", alternate = ["locationId"])
    var id: String? = null,
    var countryCode: String? = null,
    var countryName: String? = null,
    var cityCode: String? = null,
    var cityName: String? = null,
    var type: String? = null,
    var locationType: String?=null,
    @SerializedName("ne")
    var northEastLatLng: LocusLatLng? = null,
    @SerializedName("sw")
    var southWestLatLng: LocusLatLng? = null,
    @SerializedName("centre")
    var centreLatLng: LocusLatLng? = null,
    var tag: Tag? = null,
    var displayName: String? = null,
    var subtext: String?= null,
    var name: String? = null,
    var isListable: Boolean? = null,
    @SerializedName("isClickable")
    var isClickable: Boolean? = null,
    var context: LocusContextData? = null,
    @SerializedName("gid")
    var groupID: String? = null,
    @SerializedName("groupName")
    var groupName: String? = null,
    @SerializedName("label")
    var label: String? = null,
    @SerializedName("place_id")
    var placeId: String? = null,
    var htlCount: Int=0,
    var altAccoCount: Int = 0,
    var maxAltAccoPropertyCount: Int=0,
    var maxAltAccoPropertyType:String? = null,
    var htype:String? = null,
    var tagline: String? = null,
    var distanceText: String? = null,
    @SerializedName("prewarmInfo")
    var calendarCriteria: HotelCalendarCriteria? = null, // will be available in case of hotel search,
    val centerLocation: CenterLocation?=null,
    val attribute: String? = null,
    val hotelAttributes: List<String>? = null,
    @SerializedName("timezone")
    val timezoneInfo: HotelierTimezoneInfo? = null,
    @SerializedName("subLocations")
    val filters: List<AutoSuggestFilterItem>? = null,
    @SerializedName("maskedPropertyName")
    val maskedPropertyName: Boolean? = null,
    @SerializedName("iconType")
    val iconType: String? = null,
    @SerializedName("semanticPayload")
    val semanticPayload: SemanticPayload? = null
) : Parcelable

@Parcelize
data class AutoSuggestFilterItem(
    val id: String? = null,
    val text: String?= null,
    val name: String? = null,
    val type: String? = null,
    @SerializedName("ne")
    val northEastLatLng: LocusLatLng? = null,
    @SerializedName("sw")
    val southWestLatLng: LocusLatLng? = null,
    @SerializedName("centre")
    val centreLatLng: LocusLatLng? = null,
    val cityCode: String? = null, //Client end use only, not expected in response
    val context: LocusContextData? = null //Client end use only, not expected in response
): Parcelable

@Parcelize
data class Tag(
    @SerializedName("tagText")
    val tagText: String? = null,
    @SerializedName("tagColor")
    val tagColor: String? = null
): Parcelable

@Parcelize
data class HotelierTimezoneInfo(
    @SerializedName("name")
    val name: String? = null,
    @SerializedName("offset")
    val offsetInSeconds: Long?,
    @SerializedName("dstOffset")
    val dstOffsetInSeconds: Long?,
): Parcelable {

    fun getTimezoneOffsetInMilliseconds(funnelSrc: Int) = if (ExperimentUtil.isTimezoneCheckEnabled(funnelSrc) && offsetInSeconds != null) offsetInSeconds.times(1000) else INDIA_TIMEZONE_MILLISECONDS

    fun getDstOffsetInMilliseconds(funnelSrc: Int) = if (ExperimentUtil.isTimezoneCheckEnabled(funnelSrc) && dstOffsetInSeconds != null) dstOffsetInSeconds.times(1000) else 0L

    companion object {
        fun getDefault() = HotelierTimezoneInfo(name = "", offsetInSeconds = INDIA_TIMEZONE_SECONDS, dstOffsetInSeconds = 0L)
    }
}