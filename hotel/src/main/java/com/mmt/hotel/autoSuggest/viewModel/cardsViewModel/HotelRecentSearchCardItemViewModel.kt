package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.POPULAR_LOCATION_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.RECENT_SEARCH_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.RECENT_SEARCH_CARD_ITEM
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.SAVED_LOCATION_CARD
import com.mmt.hotel.autoSuggest.constants.LocusResultType.LOCUS_RESULT_TYPE_COUNTRY
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.AUTO_SUGGEST_CARD_CLICK
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestHelper
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.uikit.util.isNotNullAndEmpty

class HotelRecentSearchCardItemViewModel(val data: LocusAutoSuggestDataWrapper,
                                         private val trackText: String,
                                         private val viewType: Int,
                                         private val eventStream: MutableLiveData<HotelEvent>)
    : AbstractRecyclerItem {

    val hotelAutoSuggestIconHelper = HotelAutoSuggestHelper()
    fun getDrawable(): Int{
        return when (viewType){
            POPULAR_LOCATION_CARD -> R.drawable.default_location_icon
            RECENT_SEARCH_CARD -> R.drawable.recent_search_icon_v2
            SAVED_LOCATION_CARD -> R.drawable.htl_ic_saved_by_company
            else -> R.drawable.default_location_icon
        }
    }

    fun onClick(rank:Int?=null) {
        eventStream.value = HotelEvent(AUTO_SUGGEST_CARD_CLICK, Triple(data, rank, trackText))
    }

    fun getAutoSuggestIconDrawableV2(): String?{
        return hotelAutoSuggestIconHelper.getAutoSuggestIconDrawableV2(data)
    }
    override fun getItemType(): Int {
        return RECENT_SEARCH_CARD_ITEM
    }
}