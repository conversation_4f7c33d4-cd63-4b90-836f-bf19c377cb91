package com.mmt.hotel.autoSuggest.di

import androidx.fragment.app.Fragment
import com.mmt.analytics.omnitureclient.Events
import com.mmt.core.user.auth.LoginUtil
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.autoSuggest.constants.LocusRequestType
import com.mmt.hotel.autoSuggest.dataModel.AutoSuggestBundleData
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestDataConverter
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestObservable
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestResponseConverter
import com.mmt.hotel.autoSuggest.helper.requestGenerator.CorpHotelAutoSuggestRequestGeneratorImpl
import com.mmt.hotel.autoSuggest.helper.requestGenerator.HotelAutoSuggestRequestGenerator
import com.mmt.hotel.autoSuggest.helper.requestGenerator.HotelAutoSuggestRequestGeneratorImpl
import com.mmt.hotel.autoSuggest.repository.*
import com.mmt.hotel.autoSuggest.ui.HotelAutoSuggestActivity
import com.mmt.hotel.autoSuggest.ui.HotelAutoSuggestComposeFragment
import com.mmt.hotel.autoSuggest.viewModel.HotelAutoSuggestFragmentViewModel
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil
import com.pdt.utils.PDTConstants
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class HotelAutoSuggestFragmentModule {

    @Provides
    @IntoMap
    @ViewModelKey(HotelAutoSuggestFragmentViewModel::class)
    fun provideAutoSuggestViewModel(data: AutoSuggestBundleData,
                                    repository: HotelAutoSuggestRepository,
                                    savedLocationRepository: HotelSavedLocationRepository,
                                    popularLocationsRepository: HotelPopularLocationsRepository,
                                    generator: HotelAutoSuggestRequestGenerator,
                                    converter: HotelAutoSuggestResponseConverter,
                                    observable: HotelAutoSuggestObservable
    ): HotelViewModel {
        return HotelAutoSuggestFragmentViewModel(
            data, repository, savedLocationRepository,
            popularLocationsRepository, generator, converter, observable
        )
    }

    @Provides
    fun providesHotelAutoSuggestResponseConverter(converter: HotelAutoSuggestDataConverter): HotelAutoSuggestResponseConverter {
        return HotelAutoSuggestResponseConverter(converter)
    }

    @Provides
    fun providesHotelSavedLocationRepository(): HotelSavedLocationRepository {
        return HotelSavedLocationRepositoryImpl()
    }

    @Provides
    fun providesHotelPopularLocationRepository(): HotelPopularLocationsRepository {
        return HotelPopularLocationsRepositoryImpl()
    }

    @Provides
    fun providesAutoSuggestRepository(): HotelAutoSuggestRepository {
        if(LoginUtil.isCorporateUser()) {
            return CorpHotelAutoSuggestRepositoryImpl()
        }
        return HotelAutoSuggestRepositoryImpl()
    }

    @Provides
    fun providesAutoSuggestRequestGenerator(): HotelAutoSuggestRequestGenerator {
        if(LoginUtil.isCorporateUser()) {
            return CorpHotelAutoSuggestRequestGeneratorImpl()
        }
        return HotelAutoSuggestRequestGeneratorImpl()
    }

    @Provides
    fun provideAutoSuggestBundleData(fragment:Fragment):AutoSuggestBundleData {
        if(fragment is HotelAutoSuggestComposeFragment){
        return fragment.arguments?.getParcelable(HotelAutoSuggestActivity.AUTO_SUGGEST_BUNDLE_DATA) ?:getDummyAutoSuggestBundleData()
        }
        return getDummyAutoSuggestBundleData()
    }
    /*
        AutoSuggestBundleData cannot be null in arguments , created to just handle null checks
     */
    private fun getDummyAutoSuggestBundleData():AutoSuggestBundleData {
        return AutoSuggestBundleData(requestType = LocusRequestType.LANDING_SEARCH,
            funnelType = FunnelType.HOTEL_FUNNEL, basePageNameEvent = Events.EVENT_HOTEL_DOM_LANDING_PAGE, userSearchData =  UserSearchData(funnelSrc =HotelFunnel.HOTEL.funnelValue, id = null))
    }
}