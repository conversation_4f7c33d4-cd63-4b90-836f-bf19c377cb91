package com.mmt.hotel.autoSuggest.ui

import android.content.Intent
import android.os.Bundle
import com.mmt.core.util.executeIfCast
import com.mmt.data.model.util.DeviceUtils
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.dataModel.AutoSuggestBundleData
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.STAYCATION_NEAR_ME_CARD_CLICK
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.RECENT_SEARCH_CARD_CLICK
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.EXIT_WITH_LOCUS_DATA_WRAPPER
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.EXIT_WITH_TAG_SELECTION_V2
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.viewModel.HotelAutoSuggestActivityViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.activity.HotelActivity
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.common.extensions.openFragment
import com.mmt.hotel.common.model.response.TagSelectionForListingV2
import com.mmt.hotel.databinding.HtlActivityAutoSuggestBinding
import com.mmt.hotel.landingV3.model.request.SearchRequest
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HotelAutoSuggestActivity :
    HotelActivity<HotelAutoSuggestActivityViewModel, HtlActivityAutoSuggestBinding>(){

    private val data by lazy { intent.extras?.getParcelable<AutoSuggestBundleData>(AUTO_SUGGEST_BUNDLE_DATA)!! }

    override fun createViewModel() = getViewModel<HotelAutoSuggestActivityViewModel>()

    override fun createEventSharedViewModel() = getViewModel<HotelEventSharedViewModel>()

    override fun getLayoutId() = R.layout.htl_activity_auto_suggest


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        openAutoSuggestFragment()
    }

    private fun openAutoSuggestFragment() {
        supportFragmentManager.openFragment(
            fragment = HotelAutoSuggestComposeFragment.newInstance(data),
            container = R.id.container, tag = HotelAutoSuggestComposeFragment.TAG
        )
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            EXIT_WITH_TAG_SELECTION_V2 -> finishWithTagSelectionV2(event.data as TagSelectionForListingV2)
            EXIT_WITH_LOCUS_DATA_WRAPPER -> finishWithLocusDataWrapper(event.data as LocusAutoSuggestDataWrapper)
            HotelAutoSuggestCardEvents.ON_BACK_PRESSED -> {
                closeScreen()
            }
            RECENT_SEARCH_CARD_CLICK -> {
                event.data.executeIfCast<SearchRequest> {
                    finishWithRecentSearchData(this)
                }
            }

            STAYCATION_NEAR_ME_CARD_CLICK -> {
                closeScreen()
            }
        }
    }

    private fun closeScreen() {
        DeviceUtils.hideSoftKeyBoard(this, TAG)
        finish()
    }

    private fun finishWithTagSelectionV2(data: TagSelectionForListingV2) {
        DeviceUtils.hideSoftKeyBoard(this, TAG)
        val intent = Intent()
        intent.putExtra(KEY_TAG_SELECTION_V2, data)
        setResult(RESULT_OK, intent)
        finish()
    }

    private fun finishWithLocusDataWrapper(data: LocusAutoSuggestDataWrapper) {
        DeviceUtils.hideSoftKeyBoard(this, TAG)
        val intent = Intent()
        intent.putExtra(KEY_LOCUS_DATA_WRAPPER, data)
        setResult(RESULT_OK, intent)
        finish()
    }

    private fun finishWithRecentSearchData(data: SearchRequest) {
        DeviceUtils.hideSoftKeyBoard(this, TAG)
        val intent = Intent()
        intent.putExtra(RECENT_SEARCH_DATA_WRAPPER, data)
        setResult(RECENT_SEARCH_RESULT, intent)
        finish()
    }

    companion object {
        const val TAG = "HotelAutoSuggestActivity"
        const val AUTO_SUGGEST_BUNDLE_DATA = "AutoSuggestData"
        const val KEY_TAG_SELECTION_V2 = HotelAutoSuggestComposeFragment.KEY_TAG_SELECTION_V2
        const val KEY_LOCUS_DATA_WRAPPER = HotelAutoSuggestComposeFragment.KEY_LOCUS_DATA_WRAPPER
        const val RECENT_SEARCH_DATA_WRAPPER = "RECENT_SEARCH_DATA_WRAPPER"
        const val AUTO_SUGGEST_REQUEST_CODE = 120
        const val RECENT_SEARCH_RESULT = 1201
    }

}