package com.mmt.hotel.autoSuggest.helper.requestGenerator

import com.mmt.hotel.autoSuggest.constants.LocusRequestType
import com.mmt.hotel.common.constants.FunnelType

interface HotelAutoSuggestRequestGenerator {
    fun generateAutoSuggestUrl(queryString: String, contextID: String, funnelType: FunnelType, requestType: LocusRequestType,searchId: String,requestId: String): String
    fun fetchHotelSearchUrl(queryParam: String, contextID: String, funnelType: FunnelType,searchId: String,requestId: String): String
    fun fetchLandingSearchUrl(queryParam: String, funnelType: FunnelType,searchId: String,requestId: String): String
    fun fetchAreaPoiSearchUrl(queryParam: String, contextID: String, funnelType: FunnelType,searchId: String,requestId: String): String
    fun fetchAreaPoiSearchUrlNoContext(queryParam: String,searchId: String,requestId: String): String
    fun fetchGenericSearchUrlWithContext(queryString: String, contextID: String,searchId: String,requestId: String): String
    fun fetchGenericSearchUrlWithContextWithoutCity(queryString: String, contextID: String,searchId: String,requestId: String): String
    fun fetchCustomLocationUrl(googlePlaceId: String, latitude: String, longitude: String): String
}