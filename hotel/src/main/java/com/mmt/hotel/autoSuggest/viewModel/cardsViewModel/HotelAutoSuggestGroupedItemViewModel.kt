package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.AUTO_SUGGEST_CARD_CLICK
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardsOrder
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestHelper
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.AUTO_SUGGEST_ITEM_CLICK
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.constants.HotelsSearchRequestType.Companion.SEARCH_ZONE
import com.mmt.uikit.util.isNotNullAndEmpty

class HotelAutoSuggestGroupedItemViewModel(
    val data: LocusAutoSuggestDataWrapper,
    private val eventStream: MutableLiveData<HotelEvent>
)
    : AutoSuggestStaticCardItem {

    val getawayItem = ObservableBoolean()
    val showSecondaryText = ObservableBoolean()
    val showDotSeparator = ObservableBoolean()
    val hotelAutoSuggestIconHelper = HotelAutoSuggestHelper()

    init {
        getawayItem.set(data.locusContextType == SEARCH_ZONE)
        showSecondaryText.set(data.cityTagline.isNullOrEmpty() && data.distanceText.isNullOrEmpty())
        showDotSeparator.set(data.cityTagline.isNotNullAndEmpty() && data.distanceText.isNotNullAndEmpty())
    }

    fun getIconDrawable(): Int {
        return hotelAutoSuggestIconHelper.getAutoSuggestIconDrawable(data)
    }

    fun getAutoSuggestIconDrawableV2(): String?{
        return hotelAutoSuggestIconHelper.getAutoSuggestIconDrawableV2(data)
    }

    fun onItemClick(rank:Int?=null) {
        if (!data.isClickable) {
            return
        }
        eventStream.value = HotelEvent(AUTO_SUGGEST_CARD_CLICK, Triple(data, rank, AUTO_SUGGEST_ITEM_CLICK))
    }

    override fun cardPriority(): Int {
        return AutoSuggestStaticCardsOrder.TRENDING_HOTEL_CARD_ORDER.ordinal
    }

    override fun getItemType(): Int {
        return HotelAutoSuggestItemTypes.ITEM_TYPE_AUTO_SUGGEST_GROUPED_CARD_ITEM
    }
}