@file:OptIn(ExperimentalComposeUiApi::class)

package com.mmt.hotel.autoSuggest.ui

import android.view.View
import android.widget.TextView
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.*
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Brush.Companion.linearGradient
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTag
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.constraintlayout.compose.atMost
import androidx.constraintlayout.compose.atMostWrapContent
import androidx.core.text.HtmlCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.ITEM_TYPE_AUTO_SUGGEST_GROUPED_CARD_ITEM
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.NEAR_ME_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.POPULAR_LOCATION_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.POPULAR_LOCATION_HEADER_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.RECENT_SEARCH_CARD
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.SAVED_LOCATION_CARD
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.HANDLE_BACK_CLICK
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.HANDLE_CROSS_ICON_CLICK
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.*
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.gommt.logger.LogUtils
import com.mmt.core.util.DateUtil
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.STAYCATION_NEAR_ME_CARD
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.HANDLE_SEARCH_AGAIN_CLICK
import com.mmt.hotel.autoSuggest.model.response.Tag
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelStringsProvider
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.compose.resources.toColor
import com.mmt.hotel.mobconfig.model.response.AutoSuggestHint
import com.mmt.hotel.mobconfig.model.response.PremiumAutoSuggestNearMeCardInfo
import com.mmt.hotel.widget.compose.HTLCustomModifier
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.uikit.fonts.latoFont
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.coroutines.delay


object ResourceIds{
    const val ALERT = "alert"
    const val BACK_ARROW = "back_arrow"
    const val LOCATION_ICON = "location_icon"
    const val SEARCH_BOX = "search_box"
    const val INPUT_TEXT = "input_text"
    const val CROSS_ICON = "cross_icon"
    const val TITLE = "title"
    const val SUB_TITLE = "sub_title"
    const val NOT_FOUND_FULL_TEXT = "not_found_full_text"
    const val STAR_ICON = "star_icon"
    const val ICON = "icon"
    const val LOGO = "logo"
    const val BACKGROUND = "background"
    const val HIERARCHIAL_ITEM = "hierarchial_item"
    const val LOADER = "loader"
    const val CARD_DIVIDER = "card_divider"
    const val NOT_FOUND_TEXT = "not_found_text"
    const val SHIMMER_LIST = "shimmer_list"
    const val ERROR_IMAGE = "error_image"
    const val CHILD_TEXT = "child_text"
    const val CHILD_SUB_TEXT = "child_sub_text"
    const val TAG_TEXT = "tag_text"
    const val TV_HEADER = "tv_header"
    const val CONTINUE = "continue"
    const val ACTION_CTA = "action_cta"
    const val KNOW_MORE = "know_more"
    const val RETRY = "retry"
    const val PROGRESS = "progress"
    const val CONTAINER = "container"
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun AutoSuggestBaseUi(currentViewState: MutableLiveData<HotelAutoSuggestUiState>?, searchBoxState: LiveData<ListSearchBoxState>?, handleEvents: (HotelEvent) -> Unit){
    val viewState = currentViewState?.observeAsState()?.value
    val autoSuggestSearchBoxState = searchBoxState?.observeAsState()?.value
    Column(modifier = Modifier
        .semantics { testTagsAsResourceId = true }
        .background(color = Color.White)) {
        ListSearchBox(autoSuggestSearchBoxState, handleEvents)
        if (autoSuggestSearchBoxState?.funnelType != FunnelType.ALT_ACCO_FUNNEL &&
            ExperimentUtil.isAutoSuggestUiV2() &&
            autoSuggestSearchBoxState?.queryText?.observeAsState()?.value.isNullOrEmpty()) {
            val hint = HotelSharedPrefUtil.instance.getObject<AutoSuggestHint>(SharedPrefKeys.AUTO_SUGGEST_HINT, AutoSuggestHint::class.java)
            hint?.let {
                AutoSuggestHint(
                    modifier = Modifier
                        .padding(
                            horizontal = dimensionResource(id = R.dimen.margin_large),
                            vertical = dimensionResource(id = R.dimen.margin_small)
                        )
                        .testTag("autosuggest_hint"), it
                )
            }
        }
        when(viewState){
            is HotelAutoSuggestUiState.SuccessState -> {
                AutoSuggestSuccessState(viewState = (viewState as? HotelAutoSuggestUiState.SuccessState)?.successState, searchBoxState = searchBoxState)
            }
            is HotelAutoSuggestUiState.LoadingState -> {
                Shimmer()
            }
            else -> {
                AutoSuggestErrorState(viewState = currentViewState, searchBoxState = searchBoxState, handleEvents = handleEvents)
            }
        }
    }
}

@Composable
fun AutoSuggestHint(modifier: Modifier, hint: AutoSuggestHint) {
    val list = hint.hints?: emptyList()
    var index by remember {
        mutableIntStateOf(0)
    }

    LaunchedEffect(key1 = index) {
        delay(1200)
        if (index == list.size - 1) {
            index = 0
        } else {
            index++
        }
    }
    //list is kept nullable and title non null to support static hint with no scrolling animation

    Row(modifier = modifier, horizontalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.margin_tiny))) {
        MmtComposeTextView(
            text = hint.prefixText,
            fontSize = spDimensionResource(id = R.dimen.text_size_medium),
            lineHeight = spDimensionResource(id = R.dimen.margin_17dp),
            color = colorResource(id = R.color.secondary_color),
            maxLines = 1,
            mmtFontStyle = latoRegular)

        if (list.isNotNullAndEmpty()) {
            val hintText = "\"${list[index]}\""
            AnimatedContent(targetState = hintText, transitionSpec = {
                slideInVertically { it } togetherWith  slideOutVertically { -it }
            }, label = "", content = { state ->

                MmtComposeTextView(
                        text = state,
                        fontSize = spDimensionResource(id = R.dimen.text_size_medium),
                        lineHeight = spDimensionResource(id = R.dimen.margin_17dp),
                        color = colorResource(id = R.color.secondary_color),
                        maxLines = 1,
                        mmtFontStyle = latoBold)

            })
        }
    }
}


@Composable
fun AutoSuggestErrorState(viewState: LiveData<HotelAutoSuggestUiState>?, searchBoxState: LiveData<ListSearchBoxState>?, handleEvents: (HotelEvent) -> Unit){

    val errorState = viewState?.observeAsState()?.value
    var model: HotelDestinationNotFoundViewModel? = null
    if(errorState is HotelAutoSuggestUiState.ErrorState){
        model = errorState.notFoundDestinationCard
    }
    Row {
        AutoSuggestErrorPage(model, searchBoxState, handleEvents)
    }
}

@Composable
fun GroupNameTag(groupName: String, modifier: Modifier){

    Text(text = groupName.uppercase(), modifier = modifier
        .wrapContentSize()
        .padding(top = dimensionResource(id = R.dimen.margin_extra_tiny))
        .border(
            width = dimensionResource(id = R.dimen.margin_extra_xtiny),
            color = colorResource(id = R.color.color_imp_info_gradient_light),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.radius_large))
        )
        .padding(
            vertical = dimensionResource(
                id = R.dimen.margin_extra_tiny
            ), horizontal = dimensionResource(id = R.dimen.margin_small_extra)
        )
        , style = TextStyle(
            color = colorResource(id = R.color.color_imp_info_gradient_light),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
            fontFamily = latoFont,
            fontWeight = FontWeight.Bold
        ))
}



@Composable
fun AutoSuggestSuccessState(viewState: HotelAutoSuggestFullScreenState?, searchBoxState: LiveData<ListSearchBoxState>?){
    val recommendedCardsList = viewState?.recommendedCardsList?.observeAsState()
    val searchedQuery = searchBoxState?.value?.queryText?.observeAsState()?.value

    Column(modifier = Modifier
        .background(color = Color.White)
        .fillMaxHeight()) {
        if(searchedQuery.isNullOrEmpty() || recommendedCardsList?.value.isNullOrEmpty()) {
            CreateAutoStaticSuggestCards(modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(), cardList = viewState?.staticCardsList, funnelType = searchBoxState?.value?.funnelType)
        }else {
            CreateRecommendedAutoSuggestCards(modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(), searchBoxState, recommendedCardsList = viewState?.recommendedCardsList, funnelType = searchBoxState.value?.funnelType)
        }
    }
}


@Composable
fun CreateRecommendedAutoSuggestCards(
    modifier: Modifier,
    searchBoxState: LiveData<ListSearchBoxState>?,
    recommendedCardsList: LiveData<ArrayList<Pair<Int, AbstractRecyclerItem>>>?,
    funnelType: FunnelType?
){

    val recommendedCardsState = recommendedCardsList?.observeAsState()?.value?.toList() ?: emptyList()

    LazyColumn(modifier = modifier){
        itemsIndexed(items = recommendedCardsState){_,  card ->
            when(card.second.getItemType()){
                HotelAutoSuggestItemTypes.ITEM_TYPE_AUTO_SUGGEST_CARD -> {
                    val model = card.second as? HotelAutoSuggestCardViewModel
                    AutoSuggestHierarchy(modifier = Modifier, model = model, searchBoxState, rank = card.first)
                }
                HotelAutoSuggestItemTypes.DESTINATION_NOT_FOUND_ITEM -> {
                    AutoSuggestCantFindDestinationCard(modifier = Modifier,model = card.second as? HotelDestinationNotFoundViewModel, searchBoxState)
                }
                else -> {
                    val model = card.second as? HotelAutoSuggestGroupedItemViewModel
                    CreateAutoSuggestCard(
                        modifier = Modifier,
                        model = model,
                        isLastItem = false,
                        searchBoxState = searchBoxState,
                        card.first,
                        funnelType
                    )  // there will always be can't find destination card, so it will never be last Item
                }
            }
        }
    }
}

@Composable
fun CreateAutoSuggestCard(
    modifier: Modifier,
    model: AbstractRecyclerItem?,
    isLastItem: Boolean,
    searchBoxState: LiveData<ListSearchBoxState>? = null,
    rank: Int? = null,
    funnelType: FunnelType? = null
){

    when(model?.getItemType()){
        ITEM_TYPE_AUTO_SUGGEST_GROUPED_CARD_ITEM -> {
            val viewModel = model as?  HotelAutoSuggestGroupedItemViewModel
            val imageUrl = viewModel?.getAutoSuggestIconDrawableV2()
            val imageResId = if(imageUrl.isNullOrEmpty()){
                R.drawable.default_location_icon
            }else{
                null
            }
            SuggestionLocationCard(
                modifier = modifier,
                onItemClick = { viewModel?.onItemClick(rank) },
                text = viewModel?.data?.displayText ?: EMPTY_STRING,
                subtext = viewModel?.data?.subtext ?: EMPTY_STRING,
                tagLine = viewModel?.data?.cityTagline ?: EMPTY_STRING,
                tag = viewModel?.data?.tag,
                groupName = viewModel?.data?.groupName ?: EMPTY_STRING,
                iconDrawable = imageResId,
                imageUrl = imageUrl,
                isLastItem = isLastItem,
                searchBoxState = searchBoxState,
                isClickable = viewModel?.data?.isClickable
            )
        }
        POPULAR_LOCATION_CARD, RECENT_SEARCH_CARD, SAVED_LOCATION_CARD -> {
            val viewModel = model as? HotelRecentSearchCardViewModel
            val cardItemsSize = viewModel?.recentItems?.size?:0
            if (funnelType != FunnelType.ALT_ACCO_FUNNEL && ExperimentUtil.isAutoSuggestUiV2() && RECENT_SEARCH_CARD == model.getItemType()) {
                AutoSuggestCardV2(modifier, viewModel = viewModel, cardItemsSize = cardItemsSize)
            } else {
                AutoSuggestCardV1(modifier = modifier, viewModel = viewModel, cardItemsSize = cardItemsSize, rank = rank)
            }
        }

    }
}

@Composable
private fun AutoSuggestCardV1(modifier: Modifier, viewModel: HotelRecentSearchCardViewModel?, cardItemsSize: Int, rank: Int? = 0) {
    Column {
        if (viewModel?.title?.isNotNullAndEmpty() == true) {
            HeaderCard(headerText = viewModel.title)
        }
        for (i in 1..cardItemsSize) {
            val childVM =
                viewModel?.recentItems?.get(i - 1) as? HotelRecentSearchCardItemViewModel
            SuggestionLocationCard(
                modifier = modifier,
                onItemClick = { childVM?.onClick((rank ?: 0) + (i - 1)) },
                text = childVM?.data?.displayText ?: EMPTY_STRING,
                subtext = childVM?.data?.subtext ?: EMPTY_STRING,
                tagLine = childVM?.data?.cityTagline ?: EMPTY_STRING,
                tag = childVM?.data?.tag,
                groupName = EMPTY_STRING,
                imageUrl = childVM?.getAutoSuggestIconDrawableV2(),
                iconDrawable = childVM?.getDrawable() ?: 0,
                isLastItem = (i == cardItemsSize),
                isClickable = childVM?.data?.isClickable
            )
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun AutoSuggestCardV2(modifier: Modifier, viewModel: HotelRecentSearchCardViewModel?, cardItemsSize:Int) {
    Column(modifier.semantics { testTagsAsResourceId = true }) {
        if(viewModel?.title?.isNotNullAndEmpty() == true) {
            HeaderCard(headerText = viewModel.title)
        }
        for(i in 1..cardItemsSize){
            val childVM = viewModel?.recentItems?.get(i-1) as? HotelRecentSearchCardItemViewModelV2
            val userSearchData = childVM?.data?.userSearchData
            val subtitle = getRecentSearchSubtitle(
                userSearchData?.checkInDate,
                userSearchData?.checkOutDate,
                userSearchData?.occupancyData?.adultCount ?: 0,
                userSearchData?.occupancyData?.roomCount ?: 0
            )
            RecentSearchCardItem(modifier = Modifier
                .mmtClickable {
                    childVM?.onRecentSearchItemClick(childVM.data)
                }
                .testTag("recent_search_item"), userSearchData?.displayName, subtitle)
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun LocationDetailsUi(
    modifier: Modifier,
    text: AnnotatedString,
    subtext: String,
    tagLine: String,
    tag: Tag?
) {

    Column(
        modifier = modifier
            .semantics { testTagsAsResourceId = true }
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        if (text.isNotEmpty()) {
            Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                val configuration = LocalConfiguration.current
                var screenWidth = configuration.screenWidthDp.dp
                var halfScreenWidth = screenWidth / 1.7f
                 if (tag?.tagText== null){
                     halfScreenWidth = screenWidth
                 }
                Text(
                    modifier = Modifier
                        .widthIn(max = halfScreenWidth).semantics { testTag = ResourceIds.CHILD_TEXT },
                    text = text,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = TextStyle(
                        color = Color.Black,
                        fontSize = spDimensionResource(id = R.dimen.text_size_large),
                        fontFamily = latoFont,
                        fontWeight = FontWeight.Normal
                    )
                )
                tag?.let{
                    if(it.tagText != null) {
                        MmtComposeTextView(
                            modifier = Modifier
                                .wrapContentWidth()
                                .padding(start = dimensionResource(id = R.dimen.margin_extra_small))
                                .border(
                                    width = dimensionResource(id = R.dimen.margin_extra_xtiny),
                                    color = it.tagColor.toColor(),
                                    shape = RoundedCornerShape(dimensionResource(id = R.dimen.radius_large))
                                )
                                .padding(
                                    horizontal = dimensionResource(id = R.dimen.margin_small),
                                    vertical = dimensionResource(id = R.dimen.margin_extra_tiny)
                                ),
                            text = it.tagText,
                            textAlign = TextAlign.Center,
                            color = it.tagColor.toColor(color = colorResource(id = R.color.htl_color_834CC5)),
                            fontSize = spDimensionResource(id = R.dimen.htl_text_size_extra_tiny),
                            mmtFontStyle = latoBlack
                        )
                    }
                }
            }
        }
        if (subtext.isNotNullAndEmpty() || tagLine.isNotNullAndEmpty()) {
            ShowLocationSubtext(tagLine, subtext)
        } else {
            Spacer(
                modifier = Modifier
                    .height(dimensionResource(id = R.dimen.margin_extra_tiny))
                    .fillMaxWidth()
            )
        }
    }
}

@Composable
fun ShowLocationSubtext(tagLine: String?, subtext: String) {
    ConstraintLayout(
        modifier = Modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .padding(top = dimensionResource(id = R.dimen.margin_extra_tiny))
    ) {
        val (tagText, subtitle) = createRefs()
        if (!tagLine.isNullOrBlank()) {
            TagLine(modifier = Modifier.constrainAs(tagText) {
                start.linkTo(parent.start)
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
            }, tagLine = tagLine)
        }
        if (tagLine.isNullOrBlank() && subtext.isNotNullAndEmpty()) {
            Text(
                modifier = Modifier
                    .semantics { testTag = ResourceIds.CHILD_SUB_TEXT }
                    .constrainAs(subtitle) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        start.linkTo(parent.start)
                    }, maxLines = 1, overflow = TextOverflow.Ellipsis,
                text = subtext, style = TextStyle(
                    color = colorResource(id = R.color.grey_hotel),
                    fontSize = spDimensionResource(id = R.dimen.text_size_medium),
                    fontFamily = latoFont,
                    fontWeight = FontWeight.Normal
                )
            )
        }
    }
}

@Composable
private fun TagLine(modifier: Modifier = Modifier, tagLine : String) {
    MmtComposeTextView(
        modifier = modifier
            .wrapContentWidth()
            .wrapContentHeight()
            .semantics { testTag = ResourceIds.TAG_TEXT },
        text = tagLine,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis,
        color = colorResource(id = R.color.htl_description_text_black_color),
        fontSize = spDimensionResource(id = R.dimen.margin_medium)
    )
}

@Composable
fun HeirarchialAutoSuggestItem(
    modifier: Modifier,
    text: AnnotatedString,
    subtext: String,
    tagLine: String,
    tag: Tag?
) {
    Row(
        modifier.wrapContentHeight()
    ) {
        Image(
            painter = painterResource(id = R.drawable.auto_suggest_arrow),
            contentDescription = null
        )
        LocationDetailsUi(
            modifier = modifier.padding(
                start = dimensionResource(id = R.dimen.margin_small_extra), top = dimensionResource(
                    id = R.dimen.margin_extra_small
                )
            ), text, subtext, tagLine, tag
        )
    }
}


@Composable
fun AutoSuggestHierarchy(modifier: Modifier, model: HotelAutoSuggestCardViewModel?, searchBoxState: LiveData<ListSearchBoxState>?=null, rank: Int){

    val queryState = searchBoxState?.value?.queryText?.observeAsState()?.value
    val listOfItems = model?.childItems as? List<HotelAutoSuggestGroupedItemViewModel>?: emptyList()
    var cardRank = rank
    
    ConstraintLayout(modifier = modifier) {
        val (image, title, tag, subtext, items, verticalLine, bottomDivider) = createRefs()


        val imageUrl = model?.getAutoSuggestIconDrawableV2()
        val imageResId = if(imageUrl.isNullOrEmpty()){
            R.drawable.default_location_icon
        }else{
            null
        }

        LoadImage(modifier = Modifier
            .mmtClickable { model?.onItemClick(rank) }
            .padding(start = dimensionResource(id = R.dimen.margin_medium_extra))
            .padding(
                top = dimensionResource(
                    id = R.dimen.margin_medium_extra
                )
            )
            .height(dimensionResource(id = R.dimen.image_dimen_large))
            .width(dimensionResource(id = R.dimen.image_dimen_large))
            .constrainAs(image) {
                start.linkTo(parent.start)
                top.linkTo(parent.top)
            }, resourceId = imageResId, imageUrl = imageUrl)

        val subText = model?.data?.subtext ?: EMPTY_STRING
        val groupName = model?.data?.groupName ?: EMPTY_STRING
        Text(text = getAnnotatedString(
            text = model?.data?.displayText ?: EMPTY_STRING,
            bold = queryState ?: EMPTY_STRING,
            textColor = if (model?.data?.isClickable == true) {
                Color.Black
            } else {
                colorResource(id = R.color.color_757575)
            }
        ), modifier = Modifier
            .padding(
                top = dimensionResource(
                    id = R.dimen.margin_medium_extra
                )
            )
            .fillMaxWidth()
            .wrapContentHeight()
            .mmtClickable { model?.onItemClick(rank) }
            .padding(start = dimensionResource(id = R.dimen.margin_small_extra))
            .constrainAs(title) {
                top.linkTo(image.top)
                start.linkTo(image.end)
                if (subText.isEmpty()) {
                    bottom.linkTo(image.bottom)
                }
            }, fontSize = spDimensionResource(id = R.dimen.margin_large),
            fontFamily = latoFont, fontWeight = FontWeight.Normal)

        model?.data?.tag?.let{
            if(it.tagText != null) {
                MmtComposeTextView(
                    modifier = Modifier
                        .wrapContentWidth()
                        .padding(start = dimensionResource(id = R.dimen.margin_extra_small))
                        .constrainAs(tag) {
                            top.linkTo(title.top)
                            bottom.linkTo(title.bottom)
                            start.linkTo(title.end)
                        }
                        .border(
                            width = dimensionResource(id = R.dimen.margin_extra_xtiny),
                            color = it.tagColor.toColor(),
                            shape = RoundedCornerShape(dimensionResource(id = R.dimen.radius_large))
                        )
                        .padding(
                            horizontal = dimensionResource(id = R.dimen.margin_small),
                            vertical = dimensionResource(id = R.dimen.margin_tiny)
                        ),
                    text = it.tagText,
                    textAlign = TextAlign.Center,
                    color = it.tagColor.toColor(color = colorResource(id = R.color.htl_color_834CC5)),
                    fontSize = spDimensionResource(id = R.dimen.htl_text_size_extra_tiny),
                    mmtFontStyle = latoBlack
                )
            }
        }

        val subTextModifier = Modifier
            .semantics { testTag = ResourceIds.SUB_TITLE }
            .constrainAs(subtext) {
                top.linkTo(title.bottom, 0.dp)
                start.linkTo(image.end)
            }
            .padding(top = dimensionResource(id = R.dimen.margin_extra_tiny))
            .clickable { model?.onItemClick(rank) }

            if(groupName.isNotNullAndEmpty()){
                GroupNameTag(groupName = groupName, modifier = subTextModifier)
            }else if(subText.isNotEmpty()){
                Text(
                    modifier = subTextModifier
                        .padding(start = dimensionResource(id = R.dimen.margin_small_extra)),
                    text = model?.data?.subtext ?: EMPTY_STRING, maxLines = 1, overflow = TextOverflow.Ellipsis,
                    fontSize = spDimensionResource(id = R.dimen.text_size_medium),
                    fontWeight = FontWeight.Normal, fontFamily = latoFont, color = colorResource(
                        id = R.color.grey_hotel
                    ))
            }


        val barrier = createBottomBarrier(image, subtext)

        Column(modifier = Modifier
            .padding(
                top = dimensionResource(
                    id = R.dimen.margin_medium_extra
                )
            )
            .padding(start = dimensionResource(id = R.dimen.margin_huge_extra))
            .wrapContentHeight()
            .constrainAs(items) {
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                top.linkTo(barrier)
                width = Dimension.preferredWrapContent
            }) {
            listOfItems.forEach {
                cardRank++
                val itemRank = cardRank
                HeirarchialAutoSuggestItem(
                    modifier = Modifier
                        .semantics { testTag = ResourceIds.HIERARCHIAL_ITEM }
                        .padding(bottom = dimensionResource(id = R.dimen.margin_extra_small))
                        .mmtClickable { it.onItemClick(itemRank) },
                    text = getAnnotatedString(
                        text = it.data.displayText,
                        bold = queryState ?: EMPTY_STRING,
                        textColor = if (it.data.isClickable) {
                            Color.Black
                        } else {
                            colorResource(id = R.color.color_757575)
                        }
                    ),
                    subtext = it.data.subtext,
                    tagLine = it.data.cityTagline ?: EMPTY_STRING,
                    tag = it.data.tag
                )
            }
        }
        HorizontalDivider(modifier = Modifier
            .padding(
                start = dimensionResource(id = R.dimen.margin_huge_extra),
                top = dimensionResource(id = R.dimen.margin_small)
            )
            .constrainAs(verticalLine) {
                start.linkTo(items.start)
                top.linkTo(image.bottom)
                bottom.linkTo(parent.bottom)
                height = Dimension.fillToConstraints
            }
            .width(dimensionResource(id = R.dimen.margin_extra_xtiny))
            .padding(
                top = dimensionResource(id = R.dimen.margin_extra_tiny), bottom = dimensionResource(
                    id = R.dimen.htl_property_highlight_divider_height
                )
            ), thickness = dimensionResource(id = R.dimen.margin_extra_xtiny), color = colorResource(id = R.color.c5_white))

        HorizontalDivider(modifier = Modifier
            .constrainAs(bottomDivider) {
                bottom.linkTo(parent.bottom)
                width = Dimension.matchParent
            }
            .padding(bottom = dimensionResource(id = R.dimen.margin_extra_xtiny)), thickness = dimensionResource(id = R.dimen.margin_extra_xtiny), color = colorResource(id = R.color.color_f2f2f2))
    }
}


@Composable
fun CreateAutoStaticSuggestCards(
    modifier: Modifier,
    cardList: LiveData<ArrayList<Pair<Int, AutoSuggestStaticCardItem>>>?,
    funnelType: FunnelType?
){

    val cards = cardList?.observeAsState()?.value ?: arrayListOf()

    LazyColumn(modifier = modifier){
       itemsIndexed(cards){ _, card ->
           when(card.second.getItemType()){
               NEAR_ME_CARD -> {
                   NearMeCardItem(model = card.second as? NearMeCardViewModel)
               }
               STAYCATION_NEAR_ME_CARD -> {
                   (card.second as? StaycationNearMeCardViewModel)?.let {
                       it.trackShownEvent()
                       StaycationNearMeCardItem(model = it)
                   }
               }
               POPULAR_LOCATION_HEADER_CARD -> {
                   HeaderCard(headerText = stringResource(id = R.string.htl_popular))

               }
               POPULAR_LOCATION_CARD, RECENT_SEARCH_CARD, ITEM_TYPE_AUTO_SUGGEST_GROUPED_CARD_ITEM, SAVED_LOCATION_CARD -> {
                   CreateAutoSuggestCard(modifier = Modifier, model = card.second, isLastItem = false, rank = card.first, funnelType = funnelType)
               }

           }
       }
   }
}


@Composable
fun HeaderCard(headerText: String){
    Row(modifier = Modifier
        .background(color = colorResource(id = R.color.light_gray))
        .fillMaxWidth()
        .wrapContentHeight()
        .padding(
            horizontal = dimensionResource(
                id = R.dimen.margin_large
            ), vertical = dimensionResource(id = R.dimen.margin_small)
        )) {
        Text(text = headerText, color = Color.Black,  fontSize = spDimensionResource(id = R.dimen.htl_text_size_small))
    }

}


@Preview
@Composable
fun NearMeCardItemPreview(){
    NearMeCardItem(null)
}

@Composable
fun NearMeCardItem(model: NearMeCardViewModel?){
    ConstraintLayout(
        modifier = Modifier
            .background(color = colorResource(id = R.color.white))
            .fillMaxWidth()
            .height(dimensionResource(id = R.dimen.htl_header_search_box_height))
            .mmtClickable { model?.onClick() }
            .padding(start = dimensionResource(id = R.dimen.margin_medium_extra))
    ) {
        val (locationIcon, locationDetails) = createRefs()

        Image(painter = painterResource(id = R.drawable.htl_nearme_icon),
            contentDescription = EMPTY_STRING,
            modifier = Modifier
                .height(dimensionResource(id = R.dimen.image_dimen_large))
                .width(dimensionResource(id = R.dimen.image_dimen_large))
                .constrainAs(locationIcon) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                })

        Column(modifier = Modifier
            .constrainAs(locationDetails) {
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
                start.linkTo(locationIcon.end)
            }
            .padding(horizontal = dimensionResource(id = R.dimen.margin_small_extra))) {
            Text(text = stringResource(id = R.string.htl_USE_CURRENT_LOCATION), fontSize = spDimensionResource(id = R.dimen.text_size_large), fontFamily = latoFont, fontWeight = FontWeight.Normal, color = colorResource(id = R.color.black))
            Text(text = stringResource(id = R.string.htl_properties_near_me), fontSize = spDimensionResource(id = R.dimen.text_size_medium), fontFamily = latoFont, fontWeight = FontWeight.Normal, color = colorResource(id = R.color.grey_hotel))
        }
    }
}

@Preview
@Composable
fun PremiumNearMeCardItemPreview(){
    StaycationNearMeCardItem(StaycationNearMeCardViewModel(
        data = PremiumAutoSuggestNearMeCardInfo(
            text = "Explore Staycations Hotels Near Me",
            subtext = "Handpicked Luxury Stays within 300 kms",
            bgImageUrl = null,
            deeplink = "",
            trackingKey = null
        ),
        eventStream = MutableLiveData<HotelEvent>()
    ))
}

@Composable
fun StaycationNearMeCardItem(model: StaycationNearMeCardViewModel){

    Box(modifier = Modifier
        .semantics { testTagsAsResourceId = true }
        .padding(
            top = dimensionResource(id = R.dimen.margin_medium),
            bottom = dimensionResource(id = R.dimen.margin_xLarge)
        )
        .padding(horizontal = dimensionResource(id = R.dimen.margin_large))
        .fillMaxWidth()
        .background(
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_xxlarge)),
            color = colorResource(id = R.color.white)
        )
        .border(
            width = dimensionResource(id = R.dimen.margin_extra_xtiny),
            color = colorResource(id = R.color.htl_grey_d8d8d8),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_xxlarge))
        )
    ) {

        // Load image from url
        model.data.bgImageUrl?.let {
            LoadImage(
                modifier = HTLCustomModifier(testTag = "staycation_near_me_card_bg_image")
                    .fillMaxSize()
                    .clip(shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_xxlarge))),
                imageUrl = it,
                contentScale = ContentScale.Crop
            )
        }

        Row(modifier = HTLCustomModifier(testTag = "staycation_near_me_card_view")
            .fillMaxWidth()
            .padding(dimensionResource(id = R.dimen.margin_large))
            .mmtClickable { model.onClick() }
        ) {

            Column(modifier = Modifier
                .weight(1f)
                .padding(end = dimensionResource(id = R.dimen.margin_small_extra))
            ) {
                model.data.text?.let {
                    MmtComposeTextView(
                        modifier = HTLCustomModifier(testTag = "staycation_near_me_card_text"),
                        text = it,
                        fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium),
                        mmtFontStyle = latoBlack,
                        color = colorResource(id = R.color.black))
                }
                model.data.subtext?.let {
                    MmtComposeTextView(
                        modifier = HTLCustomModifier(testTag = "staycation_near_me_card_subtext")
                            .padding(top = dimensionResource(id = R.dimen.margin_tiny)),
                        text = it,
                        fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
                        mmtFontStyle = latoRegular,
                        color = colorResource(id = R.color.black))
                }
            }

            Image(modifier = Modifier
                .height(dimensionResource(id = R.dimen.image_dimen_xmedium))
                .width(dimensionResource(id = R.dimen.image_dimen_xmedium))
                .padding(dimensionResource(id = R.dimen.margin_extra_small))
                .align(Alignment.CenterVertically),
                painter = painterResource(id = R.drawable.arrow_right_blue),
                contentDescription = EMPTY_STRING
            )
        }

    }
}


@Composable
fun ListSearchBox(searchBoxState: ListSearchBoxState?, handleEvents: (HotelEvent) -> Unit){

    val showLoader = searchBoxState?.showLoader?.observeAsState()?.value

    ConstraintLayout(modifier = Modifier
        .semantics { testTag = ResourceIds.SEARCH_BOX }
        .padding(
            top = dimensionResource(id = R.dimen.margin_medium),
            start = dimensionResource(id = R.dimen.margin_medium_extra),
            end = dimensionResource(id = R.dimen.margin_medium_extra)
        )
        .fillMaxWidth()
        .wrapContentHeight()
        .background(
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny)),
            color = colorResource(id = R.color.auto_suggest_search_box)
        )
        .border(
            width = dimensionResource(id = R.dimen.margin_extra_xtiny),
            color = colorResource(id = R.color.auto_suggest_stroke),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny))
        )) {
        val ( backArrow, searchText, crossIcon, loaderIcon) = createRefs()
        val rightBarrier = createStartBarrier(crossIcon)

        BackArrow(modifier = Modifier
            .semantics {
                testTag = ResourceIds.BACK_ARROW
            }
            .constrainAs(backArrow) {
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
                start.linkTo(parent.start, margin = 16.dp)
                height = Dimension.fillToConstraints
                width = Dimension.wrapContent
            }
            .graphicsLayer {
                rotationY = ResourceProvider.instance
                    .getInteger(R.integer.rtl_image_rotation)
                    .toFloat()
            }, handleEvents)

        SearchText(searchBoxState,
            modifier = Modifier
                .semantics { testTag = ResourceIds.INPUT_TEXT }
                .constrainAs(searchText) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(backArrow.end)
                    end.linkTo(rightBarrier)
                    width = Dimension.fillToConstraints
                    height = Dimension.wrapContent
                })

        if(showLoader == true) {
            val marginEnd = dimensionResource(id = R.dimen.margin_small)
            CircularProgressIndicator(modifier = Modifier
                .semantics { ResourceIds.LOADER }
                .constrainAs(loaderIcon) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(
                        crossIcon.start,
                        margin = marginEnd
                    )
                }
                .height(dimensionResource(id = R.dimen.margin_huge))
                .width(dimensionResource(id = R.dimen.margin_huge)),
                color = colorResource(id = R.color.green_00915a),
                strokeWidth = dimensionResource(id = R.dimen.margin_extra_tiny))
        }

        ShowCrossIcon(searchBoxState, modifier = Modifier
            .semantics { testTag = ResourceIds.CROSS_ICON }
            .wrapContentHeight()
            .constrainAs(crossIcon) {
                top.linkTo(parent.top)
                bottom.linkTo(parent.bottom)
                end.linkTo(parent.end)
                height = Dimension.wrapContent
            }
            .padding(end = dimensionResource(id = R.dimen.margin_large)), handleEvents)
    }
}

@Composable
fun BackArrow(modifier: Modifier, onBackClick: (HotelEvent) -> Unit){
    val event = HotelEvent(HANDLE_BACK_CLICK)
    IconButton(onClick = { onBackClick(event) }, modifier = modifier
        .height(dimensionResource(id = R.dimen.margin_large))
        .width(dimensionResource(id = R.dimen.margin_large))) {
        Image(
            painter = painterResource(id = R.drawable.ic_black_arrow),
            contentDescription = EMPTY_STRING,
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight())
    }
}

@Composable
fun SearchText(searchBoxState: ListSearchBoxState?, modifier: Modifier){

    val queryTextState = searchBoxState?.queryText?.observeAsState()?.value
    val textState = searchBoxState?.queryText
    val focusRequester = remember { FocusRequester() }

    val hintText = searchBoxState?.hintText ?: EMPTY_STRING


    if (queryTextState != null) {
        TextField(modifier = modifier.focusRequester(focusRequester), value = textState?.value?: EMPTY_STRING,
            onValueChange = {
                textState?.value = it
            }, placeholder = {
                Text(hintText, fontSize = spDimensionResource(id = R.dimen.text_size_large), fontFamily = latoFont, fontWeight = FontWeight.Bold, color = colorResource(
                    id = R.color.color_9B9B9B
                ))
            }, colors = TextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                disabledContainerColor = Color.Transparent,

                focusedTextColor = Color.Black,
                unfocusedTextColor = Color.Black,
                disabledTextColor = Color.Black,

                errorContainerColor = Color.Transparent,
                errorLabelColor = Color.Transparent,
                errorTextColor = Color.Transparent,
                cursorColor = colorResource(id = R.color.blue_00),
                errorIndicatorColor = Color.Transparent,

                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledIndicatorColor = Color.Transparent,
            ), maxLines = 1,
            singleLine= true,
            textStyle = TextStyle(fontSize = spDimensionResource(id = R.dimen.text_size_large), fontFamily = latoFont, fontWeight = FontWeight.Bold, color = Color.Black, textDecoration = TextDecoration.None)
        )
    }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }
}

@Composable
fun ShowCrossIcon(searchBoxState: ListSearchBoxState?, modifier: Modifier, crossAction: (HotelEvent) -> Unit){
    val event = HotelEvent(HANDLE_CROSS_ICON_CLICK)
    val textState = searchBoxState?.queryText?.observeAsState()

    if(textState?.value != EMPTY_STRING) {
        IconButton(
            onClick = { crossAction(event) }, modifier = modifier
                .height(dimensionResource(id = R.dimen.margin_huge))
                .width(dimensionResource(id = R.dimen.margin_huge))
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_circular_cross_grey),
                contentDescription = EMPTY_STRING,
                modifier = Modifier
                    .height(dimensionResource(id = R.dimen.margin_huge))
                    .width(dimensionResource(id = R.dimen.margin_huge))
            )

        }

    }
}

@Composable
fun SuggestionLocationCard(
    modifier: Modifier,
    onItemClick: () -> Unit,
    text: String,
    tag: Tag? = null,
    subtext: String,
    tagLine: String? = null,
    groupName: String,
    iconDrawable: Int? = null,
    imageUrl: String? = null,
    isLastItem: Boolean,
    searchBoxState: LiveData<ListSearchBoxState>? = null,
    isClickable: Boolean? = true
) {

    val queryState = searchBoxState?.value?.queryText?.observeAsState()?.value
    ConstraintLayout(
        modifier = modifier
            .background(color = colorResource(id = R.color.white))
            .fillMaxWidth()
            .wrapContentHeight()
            .heightIn(min = dimensionResource(id = R.dimen.htl_header_search_box_height))
            .mmtClickable(onClick = onItemClick)
    ) {
        val (locationIcon, locationDetails, divider) = createRefs()


        LoadImage(resourceId = iconDrawable,
            imageUrl = imageUrl,
            contentScale = ContentScale.Fit,
            modifier = Modifier
                    .height(dimensionResource(id = R.dimen.image_dimen_large))
                    .padding(start = dimensionResource(id = R.dimen.margin_medium_extra))
                    .width(dimensionResource(id = R.dimen.image_dimen_large))
                    .semantics { testTag = ResourceIds.LOCATION_ICON }
                    .constrainAs(locationIcon) {
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                        start.linkTo(parent.start)
                    })

        val horizontalMargin = dimensionResource(id = R.dimen.margin_small_extra)
        Column(modifier = Modifier
                .wrapContentHeight()
                .padding(
                        top = dimensionResource(id = R.dimen.margin_medium),
                        bottom = dimensionResource(id = R.dimen.margin_medium),
                )
                .constrainAs(locationDetails) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(locationIcon.end, margin = horizontalMargin)
                    end.linkTo(parent.end, margin = horizontalMargin)
                    width = Dimension.fillToConstraints
                }) {
            if(text.isNotNullAndEmpty()) {
                Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically){
                    Text(
                        modifier = Modifier.semantics { testTag = ResourceIds.TITLE },
                        text = getAnnotatedString(
                            text = text,
                            bold = queryState ?: EMPTY_STRING,
                            textColor = if (isClickable == true) {
                                Color.Black
                            } else {
                                colorResource(id = R.color.color_757575)
                            }
                        ), fontSize = spDimensionResource(id = R.dimen.text_size_large), fontFamily = latoFont,
                        fontWeight = FontWeight.Normal, color = Color.Black, maxLines = 2, overflow = TextOverflow.Ellipsis
                    )
                    tag?.let{
                        if(it.tagText != null) {
                            MmtComposeTextView(
                                modifier = Modifier
                                    .wrapContentWidth()
                                    .padding(start = dimensionResource(id = R.dimen.margin_extra_small))
                                    .border(
                                        width = dimensionResource(id = R.dimen.margin_extra_xtiny),
                                        color = it.tagColor.toColor(),
                                        shape = RoundedCornerShape(dimensionResource(id = R.dimen.radius_large))
                                    )
                                    .padding(horizontal = dimensionResource(id = R.dimen.margin_small), vertical = dimensionResource(id = R.dimen.margin_tiny)),
                                text = it.tagText,
                                textAlign = TextAlign.Center,
                                color = it.tagColor.toColor(color = colorResource(id = R.color.htl_color_834CC5)),
                                fontSize = spDimensionResource(id = R.dimen.htl_text_size_extra_tiny),
                                mmtFontStyle = latoBlack
                            )
                        }
                    }
                }
            }
            if (groupName.isNotNullAndEmpty()) {
                GroupNameTag(
                    groupName,
                    modifier = Modifier
                        .semantics { testTag = ResourceIds.SUB_TITLE }
                        .padding(top = dimensionResource(id = R.dimen.margin_extra_tiny)))
            } else if (subtext.isNotNullAndEmpty()) {
                Text(
                    modifier = Modifier
                        .semantics { testTag = ResourceIds.SUB_TITLE }
                        .padding(top = dimensionResource(id = R.dimen.margin_extra_tiny)),
                    text = subtext,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    fontSize = spDimensionResource(id = R.dimen.text_size_medium),
                    fontWeight = FontWeight.Normal,
                    fontFamily = latoFont,
                    color = colorResource(
                        id = R.color.grey_hotel
                    )
                )
            }
            if (!tagLine.isNullOrBlank()) {
                TagLine(modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_extra_tiny)), tagLine = tagLine)
            }
        }

        if(!isLastItem) {
            HorizontalDivider(modifier = Modifier
                    .semantics { testTag = ResourceIds.CARD_DIVIDER }
                    .fillMaxWidth()
                    .padding(bottom = dimensionResource(id = R.dimen.margin_extra_xtiny))
                    .constrainAs(divider) {
                        start.linkTo(parent.start)
                        bottom.linkTo(parent.bottom)
                    }, thickness = dimensionResource(id = R.dimen.margin_extra_xtiny), color = colorResource(id = R.color.color_f2f2f2))
        }
    }
}

@Composable
fun HtmlText(html: String, modifier: Modifier) {
    AndroidView(
        modifier = modifier
                .background(
                        shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny)),
                        color = colorResource(id = R.color.light_green_cosmos)
                )
                .padding(
                        top = dimensionResource(id = R.dimen.margin_small),
                        start = dimensionResource(id = R.dimen.margin_small),
                        bottom = dimensionResource(id = R.dimen.margin_medium),
                        end = dimensionResource(id = R.dimen.margin_small)
                ),
        factory = { context -> TextView(context).apply {
            this.textSize = 14.dp.value
            this.gravity = View.TEXT_ALIGNMENT_GRAVITY
        } },
        update = { it.text = HtmlCompat.fromHtml(html, HtmlCompat.FROM_HTML_MODE_COMPACT) }
    )
}

@Composable
fun AutoSuggestCantFindDestinationCard(modifier: Modifier, model: HotelDestinationNotFoundViewModel?, searchBoxState: LiveData<ListSearchBoxState>?){

    val query = searchBoxState?.value?.queryText?.observeAsState()?.value?: EMPTY_STRING
    val isClicked = model?.locationNotfoundClicked?.observeAsState()?.value
    Row(horizontalArrangement = Arrangement.Center, modifier = modifier
            .padding(
                    start = dimensionResource(id = R.dimen.margin_large),
                    end = dimensionResource(id = R.dimen.margin_large)
            )
            .fillMaxWidth()
            .wrapContentHeight() ) {

        if(isClicked == true) {
            HtmlText(html = ResourceProvider.instance.getString(
                R.string.htl_destination_not_found_selected_message,
                query
            ), modifier = Modifier
                    .semantics { testTag = ResourceIds.NOT_FOUND_FULL_TEXT }
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(top = dimensionResource(id = R.dimen.margin_large), bottom = dimensionResource(id = R.dimen.margin_large)))
        }else {

            Text(
                modifier = Modifier
                        .semantics { testTag = ResourceIds.NOT_FOUND_TEXT }
                        .wrapContentHeight()
                        .wrapContentWidth()
                        .padding(vertical = dimensionResource(id = R.dimen.margin_large)),
                text = stringResource(id = R.string.htl_cant_find_destination_text),
                fontSize = spDimensionResource(id = R.dimen.margin_large),
                color = colorResource(id = R.color.grey_777777),
                fontFamily = latoFont,
                fontWeight = FontWeight.Normal
            )

            Text(
                modifier = Modifier
                        .wrapContentHeight()
                        .mmtClickable(onClick = { model?.onClicked() })
                        .padding(
                                start = dimensionResource(id = R.dimen.margin_extra_tiny),
                                top = dimensionResource(id = R.dimen.margin_large),
                                bottom = dimensionResource(id = R.dimen.margin_large)
                        ),
                text = stringResource(id = R.string.htl_let_us_know),
                color = colorResource(id = R.color.color_0084ff),
                fontSize = spDimensionResource(id = R.dimen.margin_large),
                fontFamily = latoFont,
                fontWeight = FontWeight.Normal
            )
        }
    }
}

fun getRecentSearchSubtitle(checkinDate : String?, checkoutDate: String?, pax: Int, rooms: Int) : String {
    try {
        val checkIn = DateUtil.changeDateFormat(checkinDate, DateUtil.DATE_FORMAT_MM_dd_yyyy, DateUtil.DATE_FORMAT_DD_MMM)
        val checkOut = DateUtil.changeDateFormat(checkoutDate, DateUtil.DATE_FORMAT_MM_dd_yyyy, DateUtil.DATE_FORMAT_DD_MMM)
        val paxString = HotelStringsProvider.getFormattedQuantityString(R.plurals.htl_labelGuest, pax, pax)
        val roomsString = HotelStringsProvider.getFormattedQuantityString(R.plurals.htl_rs_room, rooms, rooms)
        return ResourceProvider.instance.getString(R.string.htl_recent_search_subtitle, checkIn, checkOut, paxString, roomsString)
    } catch (e: Exception) {
        LogUtils.error("AutoSuggest", "Error in getting recent search subtitle", e)
    }
    return EMPTY_STRING
}


fun getAnnotatedString(text: String, bold: String, textColor: Color): AnnotatedString {
    val fullText = text.lowercase()
    val boldText = bold.lowercase()
    val startIdx = fullText.indexOf(boldText)
    val endIdx = startIdx + boldText.length - 1

    var annotatedString: AnnotatedString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                color = textColor,
                fontWeight = FontWeight.Normal,
                fontFamily = latoFont
            )
        ) {
            append(text)
        }
    }

    try {
        if (startIdx >= 0) {
            annotatedString = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        color = textColor,
                        fontWeight = FontWeight.Normal,
                        fontFamily = latoFont
                    )
                ) {
                    append(text.subSequence(0, startIdx).toString())
                }
                withStyle(
                    SpanStyle(
                        color = textColor,
                        fontWeight = FontWeight.Bold,
                        fontFamily = latoFont
                    )
                ) {
                    append(text.subSequence(startIdx, endIdx + 1).toString())
                }
                withStyle(
                    SpanStyle(
                        color = textColor,
                        fontWeight = FontWeight.Normal,
                        fontFamily = latoFont
                    )
                ) {
                    append(text.subSequence(endIdx + 1, text.length).toString())
                }
            }
        }
    } catch (e: Exception){
        LogUtils.error("AutoSuggest Exception", "FullText: $fullText Query: $boldText", e)
    }

    return annotatedString
}

@Composable
fun ShimmerGridItem(brush: Brush) {
    Row(modifier = Modifier
            .semantics { testTag = ResourceIds.SHIMMER_LIST }
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(all = dimensionResource(id = R.dimen.margin_medium_extra)), verticalAlignment = Alignment.Top) {

        Spacer(modifier = Modifier
                .size(dimensionResource(id = R.dimen.margin_huge))
                .clip(RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny)))
                .background(brush)
        )
        Spacer(modifier = Modifier.width(dimensionResource(id = R.dimen.margin_small_extra)))
        Column(verticalArrangement = Arrangement.Center) {
            Spacer(modifier = Modifier
                    .height(dimensionResource(id = R.dimen.margin_xLarge))
                    .clip(RoundedCornerShape(dimensionResource(id = R.dimen.margin_extra_tiny)))
                    .fillMaxWidth(fraction = 0.5f)
                    .background(brush)
            )

            Spacer(modifier = Modifier.height(dimensionResource(id = R.dimen.margin_extra_small))) //creates an empty space between
            Spacer(modifier = Modifier
                    .height(dimensionResource(id = R.dimen.margin_small_extra))
                    .clip(RoundedCornerShape(dimensionResource(id = R.dimen.margin_extra_tiny)))
                    .fillMaxWidth(fraction = 0.7f)
                    .background(brush)
            )

            Spacer(modifier = Modifier.height(dimensionResource(id = R.dimen.margin_small_extra))) //creates an empty space between
        }
    }
}


@Composable
fun AutoSuggestErrorPage(
    model: HotelDestinationNotFoundViewModel?,
    searchBoxState: LiveData<ListSearchBoxState>?,
    handleEvents: (HotelEvent) -> Unit
){

    Box(modifier = Modifier.fillMaxHeight().fillMaxWidth()) {
    Column(modifier = Modifier.align(Alignment.TopCenter).padding(top = dimensionResource(id = R.dimen.margin_xxHugex)), horizontalAlignment = Alignment.CenterHorizontally) {
        Image(
            painter = painterResource(id = R.drawable.autosuggest_no_result_icon),
            modifier = Modifier
                .semantics { testTag = ResourceIds.ERROR_IMAGE }
                .padding(top = dimensionResource(id = R.dimen.margin_xxHuge))
                .wrapContentHeight()
                .wrapContentWidth(),
            contentDescription = null,
        )

        MmtComposeTextView(
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_large)),
            text = stringResource(id = R.string.htl_no_result_found_autosuggest),
            textAlign = TextAlign.Center,
            color = colorResource(id = R.color.htl_description_text_black_color),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_large),
            mmtFontStyle = latoBlack
        )

        MmtComposeTextView(
            modifier = Modifier
                .padding(top = dimensionResource(id = R.dimen.margin_small))
                .mmtClickable {
                    handleEvents(HotelEvent(HANDLE_SEARCH_AGAIN_CLICK))
                },
            text = stringResource(id = R.string.htl_search_again),
            textAlign = TextAlign.Center,
            color = colorResource(id = R.color.htl_color_008cff),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium),
            mmtFontStyle = latoBold
        )
    }

        AutoSuggestCantFindDestinationCard(modifier = Modifier.align(Alignment.BottomCenter), model = model, searchBoxState = searchBoxState)
    }
}


// ========================================  PREVIEWS ===========================================================
@Preview(showBackground = true)
@Composable
fun LocationCardPreview(){
    SuggestionLocationCard(Modifier, {}, "LOCATION 1", null,"SUBTEXT", "Queen of Hills",  "", R.drawable.default_location_icon, null, false, null)
}

@Preview
@Composable
fun ShowAutoSuggestPreview(){
    ListSearchBox(ListSearchBoxState(MutableLiveData(""), hintText = ResourceProvider.instance.getString(R.string.htl_autosuggest_search_box_hint)), handleEvents = {})
}

@Composable
@Preview(showBackground = true)
fun Shimmer(){

    val transition = rememberInfiniteTransition(label = "")
    val translateAnim by transition.animateFloat(
        /*
         Specify animation positions,
         initial Values 0F means it
         starts from 0 position
        */
        initialValue = 0f,
        targetValue = 1000f,
        animationSpec = infiniteRepeatable(


            // Tween Animates between values over specified [durationMillis]
            tween(durationMillis = 1200, easing = FastOutSlowInEasing),
        ), label = ""
    )

    Column(modifier = Modifier
            .wrapContentHeight()
            .fillMaxWidth()) {
        repeat(10) {
            ShimmerGridItem(
                brush = linearGradient(
                    colors = listOf(
                        Color.LightGray.copy(alpha = 0.7f),
                        Color.LightGray.copy(alpha = 0.4f),
                        Color.LightGray.copy(alpha = 0.7f)
                    ),
                    start = Offset(10f, 10f),
                    end = Offset(translateAnim, translateAnim)
                )
            )
        }
    }

}

@Preview(showBackground = true)
@Composable
fun HeirarchialAutoSuggestItemPreview() {
    HeirarchialAutoSuggestItem(
        Modifier,
        buildAnnotatedString { append("HELLO") },
        "World",
        "HEART OF THE DISTRICT",
        null
    )
}

@Preview(showBackground = true)
@Composable
fun ShowLocationSubtextPreview() {
    ShowLocationSubtext("Heart of the District", "Dehradun asdf asdf asdf asdf asdf asdf asdf")
}