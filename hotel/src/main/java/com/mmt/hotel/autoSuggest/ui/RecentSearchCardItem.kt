package com.mmt.hotel.autoSuggest.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.tooling.preview.Preview
import com.mmt.hotel.R
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.widgets.MMTComposeImageView
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun RecentSearchCardItem(modifier: Modifier = Modifier, title : String?, subtitle : String?) {
    Row(
        modifier = modifier
            .semantics { testTagsAsResourceId = true }
            .fillMaxWidth()
            .padding(
                vertical = dimensionResource(id = R.dimen.margin_medium),
                horizontal = dimensionResource(id = R.dimen.margin_large)
            ), verticalAlignment = Alignment.CenterVertically) {
        MMTComposeImageView(
            painter = painterResource(id = R.drawable.recent_search_icon_v2),
            contentDescription = "",
            Modifier.size(dimensionResource(id = R.dimen.image_dimen_xxmedium))
                .testTag("iv_icon")
        )
        Column(modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = dimensionResource(id = R.dimen.margin_medium))) {
            MmtComposeTextView(
                text = title,
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium),
                mmtFontStyle = latoRegular,
                color = colorResource(id = R.color.black),
                modifier = Modifier.testTag("tv_title")
            )
            MmtComposeTextView(
                text = subtitle,
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
                mmtFontStyle = latoRegular,
                color = colorResource(id = R.color.secondary_color),
                modifier = Modifier.testTag("tv_subtitle")
            )
        }
    }
}

@Preview
@Composable
fun RecentSearchCardItemPreview1() {
    RecentSearchCardItem(
        title = "Goa",
        subtitle = "Goa, India"
    )
}

@Preview
@Composable
fun RecentSearchCardItemPreview2() {
    RecentSearchCardItem(
        title = null,
        subtitle = null
    )
}