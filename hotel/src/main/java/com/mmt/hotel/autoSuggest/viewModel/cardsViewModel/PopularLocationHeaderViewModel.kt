package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.POPULAR_LOCATION_HEADER_CARD
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardsOrder

class PopularLocationHeaderViewModel(val title: String): AutoSuggestStaticCardItem {

    override fun cardPriority(): Int {
        return AutoSuggestStaticCardsOrder.TRENDING_HOTEL_CARD_ORDER.ordinal
    }

    override fun getItemType(): Int {
        return POPULAR_LOCATION_HEADER_CARD
    }
}