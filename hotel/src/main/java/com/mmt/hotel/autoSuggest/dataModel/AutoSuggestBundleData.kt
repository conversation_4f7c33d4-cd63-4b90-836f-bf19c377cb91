package com.mmt.hotel.autoSuggest.dataModel

import android.os.Parcelable
import com.mmt.analytics.omnitureclient.Events
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.autoSuggest.constants.LocusRequestType
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.model.UserSearchData
import kotlinx.parcelize.Parcelize

@Parcelize
data class AutoSuggestBundleData(
    val requestType: LocusRequestType,
    val funnelType: FunnelType,
    val locationContextID: String = EMPTY_STRING,
    val locationName: String = EMPTY_STRING,
    val animX: Int = 0,
    val animY: Int = 0,
    val basePageNameEvent: Events, // used for tracking,
    val source: String? = null,
    val userSearchData: UserSearchData?,
    val funnelStep: HotelPdtV2Constants.FunnelStep =  HotelPdtV2Constants.FunnelStep.landing,
    ) : Parcelable