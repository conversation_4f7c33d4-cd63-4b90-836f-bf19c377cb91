package com.mmt.hotel.autoSuggest.viewModel

import android.os.Parcelable
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.mmt.core.constant.CoreConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.hotel.autoSuggest.constants.LocusRequestType
import com.mmt.hotel.autoSuggest.dataModel.AutoSuggestBundleData
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.EXIT_WITH_LOCUS_DATA_WRAPPER
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.EXIT_WITH_TAG_SELECTION_V2
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.UPDATE_AUTO_SUGGEST_CARDS
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.UPDATE_POPULAR_LOCATIONS_CARD
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.UPDATE_RECENT_SEARCH_CARD
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.UPDATE_SAVED_LOCATION_CARD
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestObservable
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestResponseConverter
import com.mmt.hotel.autoSuggest.helper.requestGenerator.HotelAutoSuggestRequestGenerator
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.model.response.HotelCustomLocationResponse
import com.mmt.hotel.autoSuggest.repository.HotelAutoSuggestRepository
import com.mmt.hotel.autoSuggest.repository.HotelPopularLocationsRepository
import com.mmt.hotel.autoSuggest.repository.HotelSavedLocationRepository
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.common.constants.HotelConstants.AUTO_SUGGEST_THRESHOLD_QUERY_SIZE
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.isHotelsLightUi
import com.pdt.eagleEye.managers.IdsHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okio.ByteString.Companion.encode
import okio.ByteString.Companion.encodeUtf8
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import javax.inject.Inject

class HotelAutoSuggestFragmentViewModel @Inject constructor(
    private val data: AutoSuggestBundleData,
    private val repository: HotelAutoSuggestRepository,
    private val savedLocationRepository: HotelSavedLocationRepository,
    private val popularLocationRepository: HotelPopularLocationsRepository,
    private val generator: HotelAutoSuggestRequestGenerator,
    private val converter: HotelAutoSuggestResponseConverter,
    val autoSuggestObservable: HotelAutoSuggestObservable
): HotelViewModel() {

    private val requestFlow = MutableSharedFlow<String>()
    val searchId = IdsHelper.getRequestId()
    var requestId = ""

    /**
     * saved locations data and recent search data are fetched at same time
     * [eventStream] will not be able to observe both
     * for this reason, this live data is used
     */
    val savedLocationEventStream = MutableLiveData<HotelEvent>()
    val popularLocationEventStream = MutableLiveData<HotelEvent>()
    val showLoader = ObservableBoolean(false)
    val showShimmer = ObservableBoolean(false)

    var showLoaderType:AutoSuggestLoaderType = AutoSuggestLoaderType.SHIMMER_LOADER
    init {
        configureAutoSuggest()
    }

    fun configureAutoSuggest() {
        viewModelScope.launch {
            requestFlow
                .debounce(300)
                .distinctUntilChanged()
                .filter { it.length >= AUTO_SUGGEST_THRESHOLD_QUERY_SIZE }
                .flatMapLatest { queryString -> fetchSuggestedItems(queryString)}
                .catch { emit(mutableListOf()); it.printStackTrace() }
                .collect {
                    updateEventStream(HotelEvent(UPDATE_AUTO_SUGGEST_CARDS, it))
                }
        }
    }

    private fun fetchSuggestedItems(queryString: String):Flow<List<AbstractRecyclerItem>> {
        handleLoaderState(true)
        requestId = IdsHelper.getRequestId()
        autoSuggestObservable.setLoadingViewState(true, showLoaderType)
         return repository.fetchAutoSuggestData(with(data) { generator.generateAutoSuggestUrl(
             URLEncoder.encode(queryString, Charsets.UTF_8.toString()), locationContextID, funnelType,requestType,searchId,requestId)})
             .catch {
                 handleLoaderState(false)
                 autoSuggestObservable.setLoadingViewState(false, showLoaderType)
                 it.printStackTrace()
             }
             .map {
                 handleLoaderState(false)
                 showLoaderType = AutoSuggestLoaderType.PROGRESS_BAR_LOADER
                 val cards = mutableListOf<AbstractRecyclerItem>()
                  cards.addAll(converter.createAutoSuggestCards(data.funnelType, queryString, it, eventStream))
                 cards.add(
                     converter.createDestinationNotFoundCard(
                     queryString,
                    cards.size > 0,
                    eventStream
                ))
                 autoSuggestObservable.setLoadingViewState(false, showLoaderType)
                 cards
        }
    }

    private fun handleLoaderState(state: Boolean) {
        if (showLoaderType == AutoSuggestLoaderType.SHIMMER_LOADER) {
            showShimmer.set(state)
        } else {
            showLoader.set(state)
        }
    }


    fun fetchAutoSuggestList(query: String) {
        viewModelScope.launch { requestFlow.emit(query) }
    }

    fun fetchCustomLocationData(
        latitude: String,
        longitude: String,
        isNearMe: Boolean,
        fetchAsTag: Boolean,
        data: LocusAutoSuggestDataWrapper? = null
    ) {
        viewModelScope.launch {
            val url = generator.fetchCustomLocationUrl(data?.googlePlaceID.orEmpty(), latitude, longitude)
            repository.fetchCustomLocationData(url)
                .map { convertToList(it, fetchAsTag, data, isNearMe) }
                .filter { it != null }.catch { it.printStackTrace() }
                .collect {
                    if (fetchAsTag) {
                        updateEventStream(EXIT_WITH_TAG_SELECTION_V2, it)
                    } else {
                        updateEventStream(EXIT_WITH_LOCUS_DATA_WRAPPER, it)
                    }
                }
        }
    }

    private suspend fun convertToList(
        response: HotelCustomLocationResponse,
        fetchAsTag: Boolean,
        data: LocusAutoSuggestDataWrapper?, isNearMe: Boolean
    ): Parcelable? {
        return withContext(Dispatchers.IO) {
            response.response?.let {
                val convertedData = if (fetchAsTag) {
                    converter.convertToTagSelectionV2(it, data?.displayText.orEmpty())
                } else {
                    converter.convertToLocusDataWrapper(it, isNearMe, data)
                }
                convertedData
            }
        }
    }

    fun fetchRecentSearchData() {
        if(isHotelsLightUi()) return
        viewModelScope.launch {
            repository.fetchRecentSearchData(data.funnelType)
                .map { converter.convertToRecentSearchCard(it, eventStream) }
                .flowOn(Dispatchers.IO)
                .catch { it.printStackTrace() }
                .collect {
                    if (it.isNotEmpty()) {
                        updateEventStream(UPDATE_RECENT_SEARCH_CARD, it)
                    }
                }
        }
    }

    fun fetchRecentSearchDataV2() {
        if(isHotelsLightUi()) return
        viewModelScope.launch {
            repository.fetchRecentSearchDataWithRoomAndPax(5, data.funnelType)
                .map { converter.convertToRecentSearchCardV2(it, eventStream) }
                .flowOn(Dispatchers.IO)
                .catch { it.printStackTrace() }
                .collect {
                    if (it.isNotEmpty()) {
                        updateEventStream(UPDATE_RECENT_SEARCH_CARD, it)
                    }
                }
        }
    }

    fun fetchSavedLocationsData() {
        viewModelScope.launch {
            savedLocationRepository.fetchSavedLocationsData()
                .map {
                    converter.convertToSavedLocationCard(data.funnelType, it, eventStream)
                }
                .catch { it.printStackTrace() }
                .collect {
                    if (it.isNotEmpty()) {
                        savedLocationEventStream.postValue(
                            HotelEvent(
                                UPDATE_SAVED_LOCATION_CARD,
                                it
                            )
                        )
                    }
                }
        }
    }

    fun fetchPopularLocationsData() {
        if(isHotelsLightUi()) return
        val isAreaSearch = isAreaSearch(data.requestType)
        if (!isAreaSearch && data.requestType != LocusRequestType.LANDING_SEARCH) return
        viewModelScope.launch {
            popularLocationRepository.fetchPopularLocationsData(isAreaSearch, data.locationContextID,searchId,requestId)
                .map {
                    converter.convertToPopularLocationCard(data.funnelType, isAreaSearch, data.locationName, it, eventStream)
                }
                .catch { it.printStackTrace() }
                .collect {
                    if (it.isNotEmpty()) {
                        popularLocationEventStream.postValue(HotelEvent(UPDATE_POPULAR_LOCATIONS_CARD, it))
                    }
                }
        }
    }

    private fun isAreaSearch(requestType: LocusRequestType): Boolean {
        return (requestType == LocusRequestType.AREA_POI_SEARCH ||
                requestType == LocusRequestType.GENERIC_SEARCH_WITH_CONTEXT) &&
                !LoginUtil.isCorporateUser()
    }

    fun addNearMeCard(isHotelFunnelOnLanding: Boolean): List<AutoSuggestStaticCardItem> {
        return if(ExperimentUtil.enableStaycationNearMe() && HotelUtil.isPremiumExperience() && isHotelFunnelOnLanding){
            listOf(converter.createStaycationNearMeCard(eventStream))
        } else
            listOf(converter.createNearMeCard(eventStream))
    }

    fun clearEventStreams() {
        // To avoid adding already added sections if fragment is recreated due to configuration change
        autoSuggestObservable.eventStream.value = HotelEvent(CoreConstants.EMPTY_STRING)
        savedLocationEventStream.value = HotelEvent(CoreConstants.EMPTY_STRING)
        popularLocationEventStream.value = HotelEvent(CoreConstants.EMPTY_STRING)
    }

    enum class AutoSuggestLoaderType {
        SHIMMER_LOADER,
        PROGRESS_BAR_LOADER
    }

}