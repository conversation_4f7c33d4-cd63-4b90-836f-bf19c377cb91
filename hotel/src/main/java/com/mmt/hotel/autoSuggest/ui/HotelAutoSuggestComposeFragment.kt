package com.mmt.hotel.autoSuggest.ui

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.rememberNestedScrollInteropConnection
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.lifecycleScope
import com.google.android.material.snackbar.Snackbar
import com.mmt.core.MPermission.PermissionConstants
import com.mmt.core.MPermission.PermissionManager
import com.mmt.core.MPermission.PermissionUtil
import com.mmt.core.MPermission.ui.PermissionSnackBar
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.ActivityResultLifeCycleObserver
import com.mmt.core.extensions.OnActivityResult
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.util.KeyBoardUtils
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.Utils
import com.mmt.core.util.executeIfCast
import com.mmt.data.model.util.LocationUtil
import com.mmt.hotel.R
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.autoSuggest.constants.LocusRequestType
import com.mmt.hotel.autoSuggest.dataModel.AutoSuggestBundleData
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestDataEvents.EXIT_WITH_LOCUS_DATA_WRAPPER
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestDataConverter
import com.mmt.hotel.autoSuggest.helper.HotelAutoSuggestObservable
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.tracking.HotelAutoSuggestTracker
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.RECENT_SEARCH_CLICKED
import com.mmt.hotel.autoSuggest.viewModel.HotelAutoSuggestFragmentViewModel
import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.HotelAutoSuggestCardViewModel
import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.HotelAutoSuggestGroupedItemViewModel
import com.mmt.hotel.autoSuggest.viewModel.cardsViewModel.HotelDestinationNotFoundViewModel
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelComposeFragment
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelsSearchRequestType
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.uikit.util.UiUtil
import com.mmt.uikit.util.isNotNullAndEmpty
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class HotelAutoSuggestComposeFragment: HotelComposeFragment<HotelAutoSuggestFragmentViewModel>(),
    PermissionManager.PermissionInterface, PermissionSnackBar.SnackBarCallback, OnActivityResult {

    private val mPermissionManager by lazy { PermissionManager() }

    @Inject
    lateinit var bundleData:AutoSuggestBundleData

    @Inject
    lateinit var tracker: HotelAutoSuggestTracker

    @Inject
    lateinit var dataConverter: HotelAutoSuggestDataConverter

    lateinit var baseView: ComposeView

    private var observer : ActivityResultLifeCycleObserver? = null

    lateinit var autoSuggestObservable: HotelAutoSuggestObservable

    override fun initViewModel() = getViewModel<HotelAutoSuggestFragmentViewModel>(factory)

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        autoSuggestObservable.liveSearchedQuery.observe(viewLifecycleOwner) {
            val text = autoSuggestObservable.liveSearchedQuery.value
            if (text.isNullOrEmpty()) {
                autoSuggestObservable.liveAutoSuggestCards.value = arrayListOf()
                viewModel.showLoaderType = HotelAutoSuggestFragmentViewModel.AutoSuggestLoaderType.SHIMMER_LOADER
            } else {
                viewModel.fetchAutoSuggestList(text)
            }
        }
        autoSuggestObservable.eventStream.observe(viewLifecycleOwner, { handleEvents(it) })
        viewModel.savedLocationEventStream.observe(viewLifecycleOwner, { handleEvents(it) })
        viewModel.popularLocationEventStream.observe(viewLifecycleOwner, { handleEvents(it) })
        if (isFragmentRecreating()) {
            return
        }
        viewModel.fetchPopularLocationsData()
        if (bundleData.requestType == LocusRequestType.LANDING_SEARCH) {
            val isHotelFunnelOnLanding = bundleData.funnelType == FunnelType.HOTEL_FUNNEL && bundleData.funnelStep == HotelPdtV2Constants.FunnelStep.landing
            autoSuggestObservable.updateStaticCards(viewModel.addNearMeCard(isHotelFunnelOnLanding))
            if(ExperimentUtil.isAutoSuggestUiV2() && bundleData.funnelType != FunnelType.ALT_ACCO_FUNNEL) {
                viewModel.fetchRecentSearchDataV2()
            } else {
                viewModel.fetchRecentSearchData()
            }
            if (LoginUtil.isCorporateUser()) {
                viewModel.fetchSavedLocationsData()
            }
        }
    }

    override fun onStop() {
        viewModel.clearEventStreams()
        super.onStop()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activity?.let {
            lifecycleScope.launchWhenCreated {
                observer = ActivityResultLifeCycleObserver(it.activityResultRegistry,this@HotelAutoSuggestComposeFragment, fragmentID)
                observer?.let{
                    it.registerForResult(HotelConstants.LOCATION_SERVICES_REQUEST_CODE)
                    lifecycle.addObserver(it)
                }
            }
        }
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            HotelAutoSuggestDataEvents.UPDATE_AUTO_SUGGEST_CARDS -> {
                handleAutoSuggestCardsUpdate(event.data as List<AbstractRecyclerItem>)
            }
            HotelAutoSuggestDataEvents.UPDATE_RECENT_SEARCH_CARD, HotelAutoSuggestDataEvents.UPDATE_SAVED_LOCATION_CARD, HotelAutoSuggestDataEvents.UPDATE_POPULAR_LOCATIONS_CARD -> {
                autoSuggestObservable.updateStaticCards(event.data as List<AutoSuggestStaticCardItem>)
            }
            HotelAutoSuggestCardEvents.AUTO_SUGGEST_CARD_CLICK -> with(event.data as Triple<LocusAutoSuggestDataWrapper, Integer?, String>) {
                handleAutoSuggestCardClick(first, second, third)
            }
            HotelAutoSuggestCardEvents.RECENT_SEARCH_CARD_CLICK -> event.data.executeIfCast<SearchRequest> {
                trackRecentSearchClickedEvent(this)
                sendEventToActivity(event)
            }
            HotelAutoSuggestCardEvents.DESTINATION_NOT_FOUND -> {
                bundleData?.let {
                    tracker.trackMissinDestinationData(event.data as String, it.basePageNameEvent)
                }
            }
            EXIT_WITH_LOCUS_DATA_WRAPPER -> {
                event.data.executeIfCast<LocusAutoSuggestDataWrapper> {
                    sendLocusDataToParent(event.data as LocusAutoSuggestDataWrapper)
                }
            }
            HotelAutoSuggestCardEvents.NEAR_ME_CARD_CLICK -> checkForPermission(false)
            HotelAutoSuggestCardEvents.TRACK_STAYCATION_NEAR_ME_CARD_SHOWN -> {
                event.data.executeIfCast<String> {
                    tracker.trackStaycationNearMeShown(this, bundleData.basePageNameEvent)
                }

            }
            HotelAutoSuggestCardEvents.STAYCATION_NEAR_ME_CARD_CLICK -> {
                event.data.executeIfCast<String> {
                    bundleData.let {
                        tracker.trackStaycationNearMeClick(this,it.basePageNameEvent,viewModel.requestId,viewModel.searchId,it.funnelType,it.userSearchData, it.funnelStep)
                    }
                }
                sendEventToActivity(event)
            }
            HotelAutoSuggestCardEvents.ON_BACK_PRESSED -> {
                setFragmentResult(ON_BACK_PRESSED, bundleOf())
                sendEventToActivity(event)
            }
            else -> sendEventToActivity(event)
        }
    }

    private fun trackRecentSearchClickedEvent(searchRequest: SearchRequest) {
        val trackText = RECENT_SEARCH_CLICKED
        bundleData.let { bundleData ->
            tracker.trackRecentSearchItemClick(searchRequest, trackText, bundleData.source,bundleData.basePageNameEvent)
        }
    }

    private fun handleAutoSuggestCardClick(data: LocusAutoSuggestDataWrapper, rank: Integer?, trackText: String) {
        var typedText = "autosuggest_typed_${autoSuggestObservable.liveSearchedQuery.value}"
        data.suggestResult?.let {
            bundleData?.let { bundleData ->
                tracker.trackItemClick(data, trackText, bundleData.source,bundleData.basePageNameEvent, typedText, rank,viewModel.requestId,viewModel.searchId,bundleData.funnelType, bundleData.userSearchData, bundleData.funnelStep)
            }
        }
        when (bundleData?.requestType) {
            LocusRequestType.AREA_POI_SEARCH, LocusRequestType.AREA_POI_SEARCH_NO_CONTEXT -> {
                if (data.googlePlaceID.isNotNullAndEmpty()) {
                    viewModel.fetchCustomLocationData(
                        CoreConstants.EMPTY_STRING, CoreConstants.EMPTY_STRING,
                        false, true, data)
                } else {
                    val tag = dataConverter.convertToTagSelectionV2(data)
                    sendEventToActivity(HotelEvent(HotelAutoSuggestDataEvents.EXIT_WITH_TAG_SELECTION_V2, tag))
                    setFragmentResult(
                        KEY_TAG_SELECTION_V2, bundleOf(
                        KEY_TAG_SELECTION_V2_BUNDLE_DATA to tag)
                    )
                }
            }
            else -> {
                if (data.googlePlaceID.isNotNullAndEmpty() && data.suggestResult?.bounds == null) {
                    viewModel.fetchCustomLocationData(
                        CoreConstants.EMPTY_STRING, CoreConstants.EMPTY_STRING, false, false, data)
                } else {
                    sendLocusDataToParent(data)
                }
            }
        }
    }

    private fun sendLocusDataToParent(data: LocusAutoSuggestDataWrapper) {
        sendEventToActivity(HotelEvent(EXIT_WITH_LOCUS_DATA_WRAPPER, data))
        setFragmentResult(
            KEY_LOCUS_DATA_WRAPPER, bundleOf(
                KEY_LOCUS_DATA_WRAPPER_BUNDLE_DATA to data)
        )
    }

    private fun handleAutoSuggestCardsUpdate(cards: List<AbstractRecyclerItem>) {
        autoSuggestObservable.liveAutoSuggestCards.value = arrayListOf()
        var locationFound = true
        bundleData?.let {
            if (cards.isEmpty() && it.requestType == LocusRequestType.LANDING_SEARCH ||
                cards.size==1 && cards[0] is HotelDestinationNotFoundViewModel
            ) {
                autoSuggestObservable.viewState.value = HotelAutoSuggestUiState.ErrorState(cards.firstOrNull() as? HotelDestinationNotFoundViewModel)
                locationFound = false
                tracker.trackNoLocusItemFound(autoSuggestObservable.liveSearchedQuery.value ?: CoreConstants.EMPTY_STRING, it.basePageNameEvent,it.source)
            }
        }
        if(locationFound) {
            var currList = autoSuggestObservable.liveAutoSuggestCards.value ?: arrayListOf()
            var cardIdx = if(currList.isNullOrEmpty()) { 0 } else { currList.size - 1}
            cards.forEach{
                currList.add(Pair(cardIdx, it))
                if(it is HotelAutoSuggestCardViewModel){
                    cardIdx += (1 + it.childItems.size)
                }else if(it is HotelAutoSuggestGroupedItemViewModel){
                    cardIdx += 1
                }
            }
            if(!(autoSuggestObservable.viewState.value is HotelAutoSuggestUiState.SuccessState)) {
                autoSuggestObservable.viewState.value = HotelAutoSuggestUiState.SuccessState(
                    successState = HotelAutoSuggestFullScreenState(
                        staticCardsList = autoSuggestObservable.liveStaticCards,
                        recommendedCardsList = autoSuggestObservable.liveAutoSuggestCards
                    )
                )
            }
            autoSuggestObservable.liveAutoSuggestCards.value = currList
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): ComposeView  {
        baseView = super.onCreateView(inflater, container, savedInstanceState)
        return baseView
    }

    override fun initFragmentView() {
        autoSuggestObservable = viewModel.autoSuggestObservable
    }

    @Composable
    override fun MainContent() {
        Surface(
            modifier = Modifier.nestedScroll(rememberNestedScrollInteropConnection())
        ) {
            AutoSuggestBaseUi(
                autoSuggestObservable.viewState,
                autoSuggestObservable.getSearchBoxState(),
                autoSuggestObservable::handleClickEvent
            )
        }
    }


    fun handleNearMeClick() {
        if(!checkForNetworkPermission() ) {
            observer?.let { activityLifeCycleObserver->
                activity?.let {
                    HotelUtil.showLocationDialog(it, null, activityLifeCycleObserver)
                }
            }
            return
        }
        bundleData?.let {
            tracker.trackNearMeClick(it.basePageNameEvent,viewModel.requestId,viewModel.searchId,it.funnelType,it.userSearchData, it.funnelStep)
        }
        if (bundleData?.funnelType == FunnelType.GETAWAYS_FUNNEL) {
            val data = LocusAutoSuggestDataWrapper().also {
                it.suggestResult?.type = HotelsSearchRequestType.GPS_NEARBY_SEARCH
            }
            sendLocusDataToParent(data)
            return
        }
        val bestLocation = LocationUtil.getLastKnownLocation()
        if (bestLocation.isNotNullAndEmpty()) {
            val (lat,long) = bestLocation!!.split(CoreConstants.COMMA)
            viewModel.fetchCustomLocationData(lat, long, true, false)
        } else {
            Toast.makeText(context, ResourceProvider.instance.getString(R.string.htl_location_not_found_toast), Toast.LENGTH_LONG).show()
        }
    }



    companion object {
        const val TAG = "HotelAutoSuggestComposeFragment"
        const val KEY_TAG_SELECTION_V2 = "KEY_TAG_SELECTION_V2"
        const val KEY_TAG_SELECTION_V2_BUNDLE_DATA = "KEY_TAG_SELECTION_V2_BUNDLE_DATA"
        const val KEY_LOCUS_DATA_WRAPPER = "KEY_LOCUS_DATA_WRAPPER"
        const val KEY_LOCUS_DATA_WRAPPER_BUNDLE_DATA = "KEY_LOCUS_DATA_WRAPPER_BUNDLE_DATA"
        const val ON_BACK_PRESSED = "on_back_pressed"

        @JvmStatic
        fun newInstance(bundleData: AutoSuggestBundleData): HotelAutoSuggestComposeFragment {
            val fragment = HotelAutoSuggestComposeFragment()
            val args = Bundle().apply {
                putParcelable(HotelAutoSuggestActivity.AUTO_SUGGEST_BUNDLE_DATA, bundleData)
            }
            fragment.arguments = args
            return fragment
        }
    }

    private fun checkForPermission(explanationShown: Boolean) {
        val permission = arrayOf(Manifest.permission.ACCESS_FINE_LOCATION)
        val view = getParentBottomSheetFragmentRootView()
        if (view == null) {
            mPermissionManager.checkForPermission(
                this,
                PermissionUtil.getPermissionGroupMap()[Manifest.permission.ACCESS_FINE_LOCATION],
                explanationShown,
                this,
                permission,
                PermissionConstants.REQUEST_CODE.REQUEST_LOCATION.requestCode,
                this, PermissionConstants.HOTEL_LANDING_PAGE
            )
        } else {
            mPermissionManager.checkForPermissionFragment(
                this,
                PermissionUtil.getPermissionGroupMap()[Manifest.permission.ACCESS_FINE_LOCATION],
                explanationShown,
                this,
                permission,
                PermissionConstants.REQUEST_CODE.REQUEST_LOCATION.requestCode,
                this,
                PermissionConstants.HOTEL_LANDING_PAGE,
                view
            )
        }
    }

    private fun getParentBottomSheetFragmentRootView(): View? {
        KeyBoardUtils.hideKeyboard(activity)
        return parentFragment?.view?.findViewById(R.id.content1)
    }

    private fun showSnackBar(isNeverAskAgain: Boolean) {
        var view = getParentBottomSheetFragmentRootView()
        if (view == null && ::baseView.isInitialized){
            view = baseView
        }
        UiUtil.showSnackbar(view, getString(R.string.htl_please_provide_location_per), Snackbar.LENGTH_LONG)
    }

    override fun onActivityResultReceived(requestCode: Int, resultCode: Int, data: Intent?) {

    }

    private fun checkForNetworkPermission(): Boolean {
        return activity?.let { Utils.isNetworkAvailable(it) && HotelUtil.isLocationEnabled() }?: false
    }


    override fun permissionGranted(pRequestCode: Int) {
        if (pRequestCode == PermissionConstants.REQUEST_CODE.REQUEST_LOCATION.requestCode) {
            handleNearMeClick()
        }
    }

    override fun permissionNotGranted(pRequestCode: Int) {
        if (pRequestCode == PermissionConstants.REQUEST_CODE.REQUEST_LOCATION.requestCode) {
            showSnackBar(false)
        }
    }

    override fun onNeverAskAgainChecked(pRequestCode: Int) {
        if (pRequestCode == PermissionConstants.REQUEST_CODE.REQUEST_LOCATION.requestCode) {
            showSnackBar(true)
        }
    }

    override fun onGrantPermissionClick(mPermissions: Array<out String>?, mRequestCode: Int) {
        if (mRequestCode == PermissionConstants.REQUEST_CODE.REQUEST_LOCATION.requestCode) {
            checkForPermission(true)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        mPermissionManager.onRequestPermissionsResult(this, requestCode, permissions, grantResults, this, null)
    }

    override fun onSettingsClick(mRequestCode: Int) {
        activity?.let {
            if (mRequestCode == HotelConstants.LOCATION_SERVICES_REQUEST) {
                val callGPSSettingIntent = Intent(
                    Settings.ACTION_LOCATION_SOURCE_SETTINGS
                )
                observer?.startActivityForResult(callGPSSettingIntent,
                    HotelConstants.LOCATION_SERVICES_REQUEST_CODE
                )
                Toast.makeText(
                    it,
                    resources.getString(R.string.htl_LOCATION_SERVICES_REQUEST_MSG),
                    Toast.LENGTH_SHORT
                ).show()
            } else {
                Utils.openAppSetting(it)
            }
        }
    }

    override fun onDismissClick() {
        // not required
    }

}