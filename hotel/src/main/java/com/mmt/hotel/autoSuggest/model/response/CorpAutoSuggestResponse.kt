package com.mmt.hotel.autoSuggest.model.response

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class CorpAutoSuggestResponse(
    @SerializedName("locationResponse")
    val locationResponse: List<CorpAutoSuggestLocationResponse>? = null
) : Parcelable

@Parcelize
data class CorpAutoSuggestLocationResponse(
    @SerializedName("groupName")
    val groupName: String? = null,
    @SerializedName("groupid", alternate = ["groupId"])
    val groupId: String,
    @SerializedName("location")
    val location: List<HotelAutoSuggestResponseItem>? = null
) : Parcelable