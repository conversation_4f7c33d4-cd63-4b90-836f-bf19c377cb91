package com.mmt.hotel.autoSuggest.model

import android.os.Parcelable
import com.mmt.core.constant.CoreConstants
import com.mmt.data.model.hotel.hotellocationpicker.response.SuggestResult
import com.mmt.hotel.autoSuggest.model.response.AutoSuggestFilterItem
import com.mmt.hotel.autoSuggest.model.response.CenterLocation
import com.mmt.hotel.autoSuggest.model.response.HotelierTimezoneInfo
import com.mmt.hotel.autoSuggest.model.response.Tag
import com.mmt.hotel.old.model.hotelListingResponse.HotelCalendarCriteria
import com.mmt.hotel.semantic.payload.SemanticPayload
import kotlinx.parcelize.Parcelize

@Parcelize
data class LocusAutoSuggestDataWrapper(
    var itemID: String? = null,
    var locationID: String? = null,
    var locationContext: String? = null,
    var name: String? = null,
    var displayText: String = CoreConstants.EMPTY_STRING,
    var subtext: String = CoreConstants.EMPTY_STRING,
    var displayType: String = CoreConstants.EMPTY_STRING,
    var googlePlaceID: String? = null,
    var suggestResult: SuggestResult? = SuggestResult(),
    var tag: Tag? = null,
    var groupID: String? = null,
    var queryParam: String = "",
    var isClickable: Boolean = true,
    var propertyDetails: String = "",
    var locusContextID: String? = null,
    var locusContextType: String? = null,
    var isFromAltAccoScreen: Boolean = true,
    var position: Int = -1,
    var parentPosition: Int = -1,
    var parentName: String? = null,
    var groupName: String = CoreConstants.EMPTY_STRING,
    var label: String? = null,
    var groupType: String? = null,
    var trackingLocType: String? = null,
    var groupTrackingLocType: String? = null,
    var cityTagline: String? = null,
    var distanceText: String? = null,
    var calendarCriteria: HotelCalendarCriteria? = null,
    var centerLocation: CenterLocation? = null,
    var locusContextName:String?=null,
    var attribute: String? = null,
    var hotelAttributes: List<String>? = null,
    var timezoneInfo: HotelierTimezoneInfo? = null,
    var filter: AutoSuggestFilterItem? = null,
    var maskedPropertyName: Boolean? = null,
    var iconType: String? = null,
    var semanticPayload: SemanticPayload? = null
) : Parcelable