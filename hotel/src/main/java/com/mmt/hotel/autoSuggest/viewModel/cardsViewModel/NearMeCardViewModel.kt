package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.NEAR_ME_CARD
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.NEAR_ME_CARD_CLICK
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardsOrder
import com.mmt.hotel.base.events.HotelEvent

class NearMeCardViewModel(private val eventStream: MutableLiveData<HotelEvent>): AutoSuggestStaticCardItem {

    fun onClick() {
        eventStream.value = HotelEvent(NEAR_ME_CARD_CLICK)
    }

    override fun cardPriority(): Int {
        return AutoSuggestStaticCardsOrder.NEAR_ME_CARD_ORDER.ordinal
    }

    override fun getItemType(): Int {
        return NEAR_ME_CARD
    }
}