package com.mmt.hotel.autoSuggest.viewModel.cardsViewModel

import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.autoSuggest.adapter.adapterItemTypes.HotelAutoSuggestItemTypes.STAYCATION_NEAR_ME_CARD
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents
import com.mmt.hotel.autoSuggest.event.HotelAutoSuggestCardEvents.STAYCATION_NEAR_ME_CARD_CLICK
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardsOrder
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.mobconfig.model.response.PremiumAutoSuggestNearMeCardInfo

class StaycationNearMeCardViewModel(val data : PremiumAutoSuggestNearMeCardInfo, private val eventStream: MutableLiveData<HotelEvent>): AutoSuggestStaticCardItem {

    private var shownEventTracked = false

    fun onClick() {
        HotelUtil.openDeeplink(data.deeplink)
        data.trackingKey?.let {
            // tracking value = staycation_near_me_clicked
            eventStream.value = HotelEvent(STAYCATION_NEAR_ME_CARD_CLICK, "${it}_clicked")
        }
    }

    override fun cardPriority(): Int {
        return AutoSuggestStaticCardsOrder.PREMIUM_NEAR_ME_CARD.ordinal
    }

    override fun getItemType(): Int {
        return STAYCATION_NEAR_ME_CARD
    }

    fun trackShownEvent() {
        if(!shownEventTracked){
            data.trackingKey?.let {
                // tracking value = staycation_near_me_shown
                eventStream.value = HotelEvent(HotelAutoSuggestCardEvents.TRACK_STAYCATION_NEAR_ME_CARD_SHOWN, "${it}_shown")
                shownEventTracked = true
            }
        }
    }
}