package com.mmt.hotel.autoSuggest.tracking

import com.mmt.analytics.ActivityTypeEvent
import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper.OEPK_C_54
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.AUTOSUGGEST_NO_RESULT_FOUND_TEXT
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.USE_LOCATION_CLICKED
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import javax.inject.Inject

class HotelAutoSuggestTracker @Inject constructor(
    private val omnitureTracker: HotelAutoSuggestOmnitureTrackingHelper,
    private val pdtTracker: HotelAutoSuggestPdtTrackingHelper,
    private val pdtTrackerV2 : HotelAutoSuggestPdtTrackingHelperV2
) {

    fun trackPageLoad(basePageNameEvent: Events) {
        omnitureTracker.trackEvent(basePageNameEvent)
    }

    fun trackMissinDestinationData(
        searchedQuery: String,
        basePageNameEvent: Events
    ) {
        omnitureTracker.trackMissingDestinationData(searchedQuery, basePageNameEvent)
    }

    fun trackNearMeClick(basePageNameEvent: Events,requestId: String,
                         searchId: String,
                         funnelType: FunnelType,
                         userSearchData: UserSearchData?, funnelStep: HotelPdtV2Constants.FunnelStep) {
        omnitureTracker.trackClickEvent(
            USE_LOCATION_CLICKED,
            LocusAutoSuggestDataWrapper(),
            pageNameEvent = basePageNameEvent
        )
        pdtTracker.trackEvent(USE_LOCATION_CLICKED, Events.EVENT_HTL_AUTO_SUGGEST_PAGE, ActivityTypeEvent.CLICK)
        pdtTrackerV2.trackNearMeClickPDT(requestId,searchId,funnelType,userSearchData,funnelStep)
    }

    fun trackStaycationNearMeClick(eventName : String, basePageNameEvent: Events,requestId: String,
                         searchId: String,
                         funnelType: FunnelType,
                         userSearchData: UserSearchData?, funnelStep: HotelPdtV2Constants.FunnelStep) {
        omnitureTracker.trackClickEvent(
            eventName,
            LocusAutoSuggestDataWrapper(),
            pageNameEvent = basePageNameEvent
        )
        pdtTrackerV2.trackStaycationNearMeClickPDT(requestId,searchId,funnelType,userSearchData,funnelStep)
    }

    fun trackStaycationNearMeShown(eventName : String, basePageNameEvent: Events) {
        omnitureTracker.trackClickEvent(
            eventName,
            LocusAutoSuggestDataWrapper(),
            pageNameEvent = basePageNameEvent
        )
    }

    fun trackNoLocusItemFound(searchedQuery: String, basePageNameEvent: Events, source: String?) {
        omnitureTracker.trackEventWithDomainSbu(OEPK_C_54, AUTOSUGGEST_NO_RESULT_FOUND_TEXT.format(searchedQuery), basePageNameEvent,source)
    }

    fun trackItemClick(
        item: LocusAutoSuggestDataWrapper, trackText: String, source:String?,
        basePageNameEvent: Events, typedText: String? = EMPTY_STRING, rank: Integer?=null,requestId: String,
        searchId: String,
        funnelType: FunnelType,
        userSearchData: UserSearchData?,
        funnelStep : HotelPdtV2Constants.FunnelStep
    ) {
        omnitureTracker.trackClickEvent(trackText, item, source,basePageNameEvent, typedText, rank)
        pdtTracker.trackEvent(trackText, Events.EVENT_HTL_AUTO_SUGGEST_PAGE,ActivityTypeEvent.CLICK)
        pdtTrackerV2.trackClick(item,trackText,source,basePageNameEvent,typedText,rank,requestId,searchId,funnelType,userSearchData,funnelStep)
    }

    fun trackRecentSearchItemClick(
        searchRequest: SearchRequest,
        trackText: String, source:String?,
        basePageNameEvent: Events, typedText: String? = EMPTY_STRING
    ) {
        omnitureTracker.trackClickEvent(trackText, searchRequest, source,basePageNameEvent, typedText)
    }


}