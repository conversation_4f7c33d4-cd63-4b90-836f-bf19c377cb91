package com.mmt.hotel.autoSuggest.tracking

import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureHelper
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.data.model.hotel.hotellocationpicker.response.SuggestResult
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.AUTOSUGGEST_CARD_CLICK_RANK
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.AUTOSUGGEST_CARD_DISPLAY
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.DOMESTIC
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.EVENT_REPORT_DESTINATION_NOT_FOUND
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.INTERNATIONAL
import com.mmt.hotel.autoSuggest.tracking.constants.HotelAutoSuggestTrackingConstants.MISSING_DESTINATION_TEXT
import com.mmt.hotel.common.constants.HotelConstants.COUNTRY_CODE_INDIA
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.landingV3.model.request.SearchRequest
import javax.inject.Inject

class HotelAutoSuggestOmnitureTrackingHelper @Inject constructor() {

    fun trackEvent(pageNameEvent: Events) {
        val eventParams: MutableMap<String, Any?> = HashMap()
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        eventParams[OmnitureTrackingHelper.OEPK_C_1] = HotelAutoSuggestTrackingConstants.AUTO_SUGGEST_SEARCH
        OmnitureTrackingHelper.trackAppState(pageNameEvent, eventParams)
    }

    fun trackEventWithDomainSbu(eventName: String, eventValue: String, pageNameEvent: Events, source: String? = null) {
        val eventParams: MutableMap<String, Any?> = HashMap()
        eventParams[eventName] = eventValue
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        source?.let {
            eventParams[OmnitureTrackingHelper.OEPK_C_41] = it
        }
        OmnitureTrackingHelper.trackAppState(pageNameEvent, eventParams)
    }

    fun trackMissingDestinationData(
        searchedQuery: String,
        pageNameEvent: Events
    ) {
        val eventParams: MutableMap<String, Any> = HashMap()
        eventParams[OmnitureTrackingHelper.M_V22] = searchedQuery
        eventParams[OmnitureTrackingHelper.OEPK_V_54] = MISSING_DESTINATION_TEXT.format(searchedQuery)
        eventParams[OmnitureTrackingHelper.M_C8] = EVENT_REPORT_DESTINATION_NOT_FOUND
        eventParams[OmnitureTrackingHelper.OEPK_C_1] = HotelAutoSuggestTrackingConstants.AUTO_SUGGEST_SEARCH_PAGE
        OmnitureTrackingHelper.trackAppState(pageNameEvent, eventParams)
    }

    fun trackEvent(eventName: String, eventValue: String, pageNameEvent: Events) {
        val eventParams: MutableMap<String, Any?> = HashMap()
        eventParams[eventName] = eventValue
        OmnitureTrackingHelper.trackAppState(pageNameEvent, eventParams)
    }

    fun trackClickEvent(
        prop50Event: String?,
        locusData: LocusAutoSuggestDataWrapper,
        source: String? = null,
        pageNameEvent: Events,
        typedText: String? = EMPTY_STRING,
        rank: Integer? = null
    ) {
        val selectedSuggestion = locusData.suggestResult
        val eventParams: MutableMap<String, Any?> = HashMap()
        eventParams[OmnitureTrackingHelper.OEPK_C_50] = prop50Event
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        eventParams[OmnitureTrackingHelper.OEPK_C_8] = typedText
        if(rank != null) {
            eventParams[OmnitureTrackingHelper.OEPK_m44] = AUTOSUGGEST_CARD_CLICK_RANK+rank.toString()
        }
        eventParams[OmnitureTrackingHelper.OPEK_m29] = AUTOSUGGEST_CARD_DISPLAY + locusData.displayText
        eventParams[OmnitureTrackingHelper.OEPK_C_27] =
            selectedSuggestion?.type + CoreConstants.PIPE_SEPARATOR +
                    selectedSuggestion?.originalLocusType +
                    "|" + selectedSuggestion?.displayText +
                    "|" + if (HotelUtil.isDom(
                    selectedSuggestion?.countryCode ?: COUNTRY_CODE_INDIA
                )
            )
                DOMESTIC else INTERNATIONAL
        eventParams[OmnitureTrackingHelper.OEPK_C_54] = getLocusTrackText(selectedSuggestion)
        eventParams[OmnitureTrackingHelper.OEPK_C_52] = locusData.position.toString() + CoreConstants.PIPE_SEPARATOR + locusData.queryParam
        eventParams[OmnitureTrackingHelper.OEPK_V_3] = "|" + selectedSuggestion?.cityName
        eventParams[OmnitureTrackingHelper.OEPK_C_1] = HotelAutoSuggestTrackingConstants.AUTO_SUGGEST_SEARCH
        source?.let {
            eventParams[OmnitureTrackingHelper.OEPK_C_41] = it
        }
        OmnitureTrackingHelper.trackAppState(pageNameEvent, eventParams)
    }

    fun trackClickEvent(
        prop50Event: String?,
        request: SearchRequest,
        source: String? = null,
        pageNameEvent: Events,
        typedText: String? = EMPTY_STRING,
        rank: Int? = null
    ) {
        val userSearchData = request.userSearchData
        val eventParams: MutableMap<String, Any?> = HashMap()
        eventParams[OmnitureTrackingHelper.OEPK_C_50] = prop50Event
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        eventParams[OmnitureTrackingHelper.OEPK_C_8] = typedText
        if(rank != null) {
            eventParams[OmnitureTrackingHelper.OEPK_m44] = AUTOSUGGEST_CARD_CLICK_RANK+rank.toString()
        }
        eventParams[OmnitureTrackingHelper.OPEK_m29] = AUTOSUGGEST_CARD_DISPLAY + userSearchData?.displayName
        eventParams[OmnitureTrackingHelper.OEPK_C_27] =
            userSearchData?.searchType + CoreConstants.PIPE_SEPARATOR +
                    userSearchData?.originalLocusType +
                    "|" + userSearchData?.displayName +
                    "|" + if (HotelUtil.isDom(
                    userSearchData?.countryCode ?: COUNTRY_CODE_INDIA
                )
            )
                DOMESTIC else INTERNATIONAL
        eventParams[OmnitureTrackingHelper.OEPK_V_3] = "|" + userSearchData?.locationName
        eventParams[OmnitureTrackingHelper.OEPK_C_1] = HotelAutoSuggestTrackingConstants.AUTO_SUGGEST_SEARCH
        source?.let {
            eventParams[OmnitureTrackingHelper.OEPK_C_41] = it
        }
        OmnitureTrackingHelper.trackAppState(pageNameEvent, eventParams)
    }

    private fun getLocusTrackText(selectedSuggestion: SuggestResult?): String {
        if (selectedSuggestion?.originalLocusType.isNullOrEmpty()) {
            return EMPTY_STRING
        }
        val locusTrackBuilder = StringBuilder("and_LP_")
        locusTrackBuilder.append(selectedSuggestion?.originalLocusType?.uppercase())
        if (selectedSuggestion?.isHasGroupId == true) {
            locusTrackBuilder.append("_AsChild")
        }
        locusTrackBuilder.append("_CLICKED")
        locusTrackBuilder.append("_")
        locusTrackBuilder.append(selectedSuggestion?.displayText)
        return locusTrackBuilder.toString()
    }
}