package com.mmt.hotel.autoSuggest.ui

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.hotel.autoSuggest.helper.AutoSuggestStaticCardItem
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.common.constants.FunnelType

data class ListSearchBoxState (
        var queryText: MutableLiveData<String> = MutableLiveData(EMPTY_STRING),
        var showLoader: MutableLiveData<Boolean> = MutableLiveData(false),
        var hintText: String = EMPTY_STRING,
        val funnelType: FunnelType = FunnelType.HOTEL_FUNNEL
    )


data class HotelAutoSuggestFullScreenState(
    var staticCardsList: LiveData<ArrayList<Pair<Int, AutoSuggestStaticCardItem>>>,
    var recommendedCardsList: LiveData<ArrayList<Pair<Int, AbstractRecyclerItem>>>
)