package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.MMTFontStyle
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.landingV3.viewModel.adapter.LandingCardsViewModel
import com.mmt.hotel.widget.compose.MmtComposeTextView

@Composable
fun BannerTextCardLayout(data: LandingCardsViewModel) {
    Box(modifier = Modifier
        .padding(dimensionResource(id = R.dimen.margin_large))
        .fillMaxWidth()
        .height(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_image_height))
        .clip(shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny)))
        .testTag("banner_text_card_layout")) {
        LoadImage(
            modifier = Modifier.height(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_image_height)).testTag("banner_text_card_image"),
            imageUrl = data.imageUrl,
            placeHolderId = R.drawable.bgdefault_bg
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_image_height))
                .testTag("banner_text_card_content")
        ) {
            BannerCardTitle(data)
            BannerTextCardSubHeader(data = data)
            BannerTextCardCta(data = data)
        }
    }
}

@Composable
private fun BannerCardTitle(data: LandingCardsViewModel) {
    MmtComposeTextView(
        text = data.title,
        fontSize = spDimensionResource(id = R.dimen.htl_text_size_large),
        color = colorResource(id = R.color.white),
        mmtFontStyle = latoBlack,
        maxLines = 2,
        overflow = TextOverflow.Clip,
        modifier = Modifier
            .padding(
                start = dimensionResource(id = R.dimen.margin_xLarge),
                top = dimensionResource(id = R.dimen.margin_medium_extra)
            )
            .width(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_sub_header_width))
            .testTag("banner_text_card_title")
    )
}

@Composable
fun BannerTextCardSubHeader(data: LandingCardsViewModel) {
    MmtComposeTextView(
        text = data.subTitle,
        fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
        color = colorResource(id = R.color.home_row_divider),
        mmtFontStyle = latoBold,
        maxLines = 2,
        overflow = TextOverflow.Clip,
        modifier = Modifier
            .padding(
                start = dimensionResource(id = R.dimen.margin_xLarge),
                top = dimensionResource(id = R.dimen.margin_tiny)
            )
            .width(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_sub_header_width))
            .testTag("banner_text_card_sub_header")
    )
}

@Composable
fun BannerTextCardCta(data: LandingCardsViewModel) {
    Box(modifier = Modifier
        .padding(
            start = dimensionResource(id = R.dimen.margin_xLarge),
            top = dimensionResource(id = R.dimen.margin_large)
        )
        .width(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_cta_width))
        .height(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_cta_height))
        .clip(shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_alt_acco_landing_host_card_cta_radius)))
        .background(colorResource(id = R.color.white))
        .mmtClickable { data.onCtaClick() }.testTag("banner_card_cta"), contentAlignment = Alignment.Center) {
        MmtComposeTextView(
            text = data.getCtaText(),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            color = colorResource(id = R.color.bb_selected_color),
            mmtFontStyle = latoBlack,
            textAlign = TextAlign.Center
        )
    }
}