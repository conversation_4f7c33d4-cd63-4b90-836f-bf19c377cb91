package com.mmt.hotel.altacco.ui

import android.content.Context
import android.content.Intent
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.core.MMTCore
import com.mmt.core.extensions.ActivityResultLifeCycleObserver
import com.mmt.core.util.executeIfCast
import com.mmt.hotel.R
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.altacco.viewModel.AltAccoLandingViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.events.TrackEvent
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelConstants.FROM_LISTING
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelPageActionName
import com.mmt.hotel.common.extensions.openFragment
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.databinding.ActivityAltAccoBinding
import com.mmt.hotel.landingV3.event.LandingEvents
import com.mmt.hotel.landingV3.event.LandingEvents.CHAT_CLICKED
import com.mmt.hotel.landingV3.event.LandingEvents.CHAT_MESSAGE_STATUS
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.tracking.LandingTrackingConstants
import com.mmt.hotel.landingV3.ui.HotelLandingActivityV3
import com.mmt.hotel.landingV3.ui.HotelLandingBaseActivity
import com.mmt.hotel.landingV3.ui.LandingBaseFragment
import com.mmt.hotel.landingV3.viewModel.HotelLandingActivityV3ViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject


/**
 * Created by Gurtek Singh on 21/04/21.
 */

@AndroidEntryPoint
class AltAccoLandingActivity : HotelLandingBaseActivity<AltAccoLandingViewModel, ActivityAltAccoBinding>() {

    @Inject lateinit var tracker: AltAccoTracker

    override fun getLayoutId() = R.layout.activity_alt_acco

    override fun createViewModel(): AltAccoLandingViewModel {
        return getViewModel(factory)
    }

    override fun getScreenTheme(): Int {
        return  R.style.Theme_CosmosTheme
    }

    override fun initScreen() {
        loadLandingFragment()
    }

    override fun getBackGroundColor(): Int {
        return R.color.grey_EF
    }

    override fun setDataBinding() {
        with(viewDataBinding) {
            viewModel = <EMAIL>
            executePendingBindings()
            toolbar.viewModel = viewModel
            toolbar.executePendingBindings()
        }
    }

    override fun mobConfigResultRequired(): Boolean {
        return true
    }

    override fun trackEvents(event: HotelEvent) {
        when(event.eventID) {
            CHAT_MESSAGE_STATUS -> {
                event.data.executeIfCast<String> {
                    tracker.trackChatMessageStatus(this)
                }
            }
            LandingEvents.WISHLIST_ICON_CLICKED -> {
                tracker.trackWishlistIconClick()
            }
            CHAT_CLICKED -> {
                tracker.trackChatClicked()
            }
            else -> {

            }
        }
    }

    override fun getCurrentVisibleFragment(): LandingBaseFragment<*, *>? {
        return (supportFragmentManager.findFragmentByTag(ALT_ACCO_FRAGMENT_TAG) as? LandingBaseFragment<*, *>)
    }

    private fun loadLandingFragment() {
        supportFragmentManager.openFragment(
            landingFactory.create(data.copy(), HotelFunnel.HOMESTAY, false), R.id.container, tag = ALT_ACCO_FRAGMENT_TAG
        )
    }

    companion object {
        const val AREA_FIELD_EDITABLE = "AREA_FIELD_EDITABLE"
        const val IS_FROM_APP_LANDING = "IS_FROM_LANDING"
        const val REQUEST_TRANSPARENT_BACKGROUND = "REQUEST_TRANSPARENT_BACKGROUND"
        const val ALT_ACCO_FRAGMENT_TAG = "AltAccoLandingFragment"

        fun newInstance(context: Context, searchRequest: SearchRequest?, isFromAppLanding: Boolean,
                        areaFieldEditable: Boolean, transParentBackGround: Boolean = false, isFromListing: Boolean = false): Intent {
            return Intent(context, AltAccoLandingActivity::class.java).apply {
                putExtra(HotelConstants.KEY_HOTEL_SEARCH_REQUEST_V2, searchRequest)
                putExtra(IS_FROM_APP_LANDING, isFromAppLanding)
                putExtra(FROM_LISTING, isFromListing)
                putExtra(AREA_FIELD_EDITABLE, areaFieldEditable)
                putExtra(REQUEST_TRANSPARENT_BACKGROUND,transParentBackGround)
            }
        }
        fun newInstance(searchRequest: SearchRequest?, isFromAppLanding: Boolean,
                        areaFieldEditable: Boolean, transParentBackGround: Boolean = false, isFromListing: Boolean = false): Intent {
            return Intent(HotelPageActionName.ALT_ACCO_LANDING_V2).apply {
                putExtra(HotelConstants.KEY_HOTEL_SEARCH_REQUEST_V2, searchRequest)
                putExtra(IS_FROM_APP_LANDING, isFromAppLanding)
                putExtra(FROM_LISTING, isFromListing)
                putExtra(AREA_FIELD_EDITABLE, areaFieldEditable)
                putExtra(REQUEST_TRANSPARENT_BACKGROUND, transParentBackGround)
                setPackage(MMTCore.mContext.packageName)
            }
        }
    }

    override fun onActivityResultReceived(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            HotelLandingActivityV3ViewModel.LISTING_REQUEST_CODE -> {
                getCurrentVisibleFragment()?.refetchAndUpdateSearchRequest()
            }
        }
    }

    override fun onHandleBackPress() {
        try {
            if (!isFinishing && supportFragmentManager.backStackEntryCount > 1) {
                supportFragmentManager.popBackStack()
            } else {
                if (getUserSearchData()?.userInputMandatory == true) {
                    setResult(HotelLandingActivityV3.MANDATORY_INPUT_NOT_PROVIDED)
                }
                finish()
            }
        } catch (_: Exception) {}
    }

    override fun registerActivityResultReceiver() {
        observer = ActivityResultLifeCycleObserver(activityResultRegistry,this, activityID)
        observer?.let {
            it.registerForResult(HotelLandingActivityV3ViewModel.LISTING_REQUEST_CODE)
            lifecycle.addObserver(it)
        }
    }
}