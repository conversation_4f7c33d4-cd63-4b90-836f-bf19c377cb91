package com.mmt.hotel.altacco.helper

import com.mmt.data.model.homepage.empeiria.cards.recentsearches.RecentSearchesCardData
import com.mmt.data.model.homepage.empeiria.response.EmpeiriaResponse
import com.mmt.hotel.altacco.model.ui.AltAccoChatData
import com.mmt.hotel.landingV3.helper.LandingResponseConverter
import javax.inject.Inject

/**
 * Created by Keshav Aggarwal on 17,June,2021
 */
class AltAccoLandingResponseConverter @Inject constructor() : LandingResponseConverter() {

    fun fetchChatData(response: EmpeiriaResponse): AltAccoChatData? {
        response.data?.baseSheet?.cardData?.values?.forEach { cardTemplateData ->
            if (cardTemplateData is RecentSearchesCardData) {
                cardTemplateData.data?.chatDetails?.let {
                    return AltAccoChatData(it, 0,0, cardTemplateData.trackingKey)
                }
            }
        }
        return null
    }

}