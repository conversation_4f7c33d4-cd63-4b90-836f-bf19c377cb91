package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadDownloadedWebpImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.service.LottieDownloadInfo
import com.mmt.hotel.widget.compose.MmtComposeTextView
import kotlinx.coroutines.delay


@OptIn(ExperimentalComposeUiApi::class)
@Composable
@Preview
fun AltAccoListingLoadingScreen() {

    var changeLoaderWidth by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(500)
        changeLoaderWidth = true
    }

    val loaderWidth by animateDpAsState(
        targetValue =
        if (changeLoaderWidth)
            dimensionResource(id = R.dimen.aa_trending_card_width)
        else
            dimensionResource(id = R.dimen.htl_listing_mmt_black_container_min_height),
        tween(500), label = ""
    )

    Column(
        Modifier
            .fillMaxSize()
            .background(color = colorResource(id = R.color.white))
            .padding(
                start = dimensionResource(id = R.dimen.htl_card_radius),
                end = dimensionResource(id = R.dimen.htl_card_radius)
            ).semantics { testTagsAsResourceId = true },
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        LoadDownloadedWebpImage(
            modifier = Modifier
                .fillMaxHeight(0.5f)
                .wrapContentWidth(),
            animationFilePath = LottieDownloadInfo.altAccoListingLoaderFileName,
            alignment = Alignment.BottomCenter
        )
        MmtComposeTextView(
            text = stringResource(id = R.string.htl_alt_acco_listing_loader_label),
            mmtFontStyle = latoBlack,
            color = colorResource(id = R.color.black),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium)
        )
        Spacer(modifier = Modifier
            .padding(top = dimensionResource(id = R.dimen.margin_tiny))
            .width(loaderWidth)
            .height(dimensionResource(id = R.dimen.margin_tiny))
            .background(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        colorResource(id = R.color.htl_pl_button_gradient_start_color),
                        colorResource(id = R.color.htl_pl_button_gradient_end_color)
                    )
                )
            )
        )
        MmtComposeTextView(
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_small)),
            text = stringResource(id = R.string.htl_loading_screen_text),
            mmtFontStyle = latoRegular,
            color = colorResource(id = R.color.htl_pl_dark_grey),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium),
            textAlign = TextAlign.Center
        )
    }
}