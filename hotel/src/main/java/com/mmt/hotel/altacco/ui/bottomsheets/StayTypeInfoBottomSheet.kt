package com.mmt.hotel.altacco.ui.bottomsheets

import android.os.Bundle
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.bottomsheets.viewmodel.StayTypeInfoBottomSheetViewModel
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelBottomSheetDialogFragment
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.databinding.HtlStayTypeInfoBottomSheetBinding
import com.mmt.hotel.detail.model.response.places.DirectionsToReach
import com.mmt.hotel.detail.ui.fragments.DirectionsToReachBottomSheet
import com.mmt.hotel.listingV2.model.response.hotels.ExtraDetails
import com.mmt.uikit.util.fragment.put
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class StayTypeInfoBottomSheet: HotelBottomSheetDialogFragment<StayTypeInfoBottomSheetViewModel, HtlStayTypeInfoBottomSheetBinding>() {

    companion object {
        const val TAG = "StayTypeInfoBottomSheet"
        private const val BUNDLE_DATA = "bundle_data"
        private const val TITLE = "title"

        fun getInstance(title : String, actionType: String): StayTypeInfoBottomSheet {
            return StayTypeInfoBottomSheet().apply {
                arguments = Bundle().apply {
                    putString(BUNDLE_DATA, actionType)
                    putString(TITLE, title)
                }
            }
        }
    }

    val data by lazy {
        arguments?.getString(BUNDLE_DATA) ?: String
    }

    @Inject
    lateinit var viewModelFactory: HotelViewModelFactory
    override fun initViewModel() = getViewModel<StayTypeInfoBottomSheetViewModel>(viewModelFactory)

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId() = R.layout.htl_stay_type_info_bottom_sheet

    override fun initFragmentView() {
        val title = arguments?.getString(TITLE)
        viewModel.initViewModel(title = title, data.toString())
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            StayTypeInfoBottomSheetViewModel.DISMISS_BOTTOM_SHEET -> {
                dismissFragment()
            }
        }
    }

    fun dismissFragment() {
        dismiss()
    }
}