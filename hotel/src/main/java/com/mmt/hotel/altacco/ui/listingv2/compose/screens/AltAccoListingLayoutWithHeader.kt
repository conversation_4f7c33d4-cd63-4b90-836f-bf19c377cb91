package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import com.mmt.hotel.altacco.ui.listingv2.compose.components.AltAccoListingHeaderBg
import com.mmt.hotel.listingV2.model.ui.HotelListingUiState

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun AltAccoListingLayoutWithHeader(
    uiState: State<HotelListingUiState>,
    handleEvent: (String, Any?) -> Unit
) {
    val state = uiState.value
    var isExpanded = true
    var url : String? = null
    var mediaType : String? = null

     when (uiState.value) {
        is HotelListingUiState.Success -> {
            url = (state as HotelListingUiState.Success).uiData.headerUiData.altAccoExtraHeaderUiData?.mediaUrl
            mediaType = state.uiData.headerUiData.altAccoExtraHeaderUiData?.mediaType
            isExpanded = state.uiData.headerUiData.altAccoExtraHeaderUiData?.isExpanded == true
        }
        is HotelListingUiState.PropertyLoading -> {
            url = (state as HotelListingUiState.PropertyLoading).headerUiData.altAccoExtraHeaderUiData?.mediaUrl
            mediaType = state.headerUiData.altAccoExtraHeaderUiData?.mediaType
            isExpanded = state.headerUiData.altAccoExtraHeaderUiData?.isExpanded == true
        }

        else -> { null }
    }


    Box(modifier = Modifier
        .fillMaxSize()
        .semantics {
            testTagsAsResourceId = true
        }
    ) {
        AltAccoListingHeaderBg(isExpanded,url, mediaType)
        AltAccoListingSuccessView(uiState = uiState, handleEvent = handleEvent)
    }
}