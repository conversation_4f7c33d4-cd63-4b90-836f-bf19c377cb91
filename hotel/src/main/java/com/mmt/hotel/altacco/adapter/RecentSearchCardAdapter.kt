package com.mmt.hotel.altacco.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.viewHolder.RecentSearchCardViewHolder
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.HotelBaseRecyclerAdapter
import com.mmt.hotel.base.ui.viewHolder.HotelGeneralRecyclerViewHolder
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.landingV3.event.LandingCardAdapterKeys

class RecentSearchCardAdapter : HotelBaseRecyclerAdapter(arrayListOf()) {

    override fun getViewHolder(viewType: Int, layoutInflater: LayoutInflater, parent: ViewGroup): HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem> {
        return when (viewType) {
            LandingCardAdapterKeys.RECENT_SEARCH_CARD_V2 -> RecentSearchCardViewHolder(layoutInflater, parent)
            else -> HotelGeneralRecyclerViewHolder(layoutInflater, R.layout.item_dummy_view, parent)
        } as HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>
    }
}