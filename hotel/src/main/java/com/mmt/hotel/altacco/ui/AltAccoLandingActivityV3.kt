package com.mmt.hotel.altacco.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.fragment.app.FragmentResultListener
import com.mmt.auth.login.model.LoginPageExtra
import com.mmt.auth.login.mybiz.SelectEmployeeActivity
import com.mmt.auth.login.util.LoginUtils
import com.mmt.auth.login.util.LoginUtils.LOGIN_PAGE_EXTRA
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.ActivityResultLifeCycleObserver
import com.mmt.core.extensions.OnActivityResult
import com.mmt.core.interfaces.AppInterfaceHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.executeIfCast
import com.mmt.core.util.performIfActivityActive
import com.mmt.data.model.calendarv2.CalendarDay
import com.mmt.hotel.R
import com.mmt.hotel.altacco.event.LandingV2DataEvents
import com.mmt.hotel.altacco.model.ui.AltAccoChatData
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants
import com.mmt.hotel.altacco.ui.landingv2.compose.AltAccoLandingScreen
import com.mmt.hotel.altacco.viewModel.AltAccoLandingViewModelV3
import com.mmt.hotel.analytics.pdtMetrics.helper.HotelPdtV2MetricsHelper.MetricsEntityId.ALTACCO_LANDING_SCREEN_RENDER
import com.mmt.hotel.analytics.pdtMetrics.helper.TraceMetricsOnLifecycleStop
import com.mmt.hotel.analytics.pdtMetrics.helper.TraceScreenRenderedOnNextFrame
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.ui.HotelAutoSuggestActivity
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelPageActionName
import com.mmt.hotel.common.extensions.setFragmentResultListener
import com.mmt.hotel.common.extensions.toOccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.HotelRequestConstants
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.ui.HotelComposeBaseActivity
import com.mmt.hotel.common.ui.bottomsheet.LandingContainerBottomSheet
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.landingV3.event.HotelLandingEmperiaEvents
import com.mmt.hotel.landingV3.event.HotelLandingSearchModifyEvents
import com.mmt.hotel.landingV3.event.LandingEvents
import com.mmt.hotel.landingV3.helper.BundleCreatorHelper
import com.mmt.hotel.landingV3.helper.HotelLandingSearchClickHelper
import com.mmt.hotel.landingV3.helper.LocusUtilsV2
import com.mmt.hotel.landingV3.model.EmperiaEventData
import com.mmt.hotel.landingV3.model.HotelLandingLocationData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.model.response.CardList
import com.mmt.hotel.landingV3.ui.HotelCalenderActivityV2
import com.mmt.hotel.landingV3.ui.HotelLandingActivityV4
import com.mmt.hotel.landingV3.ui.SearchModifyBaseFragment
import com.mmt.hotel.landingV3.ui.compose.bottomSheets.RoomAndGuestBottomSheetCompose
import com.mmt.hotel.landingV3.viewModel.HotelLandingActivityV3ViewModel
import com.mmt.hotel.listingV2.event.HotelListingClickEvents
import com.mmt.hotel.listingV2.ui.fragments.HotelMultiCurrencyBS
import com.mmt.hotel.service.LottieDownloadHelper
import com.mmt.uikit.util.isNotNullAndEmpty
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlin.math.min

@AndroidEntryPoint
class AltAccoLandingActivityV3 : HotelComposeBaseActivity<AltAccoLandingViewModelV3>(), OnActivityResult {

    @Inject
    lateinit var factory: HotelViewModelFactory

    var observer : ActivityResultLifeCycleObserver? = null

    @Inject
    lateinit var searchHelper: HotelLandingSearchClickHelper
    @Inject
    lateinit var bundleHelper: BundleCreatorHelper

    override fun handleEvents(event: HotelEvent) {
        when(event.eventID) {
            HotelLandingSearchModifyEvents.SEARCH_PERFORMED -> {
                if (viewModel.canProceedWithSearch()) {
                    viewModel.getSearchRequest()?.let { handleSearchClicked(it) }
                }
            }
            HotelLandingSearchModifyEvents.CHECK_IN_CLICKED -> {
                viewModel.getSearchRequest()?.let {
                    it.userSearchData?.let { searchData ->
                        viewModel.altAccoTracker.trackCalenderCheckInClicked(searchData)
                    }
                    openCheckInOutDateBottomSheet(true, it)
                }
            }
            HotelLandingSearchModifyEvents.CHECK_OUT_CLICKED -> {
                viewModel.getSearchRequest()?.let {
                    it.userSearchData?.let { searchData ->
                        viewModel.altAccoTracker.trackCalenderCheckOutClicked(searchData)
                    }
                    openCheckInOutDateBottomSheet(false, request = it)
                }
            }
            LandingEvents.CHAT_CLICKED -> {
                viewModel.hotelLandingTracker.trackChatIconClicked()
                HotelMigratorHelper.instance.postCustomerUnreadMessageData(
                    this,
                    viewModel.getUnreadMessageData()?.copy(unreadMsgCount = null)
                )
            }
            LandingEvents.CHANGE_STATUS_BAR_COLOR_TO_WHITE -> {
                HotelUtil.setLightStatusBar(this)
            }
            LandingEvents.CHANGE_STATUS_BAR_COLOR_TO_TRANSPARENT -> {
                HotelUtil.setTransparentStatusBar(this)
            }
            HotelLandingSearchModifyEvents.OPEN_LOCATION_PICKER -> {
                viewModel.getUserSearchData()?.let {
                    viewModel.altAccoTracker.trackOnDestinationBlockClicked(it)
                }
                openLocationPickerActivity(viewModel.getUserSearchData(), HotelUtil.getFunnelType(viewModel.getCurrentFunnel().funnelValue))
            }
            LandingEvents.RECENT_SEARCH_CLICKED -> {
                event.data.executeIfCast<Pair<String, SearchRequest>> {
                    recentSearchClicked(this)
                }
            }
            LandingEvents.CARD_CLICKED -> {
                event.data.executeIfCast<Pair<String, CardList>> {
                    handleCardClicked(this)
                }
            }
            LandingEvents.HOST_CARD_CLICKED -> {
                event.data.executeIfCast<String> {
                    viewModel.altAccoTracker.trackHostCardClicked()
                    HotelMigratorHelper.instance.openDeepLink(event.data as String, true)
                }
            }
            LandingEvents.ON_BACK_PRESSED -> finish()
            HotelListingClickEvents.OPEN_CURRENCY_BOTTOMSHEET -> {
                openCurrencyBottomSheet()
            }
            HotelLandingSearchModifyEvents.OPEN_ROOM_AND_GUEST_FRAGMENT -> {
                viewModel.getSearchRequest()?.let {
                    it.userSearchData?.let { searchData ->
                        viewModel.altAccoTracker.trackOnRoomAndGuestSelectionClicked(searchData)
                    }
                    openRoomAndGuestFragment(it)
                }
            }
            LandingV2DataEvents.IMAGE_ARROW_CLICKED ->  {
                event.data.executeIfCast<Pair<String, String>> {
                    if (this.first.isNotEmpty()){
                        openDeepLink(this.first)
                        if (this.second.isNotEmpty()){
                            val propName = this.second.substring(0, min((this.second.length -1), 20))
                            viewModel.altAccoTracker.trackInProp1PageExit(AltAccoTrackingConstants.PROPERTY_CLICK.format(propName))
                        }
                    }
                }
            }
            LandingEvents.CHAT_CARD_CLICKED -> {
                event.data.executeIfCast<AltAccoChatData> {
                    viewModel.altAccoTracker.trackChatCardClicked(this)
                    chatData.deeplink?.let {
                        HotelMigratorHelper.instance.openDeepLink(it, true)
                    }
                }
            }
            LandingEvents.WISHLIST_ICON_CLICKED -> {
                viewModel.altAccoTracker.trackWishlistIconClick()
                if (LoginUtils.isLoggedIn) {
                    HotelMigratorHelper.instance.openWishlistActivity(this, observer)
                } else {
                    openLoginActivity(ResourceProvider.instance.getString(R.string.htl_login_header_for_wishlist))
                }
            }
            HotelLandingEmperiaEvents.EMPERIA_DATA -> {
                event.data.executeIfCast<Pair<HotelFunnel, EmperiaEventData?>> {
                    second?.let {
                        it.locationData?.let { updateLocation(it) }
                        handleEmperiaData(it)
                    }
                }
            }
            else -> viewModel.handleEvents(event)
        }
    }

    override fun initScreenMetricsTracker() {
        viewModel.getUserSearchData()?.let {
            screenMetricsTracker.initTracker(
                userSearchData = it,
                pageName = HotelPdtV2Constants.PageName.landing.name,
                funnelStep = HotelPdtV2Constants.FunnelStep.landing
            )
        }
    }

    private fun openCheckInOutDateBottomSheet(isCheckin: Boolean, request: SearchRequest) {
        val calendarDate = bundleHelper.createCalendarData(isCheckin,request)
        LandingContainerBottomSheet.newInstance(calendarDate).show(supportFragmentManager, LandingContainerBottomSheet.TAG)
    }

    private fun initOnLaunchedFromLanding() {
        HotelMigratorHelper.instance.setCustomerUnreadMessageData(null)
        observeLiveData()
        LottieDownloadHelper.scheduleDownloadService(this)
    }

    private fun handleCardClicked(cardClickedInfo: Pair<String, CardList>) {
        val searchRequest = viewModel.getSearchRequest()
        viewModel.altAccoTracker.trackCardClicked(cardClickedInfo.first, searchRequest)
        if (cardClickedInfo.second.deepLink != null) {
            HotelMigratorHelper.instance.openDeepLink(cardClickedInfo.second.deepLink!!, true)
        } else if (cardClickedInfo.second.cityTrendingDataMap != null) {
            performIfActivityActive(this) {
                HotelUtil.openTrendingActivityV2(it, cardClickedInfo.second, HotelRequestConstants.PAGE_CONTEXT_ALT_ACCO_TRENDING,
                    HotelFunnel.HOMESTAY.funnelValue)
            }
        }
    }

    private fun updateLocation(locationData: HotelLandingLocationData) {
        if (locationData.businessCode.isNotNullAndEmpty()) {
            viewModel.updateLocationOnApiResponse(locationData)
        }
    }

    private fun observeLiveData() {
        HotelMigratorHelper.instance.getCustomerUnreadMessageData().observe(this) {
            viewModel.updateCustomerUnreadMsgData(it)
        }
    }

    private fun openLoginActivity(header: String? = null) {
        val intent = Intent(CoreConstants.ACTION_LAUNCH_LOGIN).apply {
            header?.let { putExtra(LOGIN_PAGE_EXTRA, LoginPageExtra(header)) }
        }
        intent.setPackage(MMTCore.mContext.packageName)
        startActivity(intent)
        overridePendingTransition(R.anim.top_bottom_enter_anim, R.anim.top_bottom_exit_anim)
    }

    companion object {
        const val AREA_FIELD_EDITABLE = "AREA_FIELD_EDITABLE"
        const val IS_FROM_APP_LANDING = "IS_FROM_LANDING"
        const val REQUEST_TRANSPARENT_BACKGROUND = "REQUEST_TRANSPARENT_BACKGROUND"

        fun newInstance(
            context: Context,
            searchRequest: SearchRequest?,
            isFromAppLanding: Boolean,
            areaFieldEditable: Boolean,
            transParentBackGround: Boolean = false,
            isFromListing: Boolean = false
        ): Intent {
            return Intent(context, AltAccoLandingActivityV3::class.java).apply {
                putExtra(HotelConstants.KEY_HOTEL_SEARCH_REQUEST_V2, searchRequest)
                putExtra(IS_FROM_APP_LANDING, isFromAppLanding)
                putExtra(HotelConstants.FROM_LISTING, isFromListing)
                putExtra(AREA_FIELD_EDITABLE, areaFieldEditable)
                putExtra(REQUEST_TRANSPARENT_BACKGROUND, transParentBackGround)
            }
        }
    }

    override fun createViewModel(): AltAccoLandingViewModelV3 = getViewModel<AltAccoLandingViewModelV3>(factory)

    override fun createEventSharedViewModel(): HotelEventSharedViewModel = getViewModel<HotelEventSharedViewModel>()

    private fun recentSearchClicked(bundle: Pair<String, SearchRequest>) {
        viewModel.altAccoTracker.trackRecentSearchClicked(bundle.first, bundle.second)
        openListing(bundle.second)
    }

    private fun openListing(bundle: SearchRequest,saveRecentSearchData: Boolean = false){
        with(HotelScreenIntentUtil.getListingIntent()) {
            putExtra(HotelConstants.KEY_HOTEL_SEARCH_REQUEST_V2, bundle)
            putExtra(HotelConstants.SAVE_RECENT_SEARCH_ONLINE, saveRecentSearchData)
            putExtra(HotelConstants.SHOW_FILTER_BOTTOMSHEET, false)
            startActivityForResult(this, HotelLandingActivityV3ViewModel.LISTING_REQUEST_CODE)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme(getScreenTheme())
        super.onCreate(savedInstanceState)
        viewModel.initWithData()
        viewModel.initializeRecentItemsFetch()
        registerActivityResultReceiver()
        initFragmentResultListener()
        setContent {
            val uiData = viewModel.getAltAccoViewState().value

            TraceMetricsOnLifecycleStop(ALTACCO_LANDING_SCREEN_RENDER, ::initScreenMetricsTracker)

            //Not tracing screenDisplayed here as no loading screen, first frame belongs to success screen
            TraceScreenRenderedOnNextFrame()

            AltAccoLandingScreen(uiData) {
                handleEvents(it)
            }
        }
        supportFragmentManager.setFragmentResultListener(HotelMultiCurrencyBS.KEY_RESULT,this) { _, bundle ->
            val currency = bundle.getString(HotelMultiCurrencyBS.CURRENCY_SELECTED)
            viewModel.handleCurrencyChange(currency)
        }
        supportFragmentManager.setFragmentResultListener(LandingContainerBottomSheet.CHECK_IN_CHECKOUT_RESULT_KEY, this) { key, result ->
            handleCheckInOutChange(result)
        }
        initOnLaunchedFromLanding()
        HotelUtil.setTransparentStatusBar(this)
    }

    private fun openDeepLink(deeplink: String) {
        HotelMigratorHelper.instance.openDeepLink(deeplink, true)
    }

    private fun initFragmentResultListener() {
        supportFragmentManager.setFragmentResultListener(
            listOf(
                RoomAndGuestBottomSheetCompose.KEY_ROOM_AND_GUEST_REQUEST
            ), lifecycleOwner = this, fragmentResultListener
        )
    }

    private val fragmentResultListener = FragmentResultListener { requestKey, result ->
        when (requestKey) {
            RoomAndGuestBottomSheetCompose.KEY_ROOM_AND_GUEST_REQUEST -> {
                val roomStayCandidates =
                    result.getParcelableArrayList(RoomAndGuestBottomSheetCompose.ROOM_STAY_CANDIDATES)
                        ?: emptyList<RoomStayCandidatesV2>()
                val searchRequest = viewModel.getSearchRequest()
                if (roomStayCandidates.isNotEmpty()) {
                    viewModel.getSearchRequest()?.let {
                        it.roomStayCandidate = roomStayCandidates.toMutableList()
                        it.userSearchData?.occupancyData = roomStayCandidates.toOccupancyData()
                        it.isFlexiWithRoomCountChecked = roomStayCandidates.firstOrNull()?.isFlexiWithRoomCountCheckboxChecked?:false
                    }
                }

                searchRequest?.userSearchData?.let {
                    viewModel.setSearchRequest(searchRequest, false)
                }
            }
        }
    }

    private fun getScreenTheme(): Int {
        return if (LoginUtils.isCorporateUser) R.style.Theme_MyBizTheme else R.style.Theme_CosmosTheme
    }

    fun registerActivityResultReceiver() {
        observer = ActivityResultLifeCycleObserver(activityResultRegistry,this, activityID)
        observer?.let {
            it.registerForResult(
                HotelLandingActivityV3ViewModel.LISTING_REQUEST_CODE,
                SearchModifyBaseFragment.CHECK_IN_OUT_REQUEST_CODE,
                HotelAutoSuggestActivity.AUTO_SUGGEST_REQUEST_CODE,
                SelectEmployeeActivity.REQUEST_CODE
            )
            lifecycle.addObserver(it)
        }
    }

    fun handleEmperiaData(emperiaEventData: EmperiaEventData) {
        viewModel.handleEmperiaData(emperiaEventData)
    }

    private fun openRoomAndGuestFragment(searchRequest: SearchRequest) {
        val occupancyData = searchRequest.roomStayCandidate?.toOccupancyData()
        val prevFragment = supportFragmentManager.findFragmentByTag(RoomAndGuestBottomSheetCompose.TAG)
        searchRequest.userSearchData?.let {
            if (prevFragment == null) {
                val roomAndGuestFragment = RoomAndGuestBottomSheetCompose.newInstance(
                    occupancyData,
                    it,
                    isCorpPersonalBooking = searchRequest.personalCorpBooking,
                    canUpdateStatusBar = false,
                    isFromLanding = true,
                    isFromListing = false,
                    travellingWithPets = searchRequest.roomStayCandidate?.get(0)?.travellingWithPets
                        ?: false,
                    isFlexiWithRoomCount = searchRequest.isFlexiWithRoomCountChecked,
                    isAltAcco = true
                )
                performIfActivityActive(this) {
                    roomAndGuestFragment.show(supportFragmentManager, RoomAndGuestBottomSheetCompose.TAG)
                }
            }
        }
    }

    private fun openLocationPickerActivity(request: UserSearchData?, funnelType: FunnelType) {
        val data = bundleHelper.createAutoSuggestData(request,funnelType,false)
        with(Intent(HotelPageActionName.HOTEL_AUTO_SUGGEST)) {
            setPackage(MMTCore.mContext.packageName)
            putExtra(HotelAutoSuggestActivity.AUTO_SUGGEST_BUNDLE_DATA, data)
            observer?.startActivityForResult(this,
                HotelAutoSuggestActivity.AUTO_SUGGEST_REQUEST_CODE
            )
        }
    }

    private fun handleSearchClicked(searchRequest: SearchRequest) {
        viewModel.bundleData.searchRequest = searchRequest
        val currentFunnel = HotelFunnel.HOMESTAY
        viewModel.bundleData.searchRequest = searchRequest
        searchHelper.handleSearchClick(viewModel.bundleData,
            this,
            observer,
            HotelUtil.getFunnelType(currentFunnel.funnelValue)
        )
        if(HotelUtil.canSaveRequestInRecent(searchRequest)) {
            LocusUtilsV2.saveSearchRequest(searchRequest, HotelUtil.getFunnelType(currentFunnel.funnelValue)) // save searchRequest in SharedPreferences
            viewModel.saveSearchRequest(searchRequest)
        }
        searchRequest.userSearchData?.let {
            viewModel.altAccoTracker.trackSearchPerformed(searchRequest, it)
        }
    }

    fun saveRequest(finalRequest: SearchRequest) {
        viewModel.saveRequest(finalRequest)
    }


    private fun openCurrencyBottomSheet() {
        viewModel.hotelLandingTracker.trackCurrencyIconClicked()
        if (HotelCurrencyUtil.isCommonsMultiCurrencyEnabled()) {
            AppInterfaceHelper.get().openCurrencySelector(this) { selectedCurrency ->
                viewModel.handleCurrencyChange(selectedCurrency.code)
            }
        } else {
            val currencyChangeBottomSheet = HotelMultiCurrencyBS.newInstance()
            currencyChangeBottomSheet.show(supportFragmentManager, HotelMultiCurrencyBS.TAG)
        }
    }

    private fun handleCheckInOutChange(extras: Bundle?) {
        val checkIn = extras?.getParcelable<CalendarDay>(HotelCalenderActivityV2.SELECTED_CHECK_IN)
        val checkOut = extras?.getParcelable<CalendarDay>(HotelCalenderActivityV2.SELECTED_CHECK_OUT)
        if (checkIn != null && checkOut != null) {
            viewModel.updateCheckInAndOutDate(checkIn, checkOut)
        }
    }

    override fun onActivityResultReceived(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            HotelLandingActivityV4.LISTING_REQUEST_CODE -> {
                if (viewModel.getSearchRequest()?.userSearchData?.requisitionID.isNullOrBlank()) {
                    viewModel.populateSearchRequest(false, HotelFunnel.HOMESTAY)
                }
            }
            HotelAutoSuggestActivity.AUTO_SUGGEST_REQUEST_CODE -> {
                if (resultCode == HotelAutoSuggestActivity.RECENT_SEARCH_RESULT) {
                    data?.extras?.getParcelable<SearchRequest>(HotelAutoSuggestActivity.RECENT_SEARCH_DATA_WRAPPER)
                        ?.let {
                            viewModel.setSearchRequest(it, true)
                        }
                } else {
                    data?.extras?.getParcelable<LocusAutoSuggestDataWrapper>(
                        HotelAutoSuggestActivity.KEY_LOCUS_DATA_WRAPPER
                    )
                        ?.let {
                            viewModel.updateSearchRequestWithAutoSuggestData(it)
                        }
                }
            }
        }
    }

    override fun onStop() {
        super.onStop()
        viewModel.trackPageExitEvent()
    }

}