package com.mmt.hotel.altacco.ui.listingv2.compose

import android.net.Uri
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.google.android.exoplayer2.DefaultRenderersFactory
import com.google.android.exoplayer2.ExoPlayerFactory
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.SimpleExoPlayer
import com.google.android.exoplayer2.source.hls.HlsMediaSource
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout
import com.google.android.exoplayer2.upstream.DefaultHttpDataSourceFactory
import com.google.android.exoplayer2.upstream.cache.Cache
import com.google.android.exoplayer2.upstream.cache.CacheDataSourceFactory
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor
import com.mmt.hotel.altacco.ui.customui.HotelListingCustomPlayer
import java.io.File

@Composable
fun VideoPlayer(url: String?, modifier: Modifier = Modifier, onVideoStarted: () -> Unit, exoPlayer: SimpleExoPlayer? = null) {

    val player = exoPlayer ?: createExoPlayer(url)
    player.apply {
        repeatMode = Player.REPEAT_MODE_ALL
        addListener(object : Player.EventListener {
            override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
                if (playbackState == Player.STATE_READY) {
                    onVideoStarted()
                }
            }
        })
    }
    val context = LocalContext.current
    val view = remember(url) {
        HotelListingCustomPlayer(context).apply {
            layoutParams = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            resizeMode = AspectRatioFrameLayout.RESIZE_MODE_FILL
            hideController()
            useController = false
            this.player = player
            player.playWhenReady = true
        }
    }
    AndroidView(factory = { _ ->
        view
    }, modifier = modifier)
}

@Composable
private fun createExoPlayer(url: String?): SimpleExoPlayer {
    val context = LocalContext.current
    val exoPlayer = remember {
        val cacheFolder = File(context.filesDir.toString() + "/toro_cache_listing")

        val cacheEvictor = LeastRecentlyUsedCacheEvictor(100 * 1024 * 1024) //  cache size will be 100MB and it will automatically remove least recently used files if the size is reached out.

        val cache: Cache? = com.mmt.hotel.common.util.VideoCache.getInstance(cacheFolder, cacheEvictor)

        val cacheDataSourceFactory = CacheDataSourceFactory(cache, DefaultHttpDataSourceFactory("ExoplayerDemo"))
        val mediaSource = HlsMediaSource.Factory(cacheDataSourceFactory)
                .createMediaSource(Uri.parse(url))
        val player = ExoPlayerFactory.newSimpleInstance(
                context,
                DefaultRenderersFactory(context),
                DefaultTrackSelector()
        )
        player.prepare(mediaSource)
        player.volume = 0.0f
        player
    }


    return exoPlayer
}


