package com.mmt.hotel.altacco.di

import com.mmt.hotel.altacco.repository.AltAccoLandingRepositoryImp
import com.mmt.hotel.common.di.NamedConstants
import com.mmt.hotel.landingV3.repository.LandingRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import javax.inject.Named

/**
 * Created by <PERSON><PERSON><PERSON> on 22/04/21.
 */
@Module
@InstallIn(ViewModelComponent::class)
interface AltAccoLandingModule {
    @Binds
    @Named(NamedConstants.ALT_ACCO_REPOSITORY)
    fun provideLandingRepository(repositoryImp: AltAccoLandingRepositoryImp) : LandingRepository
}