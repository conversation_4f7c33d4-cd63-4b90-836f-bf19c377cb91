package com.mmt.hotel.altacco.viewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.mmt.core.util.executeIfCast
import com.mmt.hotel.altacco.event.LandingV2DataEvents
import com.mmt.hotel.altacco.helper.AltAccoLandingResponseConverter
import com.mmt.hotel.altacco.model.AltAccoSessionModel
import com.mmt.hotel.altacco.model.ui.AltAccoChatData
import com.mmt.hotel.altacco.repository.AltAccoLandingRepositoryImp
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.extensions.convertTo
import com.mmt.hotel.common.model.request.HotelRequestConstants
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.landingV3.helper.HotelLandingConfig
import com.mmt.hotel.landingV3.helper.LandingResponseConverter
import com.mmt.hotel.landingV3.helper.RandomisationAlgorithms
import com.mmt.hotel.landingV3.model.EmperiaEventData
import com.mmt.hotel.landingV3.model.HotelLandingDataV3
import com.mmt.hotel.landingV3.model.HotelLandingLocationData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.model.response.Response
import com.mmt.hotel.landingV3.repository.CollectionRepo
import com.mmt.hotel.landingV3.repository.HotelLandingRepositoryImpl
import com.mmt.hotel.landingV3.repository.LandingRepository
import com.mmt.hotel.landingV3.tracking.HotelLandingTracker
import com.mmt.hotel.landingV3.viewModel.HotelLandingActivityV4ViewModel
import com.mmt.hotel.landingV3.viewModel.adapter.RecentSearchCardViewModel
import com.mmt.hotel.landingV3.widget.AltAccoLandingAction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AltAccoLandingViewModelV3(
    config: HotelLandingConfig,
    bundleData: HotelLandingDataV3,
    repository: LandingRepository,
    converter: LandingResponseConverter,
    hotelLandingTracker: HotelLandingTracker,
    val altAccoTracker: AltAccoTracker,
    private val altAccoRepository: AltAccoLandingRepositoryImp,
    val repo: HotelLandingRepositoryImpl
): HotelLandingActivityV4ViewModel(config, bundleData, repository, converter, hotelLandingTracker) {

    var isPagerRequestMade = false
    val pageContext = HotelRequestConstants.PAGE_CONTEXT_ALT_ACCO_LANDING
    private var chatData: AltAccoChatData? = null
    var recentItems = listOf<AbstractRecyclerItem>()

    val altAccoResponseConvertor = AltAccoLandingResponseConverter()

    override fun loadCollection(request: SearchRequest) {
        //One standard call for fetching cards
        super.loadCollection(request)

        if(isPagerRequestMade) {
            return
        }

        // For v2 landing, call to fetch pager cards
        viewModelScope.launch(Dispatchers.IO) {
            verifyCase()
            repo.loadCollection(request, pageContext, true, HotelConstants.HOTEL_COLLECTION_RESPONSE_CACHE_CACHE_KEY,
                CollectionRepo.TTL_ALT_ACCO_CACHE
            )
                .map {
                    it.error?.let { error ->
                        request.userSearchData?.let { searchData ->
                            altAccoTracker.trackFetchCollectionError(searchData, error, it.requestId)
                        }
                    }
                    converter.wrapResponse(it, request)
                }
                .catch {
                    updateEventStream(LandingV2DataEvents.PAGER_API_FAILED)
                    it.printStackTrace()
                }
                .collect {
                    handleResponse(it.originalResponse, request)
                    isPagerRequestMade = true
                }
        }
    }

    override fun getCurrentFunnel() = HotelFunnel.HOMESTAY

    override fun getSearchRequest(): SearchRequest? = bundleData.searchRequest

    private fun handleResponse(originalResponse: Response?, request: SearchRequest) {
        viewModelScope.launch {
            val data = makePropertyPagerCards(
                originalResponse,
                eventStream
            )
            val cards = converter.createCards(
                it = originalResponse,
                request = request,
                eventStream = eventStream
            )
            val scrollDuration = originalResponse?.propertiesPager?.autoScrollDuration
            altAccoReducer.reduce(AltAccoLandingAction.SetCollectionResponse(
                data.second,
                scrollDuration,
                cards
            ))
            withContext(Dispatchers.IO) {
                if (data.first) {
                    request.let {
                        repo.removeCollectionLruCache()
                        loadCollectionAndSaveToCache(it)
                    }
                }
            }
        }
    }

    fun handleEmperiaData(emperiaEventData: EmperiaEventData) {
        emperiaEventData.response?.let {
            chatData = altAccoResponseConvertor.fetchChatData(it)
            chatData?.let { chatData ->
                updateRecentItemsChatData(chatData)
                HotelMigratorHelper.instance.setCustomerUnreadMessageData(chatData.chatData.convertTo())
            }
        }
    }

    override fun trackFilterShown() {
        getUserSearchData()?.let {
            altAccoTracker.trackFilterShown(it)
        }
    }

    private fun updateRecentItemsChatData(chatData: AltAccoChatData) {
        val recentSearchViewModel = recentItems.firstOrNull()
        recentSearchViewModel?.executeIfCast<RecentSearchCardViewModel> {
            updateChatData(chatData)
            altAccoReducer.reduce(AltAccoLandingAction.SetRecentItems(recentItems))
        } ?: kotlin.run {
            recentItems = listOf(converter.convert(emptyList(), chatData, eventStream))
            altAccoReducer.reduce(AltAccoLandingAction.SetRecentItems(recentItems))
        }
    }

    private fun verifyCase() {
        val pref = HotelSharedPrefUtil.instance
        val totalSessions = pref.getObject(SharedPrefKeys.TOTAL_ALT_ACCO_SESSIONS, Int::class.java)?:14
        var sessionModel = HotelSharedPrefUtil.instance.getObject<AltAccoSessionModel>(
            SharedPrefKeys.ALT_ACCO_SESSION_MODEL, AltAccoSessionModel::class.java)
        sessionModel= AltAccoSessionModel((sessionModel?.currentSession?:0)+1 , totalSessions)
        if(sessionModel.currentSession > sessionModel.totalSession){
            repo.removeCollectionLruCache()
            sessionModel =  null
        }
        pref.saveObject(sessionModel, SharedPrefKeys.ALT_ACCO_SESSION_MODEL, AltAccoSessionModel::class.java )
    }

    private fun loadCollectionAndSaveToCache(
        searchRequest: SearchRequest
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            repo.loadCollection(searchRequest, pageContext, true,HotelConstants.HOTEL_COLLECTION_RESPONSE_CACHE_CACHE_KEY,
                CollectionRepo.TTL_ALT_ACCO_CACHE
            )
                .catch {  }
                .collect { }
        }
    }

    fun initializeRecentItemsFetch() {
        viewModelScope.launch {
            fetchRecentSearchItems(chatData).catch { it.printStackTrace() }.collect {
                recentItems = it
                altAccoReducer.reduce(AltAccoLandingAction.SetRecentItems(recentItems))
            }
        }
    }

    fun fetchRecentSearchItems(chatData: AltAccoChatData?): Flow<List<AbstractRecyclerItem>> {
        return altAccoRepository.loadRecentSearch()
            .map {
                if (it.isEmpty() && chatData == null) {
                    emptyList()
                } else {
                    listOf(converter.convert(it, chatData, eventStream))
                }
            }.flowOn(Dispatchers.Default)
    }

    override fun trackPageLoad() {
        altAccoTracker.trackPageEntry()
        getUserSearchData()?.let {
            altAccoTracker.trackOmniturePageEntry(it, false)
        }
    }

    override fun trackPageExitEvent() {
        // page exit is tracked in AltaccoCardFragment for Landing case, this is for edit case from detail and listing
        getUserSearchData()?.let {
            altAccoTracker.trackPageExit(emptyList(), -1, it)
        }
    }

    fun updateLocationOnApiResponse(locationData: HotelLandingLocationData) {
        viewModelScope.launch {
            val searchRequest = searchModifyRepository.createDefaultSearchRequestBasedOnLocationData(locationData)
            setSearchRequest(searchRequest)
        }
    }

    fun makePropertyPagerCards(
        response: Response?,
        eventStream: MutableLiveData<HotelEvent>
    ): Pair<Boolean, List<PropertyPagerViewModel>> {
        val list = mutableListOf<PropertyPagerViewModel>()
        val randomisationAlgorithms = RandomisationAlgorithms()
        val indexMap = randomisationAlgorithms.getImagesIndexMap(response?.propertiesPager?.categories?.size?:0)
        val listOfSize = mutableListOf<Int>()
        response?.propertiesPager?.categories?.forEachIndexed { index, it ->
            val imageIndex = indexMap.getOrElse(index) { 0 }
            val item = it.items?.getOrNull(imageIndex)
            listOfSize.add(it.items?.size ?: 0)
            list.add(
                PropertyPagerViewModel(
                    it.title ?: "",
                    it.subTitle ?: "",
                    item?.title.orEmpty(),
                    item?.subTitle.orEmpty(),
                    item?.imgUrl.orEmpty(),
                    item?.deeplink.orEmpty(),
                    1f,
                    eventStream
                )
            )
        }
        val weReachedAtEnd = randomisationAlgorithms.incrementImagesIndexMapAndSave(listOfSize, response?.propertiesPager?.categories?.size?:0)
        return Pair(weReachedAtEnd, list)
    }
}