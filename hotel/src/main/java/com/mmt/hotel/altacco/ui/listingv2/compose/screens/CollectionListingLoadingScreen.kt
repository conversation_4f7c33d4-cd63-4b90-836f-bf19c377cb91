package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import com.mmt.hotel.R


@Composable
fun CollectionListingLoadingScreen(header: String, desc: String) {
    Column(modifier = Modifier.fillMaxSize().background(Color.White)) {
        ListingCollectionHeaderLoadingCard(Modifier, header, desc)
        for (i in 1..2 step 1) {
            AltAccoListingPropertyLoadingCard(Modifier.padding(top= dimensionResource(R.dimen.margin_huge)))
        }
    }
}

@Composable
@Preview
fun CollectionListingLoadingScreenPreview(){
    CollectionListingLoadingScreen("header", "desc")
}