package com.mmt.hotel.altacco.repository

import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.database.HotelLandingRecentSearchRepo
import com.mmt.hotel.landingV3.helper.LandingCollectionsRequestHelper
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.repository.AltAccoLandingRepository
import com.mmt.hotel.landingV3.repository.LandingRepositoryImp
import com.mmt.hotel.landingV3.repository.RECENT_SEARCH_LIMIT
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * Created by <PERSON><PERSON><PERSON> A<PERSON>wal on 17,June,2021
 */
class AltAccoLandingRepositoryImp @Inject constructor(collectionsRequest: LandingCollectionsRequestHelper
) : LandingRepositoryImp(collectionsRequest),
    AltAccoLandingRepository {

    override fun loadRecentSearch(): Flow<List<SearchRequest>> {
        return HotelLandingRecentSearchRepo.getSavedResultBasedOnFunnelAndRegionRecent(RECENT_SEARCH_LIMIT, FunnelType.ALT_ACCO_FUNNEL)
    }
}