package com.mmt.hotel.altacco.viewModel

import android.annotation.SuppressLint
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.mmt.data.model.homepage.wrapper.OfferData
import com.mmt.hotel.R
import com.mmt.hotel.altacco.event.LandingV2DataEvents
import com.mmt.hotel.altacco.model.AltAccoSessionModel
import com.mmt.hotel.altacco.model.ui.OffersDataItemModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.model.request.HotelRequestConstants

import com.mmt.hotel.landingV3.dataModel.MessageCard
import com.mmt.hotel.landingV3.dataModel.MessageCardConfig
import com.mmt.hotel.landingV3.helper.LandingResponseConverter
import com.mmt.hotel.landingV3.helper.RandomisationAlgorithms
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.model.response.Response
import com.mmt.hotel.landingV3.repository.CollectionRepo.Companion.TTL_ALT_ACCO_CACHE
import com.mmt.hotel.landingV3.repository.HotelLandingRepositoryImpl
import com.mmt.hotel.landingV3.viewModel.HotelLandingBaseFragmentViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class AltAccoLandingFragmentVM @Inject constructor(
    private val repo: HotelLandingRepositoryImpl,
    converter: LandingResponseConverter
) : HotelLandingBaseFragmentViewModel(
        repo,
        converter,
        HotelRequestConstants.PAGE_CONTEXT_ALT_ACCO_LANDING,
        HotelFunnel.HOMESTAY
    ) {

    private var isEmperiaApiRequested = false
    var isPagerRequestMade = false

    override fun onSearchRequestUpdated(searchRequest: SearchRequest, isFromLanding: Boolean) {
        super.onSearchRequestUpdated(searchRequest, isFromLanding)
        if (isFromLanding && !isEmperiaApiRequested) {
            super.makeEmperiaApiCall(searchRequest, "", canUpdateUserLocation = true)
            isEmperiaApiRequested = true
        }
    }

    override fun loadCollection(request: SearchRequest, pageContext: String) {
        //One standard call for fetching cards
        super.loadCollection(request, pageContext)

        if(isPagerRequestMade || !isShownInHotelLanding.get()) {
            return
        }

        // For v2 landing, call to fetch pager cards
        viewModelScope.launch(Dispatchers.IO) {
            verifyCase()
            repo.loadCollection(request, pageContext, true, HotelConstants.HOTEL_COLLECTION_RESPONSE_CACHE_CACHE_KEY, TTL_ALT_ACCO_CACHE)
                .map { converter.wrapResponse(it, request) }
                .catch {
                    updateEventStream(LandingV2DataEvents.PAGER_API_FAILED)
                    it.printStackTrace()
                }
                .collect {
                    handleResponse(it.originalResponse, request)
                    isPagerRequestMade = true
                }
        }
    }

    private fun verifyCase() {
        val pref = HotelSharedPrefUtil.instance
        val totalSessions = pref.getObject(SharedPrefKeys.TOTAL_ALT_ACCO_SESSIONS, Int::class.java)?:14
        var sessionModel = HotelSharedPrefUtil.instance.getObject<AltAccoSessionModel>(SharedPrefKeys.ALT_ACCO_SESSION_MODEL, AltAccoSessionModel::class.java)
        sessionModel= AltAccoSessionModel((sessionModel?.currentSession?:0)+1 , totalSessions)
        if(sessionModel.currentSession > sessionModel.totalSession){
            repo.removeCollectionLruCache()
            sessionModel =  null
        }
        pref.saveObject(sessionModel,SharedPrefKeys.ALT_ACCO_SESSION_MODEL, AltAccoSessionModel::class.java )
    }

    @SuppressLint("ResourceType")
    fun updateOffersData(it: OfferData) {
        if (it.subheading.isNullOrEmpty()) {
            return
        }
        messageLayout.set(
            MessageCard(
                OffersDataItemModel(it, eventStream),
                MessageCardConfig(R.layout.landing_offer_banner)
            )
        )
    }

    private fun handleResponse(originalResponse: Response?, request: SearchRequest) {
        viewModelScope.launch(Dispatchers.Main) {
            val data = makePropertyPagerCards(
                originalResponse,
                eventStream
            )
            val scrollDuration = originalResponse?.propertiesPager?.autoScrollDuration
            eventStream.value = HotelEvent(LandingV2DataEvents.UPDATE_CATEGORY_IMAGES, Pair(data.second, scrollDuration))
            withContext(Dispatchers.IO) {
                if (data.first) {
                    request?.let {
                        repo.removeCollectionLruCache()
                        loadCollectionAndSaveToCache(it)
                    }
                }
            }
        }
    }

    fun makePropertyPagerCards(
        response: Response?,
        eventStream: MutableLiveData<HotelEvent>
    ): Pair<Boolean, List<PropertyPagerViewModel>> {
        val list = mutableListOf<PropertyPagerViewModel>()
        val randomisationAlgorithms = RandomisationAlgorithms()
        val indexMap = randomisationAlgorithms.getImagesIndexMap(response?.propertiesPager?.categories?.size?:0)
        val listOfSize = mutableListOf<Int>()
        response?.propertiesPager?.categories?.forEachIndexed { index, it ->
            val imageIndex = indexMap.getOrElse(index) { 0 }
            val item = it.items?.getOrNull(imageIndex)
            listOfSize.add(it.items?.size ?: 0)
            list.add(
                PropertyPagerViewModel(
                    it.title ?: "",
                    it.subTitle ?: "",
                    item?.title.orEmpty(),
                    item?.subTitle.orEmpty(),
                    item?.imgUrl.orEmpty(),
                    item?.deeplink.orEmpty(),
                    1f,
                    eventStream
                )
            )
        }
        val weReachedAtEnd = randomisationAlgorithms.incrementImagesIndexMapAndSave(listOfSize, response?.propertiesPager?.categories?.size?:0)
        return Pair(weReachedAtEnd, list)
    }

    private fun loadCollectionAndSaveToCache(
        searchRequest: SearchRequest
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            repo.loadCollection(searchRequest, pageContext, true,HotelConstants.HOTEL_COLLECTION_RESPONSE_CACHE_CACHE_KEY, TTL_ALT_ACCO_CACHE)
                .catch {  }
                .collect { }
        }
    }

    fun getEmptyPagerModel(): PropertyPagerViewModel {
        return PropertyPagerViewModel(eventStream = eventStream)
    }
}