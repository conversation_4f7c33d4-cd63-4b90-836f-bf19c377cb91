package com.mmt.hotel.altacco.adapter

import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.HotelBaseRecyclerAdapter

abstract class BaseInfiniteScrollingHotelAdapter: HotelBaseRecyclerAdapter(arrayListOf()) {

    override fun getItemCount(): Int {
        return if (super.getItemCount() > 1)
            Int.MAX_VALUE
        else super.getItemCount()
    }

    override fun getItem(position: Int): AbstractRecyclerItem {
        return if (itemCount > 1)
            super.getItem( position % super.getItemCount())
        else super.getItem(position)
    }

    override fun getItemId(position: Int): Long {
        return if (itemCount > 1)
            super.getItemId(position % super.getItemCount())
        else super.getItemId(position)
    }
}