package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.listingv2.compose.components.AltAccoFilterPillsUi
import com.mmt.hotel.common.model.HotelError
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.listingV2.event.HotelListingClickEvents
import com.mmt.hotel.listingV2.model.HotelListingHeaderUiState
import com.mmt.hotel.listingV2.model.ui.HotelListingFilterUiState
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.uikit.views.toPx

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun CollectionListingErrorScreen(
    headerUiState: HotelListingHeaderUiState,
    error: HotelError,
    handleEvent: (String, Any?) -> Unit
) {
    val headerData = headerUiState.hotelHeaderUiData
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp

    Column(
        Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        colorResource(id = R.color.htl_grey_bg_color),
                        colorResource(id = R.color.white)
                    ),
                    startY = 0f,
                    endY = (screenHeight / 2).toPx()
                )
            )
            .semantics { testTagsAsResourceId = true }
    ) {

        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            CollectionListingSearchHeader(Modifier.padding(horizontal = dimensionResource(R.dimen.margin_large)).padding(top = dimensionResource(R.dimen.margin_50)),headerData.locationText,  headerData.dateGuestText, handleEvent)
            val filterPills = when (headerUiState.filterUiState) {
                is HotelListingFilterUiState.Success -> headerUiState.filterUiState.filterPills
                is HotelListingFilterUiState.Loading -> headerUiState.filterUiState.loadingPills
                is HotelListingFilterUiState.Error -> listOf()
            }

            if (filterPills.isNotEmpty()) {
                AltAccoFilterPillsUi(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = dimensionResource(id = R.dimen.htl_ic_host_dimen)
                        ),
                    filterPills = filterPills,
                    filterScrollState = rememberLazyListState(),
                    handleEvent = handleEvent
                )
            }
            ErrorLayout(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = dimensionResource(id = R.dimen.htl_card_radius)),
                error,
                handleEvent = handleEvent
            )
        }
    }
}

@Composable
private fun ErrorLayout(
    modifier: Modifier = Modifier,
    error: HotelError,
    handleEvent: (String, Any) -> Unit
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        LoadImage(
            resourceId = error.errorDrawableRes
        )
        MmtComposeTextView(
            text = error.title,
            mmtFontStyle = latoBlack,
            color = colorResource(id = R.color.black),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_large),
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_xLarge))
        )
        MmtComposeTextView(
            text = error.msg,
            mmtFontStyle = latoRegular,
            color = colorResource(id = R.color.black),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            modifier = Modifier
                .padding(
                    top = dimensionResource(id = R.dimen.margin_small),
                    start = dimensionResource(id = R.dimen.margin_large),
                    end = dimensionResource(id = R.dimen.margin_large)
                )
                .fillMaxWidth(),
            textAlign = TextAlign.Center,
        )
        MmtComposeTextView(
            text = error.positiveBtnText,
            mmtFontStyle = latoBlack,
            color = colorResource(id = R.color.white),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(
                    top = dimensionResource(id = R.dimen.margin_xHuge2),
                    start = dimensionResource(id = R.dimen.margin_large),
                    end = dimensionResource(id = R.dimen.margin_large)
                )
                .fillMaxWidth()
                .clickable {
                    handleEvent(
                        HotelListingClickEvents.ERROR_STATE_BUTTON_CLICKED,
                        error.positiveAction
                    )
                }
                .background(
                    brush = Brush.linearGradient(
                        listOf(
                            colorResource(id = R.color.htl_cta_light_b2c),
                            colorResource(id = R.color.htl_cta_dark_b2c)
                        )
                    ), shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small))
                )
                .padding(dimensionResource(id = R.dimen.margin_medium))
        )
    }
}
