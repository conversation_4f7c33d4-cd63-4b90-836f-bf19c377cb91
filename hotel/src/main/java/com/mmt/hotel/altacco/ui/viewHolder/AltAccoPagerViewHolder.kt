package com.mmt.hotel.altacco.ui.viewHolder

import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import com.mmt.hotel.R
import com.mmt.hotel.altacco.viewModel.PropertyPagerViewModel
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.databinding.ItemHtlPropertyPagerBinding
import com.mmt.uikit.binding.hotelsBindImageV2

class AltAccoPagerViewHolder(layoutInflater: LayoutInflater, parent: ViewGroup)
    : HotelRecyclerViewHolder<ItemHtlPropertyPagerBinding, PropertyPagerViewModel>(layoutInflater, R.layout.item_htl_property_pager, parent) {
    override fun bindData(data: PropertyPagerViewModel, position: Int) {
        with(dataBinding){
            model = data
            image.setImageDrawable(null)

            hotelsBindImageV2(
                dataBinding.image, data.url, true,
                    R.drawable.altacco_landing_placeholder, 0, 0, null, 1f, null
            )
            executePendingBindings()
        }
    }
}