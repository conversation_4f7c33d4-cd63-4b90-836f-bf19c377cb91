package com.mmt.hotel.altacco.ui

import android.os.Bundle
import android.view.View
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.mmt.core.util.DeviceUtil
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.viewModel.HotelViewModelFactory
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.databinding.HtlFragmentAltAccoRoomGuestBottomSheetBinding
import com.mmt.hotel.landingV3.ui.BaseRoomAndGuestsFragmentV2
import com.mmt.hotel.landingV3.viewModel.RoomAndGuestViewModelV3
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class AltAccoRoomAndGuestBottomSheetFragment: BaseRoomAndGuestsFragmentV2<RoomAndGuestViewModelV3, HtlFragmentAltAccoRoomGuestBottomSheetBinding>() {

    @Inject
    lateinit var factory: HotelViewModelFactory

    companion object {
        const val TAG = "AltAccoRoomAndGuestBottomSheetFragment"
        fun newInstance(occupancyData: OccupancyData?, userSearchData: UserSearchData,
                        isCorpPersonalBooking : Boolean = false, canUpdateStatusBar: Boolean = false): AltAccoRoomAndGuestBottomSheetFragment {
            return AltAccoRoomAndGuestBottomSheetFragment().apply {
                arguments = makeBundle(
                    occupancyData,
                    userSearchData,
                    isCorpPersonalBooking,
                    canUpdateStatusBar,
                    isAltAcco = true
                )
            }
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (dialog as? BottomSheetDialog)?.behavior?.apply {
            val height =(DeviceUtil.getDeviceHeight(activity)*.9).toInt()
            state = BottomSheetBehavior.STATE_EXPANDED
            peekHeight =height
            maxHeight =height
            val params = viewDataBinding.clContent.layoutParams
            params.height = height
            view.layoutParams = params
        }
    }

    override fun initViewModel(): RoomAndGuestViewModelV3 {
        return  getViewModel(factory)
    }

    override fun setDataBinding() {
        viewDataBinding.header.root.setBackgroundColor(ResourceProvider.instance.getColor(R.color.fully_transparent))
        viewDataBinding.viewModel = viewModel
    }

    override fun getLayoutId(): Int {
        return R.layout.htl_fragment_alt_acco_room_guest_bottom_sheet
    }
}