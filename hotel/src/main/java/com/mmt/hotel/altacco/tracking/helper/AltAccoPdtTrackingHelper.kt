package com.mmt.hotel.altacco.tracking.helper

import com.gommt.logger.LogUtils
import com.mmt.analytics.ActivityTypeEvent
import com.mmt.analytics.AnalyticsSDK
import com.mmt.analytics.EventsType
import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.pdtclient.PDTAnalyticsKeys
import com.mmt.analytics.pdtclient.PdtActivityName
import com.mmt.analytics.pdtclient.PdtPageName
import com.mmt.core.constant.CommonConstants
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.altacco.tracking.AltAccoLandingPdtModel
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.ALT_ACCO_PAGE_NAME
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.PAGE_NAME
import com.mmt.hotel.altacco.tracking.SearchClickTrackingObject
import com.mmt.hotel.analytics.pdt.HotelPdtTrackingHelperV2
import com.mmt.hotel.analytics.pdt.events.HotelLandingPageEvent
import com.mmt.hotel.detail.event.constants.HotelDetailTrackingConstants.Companion.OMNITURE_NAME_NOT_FOUND
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.tracking.LandingTrackingConstants
import com.pdt.pdtDataLogging.events.model.PageEntryEvent
import com.pdt.pdtDataLogging.events.model.PageExitEvent
import javax.inject.Inject

class AltAccoPdtTrackingHelper @Inject constructor() : HotelPdtTrackingHelperV2() {
    private var sessionStartTime: Long = 0

    fun trackPageEntry() {
        this.sessionStartTime = System.currentTimeMillis()
        val event = PageEntryEvent(
            PDTAnalyticsKeys.HOTEL_FUNNEL_TOPIC_ID, PDTAnalyticsKeys.HOTEL_FUNNEL_TEMPLATE_ID,
            ALT_ACCO_PAGE_NAME, CommonConstants.LOB_HOTEL,
            EventsType.PDT_EVENT.id, sessionStartTime,
            OMNITURE_NAME_NOT_FOUND, PAGE_NAME,
            CoreConstants.EMPTY_STRING, CoreConstants.EMPTY_STRING
        )

        AnalyticsSDK.instance.trackEvent(event)

    }

    fun trackPageExit() {
        val event = PageExitEvent(
            PDTAnalyticsKeys.HOTEL_FUNNEL_TOPIC_ID,
            PDTAnalyticsKeys.HOTEL_FUNNEL_TEMPLATE_ID,
            ALT_ACCO_PAGE_NAME,
            CommonConstants.LOB_HOTEL,
            EventsType.PDT_EVENT.id,
            sessionStartTime,
            OMNITURE_NAME_NOT_FOUND,
            PAGE_NAME,
            CoreConstants.EMPTY_STRING,
            CoreConstants.EMPTY_STRING
        )

        AnalyticsSDK.instance.trackEvent(event)
    }

    fun trackClickEvent(eventName: String, searchRequest: SearchRequest) {
        trackAltAccoV2ClickEvent(eventName, searchRequest)
    }

    private fun trackAltAccoV2ClickEvent(eventName: String, searchRequest: SearchRequest) {
        try {
            val hotelLandingPageEvent = HotelLandingPageEvent(
                    eventName,
                    "AALanding",
                    EventsType.PDT_EVENT.id,
                    OMNITURE_NAME_NOT_FOUND,
                    "Landing",
                    searchRequest.prevFunnelStepPdt,
                    searchRequest.prevPageNamePdt
            )
            HotelLandingPageEvent.bindEventParams(
                    hotelLandingPageEvent,searchRequest
            )
            AnalyticsSDK.instance.trackEvent(hotelLandingPageEvent)
        } catch (e: Exception) {
            LogUtils.error(
                    AnalyticsSDK.PDT_TRACKER,
                    e
            )
        }
    }

    fun trackClickEventsV2(eventName: String, activityTypeEvent: ActivityTypeEvent) { // track common events
        try {
            val events = AnalyticsSDK.instance.getCommonGenericEvent(eventName, Events.OPN_DOMESTIC_HOMESTAY_LANDING.value)
            AnalyticsSDK.instance.trackEvent(events, 1, activityTypeEvent)
        } catch (e: Exception) {
            LogUtils.error(LandingTrackingConstants.TAG, e)
        }
    }

    fun trackSearchPerformed(searchClickTrackingObject: SearchClickTrackingObject) {
        val commonGenericEvent = AnalyticsSDK.instance.getCommonBuilder(
            PdtActivityName.HOMESTAY_SEARCH_PERFORMED,
                PdtPageName.EVENT_PAGE_HOMESTAY_LANDING_V2)
        commonGenericEvent.addParams(PDTAnalyticsKeys.EVENT_DETAILS, AltAccoLandingPdtModel(searchClickTrackingObject))
        commonGenericEvent.addParams(PDTAnalyticsKeys.ACTIVITY_TYPE, ActivityTypeEvent.CLICK)
        AnalyticsSDK.instance.trackEvent(commonGenericEvent.build())
    }
}