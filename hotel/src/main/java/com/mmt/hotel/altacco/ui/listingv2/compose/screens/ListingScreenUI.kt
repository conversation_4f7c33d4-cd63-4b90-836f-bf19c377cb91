package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import com.mmt.hotel.listingV2.event.HotelListingHeaderEvent
import com.mmt.hotel.listingV2.model.ui.HotelListingUiState


@Composable
fun AltAccoListingScreenUi(uiState: State<HotelListingUiState>, handleEvent : (String, Any?) -> Unit) {
    handleEvent(HotelListingHeaderEvent.UPDATE_ACTION_BAR, null)
    when(uiState.value) {
        is HotelListingUiState.Success, is HotelListingUiState.PropertyLoading -> {
            AltAccoListingLayoutWithHeader(uiState, handleEvent)
        }
        is HotelListingUiState.PageLoading -> {

            CollectionListingLoadingScreen((uiState.value as HotelListingUiState.PageLoading).headerUiData.hotelHeaderUiData.locationText,
                (uiState.value as HotelListingUiState.PageLoading).headerUiData.hotelHeaderUiData.dateGuestText)
//            AltAccoListingLoadingScreen()
        }
        is HotelListingUiState.Error -> {
            val data = uiState.value as HotelListingUiState.Error
            CollectionListingErrorScreen(data.headerUiData, data.error, handleEvent = handleEvent)
//            AltAccoListingErrorScreen(data.headerUiData, data.error, handleEvent = handleEvent)
        }
    }
}