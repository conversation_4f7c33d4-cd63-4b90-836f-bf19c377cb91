package com.mmt.hotel.altacco.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.viewHolder.AltAccoPagerViewHolder
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.ui.viewHolder.HotelGeneralRecyclerViewHolder
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder

class InfiniteScrollingAdapter: BaseInfiniteScrollingHotelAdapter() {
    override fun getViewHolder(
        viewType: Int,
        layoutInflater: LayoutInflater,
        parent: ViewGroup
    ): HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem> {
        return AltAccoPagerViewHolder(layoutInflater, parent) as HotelRecyclerViewHolder<in ViewDataBinding, in AbstractRecyclerItem>
    }
}