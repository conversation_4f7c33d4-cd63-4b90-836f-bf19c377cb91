package com.mmt.hotel.altacco.model.response


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.mmt.hotel.base.AbstractRecyclerItem
import kotlinx.parcelize.Parcelize

@Parcelize
data class Card(
        @SerializedName("contextInfo")
        val contextInfo: ContextInfo = ContextInfo(),
        @SerializedName("image")
        val image: String = "",
        @SerializedName("subcards")
        val subcards: List<SubCard> = listOf(),
        @SerializedName("subtitle")
        val subtitle: String = "",
        @SerializedName("title")
        val title: String = ""
) : Parcelable

@Parcelize
data class ContextInfo(
        @SerializedName("image")
        val image: String = "",
        @SerializedName("subtitle")
        val subtitle: String = "",
        @SerializedName("title")
        val title: String = ""
) : Parcelable

@Parcelize
data class SubCard(
        @SerializedName("ctaText")
        val ctaText: String = "",
        @SerializedName("image")
        val image: String = "",
        @SerializedName("items")
        val items: List<Item> = listOf(),
        @SerializedName("templateType")
        val templateType: String = "",
        @SerializedName("title")
        val title: String = ""
) : Parcelable

@Parcelize
data class Item(
        @SerializedName("image")
        val image: String = "",
        @SerializedName("subtitle")
        val subtitle: String = "",
        @SerializedName("title")
        val title: String = ""
) : Parcelable, AbstractRecyclerItem {
    override fun getItemType(): Int {
        return 0
    }
}