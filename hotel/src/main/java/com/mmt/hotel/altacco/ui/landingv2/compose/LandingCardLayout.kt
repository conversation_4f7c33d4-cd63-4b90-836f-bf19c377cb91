package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.unit.dp
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.landingV3.event.LandingCardAdapterKeys
import com.mmt.hotel.landingV3.viewModel.adapter.CardItemViewModel
import com.mmt.hotel.landingV3.viewModel.adapter.LandingCardsViewModel
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun LandingCardLayout(data: LandingCardsViewModel, cardType: Int) {
    Column(
        Modifier
            .fillMaxWidth()
            .padding(vertical = dimensionResource(id = R.dimen.margin_large))
            .semantics { testTagsAsResourceId = true }
    ) {
        MmtComposeTextView(
            text = data.title,
            mmtFontStyle = latoBlack,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_large),
            color = colorResource(id = R.color.htl_grey),
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.margin_large)).testTag("card_title")
        )
        MmtComposeTextView(
            text = data.subTitle,
            mmtFontStyle = latoRegular,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            color = colorResource(id = R.color.color_4a4a4a),
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.margin_large)).testTag("card_subtitle")
        )
        Row(
            Modifier
                .padding(top = dimensionResource(id = R.dimen.margin_large))
                .horizontalScroll(rememberScrollState()), verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.spacedBy(
            dimensionResource(id = R.dimen.margin_small))) {
            data.itemsState.forEachIndexed {index, item ->
                val paddingStart = if (index == 0) dimensionResource(id = R.dimen.margin_large) else 0.dp
                val paddingEnd = if (index == data.itemsState.size - 1) dimensionResource(id = R.dimen.margin_large) else 0.dp
                when(cardType) {
                    LandingCardAdapterKeys.CIRCULAR_CARD -> {
                        CircularCardItem(modifier = Modifier.padding(start = paddingStart, end = paddingEnd).testTag("circular_card_item"), model = item as CardItemViewModel)
                    }
                    LandingCardAdapterKeys.SQUARE_CARD -> {
                        SquareCardItem(cardItem = item as CardItemViewModel, modifier = Modifier.padding(start = paddingStart, end = paddingEnd).testTag("square_card_item"))
                    }
                }
            }

        }
    }
}

@Composable
private fun CircularCardItem(modifier: Modifier = Modifier, model: CardItemViewModel) {
    Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier.mmtClickable { model.onCardClicked() }) {
        LoadImage(modifier = Modifier
            .size(dimensionResource(id = R.dimen.margin_120dp))
            .clip(
                RoundedCornerShape(50)
            ), imageUrl = model.card.imageUrl)
        MmtComposeTextView(
            text = model.card.description,
            mmtFontStyle = latoBlack,
            color = colorResource(id = R.color.color_4a4a4a),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_large),
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_medium_extra))
        )
        MmtComposeTextView(
            text = model.card.subDescription,
            mmtFontStyle = latoRegular,
            color = colorResource(id = R.color.color_4a4a4a),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small)
        )
    }
}