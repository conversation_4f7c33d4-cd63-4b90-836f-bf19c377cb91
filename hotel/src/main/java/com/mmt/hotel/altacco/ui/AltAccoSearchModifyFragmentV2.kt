package com.mmt.hotel.altacco.ui

import android.os.Bundle
import androidx.databinding.ViewDataBinding
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.executeIfCast
import com.mmt.hotel.R
import com.mmt.hotel.altacco.event.LandingV2DataEvents
import com.mmt.hotel.altacco.helper.AltAccoLayoutManager
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.altacco.viewModel.AltAccoSearchModifyViewModel
import com.mmt.hotel.autoSuggest.model.LocusAutoSuggestDataWrapper
import com.mmt.hotel.autoSuggest.ui.HotelAutoSuggestComposeFragment
import com.mmt.hotel.common.ui.bottomsheet.LandingContainerBottomSheet
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.databinding.FragmentHotelAltAccoModifySearchV2Binding
import com.mmt.hotel.landingV3.event.HotelLandingSearchModifyEvents
import com.mmt.hotel.landingV3.helper.SearchModifyLayoutManger
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.tracking.SearchModifyTracker
import com.mmt.hotel.landingV3.ui.HotelLandingSearchModifyFragment
import com.mmt.hotel.landingV3.ui.SearchModifyBaseFragment
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class AltAccoSearchModifyFragmentV2: SearchModifyBaseFragment<AltAccoSearchModifyViewModel, FragmentHotelAltAccoModifySearchV2Binding>() {

    @Inject
    lateinit var tracker: AltAccoTracker
    private val searchRequest by lazy { arguments?.getParcelable<SearchRequest>(REQUEST_GENERATED) }
    private val areaContainerEnabled by lazy { arguments?.getBoolean(IS_AREA_EDITABLE) ?: true }
    private var layoutManager: SearchModifyLayoutManger<ViewDataBinding>? = null
    private val isRoundCorners by lazy { arguments?.getBoolean(IS_ROUND_CORNERS,true) ?: true }
    private val isFromListing by lazy { arguments?.getBoolean(IS_FROM_LISTING,true) ?: true }

    override fun getLayoutId() = R.layout.fragment_hotel_alt_acco_modify_search_v2

    override fun initViewModel(): AltAccoSearchModifyViewModel = getViewModel()

    override fun setDataBinding() {
        viewDataBinding.viewModel = viewModel
        viewDataBinding.executePendingBindings()
        layoutManager =  AltAccoLayoutManager(viewDataBinding.searchModifyStub, viewDataBinding.viewModel) as SearchModifyLayoutManger<ViewDataBinding>
    }

    private fun initDataViews(){
        with(viewDataBinding) {
            viewModel = <EMAIL>
        }
    }

    override fun initFragmentView() {
        viewModel.isFromAppLanding = isRoundCorners
        viewModel.isFromListing = isFromListing
        activity?.supportFragmentManager?.apply {
            setFragmentResultListener( HotelAutoSuggestComposeFragment.KEY_LOCUS_DATA_WRAPPER, viewLifecycleOwner) { key, result ->
                result.getParcelable<LocusAutoSuggestDataWrapper>(HotelAutoSuggestComposeFragment.KEY_LOCUS_DATA_WRAPPER_BUNDLE_DATA)
                    ?.let {
                        handleAutoSuggestData(it)
                    }
            }
            setFragmentResultListener( LandingContainerBottomSheet.CHECK_IN_CHECKOUT_RESULT_KEY, viewLifecycleOwner) { key, result ->
                handleCheckInOutChange(result)
            }
            setFragmentResultListener(HotelAutoSuggestComposeFragment.KEY_TAG_SELECTION_V2, viewLifecycleOwner) { key, result ->
                result.getParcelable<LocusAutoSuggestDataWrapper>(HotelAutoSuggestComposeFragment.KEY_TAG_SELECTION_V2_BUNDLE_DATA)
                    ?.let {
                        // check how to handle this as this data is handled as a Tag Selection not in the search modify keeping it for now here
                    }
            }
        }
        if (isFragmentRecreating() && viewModel.getSearchRequest() != null) {
            //We want to restore the existing request and filters when the fragment is recreated due to configuration change
            viewModel.clearSavedFilters = false
            viewModel.setSearchRequest(viewModel.getSearchRequest())
        } else {
            searchRequest?.let {
                modifyBundledSearchRequest(it)
                viewModel.setSearchRequest(it)
            } ?: viewModel.populateSearchRequest(initial = true)
        }

        layoutManager?.areaContainer?.isEnabled = areaContainerEnabled
        viewModel.setButtonText(areaContainerEnabled, searchRequest?.userSearchData?.userInputMandatory)
        if (!isRoundCorners){
            viewDataBinding.searchContainer.setBackgroundColor(ResourceProvider.instance.getColor(R.color.white))
        }
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            LandingV2DataEvents.REQUEST_UPDATED -> {
                initDataViews()
                super.handleEvents(event)
            }
            HotelLandingSearchModifyEvents.SEARCH_PERFORMED -> {
                val finalRequest = viewModel.getSearchRequest()
                finalRequest?.let {
                    if(checkAllFieldsFilled()) {
                        //saveSearchRequest(it)
                        //tracker.trackSearchPerformed(it, viewModel.getUserSearchData())
                        setMandatoryInputProvided(it)
                        sendEventToActivity(HotelEvent(HotelLandingSearchModifyEvents.SEARCH_PERFORMED, it))
                    } else if (it.userSearchData?.userInputMandatory == true) {
                        viewModel.showInputErrorText.set(true)
                        tracker.trackInputMandatoryErrorShown()
                    }
                }
            }
            HotelLandingSearchModifyEvents.CHECK_IN_CLICKED -> {
                event.data.executeIfCast<SearchRequest> {
                    openCheckInOutDateBottomSheet(true, this)
                }
            }
            HotelLandingSearchModifyEvents.CHECK_OUT_CLICKED -> {
                event.data.executeIfCast<SearchRequest> {
                    openCheckInOutDateBottomSheet(false, this)
                }
            }
            HotelLandingSearchModifyEvents.AREA_CLICKED -> {
                openHotelAutoSuggestBottomSheetFragment(viewModel.getUserSearchData(), HotelUtil.getFunnelType(getFunnel().funnelValue))
            }
            else -> super.handleEvents(event)
        }
    }

    private fun openCheckInOutDateBottomSheet(isCheckin: Boolean, request: SearchRequest) {
        val calendarDate = bundleHelper.createCalendarData(isCheckin,request)
        activity?.let {
            LandingContainerBottomSheet.newInstance(calendarDate).show(it.supportFragmentManager, LandingContainerBottomSheet.TAG)
        }
    }

     private fun openHotelAutoSuggestBottomSheetFragment(request: UserSearchData?, funnelType: FunnelType) {
         val fromListing = arguments?.getBoolean(IS_FROM_LISTING, false)?: false
         val data = bundleHelper.createAutoSuggestData(request, funnelType, fromListing)
        activity?.let {
            LandingContainerBottomSheet.newInstance(data).show(it.supportFragmentManager, LandingContainerBottomSheet.TAG)
        }
    }

    companion object {
        const val TAG = "AltAccoSearchModifyFragmentV2"
        private const val REQUEST_GENERATED = "REQUEST_GENERATED"
        private const val IS_AREA_EDITABLE = "IS_AREA_EDITABLE"
        private const val IS_ROUND_CORNERS = "IS_ROUND_CORNERS"
        private const val IS_FROM_LISTING = "IS_FROM_LISTING"


        @JvmStatic
        fun newInstance(searchRequest: SearchRequest?, isAreaFieldEditable: Boolean, isRoundCorners: Boolean, isFromListing: Boolean): AltAccoSearchModifyFragmentV2 {
            val args = Bundle()
            args.putParcelable(REQUEST_GENERATED, searchRequest)
            args.putBoolean(IS_AREA_EDITABLE, isAreaFieldEditable)
            args.putBoolean(IS_ROUND_CORNERS, isRoundCorners)
            args.putBoolean(IS_FROM_LISTING, isFromListing)
            val fragment = AltAccoSearchModifyFragmentV2()
            fragment.arguments = args
            return fragment
        }
    }

    override fun getFunnel(): HotelFunnel {
        return HotelFunnel.HOMESTAY
    }

    override fun getSearchModifyTracker(): SearchModifyTracker {
        return tracker
    }

    override fun checkAllFieldsFilled(): Boolean {
        return layoutManager?.checkAllFiledsFilled()?:false
    }


}