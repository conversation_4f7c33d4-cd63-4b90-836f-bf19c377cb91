package com.mmt.hotel.altacco.ui.viewHolder

import android.view.LayoutInflater
import android.view.ViewGroup
import com.mmt.hotel.R
import com.mmt.hotel.base.ui.viewHolder.HotelRecyclerViewHolder
import com.mmt.hotel.databinding.HtlRecentSearchCardBinding
import com.mmt.hotel.landingV3.adapter.LandingCardItemAdapter
import com.mmt.hotel.landingV3.viewModel.adapter.RecentSearchCardViewModel

/**
 * Created by <PERSON><PERSON> on 15/09/21
 */
class RecentSearchCardViewHolder(layoutInflater: LayoutInflater, parent: ViewGroup) :
        HotelRecyclerViewHolder<HtlRecentSearchCardBinding, RecentSearchCardViewModel>(layoutInflater, R.layout.htl_recent_search_card, parent) {

    private val adapter = LandingCardItemAdapter()

    override fun bindData(data: RecentSearchCardViewModel, position: Int) {
        with(dataBinding) {
            if(model == null) {
                recyclerView.adapter = adapter
            }
            data.cardPosition = adapterPosition
            model = data
            executePendingBindings()
        }
    }
}