package com.mmt.hotel.altacco.tracking

import com.mmt.analytics.ActivityTypeEvent
import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureHelper
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper.OEPK_C_1
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper.OEPK_c1
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.StringUtil
import com.mmt.hotel.altacco.model.ui.AltAccoChatData
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.ALT_ACCO_COLLECTIONS_CLICKED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.ALT_ACCO_HOST_CARD_CLICKED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.CHAT_CARD_OMNITURE_TRACKING_KEY
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.CHAT_CLICKED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.CHAT_DISPLAYED_WITHOUT_BADGE
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.CHAT_DISPLAYED_WITH_BADGE
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.DOMESTIC_COUNTRY_CODE
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.DOMESTIC_HOTEL_CODE
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMESTAY_ADULT_ADDED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMESTAY_ADULT_REMOVED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMESTAY_CALENDAR_CLICKED_CHECK_IN
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMESTAY_CHILD_ADDED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMESTAY_CHILD_REMOVED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMESTAY_CITY_CLICKED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMETSAY_CALENDAR_CLICKED_CHECK_OUT
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.INTERNATIONAL_HOTEL_CODE
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.RECENT_SEARCH_CARD_CLICKED
import com.mmt.hotel.altacco.tracking.helper.AltAccoOmnitureTrackingHelper
import com.mmt.hotel.altacco.tracking.helper.AltAccoPdtTrackingHelper
import com.mmt.hotel.altacco.tracking.helper.AltAccoPdtV2TrackingHelper
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.TrackEvent
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelsSearchRequestType
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.response.HotelApiError
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.filterV2.model.response.FilterV2
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.model.response.Response
import com.mmt.hotel.landingV3.tracking.HotelLandingBaseTrackerUtils
import com.mmt.hotel.landingV3.tracking.HotelLandingBaseTrackerUtils.Companion.PET_SELECTION_CLICKED
import com.mmt.hotel.landingV3.tracking.HotelLandingBaseTrackerUtils.Companion.WISHLIST_CLICKED
import com.mmt.hotel.landingV3.tracking.LandingTrackingConstants
import com.mmt.hotel.landingV3.tracking.SearchModifyTracker
import com.mmt.hotel.selectRoom.tracking.ZERO
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject

@ActivityRetainedScoped
class AltAccoTracker @Inject constructor(private val omnitureTracker: AltAccoOmnitureTrackingHelper,
                                         private val pdtTracker: AltAccoPdtTrackingHelper,
                                         private val pdtTrackerV2: AltAccoPdtV2TrackingHelper): SearchModifyTracker {

    private var isStateAutoProgress = false
    private var isCollectionResponseTracked = false
    private var widgetSwipeTracked = false
    private var tapLCount = 0
    private var swipeLCount = 0
    private var tapRCount = 0
    private var swipeRCount = 0

    fun trackPageEntry() {
        pdtTracker.trackPageEntry()
    }

    fun trackOmniturePageEntry(userSearchData: UserSearchData, isFromListing: Boolean) {
        omnitureTracker.trackPageLoad(userSearchData, isFromListing)
        pdtTrackerV2.trackPageEntry(userSearchData)
    }

    fun trackFetchCollectionError(
        userSearchData: UserSearchData,
        error: HotelApiError,
        requestId: String?
    ) {
        pdtTrackerV2.trackFetchCollectionError(userSearchData, error, requestId)
    }

    override fun trackOnDestinationBlockClicked(userSearchData: UserSearchData) {
        pdtTracker.trackClickEventsV2(HOMESTAY_CITY_CLICKED, ActivityTypeEvent.CLICK)
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, HOMESTAY_CITY_CLICKED))
    }

    override fun trackAdultAdded() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, HOMESTAY_ADULT_ADDED))
        pdtTracker.trackClickEventsV2(HOMESTAY_ADULT_ADDED, ActivityTypeEvent.CLICK)
    }

    override fun trackAdultRemoved() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, HOMESTAY_ADULT_REMOVED))
        pdtTracker.trackClickEventsV2(HOMESTAY_ADULT_REMOVED, ActivityTypeEvent.CLICK)
    }

    override fun trackChildAdded() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, HOMESTAY_CHILD_ADDED))
        pdtTracker.trackClickEventsV2(HOMESTAY_CHILD_ADDED, ActivityTypeEvent.CLICK)
    }

    override fun trackChildRemoved() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, HOMESTAY_CHILD_REMOVED))
        pdtTracker.trackClickEventsV2(HOMESTAY_CHILD_REMOVED, ActivityTypeEvent.CLICK)
    }

    override fun trackCalenderCheckInClicked(userSearchData: UserSearchData) {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, HOMESTAY_CALENDAR_CLICKED_CHECK_IN))
        pdtTracker.trackClickEventsV2(HOMESTAY_CALENDAR_CLICKED_CHECK_IN, ActivityTypeEvent.CLICK)
    }

    override fun trackCalenderCheckOutClicked(userSearchData: UserSearchData) {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, HOMETSAY_CALENDAR_CLICKED_CHECK_OUT))
        pdtTracker.trackClickEventsV2(HOMETSAY_CALENDAR_CLICKED_CHECK_OUT, ActivityTypeEvent.CLICK)
    }


    fun trackPageExit(cardsList: List<AbstractRecyclerItem>, lastVisibleCardPos: Int, userSearchData: UserSearchData) {
        trackLandingV2PagerEvents()
        pdtTracker.trackPageExit()
        pdtTrackerV2.trackPageExit(userSearchData)
        omnitureTracker.trackPageExit(cardsList, lastVisibleCardPos,isStateAutoProgress, userSearchData)
        isStateAutoProgress = false
    }

    fun trackRecentSearchClicked(eventName: String, searchRequest: SearchRequest) {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.PROP_54, RECENT_SEARCH_CARD_CLICKED))
        omnitureTracker.appendPageExitEvent(TrackEvent(OmnitureTrackingHelper.M_LIST2, eventName))
        val pdtEventName = eventName + "_clicked"
        pdtTracker.trackClickEvent(pdtEventName, searchRequest)
    }

    fun trackCardClicked(eventName: String, searchRequest: SearchRequest?) {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OEPK_c1, "$ALT_ACCO_COLLECTIONS_CLICKED$eventName"))
        val pdtEventName = eventName + "_clicked"
        pdtTracker.trackClickEvent(pdtEventName, searchRequest ?: SearchRequest())
    }

    fun trackSearchPerformed(searchRequest: SearchRequest, userSearchData: UserSearchData) {
        omnitureTracker.trackSearchPerformed(userSearchData,
            getAutoSuggestTrackingText(searchRequest),
            getSelectedFilterTrackingText(searchRequest.listingSearchData?.appliedFilterList?: emptyList()))
        pdtTracker.trackSearchPerformed(getSearchClickTrackingObject(searchRequest))
    }

    fun trackCollectionResponse(collectionResponse: Response, searchRequest: SearchRequest?, userSearchData: UserSearchData) {
        var trackText = if(HotelUtil.isCountrySearch(searchRequest?.userSearchData?.searchType))
            "country_specific_card_loaded_${searchRequest?.userSearchData?.locationName}_"
        else
            "city_specific_card_loaded_${searchRequest?.userSearchData?.locationName}_"
        collectionResponse.collection?.forEach {
            trackText += "${it.heading}_${it.subHeading}|"
        }
        if(isCollectionResponseTracked) {
            omnitureTracker.trackPageExitEvents(userSearchData)
            trackShownEvent(OmnitureTrackingHelper.PROP_54,trackText)
        } else {
            trackShownEvent(OmnitureTrackingHelper.PROP_54,trackText)
            isCollectionResponseTracked = true
        }
    }

    fun trackChatMessageStatus(msgCount: String) {
        if (msgCount.isEmpty()) {
            trackShownEvent(OmnitureTrackingHelper.OEPK_C_50,CHAT_DISPLAYED_WITHOUT_BADGE)
            pdtTracker.trackClickEventsV2(CHAT_DISPLAYED_WITHOUT_BADGE, ActivityTypeEvent.EVENT)
        } else {
            trackShownEvent(OmnitureTrackingHelper.OEPK_C_50,CHAT_DISPLAYED_WITH_BADGE)
            pdtTracker.trackClickEventsV2(CHAT_DISPLAYED_WITH_BADGE, ActivityTypeEvent.EVENT)
        }
    }

    fun trackImageLeftClick() {
        tapLCount += 1
    }

    fun trackImageRightClick() {
        tapRCount += 1
    }

    fun trackImageLeftSwipe() {
        swipeLCount += 1
    }

    fun trackImageRightSwipe() {
        swipeRCount += 1
    }

    fun trackWidgetSwipedUp() {
        if (!widgetSwipeTracked) {
            widgetSwipeTracked = true
            trackInProp1PageExit(AltAccoTrackingConstants.WIDGET_SWIPED_UP)
        }
    }

    fun trackInProp1PageExit(value:String) {
        trackShownEvent(OmnitureTrackingHelper.OEPK_C_1, value)
    }

    fun trackShownEvent(key:String,value:String) {
        omnitureTracker.appendPageExitEvent(TrackEvent(key,value))
    }

    fun trackChatClicked() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, CHAT_CLICKED))
        pdtTracker.trackClickEventsV2(CHAT_CLICKED, ActivityTypeEvent.CLICK)
    }

    private fun getSearchClickTrackingObject(searchRequest: SearchRequest): SearchClickTrackingObject {
        val filtersData: MutableList<String?> = mutableListOf()
        searchRequest.listingSearchData?.appliedFilterList?.let {
            for (filters in it) {
                filtersData.add(filters.getTitle())
            }
        }
        return SearchClickTrackingObject(filters = filtersData,
                room_count = searchRequest.userSearchData?.occupancyData?.roomCount?.toString(),
                adult_count = searchRequest.userSearchData?.occupancyData?.adultCount.toString(),
                child_count = searchRequest.userSearchData?.occupancyData?.childAges?.size.toString(),
                itinerary = listOf(Itinerary(
                    homestay_city = searchRequest.userSearchData?.locationName,
                    checkin_date = searchRequest.userSearchData?.checkInDate,
                    checkout_date = searchRequest.userSearchData?.checkOutDate
                )),
                sub_lob = if (isDomesticSearch(searchRequest.userSearchData?.countryCode)) DOMESTIC_HOTEL_CODE else INTERNATIONAL_HOTEL_CODE
        )
    }

    private fun getAutoSuggestTrackingText(searchRequest: SearchRequest): String {
        if (searchRequest.userSearchData == null || StringUtil.isNullOrEmpty(searchRequest.userSearchData?.searchType)) {
            return CoreConstants.EMPTY_STRING
        }
        val autosuggestTrackBuilder = StringBuilder()
        if(HotelUtil.isCountrySearch(searchRequest.userSearchData?.searchType))
            autosuggestTrackBuilder.append("COUNTRY_SEARCHED_").append(searchRequest.userSearchData?.locationName)
        else{
            autosuggestTrackBuilder.append("CITY_SEARCHED_").append(searchRequest.userSearchData?.locationName)
            if (HotelsSearchRequestType.AREA_SEARCH.equals(searchRequest.userSearchData?.searchType, ignoreCase = true)) {
                autosuggestTrackBuilder.append("_AREA_")
                autosuggestTrackBuilder.append(searchRequest.userSearchData?.displayName)
            } else if (HotelsSearchRequestType.HOTEL_SEARCH.equals(searchRequest.userSearchData?.searchType, ignoreCase = true)) {
                autosuggestTrackBuilder.append("_HOTEL_")
                autosuggestTrackBuilder.append(searchRequest.userSearchData?.hotelName)
            } else if (HotelsSearchRequestType.CUSTOM_LOCATION_SEARCH.equals(searchRequest.userSearchData?.searchType, ignoreCase = true)) {
                autosuggestTrackBuilder.append("_GOOGLE_")
                autosuggestTrackBuilder.append(searchRequest.userSearchData?.displayName)
            } else if (HotelsSearchRequestType.POI_SEARCH.equals(searchRequest.userSearchData?.searchType, ignoreCase = true)) {
                autosuggestTrackBuilder.append("_POI_")
                autosuggestTrackBuilder.append(searchRequest.userSearchData?.displayName)
            }
        }
        return autosuggestTrackBuilder.toString()
    }

    private fun getSelectedFilterTrackingText(filters: List<FilterV2>): String {
        val stringBuilder = StringBuilder()
        if (filters.isNotEmpty()) {
            filters.forEach {
                stringBuilder
                        .append(CoreConstants.UNDERSCORE)
                        .append(it.toFilterTrackText())
            }
        }

        return stringBuilder.toString()
    }


    private fun isDomesticSearch(countryCode: String?): Boolean {
        countryCode?.let {
            return it == DOMESTIC_COUNTRY_CODE
        }
        return true
    }

    fun trackChatCardClicked(data: AltAccoChatData) {
        val horizontalPosition = getPositionString(data.horizontalPosition+1)
        val verticalPosition = getPositionString(data.verticalPosition+1)
        val trackingKey = CHAT_CARD_OMNITURE_TRACKING_KEY.format(data.trackingKey, horizontalPosition, verticalPosition)
        omnitureTracker.appendPageExitEvent(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, trackingKey))
    }

    private fun getPositionString(position: Int): String {
        val sb: StringBuilder = StringBuilder();
        if (position < 10) {
            sb.append(ZERO)
        }
        return sb.append(position).toString()
    }

    fun trackHostCardClicked() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OEPK_c1, ALT_ACCO_HOST_CARD_CLICKED))
    }

    fun trackWishlistIconClick() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, WISHLIST_CLICKED))
        pdtTracker.trackClickEventsV2(WISHLIST_CLICKED, ActivityTypeEvent.CLICK)
    }

    private fun trackLandingV2PagerEvents() {
        if (tapLCount > 0) {
            trackInProp1PageExit(AltAccoTrackingConstants.TAP_L.format(tapLCount))
        }
        if (tapRCount > 0) {
            trackInProp1PageExit(AltAccoTrackingConstants.TAP_R.format(tapRCount))
        }
        if (swipeLCount > 0) {
            trackInProp1PageExit(AltAccoTrackingConstants.SWIPE_L.format(swipeLCount))
        }
        if (swipeRCount > 0) {
            trackInProp1PageExit(AltAccoTrackingConstants.SWIPE_R.format(swipeRCount))
        }
    }


    override fun trackClickEvent(eventText: String,userSearchData: UserSearchData) {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, eventText))
    }

    override fun trackClickEvent(key: String, eventText: String) {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(key,eventText))
    }

    override fun trackOnRoomAndGuestSelectionClicked(userSearchData: UserSearchData) {

    }

    override fun trackFilterSelection(filterValue: String?, isSelected: Boolean, source: String, userSearchData: UserSearchData) {
        val eventText = source + CoreConstants.UNDERSCORE + if (isSelected) {
            AltAccoTrackingConstants.ADDED
        } else {
            AltAccoTrackingConstants.REMOVED
        }
        trackClickEvent(eventText, userSearchData)
    }

    override fun setStateAutoProgess(progress: Boolean) {
        isStateAutoProgress = progress
    }

    override fun trackRoomAndGuestFragmentLoad() {}
    override fun trackRoomFragmentDoneClicked() {}
    override fun trackRoomAdded() {}
    override fun trackRoomsChanged(trackText: String) {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_50, trackText))
    }
    override fun trackRoomAndGuestBottomSheetCrossClicked() {}
    override fun trackTravellingWithPetsCheckboxClicked() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OEPK_C_1,PET_SELECTION_CLICKED))
    }

    override fun trackFlexiRoomCheckboxChecked(isChecked: Boolean) {

    }

    override fun trackInputMandatoryErrorShown() {
        omnitureTracker.appendEventToPageExitWithCount(TrackEvent(OmnitureTrackingHelper.OEPK_C_1, HotelLandingBaseTrackerUtils.INPUT_MANDATORY_ERROR_SHOWN))
    }

    override fun trackInputMandatoryWidgetShown(isFromListing: Boolean) {
        //Tracked in PageLoad for AltAcco
    }

    fun trackClickEvents(events: List<TrackEvent>, userSearchData: UserSearchData?){
        userSearchData?.let {
            omnitureTracker.trackClickEvents(events, it)
        }
    }
}