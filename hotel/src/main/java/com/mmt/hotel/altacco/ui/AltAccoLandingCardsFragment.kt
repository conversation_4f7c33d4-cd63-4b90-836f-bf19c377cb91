package com.mmt.hotel.altacco.ui

import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.ActivityResultLifeCycleObserver
import com.mmt.core.extensions.OnActivityResult
import com.mmt.core.util.executeIfCast
import com.mmt.core.util.performIfActivityActive
import com.mmt.hotel.R
import com.mmt.hotel.altacco.model.ui.AltAccoChatData
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.altacco.viewModel.AltAccoLandingCardViewModel
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.fragment.HotelFragment
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelEventConstant
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelPageActionName
import com.mmt.hotel.common.model.request.HotelRequestConstants
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.util.HotelDateUtil
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.databinding.FragmentAltaccoLandingCardsBinding
import com.mmt.hotel.filterV2.model.SortingType
import com.mmt.hotel.landingV3.dataModel.LandingResponseWrapper
import com.mmt.hotel.landingV3.event.HotelLandingSearchModifyEvents
import com.mmt.hotel.landingV3.event.LandingEvents
import com.mmt.hotel.landingV3.model.EmperiaEventData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.model.response.CardList
import com.mmt.hotel.landingV3.observable.LandingObservable
import com.mmt.hotel.landingV3.viewModel.HotelLandingActivityV3ViewModel
import com.mmt.hotel.listingV2.dataModel.HotelFilterModelV2
import com.mmt.hotel.listingV2.dataModel.ListingSearchDataV2
import com.mmt.hotel.listingV2.dataModel.LocationFiltersV2
import com.mmt.hotel.listingV2.event.HotelListingClickEvents
import com.mmt.hotel.listingV2.helper.SearchRequestToListingDataV2Converter
import com.mmt.hotel.listingV2.viewModel.adapter.CityCollectionTileCardItemViewModel
import com.mmt.hotel.storyView.data.StoryViewBundleData
import com.mmt.hotel.storyView.data.StoryViewTrackingData
import com.mmt.hotel.storyView.ui.StoryViewActivity
import com.mmt.hotel.storyView.utils.StoryViewTypes
import dagger.hilt.android.AndroidEntryPoint
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class AltAccoLandingCardsFragment : HotelFragment<AltAccoLandingCardViewModel, FragmentAltaccoLandingCardsBinding>(), OnActivityResult {

    @Inject lateinit var observable: LandingObservable

    @Inject lateinit var tracker: AltAccoTracker

    private var lastVisibleCardPos: Int = -1

    override fun initViewModel(): AltAccoLandingCardViewModel = getViewModel()

    private var activityResultObserver : ActivityResultLifeCycleObserver? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityResultObserver =
            activity?.let { ActivityResultLifeCycleObserver(it.activityResultRegistry, this, fragmentID) }
        activityResultObserver?.let {
            it.registerForResult(StoryViewActivity.STORY_PAGE_REQUEST_CODE)
            lifecycle.addObserver(it)
        }
    }

    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                val lastCompletelyVisibleItem = (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
                lastVisibleCardPos = lastVisibleCardPos.coerceAtLeast(lastCompletelyVisibleItem)
            }
        }
    }

    override fun setDataBinding() {
        with(viewDataBinding) {
            observable = <EMAIL>
            viewModel = <EMAIL>
            executePendingBindings()
            recycleView.adapter = <EMAIL>
            <EMAIL>?.let {
                offersCardRecyclerView.adapter =
                    ConcatAdapter(<EMAIL>, it)
            } ?: kotlin.run{
                offersCardRecyclerView.adapter = <EMAIL>
            }
            recycleView.addOnScrollListener(scrollListener)
        }
    }

    override fun getLayoutId() = R.layout.fragment_altacco_landing_cards

    override fun onResume() {
        super.onResume()
        viewModel.initializeRecentItemsFetch()
    }

    override fun initFragmentView() {

    }

    override fun handleEvents(event: HotelEvent) {
        when(event.eventID) {
            LandingEvents.RECENT_SEARCH_ITEMS -> {
                event.data.executeIfCast<List<AbstractRecyclerItem>> {
                    observable.addRecentItemsToRecycle(this)
                }
            }
            AltAccoLandingCardViewModel.SHOW_COLLECTION_CARDS -> {
                event.data.executeIfCast<List<AbstractRecyclerItem>> {
                    observable.updateCollectionResponse(LandingResponseWrapper(cards = this))
                }
            }
            LandingEvents.CARD_CLICKED -> {
                event.data.executeIfCast<Pair<String, CardList>> {
                    handleCardClicked(this)
                }
            }
            LandingEvents.RECENT_SEARCH_CLICKED -> {
                event.data.executeIfCast<Pair<String, SearchRequest>> {
                    recentSearchClicked(this)
                }
            }
            HotelLandingSearchModifyEvents.OPEN_OFFER_DEEPLINK -> {
                event.data.executeIfCast<String?> {
                    openDeeplink(this)
                }
            }
            LandingEvents.OPEN_DEEPLINK -> HotelMigratorHelper.instance.openDeepLink(event.data as String, true)
            LandingEvents.HOST_CARD_CLICKED -> {
                event.data.executeIfCast<String> {
                    tracker.trackHostCardClicked()
                    HotelMigratorHelper.instance.openDeepLink(event.data as String, true)
                }
            }
            LandingEvents.CHAT_CARD_CLICKED -> {
                event.data.executeIfCast<AltAccoChatData> {
                    tracker.trackChatCardClicked(this)
                    chatData.deeplink?.let {
                        HotelMigratorHelper.instance.openDeepLink(it, true)
                    }
                }
            }
            HotelListingClickEvents.LISTING_STORY_CARD_ITEM_CLICK -> {
                event.data.executeIfCast<Pair<CardList, Pair<Int, Int>>> {
                    listingStoryCardClick(this.first, this.second)
                }
            }
            HotelEventConstant.OFF_BEAT_VIEW_ALL_CLICK -> {
                event.data.executeIfCast<CityCollectionTileCardItemViewModel> {
                    activity?.let {
                        HotelUtil.openHotelDeepLinkActivity(it, uiData.deepLink)
                        offBeatCardClick(this)
                    }
                }
            }

            HotelEventConstant.STORY_VIEW_CARD_ALL_CLICK -> {
                event.data.executeIfCast<Pair<String,String>> {
                    activity?.let {
                        val searchRequest = viewModel.searchRequest
                        HotelUtil.openHotelDeepLinkActivity(it, this.first)
                        tracker.trackCardClicked(this.second, searchRequest)
                    }
                }
            }
        }
    }

    private fun offBeatCardClick(data: CityCollectionTileCardItemViewModel) {
        val searchRequest = viewModel.searchRequest?:return
        tracker.trackCardClicked("_OffbeatCities_"+data.uiData.titleText+"_"+(data.position+1), searchRequest)

    }

    private fun listingStoryCardClick(storyTag: CardList, posPair: Pair<Int, Int>){
        if (posPair.first == -1 || storyTag.locId.isNullOrEmpty() || storyTag.locType.isNullOrEmpty()) return

        viewModel.searchRequest?.userSearchData?.let {
            if(it.checkInDate.isEmpty()){
                val calendar = Calendar.getInstance()
                //Add checkout date as +7
                calendar.add(Calendar.DATE, 7)
                it.checkInDate = HotelDateUtil.convertDateToString(calendar.time, HotelConstants.SEARCH_DATE_FORMAT)
                calendar.add(Calendar.DATE, 1)
                it.checkOutDate = HotelDateUtil.convertDateToString(calendar.time, HotelConstants.SEARCH_DATE_FORMAT)
            }
            val updatedUserSearchData = it.copy(locationId = storyTag.locId, locationType =  storyTag.locType,
                displayName = "", locationName = "", hType = "")

            getListingSearchData()?.let { listingSearchData ->
                if (listingSearchData.roomStayCandidate.firstOrNull()?.adultCount == 0){
                    listingSearchData.roomStayCandidate = listOf(RoomStayCandidatesV2(2,null, rooms = 1))
                }
                val updatedSearchDataV2 =  listingSearchData.copy(userSearchData = updatedUserSearchData,
                    parentLocationType = storyTag.parentLoc?.type,
                    parentLocationId = storyTag.parentLoc?.id,
                    searchHotelLimit = storyTag.searchHotelLimit,
                    filter = HotelFilterModelV2(storyTag.appliedFilterMap?.flatMap { it.value }?.toMutableList()?: mutableListOf(), sortType = SortingType.POPULARITY.toSortType()
                        , LocationFiltersV2(emptyList(), emptyList()))
                )
                openStoryViewActivity(updatedSearchDataV2, storyTag.description.orEmpty(), posPair)

            }

        }
    }

    private fun getListingSearchData() : ListingSearchDataV2?{
        return  SearchRequestToListingDataV2Converter.searchRequestToListingSearchDataV2(viewModel.searchRequest)
    }

    private fun openStoryViewActivity(searchData: ListingSearchDataV2, locTitle: String, posPair: Pair<Int, Int>){
        searchData.searchHotelLimit?.let {
            val intent = HotelScreenIntentUtil.getStoryViewIntent()
            val bundle = Bundle()
            var trackingData: StoryViewTrackingData? = null
            val hubCity = searchData.userSearchData.locationName
            trackingData = StoryViewTrackingData(searchData.userSearchData, searchData.baseTracking)
            val data = StoryViewBundleData(
                storyViewType = StoryViewTypes.HOTELS_STORY_TYPE,
                position = 0,
                trackingData = trackingData,
                searchData = searchData,
                locTitle = locTitle,
                hubCity = hubCity,
                itemAdapterPosPair = posPair // first -> pos in Listing Adapter, second -> pos in Story Card, to update card for sold out case
            )
            bundle.putParcelable(StoryViewActivity.STORY_VIEW_BUNDLE_DATA,data)
            intent.putExtras(bundle)
            activityResultObserver?.startActivityForResult(intent, StoryViewActivity.STORY_PAGE_REQUEST_CODE)
        }
    }


    fun handleCollectionResponse(response: LandingResponseWrapper) {
        viewModel.handleCollectionResponse(response)
        response.originalResponse?.let {
            tracker.trackCollectionResponse(it, response.searchRequest, viewModel.getUserSearchData(response.searchRequest))
        }
    }

    fun handleEmperiaData(emperiaEventData: EmperiaEventData) {
        viewModel.handleEmperiaData(emperiaEventData)
        val offersCardData = emperiaEventData.response?.data?.baseSheet?.cardData?.get(HotelConstants.HOMESTAY_EMPERIA_OFFERS_CARD_KEY)
        if(offersCardData != null && observable.homePageAdapter != null) {
            performIfActivityActive(this.activity){
                HotelMigratorHelper.instance.updateHomePageAdapterItems(viewDataBinding.offersCardRecyclerView,
                    observable.homePageAdapter!!, offersCardData, it)
            }
        }
    }

    private fun handleCardClicked(cardClickedInfo: Pair<String, CardList>) {
        val searchRequest = viewModel.searchRequest
        tracker.trackCardClicked(cardClickedInfo.first, searchRequest)
        if (cardClickedInfo.second.deepLink != null) {
            HotelMigratorHelper.instance.openDeepLink(cardClickedInfo.second.deepLink!!, true)
        } else if (cardClickedInfo.second.cityTrendingDataMap != null) {
            performIfActivityActive(this.activity) {
                HotelUtil.openTrendingActivityV2(it, cardClickedInfo.second, HotelRequestConstants.PAGE_CONTEXT_ALT_ACCO_TRENDING,
                    HotelFunnel.HOMESTAY.funnelValue)
            }
        }
    }

    private fun recentSearchClicked(bundle: Pair<String, SearchRequest>) {
        tracker.trackRecentSearchClicked(bundle.first, bundle.second)
        openListing(bundle.second)
    }

    private fun openListing(bundle: SearchRequest,saveRecentSearchData: Boolean = false){
        with(HotelScreenIntentUtil.getListingIntent()) {
            putExtra(HotelConstants.KEY_HOTEL_SEARCH_REQUEST_V2, bundle)
            putExtra(HotelConstants.SAVE_RECENT_SEARCH_ONLINE, saveRecentSearchData)
            putExtra(HotelConstants.SHOW_FILTER_BOTTOMSHEET, false)
            startActivityForResult(this, HotelLandingActivityV3ViewModel.LISTING_REQUEST_CODE)
        }
    }

    private fun openDeeplink(deeplink: String?) {
        deeplink?.let {
            HotelMigratorHelper.instance.openDeepLink(it, false)
        }
    }

    override fun onStop() {
        super.onStop()
        tracker.trackPageExit(observable.recycleCards.get() ?: emptyList(), lastVisibleCardPos, viewModel.getUserSearchData(viewModel.searchRequest))
    }

    companion object {

        const val TAG = "AltAccoLandingCardsFragment"

        fun getInstance() :AltAccoLandingCardsFragment {
            return AltAccoLandingCardsFragment()
        }
    }

    override fun onActivityResultReceived(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            StoryViewActivity.STORY_PAGE_REQUEST_CODE -> {
                data?.extras?.let {
                    val storyCardPos = it.getInt(StoryViewActivity.STORY_CARD_POSITION, -1)
                    val storyItemPos = it.getInt(StoryViewActivity.STORY_ITEM_POSITION, -1)
                    val errorMessage = it.getString(StoryViewActivity.STORY_ERROR_MESSAGE, CoreConstants.EMPTY_STRING)
                    observable.disableStoryCard(storyCardPos, storyItemPos, errorMessage)
                }
            }
        }
    }

}