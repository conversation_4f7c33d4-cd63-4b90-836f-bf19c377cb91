package com.mmt.hotel.altacco.tracking


/*
*    Similar to HotelLandingPdtModel
*    used to send event at search_button_click
*/

data class AltAccoLandingPdtModel(
        val HOMESTAY_search_click: Any? = null
)

data class SearchClickTrackingObject(
        val filters: MutableList<String?>? = null,
        val room_count: String? = null,
        val adult_count: String? = null,
        val child_count: String? = null,
        val itinerary: List<Itinerary>? = null,
        val sub_lob: String? = null
)

data class Itinerary(
        val homestay_city: String? = null,
        val checkin_date: String? = null,
        val checkout_date: String? = null
)