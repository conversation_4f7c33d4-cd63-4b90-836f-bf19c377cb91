package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.mmt.hotel.R
import com.mmt.hotel.altacco.viewModel.PropertyPagerViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.widgets.MMTComposeImageView
import com.mmt.hotel.landingV3.model.AltAccoLandingUiState
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun AltAccoLandingMainScreen(
    modifier: Modifier = Modifier,
    uiData: AltAccoLandingUiState,
    handleEvent: (HotelEvent) -> Unit
) {
    val pagerState = rememberPagerState(0, 0f, pageCount = {
        (uiData.pagerImages?.size?.times(10)) ?: 0
    })
    var isAutoScrollEnabled by remember { mutableStateOf(true) }
    val coroutineScope = rememberCoroutineScope()
    val beyondBoundsPageCount = 1

    LaunchedEffect(key1 = pagerState) {
        while (isAutoScrollEnabled) {
            delay(uiData.scrollDuration?:3000) // Delay for 3 seconds
            coroutineScope.launch {
                if (pagerState.pageCount > 0) {
                    val nextPage = (pagerState.currentPage + 1) % pagerState.pageCount
                    pagerState.animateScrollToPage(nextPage, animationSpec = tween(1000))
                }
            }
        }
    }

    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val statusBarHeight = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()

    Column(modifier = modifier
        .fillMaxWidth()
        .height(screenHeight + statusBarHeight + dimensionResource(id = R.dimen.margin_large))
        .semantics {
            testTagsAsResourceId = true
        }
    ) {
        Box(contentAlignment = Alignment.BottomStart, modifier = Modifier
            .weight(1f)) {
            if (uiData.pagerImages?.isNotNullAndEmpty() == true) {
                HorizontalPager(
                    state = pagerState,
                    pageSize = PageSize.Fill,
                    beyondViewportPageCount = beyondBoundsPageCount,
                    modifier = Modifier
                        .testTag("alt_acco_gallery_pager")
                        .pointerInput(Unit) {
                            detectTapGestures { offset ->
                                val width = size.width

                                // Determine which half was clicked
                                if (offset.x < width / 2) {
                                    coroutineScope.launch {
                                        pagerState.animateScrollToPage(
                                            pagerState.currentPage - 1,
                                            animationSpec = tween(1000)
                                        )
                                    }
                                } else {
                                    coroutineScope.launch {
                                        pagerState.animateScrollToPage(
                                            pagerState.currentPage + 1,
                                            animationSpec = tween(1000)
                                        )
                                    }
                                }
                            }
                        }
                ) { pos ->
                    val index = pos % uiData.pagerImages.size
                    HorizontalImagePagerItem(uiData.pagerImages[index])
                }
                AltAccoGalleryPagerIndicator(modifier = Modifier.padding(
                    start = dimensionResource(id = R.dimen.margin_large),
                    bottom = dimensionResource(
                        id = R.dimen.htl_treel_entry_point_height
                    )
                ),uiData.pagerImages.size, pagerState.currentPage % uiData.pagerImages.size)
            } else {
                val imageVector = ImageVector.vectorResource(id = R.drawable.altacco_landing_placeholder)
                Image(
                    imageVector = imageVector,
                    contentDescription = "altacco_landing_placeholder",
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                        .testTag("alt_acco_pager_loader"),
                    contentScale = ContentScale.Crop
                )
            }
        }
        AltAccoSearchModifyWidget(
            modifier = Modifier
                .offset(y = -dimensionResource(id = R.dimen.margin_large))
                .fillMaxWidth()
                .shadow(elevation = 2.dp)
                .zIndex(4f)
                .background(
                    color = Color.White, RoundedCornerShape(
                        topStart = dimensionResource(
                            id = R.dimen.htl_radius_xxlarge
                        ),
                        topEnd = dimensionResource(
                            id = R.dimen.htl_radius_xxlarge
                        )
                    )
                )
                .padding(
                    top = dimensionResource(id = R.dimen.margin_large),
                    start = dimensionResource(id = R.dimen.margin_large),
                    bottom = dimensionResource(id = R.dimen.margin_large)
                ),
            uiState = uiData.altAccoSearchModifyState,
            handleEvent = handleEvent
        )
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun HorizontalImagePagerItem(propertyPagerViewModel: PropertyPagerViewModel) {
    Box(modifier = Modifier.semantics {
        testTagsAsResourceId = true
    }) {
        val imageVector = ImageVector.vectorResource(id = R.drawable.altacco_landing_placeholder)
        Image(
            imageVector = imageVector,
            contentDescription = "altacco_landing_placeholder",
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .testTag("alt_acco_pager_loader"),
            contentScale = ContentScale.Crop
        )
        LoadImage(imageUrl = propertyPagerViewModel.url, modifier = Modifier
            .fillMaxWidth()
            .testTag("alt_acco_gallery_item"), contentScale = ContentScale.Crop)
        TopShadowView(propertyPagerViewModel.categoryWithNumberText, propertyPagerViewModel.categoryWithNumberTextDesc)
        BottomShadowView(
            modifier = Modifier.align(Alignment.BottomCenter),
            propertyPagerViewModel.propertyName,
            propertyPagerViewModel.propertyLikedTextWithCount
        ) {
            propertyPagerViewModel.onArrowClick()
        }
    }
}

@Composable
fun TopShadowView(categoryWithNumberText: String?, categoryWithNumberTextDesc: String?) {
    Column(
        Modifier
            .fillMaxWidth()
            .background(
                brush = Brush.verticalGradient(
                    listOf(
                        colorResource(id = R.color.htl_alt_acco_bg_start),
                        colorResource(id = R.color.htl_alt_acco_bg_center),
                        colorResource(id = R.color.htl_color_transparent)
                    )
                )
            )
            .padding(
                top = dimensionResource(id = R.dimen.htl_altacco_toolbar_height),
                start = dimensionResource(
                    id = R.dimen.margin_xLarge
                ),
                end = dimensionResource(
                    id = R.dimen.margin_xLarge
                )
            )
    ) {
        MmtComposeTextView(
            modifier = Modifier
                .padding(
                    top = dimensionResource(id = R.dimen.htl_drop_down_item_height),
                )
                .testTag("category_text"),
            text = categoryWithNumberText,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_huge),
            mmtFontStyle = latoBlack,
            color = colorResource(id = R.color.white)
        )
        MmtComposeTextView(
            text = categoryWithNumberTextDesc,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            mmtFontStyle = latoBlack,
            color = colorResource(id = R.color.white),
            modifier = Modifier.testTag("category_desc_text")
        )
    }
}

@Composable
fun BottomShadowView(
    modifier: Modifier = Modifier,
    propertyName: String,
    propertyLikedTextWithCount: String,
    onArrowClicked : () -> Unit
) {
    Column(
        modifier
            .fillMaxWidth()
            .background(
                brush = Brush.verticalGradient(
                    listOf(
                        colorResource(id = R.color.htl_color_transparent),
                        colorResource(id = R.color.htl_alt_acco_bg_center),
                        colorResource(id = R.color.htl_alt_acco_bg_start)
                    )
                )
            )
            .padding(
                start = dimensionResource(
                    id = R.dimen.margin_xLarge
                ),
                end = dimensionResource(
                    id = R.dimen.margin_xLarge
                )
            )
    ) {
        Row(modifier = Modifier
            .mmtClickable {
                onArrowClicked()
            }
            .padding(
                top = dimensionResource(id = R.dimen.margin_xxHuge),
            ), verticalAlignment = Alignment.CenterVertically) {
            MmtComposeTextView(
                text = propertyName,
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
                mmtFontStyle = latoBlack,
                color = colorResource(id = R.color.white),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .weight(1f, fill = false)
                    .testTag("property_name_text")
            )
            MMTComposeImageView(
                painter = painterResource(id = R.drawable.ic_arrow_right_active),
                contentDescription = "arrow_right",
                modifier = Modifier
                    .padding(horizontal = dimensionResource(id = R.dimen.margin_small))
                    .size(dimensionResource(id = R.dimen.htl_text_size_small))
                    .clip(shape = RoundedCornerShape(50))
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                colorResource(id = R.color.htl_button_gradient_start_color),
                                colorResource(id = R.color.htl_button_gradient_end_color)
                            )
                        )
                    )
                    .padding(dimensionResource(id = R.dimen.margin_tiny))
                    .testTag("arrow_icon"),
            )
        }
        MmtComposeTextView(
            modifier = Modifier
                .padding(bottom = dimensionResource(id = R.dimen.margin_xHuge))
                .testTag("property_liked_text"),
            text = propertyLikedTextWithCount,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
            mmtFontStyle = latoRegular,
            color = colorResource(id = R.color.white)
        )
    }
}