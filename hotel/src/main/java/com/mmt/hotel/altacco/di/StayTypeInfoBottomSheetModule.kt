package com.mmt.hotel.altacco.di

import com.mmt.hotel.altacco.ui.bottomsheets.viewmodel.StayTypeInfoBottomSheetViewModel
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(FragmentComponent::class)
class StayTypeInfoBottomSheetModule {
    @Provides
    @IntoMap
    @ViewModelKey(StayTypeInfoBottomSheetViewModel::class)
    fun provideViewModel(viewModel: StayTypeInfoBottomSheetViewModel): HotelViewModel = viewModel
}