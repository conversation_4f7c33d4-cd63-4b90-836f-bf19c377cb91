package com.mmt.hotel.altacco.viewModel

import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.di.NamedConstants
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData

import com.mmt.hotel.landingV3.helper.HotelLandingConfig
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.viewModel.HotelLandingActivityV3ViewModel
import javax.inject.Inject
import javax.inject.Named

/**
 * Created by <PERSON><PERSON><PERSON> on 22/04/21.
 */

class AltAccoLandingViewModel @Inject constructor(
    @Named(NamedConstants.HOMESTAY_LANDING_CONFIG) config: HotelLandingConfig
) : HotelLandingActivityV3ViewModel(config) {

    override fun getTitle(): String {
        return ResourceProvider.instance.getString(R.string.htl_alt_acco_listing_loader_label)
    }

    override fun getSubTitle(): String {
        return CoreConstants.EMPTY_STRING
    }

    /**
     * currently used only in tracking, so added a default userSearchData
     */
    override fun getUserSearchData(searchRequest: SearchRequest?): UserSearchData {
        return searchRequest?.userSearchData
            ?: UserSearchData(countryCode = HotelConstants.COUNTRY_CODE_INDIA, funnelSrc = HotelFunnel.HOMESTAY.funnelValue, id = null,
                occupancyData = OccupancyData(roomCount = 1, adultCount = 1)
            )
    }

    override fun showCrossIcon(): Boolean {
        return false
    }

    override fun getToolbarTitleSize(): Int {
        return R.dimen.htl_altacco_title_text
    }
}