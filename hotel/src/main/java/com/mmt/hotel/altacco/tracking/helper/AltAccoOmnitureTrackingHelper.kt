package com.mmt.hotel.altacco.tracking.helper

import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.omnitureclient.OmnitureHelper
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.core.constant.CoreConstants
import com.mmt.core.extensions.addTrackText
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HOMESTAY_SEARCH_PERFORMED
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.HTL_HOMESTAY_ATTRIBUTES_VALUE
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.TrackEvent
import com.mmt.hotel.base.tracking.HotelBaseOmnitureTrackingHelper
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelUtil

import com.mmt.hotel.landingV3.event.LandingCardAdapterKeys
import com.mmt.hotel.landingV3.tracking.HotelLandingBaseTrackerUtils
import com.mmt.hotel.landingV3.tracking.LandingCommonTrackingHelper
import com.mmt.hotel.landingV3.tracking.LandingTrackingConstants
import com.mmt.hotel.landingV3.tracking.LandingTrackingConstants.LANDING
import com.mmt.hotel.landingV3.viewModel.adapter.LandingCardsViewModel
import com.mmt.hotel.landingV3.viewModel.adapter.RecentSearchViewModel
import javax.inject.Inject

class AltAccoOmnitureTrackingHelper @Inject constructor() : HotelBaseOmnitureTrackingHelper() {

    private var mandatoryWidgetTracked = false

    fun trackPageLoad(userSearchData: UserSearchData, isFromListing: Boolean) {
        val eventParams: MutableMap<String, Any?> = HashMap()
        var prop1Text = LandingCommonTrackingHelper.getTrackingText(userSearchData)
        prop1Text = prop1Text.addTrackText(AltAccoTrackingConstants.LANDING_PAGE_V2_LOADED)

        if (userSearchData.userInputMandatory == true && !mandatoryWidgetTracked) {
            val trackText = if (isFromListing)
                HotelLandingBaseTrackerUtils.LISTING_INPUT_MANDATORY_LOADED
            else HotelLandingBaseTrackerUtils.DETAIL_INPUT_MANDATORY_LOADED
            prop1Text = prop1Text.addTrackText(trackText)
            mandatoryWidgetTracked = true
        }
        eventParams[OmnitureTrackingHelper.OEPK_C_1] = prop1Text
        eventParams[OmnitureTrackingHelper.OEPK_V_37] = "${HTL_HOMESTAY_ATTRIBUTES_VALUE}_1"
        OmnitureTrackingHelper.trackAppState(getScreenEvent(userSearchData), eventParams)
    }

    fun trackClickEvents(eventName: String, trackingVariable: String, userSearchData: UserSearchData) {
        val eventParams = commonOmnitureEvent(userSearchData)
        eventParams[trackingVariable] = eventName
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        OmnitureTrackingHelper.trackAppState(getScreenEvent(userSearchData), eventParams)
    }

    fun trackClickEvents(events: List<TrackEvent>, userSearchData: UserSearchData) {
        val eventParams = commonOmnitureEvent(userSearchData)
        for (event in events){
            eventParams[event.key] = event.value
        }
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        OmnitureTrackingHelper.trackAppState(getScreenEvent(userSearchData), eventParams)
    }

    fun trackPageExit(cardsList: List<AbstractRecyclerItem>, lastVisibleCardPos: Int,isStateAutoProgress: Boolean, userSearchData: UserSearchData) {
        val eventParams: MutableMap<String, Any> = HashMap()
        if (cardsList.isNotEmpty() && cardsList.size > 1 && lastVisibleCardPos > 0) {
            val pipeSeparatedSectionList = getSectionNameListTillPos(cardsList.subList(1, cardsList.size), lastVisibleCardPos)
            eventParams[OmnitureTrackingHelper.OEPK_C56] = pipeSeparatedSectionList
        }
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        eventParams[OmnitureTrackingHelper.OEPK_C_8] = if (isStateAutoProgress) LandingTrackingConstants.PROGRESSION_ENABLED else LandingTrackingConstants.PROGRESSION_DISABLED

        pageExitTrackEvents.forEach {
            val value = StringBuilder(it.value)
            if (it.count > 0){
                value.append(CoreConstants.UNDERSCORE).append(it.count.toString())
            }
            eventParams[it.key] = eventParams[it.key]?.toString()?.addTrackText(value.toString())?:value.toString()
        }
        pageExitTrackEvents.clear()
        OmnitureTrackingHelper.trackAppState(getScreenEvent(userSearchData), eventParams)
    }

    fun trackSearchPerformed(userSearchData: UserSearchData, autoSuggestTrackingText: String, selectedFilterTrackingText: String) {
        val eventParams: MutableMap<String, String> = HashMap()
        eventParams[OmnitureTrackingHelper.OEPK_C_50] = HOMESTAY_SEARCH_PERFORMED
        eventParams[OmnitureTrackingHelper.OEPK_C_54] = autoSuggestTrackingText
        eventParams[OmnitureTrackingHelper.OEPK_V_24] = if (!HotelUtil.isDom(userSearchData.countryCode)) MOB_INTL_HOMESTAY else MOB_DOMESTIC_HOMESTAY
        eventParams[OmnitureTrackingHelper.OEPK_v80] = OmnitureHelper.getDomainSbu()
        eventParams[OmnitureTrackingHelper.OEPK_c1] = "${AltAccoTrackingConstants.SEARCH_FILTERS}$selectedFilterTrackingText"
        val eventList = eventParams.map { TrackEvent(it.key, it.value) }
        appendPageExitEvent(eventList)
    }

    override fun getScreenEvent(userSearchData: UserSearchData): String {
        return HotelUtil.getPageNameEvent(HotelConstants.PAGE_NAME_LANDING, userSearchData.countryCode, userSearchData.funnelSrc).value
    }

    private fun getSectionNameListTillPos(cardsList: List<AbstractRecyclerItem>, lastVisibleCardPos: Int): String {
        val viewedSectionString = StringBuilder()
        var index = 0
        if (lastVisibleCardPos != -1 && !cardsList.isNullOrEmpty() && cardsList.size > lastVisibleCardPos && cardsList.size >= 2) {
            cardsList.forEach {
                viewedSectionString.append(when (it.getItemType()) {
                    LandingCardAdapterKeys.RECENT_SEARCH -> (it as RecentSearchViewModel).title
                    LandingCardAdapterKeys.CIRCULAR_CARD, LandingCardAdapterKeys.SQUARE_CARD,
                    LandingCardAdapterKeys.RECTANGLE_CARD, LandingCardAdapterKeys.TILES_CARD -> (it as LandingCardsViewModel).title
                    else -> CoreConstants.EMPTY_STRING
                })

                if (++index >= lastVisibleCardPos) {
                    return viewedSectionString.toString()
                }
                viewedSectionString.append("|")
            }
        }
        return viewedSectionString.toString()
    }

    override fun getSiteSectionName(isDom: Boolean, funnelSrc: Int): String {
        return LANDING
    }
}