package com.mmt.hotel.altacco.ui.listingv2.compose.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.MMTFontStyle
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun DateGuestEditComposable(modifier: Modifier = Modifier, dateGuestText : String, dateFont: TextUnit, mmtFontStyle: MMTFontStyle) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        MmtComposeTextView(
            text = dateGuestText,
            fontSize = dateFont,
            modifier = Modifier
                .semantics { testTagsAsResourceId = true }
                .layoutId("dateText")
                .testTag("dateText"),
            color = colorResource(id = R.color.white),
            mmtFontStyle = mmtFontStyle,
            style = TextStyle(fontWeight = FontWeight.Normal,fontSize = 34.sp,lineHeight = 36.sp,letterSpacing = 0.25.sp).copy(
                shadow = Shadow(
                    color = colorResource(id = R.color.secondary_color),
                    offset = Offset(2f, 4f),
                    blurRadius = 2f
                )
            ),
        )
        LoadImage(
            modifier = Modifier
                .semantics { testTagsAsResourceId = true }
                .layoutId("iv_edit")
                .testTag("iv_edit")
                .padding(start = dimensionResource(id = R.dimen.margin_small)),
            resourceId = R.drawable.htl_ic_edit_btn_white,
        )
        MmtComposeTextView(
            modifier = Modifier
                .semantics { testTagsAsResourceId = true }
                .layoutId("tv_edit")
                .testTag("tv_edit"),
            text = stringResource(id = R.string.edit),
            color = colorResource(id = R.color.white),
            mmtFontStyle = latoBold,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
            style = TextStyle(fontWeight = FontWeight.Normal,fontSize = 34.sp,lineHeight = 36.sp,letterSpacing = 0.25.sp).copy(
                shadow = Shadow(
                    color = colorResource(id = R.color.secondary_color),
                    offset = Offset(2f, 4f),
                    blurRadius = 2f
                )
            ),
        )
    }
}