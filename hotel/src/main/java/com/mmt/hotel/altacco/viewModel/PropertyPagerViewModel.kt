package com.mmt.hotel.altacco.viewModel

import android.view.View
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.altacco.event.LandingV2DataEvents
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent

class PropertyPagerViewModel (val categoryWithNumberText: String = CoreConstants.EMPTY_STRING,
                              val categoryWithNumberTextDesc: String = CoreConstants.EMPTY_STRING,
                              val propertyName: String = CoreConstants.EMPTY_STRING,
                              val propertyLikedTextWithCount: String = CoreConstants.EMPTY_STRING,
                              val url: String = CoreConstants.EMPTY_STRING,
                              val deeplink: String = CoreConstants.EMPTY_STRING,
                              val imageSizeFactor: Float = 1f,
                              private val eventStream: MutableLiveData<HotelEvent>
) : AbstractRecyclerItem {

    override fun getItemType(): Int {
        return 1
    }

    fun onArrowClick(view: View) {
        eventStream.postValue(HotelEvent(LandingV2DataEvents.IMAGE_ARROW_CLICKED, Pair(deeplink, propertyName)))
    }

    fun onArrowClick() {
        eventStream.postValue(HotelEvent(LandingV2DataEvents.IMAGE_ARROW_CLICKED, Pair(deeplink, propertyName)))
    }

    fun onLeftClick(view: View){
        eventStream.value = HotelEvent(LandingV2DataEvents.IMAGE_LEFT_CLICK)
    }

    fun onRightClick(view: View){
        eventStream.value = HotelEvent(LandingV2DataEvents.IMAGE_RIGHT_CLICK)
    }

}