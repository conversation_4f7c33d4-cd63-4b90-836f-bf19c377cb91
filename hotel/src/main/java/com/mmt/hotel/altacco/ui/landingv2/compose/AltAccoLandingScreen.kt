package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.EaseInOut
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.zIndex
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.landingV3.event.LandingCardAdapterKeys
import com.mmt.hotel.landingV3.event.LandingEvents
import com.mmt.hotel.landingV3.model.AltAccoLandingUiState
import com.mmt.hotel.landingV3.ui.compose.components.HotelLandingToolbar
import com.mmt.hotel.landingV3.ui.compose.components.SearchModifyCollapsedWidget
import com.mmt.hotel.landingV3.viewModel.adapter.LandingCardsViewModel
import com.mmt.hotel.landingV3.viewModel.adapter.RecentSearchCardViewModel
import kotlinx.coroutines.launch

@Composable
fun AltAccoLandingScreen(uiData: AltAccoLandingUiState, handleEvent: (HotelEvent) -> Unit) {

    val state = rememberLazyListState()
    var firstItemHeight by remember { mutableIntStateOf(0) }
    val coroutineScope = rememberCoroutineScope()

    val firstItemScrollOffset by remember {
        derivedStateOf { state.firstVisibleItemScrollOffset }
    }

    val firstVisibleItemIndex by remember {
        derivedStateOf { state.firstVisibleItemIndex }
    }

    val scrollThreshold = firstItemHeight / 5

    val changeToolbarColor = (firstVisibleItemIndex == 0 && firstItemScrollOffset > scrollThreshold) || firstVisibleItemIndex > 0

    val toolbarColor by animateColorAsState(
        targetValue = if(changeToolbarColor) Color.White else Color.Transparent,
        label = ""
    )
    val colorFilter by animateColorAsState(
        targetValue = if(changeToolbarColor) colorResource(id = R.color.black) else Color.White,
        label = ""
    )

    LaunchedEffect(changeToolbarColor) {
        if (changeToolbarColor) {
            handleEvent(HotelEvent(LandingEvents.CHANGE_STATUS_BAR_COLOR_TO_WHITE))
        } else
            handleEvent(HotelEvent(LandingEvents.CHANGE_STATUS_BAR_COLOR_TO_TRANSPARENT))
    }

    val statusBarHeight = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()

    Box(modifier = Modifier
        .fillMaxSize()
        .background(color = Color.White)) {
        Column(Modifier.zIndex(4f)) {
            HotelLandingToolbar(modifier = Modifier
                .padding(top = if (changeToolbarColor) dimensionResource(id = R.dimen.htl_empty_dimen) else statusBarHeight)
                .shadow(
                    elevation = if (changeToolbarColor)
                        dimensionResource(id = R.dimen.margin_tiny)
                    else
                        dimensionResource(id = R.dimen.htl_empty_dimen)
                )
                .fillMaxWidth()
                .height(dimensionResource(id = R.dimen.htl_toolbar_height))
                .background(color = toolbarColor)
                .zIndex(4f)
                .testTag("HotelLandingToolbar"), uiData = uiData.toolbarUiData, true, handleEvent = handleEvent,
                colorFilter = colorFilter
            )
            AnimatedVisibility(
                visible = firstVisibleItemIndex > 0,
                enter = slideInVertically (animationSpec = tween(500, easing = EaseInOut)) + fadeIn(animationSpec = tween(400, easing = EaseInOut)),
                exit = slideOutVertically (animationSpec = tween(300, easing = EaseInOut)) + fadeOut(animationSpec = tween(200, easing = EaseInOut))
            ) {
                SearchModifyCollapsedWidget(
                    modifier = Modifier
                        .background(colorResource(id = R.color.white))
                        .testTag("collapsed_search_widget"),
                    collapsedSearchUiData = uiData.altAccoSearchModifyState.collapsedSearchData,
                    searchButtonText = uiData.altAccoSearchModifyState.searchButtonText,
                    handleEvent = handleEvent,
                    funnel = HotelFunnel.HOMESTAY
                ) {
                    coroutineScope.launch {
                        state.animateScrollToItem(0, 0)
                    }
                    handleEvent(HotelEvent(LandingEvents.ON_COLLAPSED_SEARCH_EDIT_CLICKED))
                }
            }
        }
        LazyColumn(
            Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.color_f2f2f2)), state = state) {

            item(key = 0) {
                AltAccoLandingMainScreen(modifier = Modifier.onGloballyPositioned {
                    firstItemHeight = it.size.height
                }, uiData, handleEvent = handleEvent)
            }
            item(key = 1) {
                uiData.recentSearches?.forEach {
                    if (it is RecentSearchCardViewModel) {
                        RecentSearchLayout(recentSearchViewModel = it)
                    }
                }
            }
            uiData.collectionCards?.forEach {
                when(it.getItemType()) {
                    LandingCardAdapterKeys.CIRCULAR_CARD -> {
                        item(key = it.hashCode()) {
                            LandingCardLayout(data = it as LandingCardsViewModel, LandingCardAdapterKeys.CIRCULAR_CARD)
                        }
                    }
                    LandingCardAdapterKeys.SQUARE_CARD, LandingCardAdapterKeys.RECTANGLE_CARD -> {
                        item(key = it.hashCode()) {
                            LandingCardLayout(data = it as LandingCardsViewModel, LandingCardAdapterKeys.SQUARE_CARD)
                        }
                    }
                    LandingCardAdapterKeys.BANNER_TEXT_CARD -> {
                        item(key = it.hashCode()) {
                            BannerTextCardLayout(data = it as LandingCardsViewModel)
                        }
                    }
                    else -> {

                    }
                }
            }
        }
    }
}