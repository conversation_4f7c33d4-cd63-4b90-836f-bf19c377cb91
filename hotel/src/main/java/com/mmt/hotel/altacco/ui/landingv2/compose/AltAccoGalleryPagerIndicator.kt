package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

@Composable
@Preview
fun AltAccoGalleryPagerIndicator(modifier: Modifier = Modifier, size : Int = 5, selectedIndex : Int = 1) {
    Row(modifier = modifier, horizontalArrangement = Arrangement.spacedBy(4.dp)) {
        repeat(size) {
            IndicatorItem(isSelected = it == selectedIndex)
        }
    }
}

@Composable
@Preview
fun IndicatorItem(isSelected : Boolean = false) {
    val size by animateDpAsState(targetValue = if (isSelected) 24.dp else 12.dp, label = "", animationSpec = tween(500))
    val alpha by animateFloatAsState(targetValue = if (isSelected) 1f else 0.4f, label = "", animationSpec = tween(500))
    Spacer(
        modifier = Modifier
            .width(size)
            .background(
                color = Color.White.copy(alpha = alpha),
                shape = RoundedCornerShape(8.dp)
            )
            .height(4.dp)
    )
}