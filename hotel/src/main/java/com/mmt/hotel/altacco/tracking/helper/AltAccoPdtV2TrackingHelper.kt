package com.mmt.hotel.altacco.tracking.helper

import com.mmt.analytics.AnalyticsSDK
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.ALT_ACCO_PAGE_NAME
import com.mmt.hotel.analytics.pdt.HotelPdtTrackingHelperV2
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Helper
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.getaways.helper.ListingApiCacheAdapterV2
import com.mmt.hotel.landingV3.tracking.LandingTrackingConstants
import com.mmt.hotel.listingV2.tracking.helper.HotelListingPdtV2Helper
import com.gommt.logger.LogUtils
import com.mmt.hotel.analytics.pdtv2.HotelEventType
import com.mmt.hotel.analytics.pdtv2.model.HotelPdtErrorListItem
import com.mmt.hotel.common.model.response.HotelApiError
import com.pdt.eagleEye.constants.EventName
import com.pdt.eagleEye.constants.EventType
import com.pdt.eagleEye.models.ErrorDetailList
import javax.inject.Inject

class AltAccoPdtV2TrackingHelper @Inject constructor(var listingApiCache : ListingApiCacheAdapterV2) : HotelPdtTrackingHelperV2() {
    fun trackPageEntry(userSearchData: UserSearchData) {

        try {
            val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
                eventName = EventName.PAGE_ENTRY,
                eventType = EventType.LIFECYCLE,
                pageName = HotelPdtV2Constants.PageName.landing.name,
                userSearchData = userSearchData,
                requestId = null,
                baseTrackingData = null,
                funnelStep = HotelPdtV2Constants.FunnelStep.landing
            )
            commonEventBuilder.searchContext(HotelPdtV2Helper.createSearchContext(userSearchData, null, null))
            val event = commonEventBuilder.build()
            AnalyticsSDK.instance.trackEvent(event)
            // AnalyticsSDK.instance.trackEvent(event, 1, ActivityTypeEvent.PAGE_LOAD)
        } catch (e: Exception) {
            LogUtils.error(HotelListingPdtV2Helper.TAG, "landing pdt page load", e)
        }

    }

    fun trackFetchCollectionError(
        userSearchData: UserSearchData,
        error: HotelApiError,
        requestId: String?
    ) {
        try {
            val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
                eventName = EventName.API_STATUS,
                eventType = HotelEventType.ERROR,
                pageName = HotelPdtV2Constants.PageName.landing.name,
                userSearchData = userSearchData,
                requestId = requestId,
                baseTrackingData = null,
                funnelStep = HotelPdtV2Constants.FunnelStep.landing
            )
            commonEventBuilder.searchContext(
                HotelPdtV2Helper.createSearchContext(userSearchData = userSearchData)
            )
            commonEventBuilder.eventValue(HotelPdtV2Constants.BackendApis.fetchCollections.name)
            val errorList = mutableListOf<HotelPdtErrorListItem>()
            errorList.add(HotelPdtErrorListItem(code = error.code.orEmpty(), message = error.message.orEmpty()))
            commonEventBuilder.hotelErrorDetailList(errorList)
            val pdtEvent = commonEventBuilder.build()
            AnalyticsSDK.instance.trackEvent(pdtEvent)
        } catch (e: Exception) {
            LogUtils.error(HotelListingPdtV2Helper.TAG, "landing fetch collection error", e)
        }
    }

    fun trackPageExit(userSearchData: UserSearchData) {
        try {
            val commonEventBuilder = HotelPdtV2Helper.getCommonEventBuilder(
                eventName = EventName.PAGE_EXIT,
                eventType = EventType.LIFECYCLE,
                pageName = HotelPdtV2Constants.PageName.landing.name,
                userSearchData = userSearchData,
                requestId = null,
                baseTrackingData = null,
                funnelStep = HotelPdtV2Constants.FunnelStep.landing,
                allRequestIds = listingApiCache.listingApiCacheViewModel.requestIds.values.flatten()
            )
            commonEventBuilder.searchContext(
                HotelPdtV2Helper.createSearchContext(
                    userSearchData,
                    null,
                    null
                )
            )
            val event = commonEventBuilder.build()
            AnalyticsSDK.instance.trackEvent(event)
            clearLandingAPIRequestIdMap()
        } catch (e: Exception) {
            LogUtils.error(HotelListingPdtV2Helper.TAG, "landing pdt page exit", e)
        }
    }



    fun clearLandingAPIRequestIdMap() {
        listingApiCache.listingApiCacheViewModel.requestIds.clear()
    }

}