package com.mmt.hotel.altacco.model.ui

import androidx.lifecycle.MutableLiveData
import com.mmt.data.model.homepage.wrapper.OfferData
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.landingV3.event.HotelLandingSearchModifyEvents

class OffersDataItemModel(
    val welcomeOfferData: OfferData,
    val eventStream: MutableLiveData<HotelEvent>,
    val showBackground: Boolean = true
) {

    val backgroundColor = if (showBackground) R.color.offer_timer_bg_color_future else null

    fun onBannerClick() {
        eventStream.postValue(
            HotelEvent(
                HotelLandingSearchModifyEvents.OPEN_OFFER_DEEPLINK,
                welcomeOfferData.deeplink
            )
        )
    }
}