package com.mmt.hotel.altacco.ui.listingv2.compose.components

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.widgets.MMTComposeImageView
import com.mmt.hotel.widget.compose.MmtComposeTextView

@Composable
fun FloatingActionButtonComposable(modifier : Modifier = Modifier, isExpanded : Boolean, onClickAction : () -> Unit) {

    val resId = R.drawable.htl_map_indicator_listing
    val text = R.string.map_view

    Row(modifier = modifier
        .mmtClickable {
            onClickAction()
        }
        .background(
            brush = Brush.horizontalGradient(
                colors = listOf(
                    colorResource(id = R.color.htl_need_help_grad_end),
                    colorResource(id = R.color.htl_need_help_grad_start),
                )
            ),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_circular))
        )
        .padding(dimensionResource(id = R.dimen.margin_large))
        .animateContentSize(animationSpec = tween(500)),
        verticalAlignment = Alignment.CenterVertically
    ) {
        MMTComposeImageView(painter = painterResource(id = resId), contentDescription = "")
        if (isExpanded) {
            MmtComposeTextView(
                text = stringResource(id = text),
                mmtFontStyle = latoBlack,
                color = colorResource(id = R.color.white),
                fontSize = spDimensionResource(id = R.dimen.text_size_medium),
                modifier = Modifier
                    .padding(start = dimensionResource(id = R.dimen.margin_small_extra)).testTag("tvFab")
            )
        }
    }
}