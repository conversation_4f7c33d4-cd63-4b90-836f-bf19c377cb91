package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.unit.dp
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.compose.resources.setShimmerBackground
import com.mmt.hotel.compose.resources.toColor

@Composable
fun ListingCollectionHeaderLoadingCard(modifier: Modifier = Modifier, header: String,desc:String) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = colorResource(id = R.color.white)
            )            .padding(horizontal = dimensionResource(id = R.dimen.margin_medium))


    ) {
        CollectionListingSearchHeader(Modifier.padding(top = dimensionResource(R.dimen.margin_xxHuge)),header, desc, handleEvent = { _, _ -> })
        Row(modifier = Modifier.padding(top = dimensionResource(R.dimen.margin_xhuge)).fillMaxWidth(), verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center) {
            Spacer(modifier = Modifier.width(dimensionResource(R.dimen.htl_radius_circular))
                .height(dimensionResource(R.dimen.margin_extra_xtiny))
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf( "#00ffffff".toColor(), "#aeaeae".toColor())
                        )
                    )
                .setShimmerBackground(
                    shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small)),
                    colors = listOf(
                        colorResource(id = R.color.htl_shimmer_bg_color),
                        colorResource(id = R.color.htl_shimmer_anim_color)
                    )
                )
            )

            Spacer(modifier = Modifier.padding(horizontal = dimensionResource(R.dimen.margin_small))
                .height(dimensionResource(R.dimen.margin_medium_extra))
                .width(dimensionResource(R.dimen.htl_city_guide_info_item_width))
                .setShimmerBackground(
                    shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small)),
                    colors = listOf(
                        colorResource(id = R.color.htl_shimmer_bg_color),
                        colorResource(id = R.color.htl_shimmer_anim_color)
                    )
                )
            )
            Spacer(modifier = Modifier.width(dimensionResource(R.dimen.htl_radius_circular))
                .height(dimensionResource(R.dimen.margin_extra_xtiny))
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf( "#00ffffff".toColor(), "#aeaeae".toColor()),
                        start = Offset.Infinite,
                        end = Offset.Zero
                    )
                )
                .setShimmerBackground(
                    shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small)),
                    colors = listOf(
                        colorResource(id = R.color.htl_shimmer_bg_color),
                        colorResource(id = R.color.htl_shimmer_anim_color)
                    )
                )
            )
        }
        Row(modifier = Modifier.fillMaxWidth().padding(top = dimensionResource(R.dimen.margin_huge_extra))) {
            for (i in 1..4 step 1) {
                ListingCollectionImageCrouselShimmer(modifier.weight(1f),isSelected = i == 1)
            }
        }
        Row(modifier = Modifier.fillMaxWidth().padding(top = dimensionResource(R.dimen.margin_huge_extra))) {
            for (i in 1..4 step 1) {
                FilterPillShimmer(Modifier.weight(1f))
            }
        }


    }
}

@Composable
fun ListingCollectionImageCrouselShimmer(modifier: Modifier,isSelected: Boolean){
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        val selectedColor = if(isSelected) colorResource( R.color.white) else colorResource( R.color.htl_shimmer_bg_color)
         Box(modifier = Modifier.width(dimensionResource(R.dimen.margin_xxxHuge))
                .height(
                    dimensionResource(R.dimen.margin_xxxHuge)
                ).border(1.dp, selectedColor, shape = RoundedCornerShape(dimensionResource(R.dimen.margin_xxxHuge)))
                .background(color = colorResource(R.color.white), shape = RoundedCornerShape(dimensionResource(R.dimen.margin_xxxHuge) / 2),
                ),
                contentAlignment = Alignment.BottomEnd
            ) {
                Spacer(
                    modifier = Modifier.padding(dimensionResource(R.dimen.margin_extra_xtiny))
                        .width(dimensionResource(R.dimen.margin_xxxHuge))
                        .height(
                            dimensionResource(R.dimen.margin_xxxHuge)
                        )
                        .clip(RoundedCornerShape(dimensionResource(R.dimen.margin_xxxHuge) / 2))   .setShimmerBackground(
                            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small)),
                            colors = listOf(
                                colorResource(id = R.color.htl_shimmer_bg_color),
                                colorResource(id = R.color.htl_shimmer_anim_color)
                            )
                        ),
                )
            }

        Spacer(
            modifier = Modifier.padding(top= dimensionResource(R.dimen.margin_extra_small))
                .width(dimensionResource(R.dimen.margin_xxHuge))
                .height(
                    dimensionResource(R.dimen.margin_medium)
                )
                .clip(RoundedCornerShape(dimensionResource(R.dimen.margin_extra_tiny)))   .setShimmerBackground(
                    shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_extra_tiny)),
                    colors = listOf(
                        colorResource(id = R.color.htl_shimmer_bg_color),
                        colorResource(id = R.color.htl_shimmer_anim_color)
                    )
                ),
        )
        Spacer(
            modifier = Modifier.padding(top= dimensionResource(R.dimen.margin_extra_small))
                .width(dimensionResource(R.dimen.htl_action_card_image_dimen))
                .height(
                    dimensionResource(R.dimen.margin_medium)
                )
                .clip(RoundedCornerShape(dimensionResource(R.dimen.margin_extra_tiny))).setShimmerBackground(
                    shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_extra_tiny)),
                    colors = listOf(
                        colorResource(id = R.color.htl_shimmer_bg_color),
                        colorResource(id = R.color.htl_shimmer_anim_color)
                    )
                ),
        )



    }

}

@Composable
fun FilterPillShimmer(modifier: Modifier){
    Column(modifier = modifier.padding(end = dimensionResource(R.dimen.margin_small)), horizontalAlignment = Alignment.CenterHorizontally) {
        Box(modifier = Modifier
            .border(1.dp,  colorResource( R.color.htl_grey_d8d8d8), shape = RoundedCornerShape(dimensionResource(R.dimen.margin_small)))
            .background(color = colorResource(R.color.white), shape = RoundedCornerShape(dimensionResource(R.dimen.margin_small)),
            ),
            contentAlignment = Alignment.BottomEnd
        ) {
            Spacer(
                modifier = Modifier.padding(dimensionResource(R.dimen.margin_small_extra))
                    .width(dimensionResource(R.dimen.htl_action_card_image_dimen))
                    .height(
                        dimensionResource(R.dimen.margin_medium)
                    )
                    .setShimmerBackground(
                        shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_extra_tiny)),
                        colors = listOf(
                            colorResource(id = R.color.htl_shimmer_bg_color),
                            colorResource(id = R.color.htl_shimmer_anim_color)
                        )
                    ),
            )
        }





    }

}

