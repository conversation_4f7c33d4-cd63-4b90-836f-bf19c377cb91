package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.compose.base.altAccoWidgetItemModifier
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.landingV3.event.HotelLandingSearchModifyEvents
import com.mmt.hotel.landingV3.model.HotelLandingSearchModifyUiState
import com.mmt.hotel.landingV3.ui.compose.components.DateModifyWidget
import com.mmt.hotel.landingV3.ui.compose.components.ErrorText
import com.mmt.hotel.landingV3.ui.compose.components.ModifySearchBox
import com.mmt.hotel.landingV3.ui.compose.components.SuggestedFiltersLoader
import com.mmt.hotel.landingV3.ui.compose.components.SuggestedFiltersWidget
import com.mmt.hotel.selectRoom.compose.SelectRoomCTA

val resources = ResourceProvider.instance
@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun AltAccoSearchModifyWidget(modifier: Modifier = Modifier, uiState: HotelLandingSearchModifyUiState, handleEvent : (HotelEvent) -> Unit){
    Column(
        modifier = modifier.semantics {
            testTagsAsResourceId = true
        }
    ){
        AltAccoSearchForm(modifier = Modifier.padding(end = dimensionResource(id = R.dimen.margin_large)), uiState, handleEvent)
        uiState.suggestedFilters?.let {
            if (it.showLoader)
                SuggestedFiltersLoader()
            else
                SuggestedFiltersWidget(modifier = Modifier
                    .padding(top = dimensionResource(id = R.dimen.margin_medium))
                    .fillMaxWidth(), suggestedFilters = it, handleEvent)
        }
        if (uiState.showInputMandatoryText)
            ErrorText()
        SelectRoomCTA(modifier = Modifier
            .padding(
                top = dimensionResource(id = R.dimen.margin_large),
                end = dimensionResource(id = R.dimen.margin_large)
            )
            .fillMaxWidth()
            .testTag("search_cta"),ctaText = uiState.searchButtonText, onClick = {
            handleEvent(HotelEvent(HotelLandingSearchModifyEvents.SEARCH_PERFORMED))
        })
    }
}

@Composable
fun AltAccoSearchForm(
    modifier: Modifier = Modifier,
    uiState: HotelLandingSearchModifyUiState,
    handleEvent: (HotelEvent) -> Unit
) {
    Column(modifier = modifier
        .fillMaxWidth()
        .background(
            color = colorResource(id = R.color.htl_map_divider_grey),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_xxlarge))
        )
        .border(
            width = dimensionResource(id = R.dimen.htl_divider_height),
            color = colorResource(id = R.color.htl_grey_border_bg),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_xxlarge))
        )){
        ModifySearchBox(
            modifier = Modifier
                .testTag("location_box")
                .altAccoWidgetItemModifier()
                .mmtClickable {
                    if (uiState.locationBoxUiData.isEnabled) {
                        handleEvent(HotelEvent(HotelLandingSearchModifyEvents.OPEN_LOCATION_PICKER))
                    }
                },
            data = uiState.locationBoxUiData,
        )
        HorizontalDivider(color = colorResource(id = R.color.htl_grey_border_bg))
        DateModifyWidget(
            modifier = Modifier.altAccoWidgetItemModifier().testTag("date_box"),
            dateInfoData = uiState.dateInfoUiData, handleEvent)
        HorizontalDivider(color = colorResource(id = R.color.htl_grey_border_bg))
        ModifySearchBox(
            modifier = Modifier
                .altAccoWidgetItemModifier()
                .mmtClickable {
                    handleEvent(HotelEvent(HotelLandingSearchModifyEvents.OPEN_ROOM_AND_GUEST_FRAGMENT))
                }, data = uiState.roomAndPaxInfo
        )
    }
}