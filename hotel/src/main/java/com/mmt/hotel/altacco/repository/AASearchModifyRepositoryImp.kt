package com.mmt.hotel.altacco.repository

import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.groupbooking.viewModel.HotelSessionData
import com.mmt.hotel.landingV3.event.AutoProgressionState
import com.mmt.hotel.landingV3.helper.LocusUtilsV2
import com.mmt.hotel.landingV3.model.HotelLandingLocationData
import com.mmt.hotel.landingV3.model.HtlDefaultDataSearchFormValue
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.model.toSearchRequest
import com.mmt.hotel.landingV3.repository.SearchModifyRepositoryImp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * Created by Gurtek Singh on 23/04/21.
 */
const val AA_DEFAULT_ADULT_COUNT = 2

class AASearchModifyRepositoryImp @Inject constructor() : SearchModifyRepositoryImp(FunnelType.ALT_ACCO_FUNNEL) {

    override fun getLastSearchOrGetDefault(): Flow<SearchRequest> {
        return flow {
            emit(getLastUserSearchRequest() ?: createDefaultSearchRequest())
        }.flowOn(Dispatchers.IO)
    }

    override fun getLastSearchOrGetDefaultV2(funnelType: HotelFunnel): Flow<SearchRequest> {
        return flowOf()
    }

    override suspend fun getLastSearchOrDefault() = getLastUserSearchRequest() ?: createDefaultSearchRequest()

    override suspend fun getDefaultSearchRequest(): SearchRequest {
        return createDefaultSearchRequest()
    }

    override suspend fun createDefaultSearchRequestBasedOnLocationData(data: HotelLandingLocationData): SearchRequest {
        //No need here required in landing
        return SearchRequest()
    }

    private suspend fun createDefaultSearchRequest(): SearchRequest {
        return withContext(Dispatchers.IO) {
            val defaultSearch = HtlDefaultDataSearchFormValue.INDIA_DOM
            val searchRequest = defaultSearch.toSearchRequest(HotelFunnel.HOMESTAY).apply {
                userSearchData?.hType = defaultSearch.locusResultTypeCity.capitalize()
                userSearchData?.occupancyData = OccupancyData(null, AA_DEFAULT_ADULT_COUNT)
                this.roomStayCandidate =
                    mutableListOf(RoomStayCandidatesV2(0, null,rooms = null))
                userSearchData?.let {
                    handleDefaultAttributesValue(it)
                }
            }
            HotelUtil.updateJourneyParams(searchRequest,true)
            searchRequest
        }
    }


    private fun handleDefaultAttributesValue(userSearchData: UserSearchData) {
        removeValuesInUserSearchData(1,userSearchData)
    }

    private fun getLastUserSearchRequest(): SearchRequest? {
        val searchRequest = LocusUtilsV2.getSaveSearchRequest(FunnelType.ALT_ACCO_FUNNEL)?.apply {
            // rewrite correct funnel source, traveller type, primary traveller & applied filters
            userSearchData = userSearchData?.copy(funnelSrc = HotelFunnel.HOMESTAY.funnelValue, travellerType = 0)
            primaryTraveller = null
        }
        /*
        handle homestay default pax scenario if checkInDate of searchRequest is expired
         */
        searchRequest?.let {
            it.userSearchData?.let { userSearchData ->
                val checkInDate = userSearchData.checkInDate
                val updatedSearchRequest = HotelUtil.updateDatesIfRequired(it)
                if (checkInDate != updatedSearchRequest.userSearchData?.checkInDate) {
                    handleDefaultAttributesValue(userSearchData)
                }
            }
            HotelUtil.updateJourneyParams(it)
        }
        return searchRequest
    }

    override fun isAutoProgressionEnabled(): Boolean {
        return true
    }

    override fun progressionStatesVisitedSet(): MutableSet<AutoProgressionState> {
        return HotelSessionData.altAcooProgressionStateSet
    }

    override fun updateCurrentState(state: AutoProgressionState) {
        HotelSessionData.altAcooProgressionStateSet.add(state)
    }

    companion object {
        val TAG = AASearchModifyRepositoryImp::class.java.simpleName
    }
}