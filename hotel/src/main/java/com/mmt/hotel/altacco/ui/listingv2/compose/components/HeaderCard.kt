package com.mmt.hotel.altacco.ui.listingv2.compose.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.listingV2.viewModel.adapter.hotel.ListingSectionViewModel
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.uikit.util.isNotNullAndEmpty

@Composable
fun HeaderCard(modifier: Modifier, data : ListingSectionViewModel) {
    Column(modifier = modifier) {
        MmtComposeTextView(
            text = data.section.heading,
            mmtFontStyle = latoBlack,
            color = colorResource(id = R.color.black),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_large)
        )
        if (data.section.subheading.isNotNullAndEmpty()) {
            MmtComposeTextView(
                modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_tiny)),
                text = data.section.subheading,
                mmtFontStyle = latoBlack,
                color = colorResource(id = R.color.secondary_color),
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_small)
            )
        }
    }
}