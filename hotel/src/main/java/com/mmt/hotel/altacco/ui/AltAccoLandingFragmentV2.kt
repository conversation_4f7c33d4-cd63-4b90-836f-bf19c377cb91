package com.mmt.hotel.altacco.ui

import android.os.CountDownTimer
import android.transition.Fade
import android.transition.TransitionManager
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.doOnLayout
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.widget.NestedScrollView
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.PagerSnapHelper
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SnapHelper
import com.mmt.core.util.*
import com.mmt.data.model.homepage.empeiria.cards.adtech.v1.AdTechCardData
import com.mmt.data.model.homepage.empeiria.interfaces.CardSeenListener
import com.mmt.data.model.homepage.empeiria.interfaces.ICardDataListener
import com.mmt.data.model.homepagex.skywalker.multiviewlist.TemplateViewModel
import com.mmt.hotel.BR
import com.mmt.hotel.R
import com.mmt.hotel.altacco.adapter.InfiniteScrollingAdapter
import com.mmt.hotel.altacco.event.LandingV2DataEvents
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.altacco.tracking.AltAccoTrackingConstants.PROPERTY_CLICK
import com.mmt.hotel.altacco.viewModel.AltAccoLandingFragmentVM
import com.mmt.hotel.altacco.viewModel.PropertyPagerViewModel
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.adapter.HotelBaseRecyclerAdapter
import com.mmt.hotel.base.di.getViewModel
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.ui.activity.HotelActivity
import com.mmt.hotel.binding.htlScrollRecyclerViewOnce
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.extensions.inflateWithDataBinding
import com.mmt.hotel.common.extensions.openFragment
import com.mmt.hotel.common.extensions.toOccupancyData
import com.mmt.hotel.common.helper.AutoScroller
import com.mmt.hotel.common.helper.OnSnapPositionChangeListener
import com.mmt.hotel.common.helper.SnapOnScrollListener
import com.mmt.hotel.common.helper.attachSnapHelperWithListener
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.ui.itemDecorator.LinePagerIndicatorDecoration
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.compose.prefetchImage
import com.mmt.hotel.databinding.FragmentAltAccoLandingV2Binding
import com.mmt.hotel.landingV3.dataModel.HotelCardListFragmentData
import com.mmt.hotel.landingV3.dataModel.LandingResponseWrapper
import com.mmt.hotel.landingV3.event.HotelLandingEmperiaEvents
import com.mmt.hotel.landingV3.event.HotelLandingSearchModifyEvents
import com.mmt.hotel.landingV3.event.LandingEvents
import com.mmt.hotel.landingV3.helper.ThresholdReachScrollListener
import com.mmt.hotel.landingV3.model.EmperiaEventData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.tracking.SearchModifyTracker
import com.mmt.hotel.landingV3.ui.HotelLandingSearchModifyFragment
import com.mmt.hotel.landingV3.ui.LandingBaseFragment
import com.mmt.hotel.landingV3.ui.RoomAndGuestsFragmentV2
import com.mmt.hotel.landingV3.ui.SearchModifyBaseFragment
import com.mmt.hotel.landingV3.viewModel.HotelToolBarWithChatViewModel
import com.mmt.uikit.binding.BindingAdapters
import com.mmt.uikit.binding.BindingAdapters.applyTintAndSetDrawable
import com.mmt.uikit.util.UiUtil
import com.mmt.uikit.util.isNotNullAndEmpty
import com.mmt.uikit.widget.extenstions.extenstions.setMarginTop
import dagger.hilt.android.AndroidEntryPoint
import interfaces.GccHomeLobInteractionListener
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import kotlin.math.min

@AndroidEntryPoint
class AltAccoLandingFragmentV2 :
    LandingBaseFragment<AltAccoLandingFragmentVM, FragmentAltAccoLandingV2Binding>(),
    CardSeenListener, ICardDataListener, GccHomeLobInteractionListener {

    private var canShow = AtomicBoolean(true)

    //For adtech Tracking Purpose
    private var trackPosition = -1
    private var adTechCardData: AdTechCardData? = null
    private var statusBarHeight = 0
    private var countDownTimer: CountDownTimer? = null
    private var isTransparentToolbar: Boolean = false

    override fun getLayoutId(): Int = R.layout.fragment_alt_acco_landing_v2


    override fun initFragmentView() {
        if (isFragmentRecreating()) {
            viewModel.isPagerRequestMade = false
        }
        if (data.isFromAppLanding){
            toolbarAdaptTransparentUIState()
            setPagerHeight()
            inflateStickyCard()
        } else {
            toolbarAdaptNonTransparentUIState()
        }
        addSearchModifyFragment(R.id.container)
        super.initFragmentView()
    }

    private fun setPagerHeight(){
        viewDataBinding.container.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->

            // This state check is required because when user comes back from listing etc this listener is activated
            if (isTransparentToolbar){
                toolbarAdaptTransparentUIState()
            } else toolbarAdaptNonTransparentUIState()

            viewDataBinding.recyclerView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                DeviceUtil.getDeviceHeight(activity)?.let {
                    height = it - v.height + statusBarHeight + CoreUtil.convertDpToPixel(16f).toInt() //16dp of recyclerview is behind the sheet
                }
            }
        }
    }

    private fun inflateStickyCard() {
        val stickyLayoutId = getStickyCardLayoutId()
        if (stickyLayoutId != -1) {
            val viewGroup = viewDataBinding.stickyViewV2
            val binding = viewGroup.inflateWithDataBinding<ViewDataBinding>(stickyLayoutId)
            binding.setVariable(BR.viewModel, viewModel.stickyCardObservableV2)
            viewGroup.addView(binding.root)
        }
    }

    /**
     * hit emperia is temp as only need emperia card in hotel funnel only
     * */
    override fun openHotelCardFragment(showEmpeiraCardFragment: Boolean) {
        viewDataBinding.cardFragmentContainer.isVisible = showEmpeiraCardFragment

        if (showEmpeiraCardFragment && !isFragmentRecreating()) {
            HotelMigratorHelper.instance.openHotelCardFragment(
                childFragmentManager, HotelCardListFragmentData(
                    isFromGCCLanding = data.isFromGccLanding,
                    funnelType = HotelUtil.getFunnelType(funnel.funnelValue)
                )
            )
        }
    }

    override fun onScrollToTop() {
        viewDataBinding.nestedScrollView.smoothScrollTo(0, 0, 100)
    }

    override fun makeEmperiaCall() {
    }

    protected var thresholdReachScrollListener = object : ThresholdReachScrollListener() {
        var alphaSetToZero = true
        var height = -1
        var recyclerViewCrossTheTop = false
        override fun onScrollChange(
            v: NestedScrollView, scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int
        ) {
            super.onScrollChange(v, scrollX, scrollY, oldScrollX, oldScrollY)

            HotelMigratorHelper.instance.trackAdTechCard(
                trackPosition, adTechCardData, canShow, this@AltAccoLandingFragmentV2
            )
            val scrolledY = v?.scrollY ?: 0
            val toolbarThresholdHeight =
                viewDataBinding.recyclerView.height - viewDataBinding.toolbarRoot.root.height - CoreUtil.dpToPx(
                    16
                )
            if (scrolledY >= toolbarThresholdHeight) {
                if (recyclerViewCrossTheTop) {
                    toolbarAdaptNonTransparentUIState()
                    recyclerViewCrossTheTop = false
                    altAccoTracker.trackWidgetSwipedUp()
                }
            } else {
                if (!recyclerViewCrossTheTop) {
                    toolbarAdaptTransparentUIState()
                    recyclerViewCrossTheTop = true
                }

            }
            if (scrolledY < height) {
                if (!alphaSetToZero) {
                    alphaSetToZero = true
                }
            } else {
                if (alphaSetToZero) {
                    alphaSetToZero = false
                }
            }
        }

        override fun onEnterInThreadHoldArea() {
                hideStickyView()
        }

        override fun onExitThreadHoldArea() {
                TransitionManager.beginDelayedTransition(getStickyView(), Fade(Fade.IN))
                showStickyView()
        }
    }

    protected fun hideStickyView() {
        stickyViewVisibility(false)
    }

    private fun toolbarAdaptTransparentUIState() {
        HotelUtil.setTransparentStatusBar(activity)
        viewDataBinding.toolbarRoot.root.setMarginTop(statusBarHeight)
        transparentToolbar(true)
    }

    protected fun showStickyView() {
        stickyViewVisibility(true)
    }

    private fun toolbarAdaptNonTransparentUIState() {
        setLightStatusBar()
        viewDataBinding.toolbarRoot.root.setMarginTop(0)
        transparentToolbar(false)
    }

    private fun stickyViewVisibility(show: Boolean) {
        getStickyView().isVisible = show
        controlToolbarVisibility(!show)
    }

    override fun calculateAndSetThresholdHeight(afterVisibility: Int?) {
        with(viewDataBinding.container) {
            postDelayed({
                val initialVisibility = getStickyView().visibility
                if (initialVisibility == View.GONE) getStickyView().isInvisible = true
                doOnLayout { container ->
                    getStickyView().doOnLayout {
                        var stickyViewHeight = it.height
                        thresholdReachScrollListener.thresholdHeight(viewDataBinding.recyclerView.height + container.height - stickyViewHeight)
                        getStickyView().visibility = afterVisibility ?: initialVisibility
                    }
                }
            }, VIEW_HEIGHT_FETCH_DELAY)
        }
    }

    protected fun getStickyView(): ViewGroup {
        return viewDataBinding.stickyViewV2
    }

    override fun getSearchModifyFragment(): SearchModifyBaseFragment<*, *>? {
        val fragment =
            childFragmentManager.findFragmentById(R.id.container) as? SearchModifyBaseFragment<*, *>
        if (fragment != null && fragment.isAdded) {
            return fragment
        }
        return null
    }


    override fun dataUpdate() {
        if (canShow.get()) {
            val fragment = childFragmentManager.findFragmentById(R.id.card_fragment_container)
            performIfFragmentActive(fragment) {
                HotelMigratorHelper.instance.trackAdTechCard(
                    trackPosition, adTechCardData, canShow, it
                )
            }
        }
    }

    override fun trackCardSeen(position: Int, dataList: TemplateViewModel?) {
        if (dataList is AdTechCardData) {
            this.trackPosition = position
            this.adTechCardData = dataList
        }
    }


    override fun onStop() {
        trackPageExit()
        super.onStop()
    }

    override fun onDestroy() {
        listingApiCache.onCleared()
        super.onDestroy()
    }

    companion object {
        private const val VIEW_HEIGHT_FETCH_DELAY = 300L
        const val TAG = "HotelLandingFragment"
    }


    @Inject
    lateinit var altAccoTracker: AltAccoTracker

    private var mAdapter: HotelBaseRecyclerAdapter = InfiniteScrollingAdapter()

    private var autoScroller: AutoScroller? = null

    override fun initViewModel(): AltAccoLandingFragmentVM = getViewModel()

    override fun showCollapseViewV2() = true

    override fun addSearchModifyFragment(container: Int) {
        val fragInstance = AltAccoSearchModifyFragmentV2.newInstance(
            searchRequest = data.searchRequest, isAreaFieldEditable = data.isAreaEditable, isRoundCorners = data.isFromAppLanding, isFromListing = data.isFromListing
        )

        childFragmentManager.openFragment(
            fragment = fragInstance,
            container = container,
            tag = HotelLandingSearchModifyFragment.TAG
        )
    }

    fun getStickyCardLayoutId(): Int {
        return R.layout.layout_landing_alt_acco_collapsed_search
    }

    override fun addHotelCardFragment() {
        if (!data.isFromAppLanding || isFragmentRecreating()) {
            return
        }
        val cardFragment = AltAccoLandingCardsFragment.getInstance()
        childFragmentManager.openFragment(
            fragment = cardFragment,
            container = R.id.card_fragment_container,
            tag = AltAccoLandingCardsFragment.TAG
        )
        childFragmentManager.executePendingTransactions()
    }

    private fun findCardFragment(): Fragment? {
        return childFragmentManager.findFragmentByTag(AltAccoLandingCardsFragment.TAG)
    }

    override fun handleSharedEvents(event: HotelEvent) {
        when (event.eventID) {
            HotelLandingSearchModifyEvents.OPEN_ROOM_AND_GUEST_FRAGMENT -> {
                event.data.executeIfCast<SearchRequest> {
                    openAltAccoRoomAndGuestBottomSheetFragment(this)
                }
            }
            else -> {
                super.handleSharedEvents(event)
            }
        }
    }


    override fun setDataBinding() {
        super.setDataBinding()
        activity?.findViewById<View>(R.id.toolbar)?.apply {
            visibility = View.GONE
        }
        if (data.isFromAppLanding) {
            viewDataBinding.recyclerView.isVisible = true
            viewDataBinding.pagerLoader.shimmerParent.isVisible = true
            viewDataBinding.nonLandingSpace.isVisible = false
            viewDataBinding.recyclerView.layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
            val snapHelper: SnapHelper = PagerSnapHelper()
            attachSnapHelperWithListener(viewDataBinding.recyclerView, snapHelper,
                behavior = SnapOnScrollListener.Behavior.NOTIFY_ONLY_USER_SCROLL,
                onSnapPositionChangeListener = object : OnSnapPositionChangeListener {
                    override fun onSnapPositionChange(oldPosition: Int, newPosition: Int) {
                        pauseAutoScrollEnableAfterSomeDelay()
                        if (oldPosition > newPosition) {
                            altAccoTracker.trackImageRightSwipe()
                        } else {
                            altAccoTracker.trackImageLeftSwipe()
                        }
                    }
                })
        } else {
            viewDataBinding.recyclerView.isVisible = false
            viewDataBinding.pagerLoader.shimmerParent.isVisible = false
            viewDataBinding.nonLandingSpace.isVisible = true
        }

        with(viewDataBinding) {
            viewModel = <EMAIL>
            ((activity as? HotelActivity<*, *>)?.viewModel as? HotelToolBarWithChatViewModel)?.let {
                it.wishlistIconText.set("")
                toolbarViewModel = it
            }
            nestedScrollView.setOnScrollChangeListener(thresholdReachScrollListener)
        }
    }


    private fun transparentToolbar(isTransparent: Boolean) {
        isTransparentToolbar = isTransparent
        val colorResourceId = if (isTransparent) {
            R.color.fully_transparent
        } else {
            R.color.white
        }
        val iconColorId = if (isTransparent) R.color.white else R.color.htl_grey
        val titleColor =
            ResourceProvider.instance.getColor(if (isTransparent) R.color.white else R.color.black)
        viewDataBinding.toolbarRoot.root.setBackgroundColor(
            ResourceProvider.instance.getColor(
                colorResourceId
            )
        )
        with(viewDataBinding.toolbarRoot.root) {
            findViewById<View>(R.id.back_icon).executeIfCast<ImageView> {
                applyTintAndSetDrawable(this, iconColorId, this.drawable)
            }
            findViewById<View>(R.id.title).executeIfCast<TextView> {
                setTextColor(titleColor)
            }
            findViewById<View>(R.id.tv_currency).executeIfCast<TextView> {
                setTextColor(titleColor)
            }
            findViewById<View>(R.id.currency_icon).executeIfCast<ImageView> {
                applyTintAndSetDrawable(this, iconColorId, this.drawable)
            }
            findViewById<View>(R.id.wishlist_icon).executeIfCast<View> {
                this.findViewById<View>(R.id.message_icon).executeIfCast<ImageView> {
                    applyTintAndSetDrawable(this, iconColorId, this.drawable)
                }
                this.findViewById<View>(R.id.badgeText).executeIfCast<TextView> {
                    this.isVisible = false
                }
                this.findViewById<View>(R.id.messageCount).executeIfCast<TextView> {
                    this.isVisible = false
                }
            }
            findViewById<View>(R.id.chat_icon).executeIfCast<View> {
                this.findViewById<View>(R.id.message_icon).executeIfCast<ImageView> {
                    applyTintAndSetDrawable(this, iconColorId, this.drawable)
                }
            }
            findViewById<View>(R.id.sub_title).apply {
                visibility = View.GONE
            }
        }
    }


    private fun openAltAccoRoomAndGuestBottomSheetFragment(searchRequest: SearchRequest) {
        val occupancyData = searchRequest.roomStayCandidate?.toOccupancyData()

        searchRequest.userSearchData?.let {
            val roomAndGuestFragment = RoomAndGuestsFragmentV2.newInstance(
                occupancyData,
                it,
                isCorpPersonalBooking = searchRequest.personalCorpBooking,
                canUpdateStatusBar = false,
                isFromLanding = data.isFromAppLanding, isFromListing = data.isFromListing,
                travellingWithPets = searchRequest.roomStayCandidate?.get(0)?.travellingWithPets?:false, isAltAcco = true
            )
            performIfActivityActive(this.activity) {
                roomAndGuestFragment.show(childFragmentManager, AltAccoRoomAndGuestBottomSheetFragment.TAG)
            }
        }
    }

    override fun setWindowProperties() {
        statusBarHeight = UiUtil.getStatusBarHeight(activity)
    }

    private fun handleEditClick() {
        if (viewModel.isForceCollapsed == true) {
            viewDataBinding.nestedScrollView.scrollTo(0, 0)
            viewModel.isForceCollapsed = false
            with(viewDataBinding.container) {
                androidx.transition.TransitionManager.beginDelayedTransition(this)
                updateLayoutParams<ViewGroup.LayoutParams> {
                    this.height = ViewGroup.LayoutParams.WRAP_CONTENT
                }
            }
            hideStickyView()
            calculateAndSetThresholdHeight(View.GONE)
            thresholdReachScrollListener.forceSetInThresholdArea(true)
        } else {
            smoothScrollToTop()
        }
        getCurrentUserSearchData()?.let {
//            hotelLandingTracker.trackClickEvent(OmnitureTrackingHelper.OEPK_C_1, LandingTrackingConstants.EDIT_SEARCHED_CLICKED, it)
        }
    }

    protected fun smoothScrollToTop() {
        viewDataBinding.nestedScrollView.smoothScrollTo(0, 0)
    }

    override fun handleEvents(event: HotelEvent) {
        when (event.eventID) {
            LandingV2DataEvents.IMAGE_LEFT_CLICK -> {
                htlScrollRecyclerViewOnce(viewDataBinding.recyclerView, true)
                pauseAutoScrollEnableAfterSomeDelay()
                altAccoTracker.trackImageLeftClick()
            }
            LandingV2DataEvents.IMAGE_RIGHT_CLICK -> {
                htlScrollRecyclerViewOnce(viewDataBinding.recyclerView)
                pauseAutoScrollEnableAfterSomeDelay()
                altAccoTracker.trackImageRightClick()
            }
            LandingEvents.COLLECTION_RESPONSE -> {
                event.data.executeIfCast<LandingResponseWrapper> {
                    (findCardFragment() as? AltAccoLandingCardsFragment)?.handleCollectionResponse(this)
                }
                // super call is also required for this event
                super.handleEvents(event)
            }
            LandingEvents.EDIT_SEARCH_CLICKED -> {
                handleEditClick()
            }
            LandingV2DataEvents.STICKY_SEARCH_CLICKED -> {
                if (getSearchModifyFragment()?.checkAllFieldsFilled() == true) {
                    super.handleEvents(event)
                }else{
                    smoothScrollToTop()
                }
            }
            LandingV2DataEvents.IMAGE_ARROW_CLICKED ->  {
                event.data.executeIfCast<Pair<String, String>> {
                    if (this.first.isNotEmpty()){
                        HotelMigratorHelper.instance.openDeepLink(this.first, true)
                        if (this.second.isNotEmpty()){
                            val propName = this.second.substring(0, min((this.second.length -1), 20))
                            altAccoTracker.trackInProp1PageExit(PROPERTY_CLICK.format(propName))
                        }
                    }
                }
            }
            LandingV2DataEvents.PAGER_API_FAILED -> {
                setPagerRecyclerView(listOf(viewModel.getEmptyPagerModel()))
            }
            LandingV2DataEvents.UPDATE_CATEGORY_IMAGES -> {
                event.data.executeIfCast<Pair<List<PropertyPagerViewModel>,Long?>> {
                    if (this.first.isNotNullAndEmpty()) {
                        second?.let {
                            autoScroller = AutoScroller(it, it)
                            autoScroller?.enableAutoScroll(viewDataBinding.recyclerView)
                        }
                        val imageList = this.first.map { it.url }
                        imageList.forEach { prefetchImage(HotelUtil.appendImageDownSizeParam(it)) }
                        setPagerRecyclerView(this.first)
                    } else setPagerRecyclerView(listOf(viewModel.getEmptyPagerModel()))
                }
            }
            HotelLandingEmperiaEvents.EMPERIA_DATA -> {
                event.data.executeIfCast<EmperiaEventData?> {
                    this?.let {
                        offerData?.let { viewModel.updateOffersData(it) }
                        locationData?.let { updateLocation(it) }
                        (findCardFragment() as? AltAccoLandingCardsFragment)?.handleEmperiaData(this)
                        super.handleEvents(
                            HotelEvent(
                                LandingEvents.WISHLIST_DATA, hotelWishlistData
                            )
                        )
                    }
                }
            }
            else -> {
                super.handleEvents(event)
            }
        }

    }

    private fun setPagerRecyclerView(list: List<AbstractRecyclerItem>){
        if (list.size > 1) {
            viewDataBinding.recyclerView.addItemDecoration(
                LinePagerIndicatorDecoration(maxDots = list.size, mIndicatorHeight = CoreUtil.dpToPx(80), direction = LinePagerIndicatorDecoration.Direction.START))
        }
        mAdapter.updateList(list)
        viewDataBinding.recyclerView.adapter = mAdapter
        viewDataBinding.pagerLoader.shimmerParent.isVisible = false
    }

    private fun pauseAutoScrollEnableAfterSomeDelay() {
        countDownTimer?.cancel()
        autoScroller?.onPause()
        countDownTimer = object : CountDownTimer(3000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                // do nothing
            }

            override fun onFinish() {
                autoScroller?.onResume()
            }
        }
        countDownTimer?.start()
    }

    override fun getLandingTrackerTracker(): SearchModifyTracker {
        return altAccoTracker
    }

    override fun trackPageExit() {
        // page exit is tracked in AltaccoCardFragment for Landing case, this is for edit case from detail and listing
        if (!data.isFromAppLanding) {
            data.searchRequest?.userSearchData?.let {
                altAccoTracker.trackPageExit(emptyList(), -1, it)
            }
        }
    }

    override fun trackSearchPerformed(searchRequest: SearchRequest) {
        altAccoTracker.trackSearchPerformed(searchRequest, getUserSearchData(searchRequest))
    }

    private fun getUserSearchData(searchRequest: SearchRequest?): UserSearchData {
        return searchRequest?.userSearchData ?: UserSearchData(
            countryCode = HotelConstants.COUNTRY_CODE_INDIA,
            funnelSrc = HotelFunnel.HOMESTAY.funnelValue,
            id = null,
            occupancyData = OccupancyData(roomCount = 1, adultCount = 1)
        )
    }

    override fun trackPageLoad(searchRequest: SearchRequest) {
        altAccoTracker.trackPageEntry()
        searchRequest.userSearchData?.let {
            altAccoTracker.trackOmniturePageEntry(getUserSearchData(searchRequest), data.isFromListing)
        }
    }
}