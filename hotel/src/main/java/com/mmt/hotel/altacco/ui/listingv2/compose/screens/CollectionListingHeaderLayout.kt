package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.gommt.pan.utility.EMPTY_STRING
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.listingv2.compose.AltAccoListingItemsMapper
import com.mmt.hotel.altacco.ui.listingv2.compose.components.AltAccoFilterPillsUi
import com.mmt.hotel.altacco.ui.listingv2.compose.components.animateScrollAndCentralizeItem
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.resources.toColor
import com.mmt.hotel.listingV2.event.HotelListingHeaderEvent
import com.mmt.hotel.listingV2.model.HotelCardItem
import com.mmt.hotel.listingV2.model.HotelListingHeaderUiState
import com.mmt.hotel.listingV2.model.ui.HotelListingFilterUiState
import com.mmt.hotel.listingV3.ui.bottomsheets.ContextualisedCollectionHeaderText
import com.mmt.hotel.mobconfig.model.response.CollectionListingData
import com.mmt.hotel.widget.compose.MmtComposeTextView


@Composable
fun CollectionListingHeaderLayout(
    headerUiData: HotelListingHeaderUiState,
    filterScrollState: LazyListState,
    handleEvent: (String, Any?) -> Unit
) {
    val baseHeaderData = headerUiData.hotelHeaderUiData
    val altAccoExtraHeaderData = headerUiData.altAccoExtraHeaderUiData ?: return
    val isExpanded = altAccoExtraHeaderData.isExpanded
    val canExpand = altAccoExtraHeaderData.canExpand

    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
           val modifier = Modifier.
            padding(top = dimensionResource(R.dimen.margin_xxHugex))
                .fillMaxWidth()
                .padding(
                    horizontal = dimensionResource(
                        id = R.dimen.margin_large
                    )
                )
                CollectionListingSearchHeader(modifier,baseHeaderData.locationText, baseHeaderData.dateGuestText, handleEvent)



        if(altAccoExtraHeaderData.cardSection?.cardItems != null && altAccoExtraHeaderData.cardSection.cardItems.isNotEmpty()) {
            AnimatedVisibility(
                isExpanded && canExpand,
                enter = expandVertically(tween(500)) + fadeIn(tween(500)),
                exit = shrinkVertically(tween(500)) + fadeOut(tween(500))
            ) {
                val modifier = Modifier
                    .padding(top = dimensionResource(R.dimen.margin_xHuge))
                    .fillMaxWidth()
                    .wrapContentHeight()
                Column(modifier = Modifier) {
                    ContextualisedCollectionHeaderText(this,
                        modifier.padding(horizontal = dimensionResource(R.dimen.margin_large)),
                        altAccoExtraHeaderData.cardSection.title,
                       null,
                        {},
                        false
                    )

                        CollectionListingImageCardsCorousal(
                            modifier = Modifier.padding(start = dimensionResource(R.dimen.margin_large)).zIndex(1f),
                            altAccoExtraHeaderData.cardSection.cardItems,
                            handleEvent
                        )
                    }
                }
            }

        }

        val filterPills = when(headerUiData.filterUiState) {
            is HotelListingFilterUiState.Success -> headerUiData.filterUiState.filterPills
            is HotelListingFilterUiState.Loading -> headerUiData.filterUiState.loadingPills
            is HotelListingFilterUiState.Error ->   listOf()
        }

        Column(modifier = Modifier
            .padding(top = dimensionResource(id = R.dimen.margin_xLarge), bottom = dimensionResource(R.dimen.margin_medium))
            .fillMaxWidth()
            .clip(
                shape =
                    RoundedCornerShape(
                        topStart = dimensionResource(id = R.dimen.htl_empty_dimen),
                        topEnd = dimensionResource(id = R.dimen.htl_empty_dimen)
                    )

            )
            .background(
                color = colorResource(
                    id =
                        R.color.htl_color_transparent

                )
            )
        ) {
            if (filterPills.isNotEmpty()) {
                Box(modifier = Modifier
                )
                {
                    AltAccoFilterPillsUi(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                top = if (canExpand && isExpanded && altAccoExtraHeaderData.cardSection?.cardItems.isNullOrEmpty())
                                    dimensionResource(id = R.dimen.margin_xHuge)
                                else
                                    dimensionResource(id = R.dimen.margin_medium),
                                bottom = if (canExpand && isExpanded) {
                                    if (headerUiData.stickyCard != null)
                                        dimensionResource(id = R.dimen.margin_small)
                                    else
                                        dimensionResource(id = R.dimen.htl_empty_dimen)
                                } else dimensionResource(id = R.dimen.margin_medium)
                            ),
                        filterPills = filterPills, handleEvent = handleEvent,
                        filterScrollState = filterScrollState
                    )
                }
            }
            headerUiData.stickyCard?.let {
                AltAccoListingItemsMapper(
                    item = it,
                    index = 0,
                    showBorder = false,
                    scrollBottomBoxMPersuasions = false,
                    handleEvent = handleEvent,
                    autoSwipeImageCardIndex = -1
                )
            }
        }
        if (!canExpand || !isExpanded) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(dimensionResource(id = R.dimen.htl_divider_height))
                    .background(color = colorResource(id = R.color.htl_divider_grey))
            )
        }
    }
//}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun CollectionListingSearchHeader(modifier: Modifier = Modifier,locationTitle: String, desc: String, handleEvent: (String, Any?) -> Unit) {
    Row(modifier = modifier.fillMaxWidth()
        .border(1.dp, color = colorResource(R.color.htl_grey_d8d8d8), shape = RoundedCornerShape(dimensionResource(R.dimen.margin_small))).background(color = colorResource(id = R.color.htl_unavailable_bg_color),
            shape = RoundedCornerShape(dimensionResource(R.dimen.margin_small))).padding(
            dimensionResource(id = R.dimen.margin_small)
        ),horizontalArrangement= Arrangement.Center, verticalAlignment = Alignment.CenterVertically ) {
        LoadImage(
            modifier = Modifier.clickable {
                handleEvent(
                    HotelListingHeaderEvent.BACK_PRESSED,
                    null
                )
            }
                .semantics { testTagsAsResourceId = true }
                .layoutId("iv_back")
                .testTag("iv_back")
                .padding(start = dimensionResource(id = R.dimen.margin_small)),
            resourceId = R.drawable.htl_detail_toolbar_back,
        )
        Column(modifier = Modifier.mmtClickable {
            handleEvent(HotelListingHeaderEvent.EDIT_CLICKED_WITH_DISABLED_AREA, null)
        }.weight(1f).padding(start = dimensionResource(id = R.dimen.margin_small))) {
            MmtComposeTextView(
                text = locationTitle,
                fontSize =  spDimensionResource(id = R.dimen.htl_text_size_small),
                modifier = Modifier,
                textAlign = TextAlign.Left,
                maxLines = 1,
                color = colorResource(id = R.color.htl_listing_header),
                mmtFontStyle = latoBlack)
            MmtComposeTextView(
                text = desc,
                fontSize =  spDimensionResource(id = R.dimen.htl_text_size_tiny),
                modifier = Modifier,
                textAlign = TextAlign.Left,
                overflow= TextOverflow.Ellipsis,
                maxLines = 1,
                color = colorResource(id = R.color.htl_grey),
                mmtFontStyle = latoRegular)

    }

        Column(modifier = Modifier.mmtClickable {
            handleEvent(HotelListingHeaderEvent.EDIT_CLICKED_WITH_DISABLED_AREA, null)
        }.padding(horizontal = dimensionResource(id = R.dimen.margin_small)),horizontalAlignment = Alignment.CenterHorizontally) {
            LoadImage(
                modifier = Modifier.width(dimensionResource(id = R.dimen.image_dimen_xsmall)).height(dimensionResource(id = R.dimen.image_dimen_xsmall))
                    .semantics { testTagsAsResourceId = true }
                    .layoutId("iv_edit")
                    .testTag("iv_edit")
                    ,
                resourceId = R.drawable.htl_ic_listing_edit,
            )
            MmtComposeTextView(
                modifier = Modifier
                    .semantics { testTagsAsResourceId = true }
                    .layoutId("tv_edit")
                    .testTag("tv_edit"),
                text = stringResource(id = R.string.edit),
                color = colorResource(id = R.color.htl_color_008cff),
                mmtFontStyle = latoBold,
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny)
            )
        }
    }

}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun CollectionListingHeaderSubCardItem(modifier: Modifier,imageUrl: String, title: String,selectedColor: Color, isSelected: Boolean, handleEvent: (String, Any?) -> Unit) {
    Column(modifier = modifier
        .padding(
            top = dimensionResource(R.dimen.margin_xxLarge)
        )
        .width(dimensionResource(R.dimen.htl_detail_combo_image_height)),
        horizontalAlignment= Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
    ) {
        if (isSelected) {
            Box {
            Box(modifier = Modifier.width(dimensionResource(R.dimen.htl_detail_combo_image_height))
                .height(
                    dimensionResource(R.dimen.htl_detail_combo_image_height)
                ).border(dimensionResource(R.dimen.margin_extra_tiny),selectedColor, shape = RoundedCornerShape(dimensionResource(R.dimen.htl_detail_combo_image_height)))
                .background(color = colorResource(R.color.white), shape = RoundedCornerShape(dimensionResource(R.dimen.htl_detail_combo_image_height) / 2),
                ),
                contentAlignment = Alignment.BottomEnd
            ) {
                LoadImage(
                    modifier = Modifier.padding(dimensionResource(R.dimen.margin_tiny))
                        .width(dimensionResource(R.dimen.htl_detail_combo_image_height))
                        .height(
                            dimensionResource(R.dimen.htl_detail_combo_image_height)
                        )
                        .clip(RoundedCornerShape(dimensionResource(R.dimen.htl_detail_combo_image_height) / 2)),
                    imageUrl = imageUrl,
                    contentScale = ContentScale.FillBounds,
                    resourceId = R.drawable.htl_ugc_placeholder_bg
                )
            }
                LoadImage(
                    modifier = Modifier.align(Alignment.BottomEnd)
                        .width(dimensionResource(id = R.dimen.image_dimen_ymedium))
                        .height(dimensionResource(id = R.dimen.image_dimen_ymedium))
                        .semantics { testTagsAsResourceId = true }
                        .layoutId("iv_tick")
                        .testTag("iv_tick"),
                    resourceId = R.drawable.ic_check_circle_collection_listing,
                )
            }
        } else{
            LoadImage(
                modifier = Modifier
                    .width(dimensionResource(R.dimen.htl_detail_combo_image_height))
                    .height(
                        dimensionResource(R.dimen.htl_detail_combo_image_height)
                    )
                    .clip(RoundedCornerShape(dimensionResource(R.dimen.htl_detail_combo_image_height) / 2)),
                contentScale = ContentScale.FillBounds,
                imageUrl = imageUrl,
                resourceId = R.drawable.htl_ugc_placeholder_bg
            )

        }

        MmtComposeTextView(
            text = title,
            overflow = TextOverflow.Ellipsis,
            maxLines = 2,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            color =  if (isSelected) colorResource(id = R.color.collection_listing_card_item_selected) else colorResource(id = R.color.black),
            mmtFontStyle = if (isSelected) latoBlack else latoRegular,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(top = dimensionResource(R.dimen.margin_extra_small))
                .width(dimensionResource(R.dimen.margin_xxxHuge))
                .align(alignment = Alignment.CenterHorizontally))


    }

}

@Composable
fun CollectionListingImageCardsCorousal(modifier: Modifier = Modifier, cardItems: List<HotelCardItem>, handleEvent: (String, Any?) -> Unit) {
    val scrollState = rememberLazyListState(initialFirstVisibleItemIndex= 0)
    val snapBehavior = rememberSnapFlingBehavior(lazyListState = scrollState)

    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(key1 = cardItems, block = {
        scrollState.animateScrollToItem(0)
    })

    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                handleEvent(HotelListingHeaderEvent.HEADER_CARDS_SWIPED, true)
                return Offset.Zero
            }
        }
    }

    LazyRow(
        state = scrollState,
        modifier = modifier.nestedScroll(nestedScrollConnection),
        horizontalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.margin_huge)),
        flingBehavior = snapBehavior

    ) {
        items(cardItems.size) {
            val cardData =  cardItems[it].cardData
            val collectionListingData = HotelSharedPrefUtil.instance.getObject<CollectionListingData>(SharedPrefKeys.TRAVEL_TIPS_INFO, CollectionListingData::class.java)
            val color = collectionListingData?.selectedColor.toColor("#d84a79".toColor())
            CollectionListingHeaderSubCardItem(modifier = Modifier.mmtClickable {
                handleEvent(HotelListingHeaderEvent.COLLECTION_ITEM_CLICKED, cardData)
            }, imageUrl = cardData.mediaUrl?: EMPTY_STRING, cardData.title?: EMPTY_STRING,color ,cardData.selected == true , handleEvent = handleEvent)
        }
    }
}





@Composable
@Preview
fun CollectionListingSearchHeaderPreview(){
    CollectionListingSearchHeader(Modifier,"Mumbai", "2 Guests, 1 Room 2 Guests, 1 Room 2 Guests, 1 Room", handleEvent = { _, _ -> })

}

@Composable
@Preview
fun CollectionListingHeaderSubCardItemSelectedPreview(){
    CollectionListingHeaderSubCardItem(modifier = Modifier,imageUrl="", "Beachfront Stays","#d84a79".toColor(),true,handleEvent = { _, _ -> })
}

@Composable
@Preview
fun CollectionListingHeaderSubCardItemPreview(){
    CollectionListingHeaderSubCardItem(modifier = Modifier,imageUrl="", "Beachfront Stays","#d84a79".toColor(),false,handleEvent = { _, _ -> })
}

