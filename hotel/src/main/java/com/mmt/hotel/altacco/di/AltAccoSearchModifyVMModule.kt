package com.mmt.hotel.altacco.di

import com.mmt.hotel.altacco.repository.AASearchModifyRepositoryImp
import com.mmt.hotel.common.di.NamedConstants
import com.mmt.hotel.landingV3.repository.SearchModifyRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import javax.inject.Named

@Module
@InstallIn(ViewModelComponent::class)
interface AltAccoSearchModifyViewModelModule {
    @Binds
    @Named(NamedConstants.ALTACCO_SEARCH_MODIFY_REPO)
    fun provideRepository(repositoryImp: AASearchModifyRepositoryImp) : SearchModifyRepository
}