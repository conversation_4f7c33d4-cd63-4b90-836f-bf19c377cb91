package com.mmt.hotel.altacco.ui.listingv2.compose.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.FilterLoadingTab
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.getMMTFontStyle
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.resources.setShimmerBackground
import com.mmt.hotel.compose.widgets.MMTComposeImageView
import com.mmt.hotel.listingV2.event.HotelListingClickEvents
import com.mmt.hotel.listingV2.model.ui.customui.FilterTab
import com.mmt.hotel.listingV3.ui.components.HotelFilterPillMapItem
import com.mmt.hotel.widget.compose.MmtComposeTextView


@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun AltAccoFilterPillsUi(
    modifier: Modifier = Modifier,
    filterPills : List<AbstractRecyclerItem>,
    filterScrollState : LazyListState,
    handleEvent: (String, Any?) -> Unit
) {
    LazyRow(
        modifier = modifier.semantics { testTagsAsResourceId = true }.testTag("filterV2_recyclerview"),
        horizontalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.margin_small)),
        verticalAlignment = Alignment.CenterVertically,
        state = filterScrollState
    ) {
        filterPills.forEachIndexed { index, filterData ->

            val paddingStart = if (index == 0) R.dimen.margin_large else R.dimen.htl_empty_dimen
            val paddingEnd = if (index == filterPills.lastIndex) R.dimen.margin_large else R.dimen.htl_empty_dimen

            item(key = "$filterPills$index") {
                when(filterData) {
                    is FilterTab -> {
                        if (filterData.isMapFilterItem()) {

                            val filterItemModifier = Modifier.testTag("filterPillItem$index")
                                .padding(
                                    start = dimensionResource(id = paddingStart),
                                    end = dimensionResource(id = paddingEnd)
                                )
                                .mmtClickable {
                                    handleEvent(
                                        HotelListingClickEvents.ON_FILTER_TABV2_CLICK,
                                        filterData
                                    )
                                }

                            HotelFilterPillMapItem(
                                modifier = filterItemModifier,
                                filterTab = filterData
                            )
                        } else {
                            AltAccoFilterPillItem(modifier = Modifier.testTag("filterPillItem$index")
                                .padding(
                                    start = dimensionResource(id = paddingStart)
                                )
                                .mmtClickable {
                                    handleEvent(
                                        HotelListingClickEvents.ON_FILTER_TABV2_CLICK,
                                        filterData
                                    )
                                }
                                .testTag("clFilterTab"),
                                filterData = filterData)
                        }
                    }
                    is FilterLoadingTab -> {
                        AltAccoFilterLoadingItem(modifier = Modifier.padding(
                            start = dimensionResource(id = paddingStart),
                            end = dimensionResource(id = paddingEnd)
                        ))
                    }
                }
            }
        }
    }
}

@Composable
private fun AltAccoFilterLoadingItem(modifier: Modifier = Modifier) {
    Spacer(modifier = modifier
        .height(dimensionResource(id = R.dimen.margin_xHuge2))
        .width(dimensionResource(id = R.dimen.htl_detail_inclusion_room_layout_height))
        .setShimmerBackground(
            shape = RoundedCornerShape(
                dimensionResource(id = R.dimen.margin_small)
            ),
            colors = listOf(
                colorResource(id = R.color.htl_shimmer_bg_color),
                colorResource(id = R.color.htl_shimmer_anim_color)
            )
        )
        .border(
            width = dimensionResource(id = R.dimen.htl_divider_height),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small)),
            color = colorResource(id = R.color.htl_grey_d8d8d8)
        )
        )
}

@Composable
private fun AltAccoFilterPillItem(modifier: Modifier = Modifier, filterData: FilterTab) {
    Row(
        modifier
            .height(dimensionResource(R.dimen.margin_xxHuge3))
            .background(
                color = colorResource(
                    id = filterData.config?.backGroundColor ?: R.color.listing_white_bg
                ),
                RoundedCornerShape(dimensionResource(id = R.dimen.margin_small))
            )
            .border(
                width = dimensionResource(id = R.dimen.htl_divider_height),
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small)),
                color = colorResource(
                    id = filterData.config?.borderColor ?: R.color.htl_grey_d8d8d8
                )
            )
            .padding(
                horizontal = dimensionResource(id = R.dimen.margin_small),
                vertical = dimensionResource(id = R.dimen.margin_small_x)
            ),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val fontStyle = getMMTFontStyle(filterData.config?.fontValue)
        val colorFilter = colorResource(id = filterData.config?.textColor?: R.color.htl_black_bg_color)
        val icon = rememberSaveable { filterData.icon }
        val startIcon = rememberSaveable { filterData.startIcon ?: "" }
        FilterStartIcon(icon = startIcon)
        MmtComposeTextView(
            text = filterData.subText?:"",
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
            lineHeight = spDimensionResource(id = R.dimen.text_line_height_medium),
            mmtFontStyle = fontStyle,
            color = colorResource(id = filterData.config?.textColor ?: R.color.htl_black_bg_color),
            modifier = Modifier.testTag("filter_text")
        )
        if(filterData.filterItem == null) {
            FilterIcon(icon = icon, colorFilter = colorFilter, modifier = Modifier.testTag("filter_icon"))
        }
    }
}

@Composable
private fun FilterStartIcon(icon: String) {
    if(icon.isEmpty()) return
    LoadImage(
        modifier = Modifier
            .padding(end = dimensionResource(id = R.dimen.margin_tiny))
            .size(dimensionResource(id = R.dimen.margin_large)),
        imageUrl = icon)
}

@Composable
private fun FilterIcon(icon: Int, colorFilter: Color, modifier: Modifier = Modifier) {
    MMTComposeImageView(
        modifier = modifier.padding(start = dimensionResource(id = R.dimen.margin_tiny)),
        painter = painterResource(id = icon),
        colorFilter = ColorFilter.tint(color = colorFilter),
        contentDescription = "")
}

