package com.mmt.hotel.altacco.ui.customui

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import androidx.core.content.res.ResourcesCompat
import androidx.viewpager.widget.ViewPager
import com.mmt.hotel.R
import com.mmt.uikit.widget.extenstions.extenstions.setMarginStart

/**
 * Created by <PERSON><PERSON><PERSON> on 08/07/21.
 */

class PageIndicatorView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {
    private var lastSelectedPosition = 0

    init {
        val typeArray = getContext().obtainStyledAttributes(attrs, R.styleable.PageIndicatorView, 0, defStyleAttr)
        val totalDots = typeArray.getInt(R.styleable.PageIndicatorView_totalDots, 0)
        typeArray.recycle()
        if (totalDots > 0) {
            (0 until totalDots).forEach {
                val view = PageIndicatorImageView(context).apply {
                    setSelectedIndicator(ResourcesCompat.getDrawable(context.resources, R.drawable.ic_tab_selected, null))
                    setUnSelectedIndicator(ResourcesCompat.getDrawable(context.resources, R.drawable.ic_tab_unselected, null))
                }
                addView(view)
                if (it != 0) getChildAt(it).setMarginStart(resources.getDimension(R.dimen.margin_tiny).toInt())
            }
            getChildAt(lastSelectedPosition).isSelected = true
        }
    }

}