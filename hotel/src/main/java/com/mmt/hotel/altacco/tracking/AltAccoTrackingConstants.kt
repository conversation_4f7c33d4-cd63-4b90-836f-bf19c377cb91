package com.mmt.hotel.altacco.tracking

object AltAccoTrackingConstants {
    const val ALT_ACCO_PAGE_NAME = "AALanding"
    const val PAGE_NAME = "Landing"
    const val HOMESTAY_CALENDAR_CLICKED_CHECK_IN = "homestay_calendar_clicked|check-in"
    const val HOMETSAY_CALENDAR_CLICKED_CHECK_OUT = "homestay_calendar_clicked|check-out"
    const val RECENT_SEARCH_CARD_CLICKED = "recent_search_card_clicked"
    const val HOMESTAY_ADULT_ADDED = "homestay_adult_added"
    const val HOMESTAY_ROOMS_CHANGED = "homestay_rooms_changed"
    const val HOMESTAY_CHILD_ADDED = "homestay_child_added"
    const val HOMESTAY_ADULT_REMOVED = "homestay_adult_removed"
    const val HOMESTAY_CHILD_REMOVED = "homestay_child_removed"
    const val HOMESTAY_CITY_CLICKED = "hometstay_city_clicked"
    const val DOMESTIC_HOTEL_CODE = "DH"
    const val INTERNATIONAL_HOTEL_CODE = "IH"
    const val DOMESTIC_COUNTRY_CODE = "IN"
    const val HOMESTAY_SEARCH_PERFORMED = "homestay_search_performed"
    const val CHAT_DISPLAYED_WITHOUT_BADGE = "chat_displayed_without_badge"
    const val CHAT_DISPLAYED_WITH_BADGE = "chat_displayed_with_badge"
    const val CHAT_CLICKED = "chat_clicked"
    const val ALT_ACCO_COLLECTIONS_CLICKED = "AltAccoCollectionsClicked"
    const val SEARCH_FILTERS = "search_filter"
    const val CHAT_CARD_OMNITURE_TRACKING_KEY = "HTLCard:%s|cta_CARD|H%s|V%s|_clicked"
    const val ALT_ACCO_HOST_CARD_CLICKED = "AltAccoCollectionsClicked_BecomeHost"
    const val MORE_FILTER_CLICKED = "more_contextualfilter";
    const val CITY = "city"
    const val DATE = "date"
    const val PAX = "pax"
    const val EMPTY = "empty"
    const val FILLED = "filled"
    const val HTL_HOMESTAY_ATTRIBUTES_VALUE = "htlHomestayDefaultAttributesValue"
    const val ADDED = "added"
    const val REMOVED = "removed"
    const val LANDING_PAGE_V2_LOADED = "new_landing_page_loaded"
    const val PROPERTY_CLICK = "Property_click_%s"
    const val WIDGET_SWIPED_UP = "widget_swiped_up"
    const val SWIPE_L = "Swipe_L_%d"
    const val SWIPE_R = "Swipe_R_%d"
    const val TAP_L = "Tap_L%d"
    const val TAP_R = "Tap_R%d"
}