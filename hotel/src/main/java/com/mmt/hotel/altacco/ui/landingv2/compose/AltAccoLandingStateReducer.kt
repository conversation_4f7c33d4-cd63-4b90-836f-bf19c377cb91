package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import com.gommt.pan.utility.EMPTY_STRING
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.util.LOBS
import com.mmt.core.util.LocaleHelper
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.HotelCurrencyUtil
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.landingV3.helper.HotelLandingConfig
import com.mmt.hotel.landingV3.helper.HotelLandingUiDataHelper
import com.mmt.hotel.landingV3.model.AltAccoLandingUiState
import com.mmt.hotel.landingV3.model.CollapsedSearchData
import com.mmt.hotel.landingV3.model.DateInfo
import com.mmt.hotel.landingV3.model.HotelLandingBottomSheetsData
import com.mmt.hotel.landingV3.model.HotelLandingDataV3
import com.mmt.hotel.landingV3.model.HotelLandingSearchModifyUiState
import com.mmt.hotel.landingV3.model.HotelLandingToolbarUiData
import com.mmt.hotel.landingV3.model.LandingTab
import com.mmt.hotel.landingV3.model.SuggestedFiltersUiData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.tracking.HotelLandingTracker
import com.mmt.hotel.landingV3.widget.AltAccoLandingAction
import com.mmt.hotel.mobconfig.model.response.PetFriendlyFilter
import java.text.SimpleDateFormat

class AltAccoLandingStateReducer(private val config: HotelLandingConfig,
                                 private val landingData: HotelLandingDataV3,
                                 private val hotelLandingTracker: HotelLandingTracker
) {
    private val resources = ResourceProvider.instance
    private val altAccoViewState : MutableState<AltAccoLandingUiState> = mutableStateOf(getDefaultAltAccoState())
    private val dayWithMonthName = SimpleDateFormat(
        HotelConstants.DETAILS_CHECKIN_DATE_FORMAT, LocaleHelper.getLobCurrentLocale(
            LOBS.HOTEL))
    private val dayNameWithYear = SimpleDateFormat(
        HotelConstants.YY_E, LocaleHelper.getLobCurrentLocale(
            LOBS.HOTEL))
    var customerUnreadMessageData = HotelMigratorHelper.instance.getCustomerUnreadMessageData().value

    fun getViewState() : State<AltAccoLandingUiState> = altAccoViewState

    fun reduce(action: AltAccoLandingAction) {
        altAccoViewState.value = when(action) {
            is AltAccoLandingAction.SetAltAccoSearchModifyData -> {
                altAccoViewState.value.copy(altAccoSearchModifyState = getAltAccoSearchModifyUiData(action.searchRequest))
            }
            is AltAccoLandingAction.OnCurrencySelected -> {
                val toolbarUiData = altAccoViewState.value.toolbarUiData.copy(
                    currencyString = action.currency
                )
                altAccoViewState.value.copy(toolbarUiData = toolbarUiData)
            }
            is AltAccoLandingAction.UpdateDataFromFetchCollectionResponse -> {
                val data = action.landingResponse
                val currentFunnelData = altAccoViewState.value.altAccoSearchModifyState

                currentFunnelData.let {
                    val updatedFunnelData = currentFunnelData.copy(
                        suggestedFilters = HotelLandingUiDataHelper.convertToSuggestedFiltersUiData(data.originalResponse?.optionalFilters),
                    )
                    altAccoViewState.value.copy(altAccoSearchModifyState = updatedFunnelData)
                }
            }

            is AltAccoLandingAction.SuggestedFilterClicked -> {
                val currentFunnelData = altAccoViewState.value.altAccoSearchModifyState

                currentFunnelData.let {
                    val suggestedFilters = currentFunnelData.suggestedFilters
                    val updatedSuggestedFilters = mutableListOf<LandingTab>()

                    suggestedFilters?.items?.forEachIndexed { index, filterTab ->
                        if (index == action.index) {
                            updatedSuggestedFilters.add(filterTab.copy(isSelected = filterTab.isSelected.not()))
                        }else {
                            if (!LoginUtils.isCorporateUser)
                                updatedSuggestedFilters.add(filterTab.copy(isSelected = false))
                            else
                                updatedSuggestedFilters.add(filterTab)
                        }
                    }

                    val updatedSuggestedFiltersData = suggestedFilters?.copy(items = updatedSuggestedFilters)

                    val updatedFunnelData = currentFunnelData.copy(suggestedFilters = updatedSuggestedFiltersData)
                    altAccoViewState.value.copy(altAccoSearchModifyState = updatedFunnelData)
                }
            }
            is AltAccoLandingAction.SetRecentItems -> {
                altAccoViewState.value.copy(recentSearches = action.recentItems)
            }
            is AltAccoLandingAction.SetCollectionResponse -> {
                altAccoViewState.value.copy(pagerImages = action.pagerList, scrollDuration = action.scrollDuration, collectionCards = action.cards)
            }
            is AltAccoLandingAction.ShowFilterLoader -> {
                val currentFunnelData = altAccoViewState.value.altAccoSearchModifyState

                currentFunnelData.let {
                    val updatedFunnelData = it.copy(suggestedFilters = SuggestedFiltersUiData(showLoader = true))
                    altAccoViewState.value.copy(altAccoSearchModifyState = updatedFunnelData)
                }
            }
            is AltAccoLandingAction.UpdateChatIconData -> {
                customerUnreadMessageData = action.data
                val chatIconData = HotelLandingUiDataHelper.getChatIconData(customerUnreadMessageData)
                altAccoViewState.value.copy(
                    toolbarUiData = altAccoViewState.value.toolbarUiData.copy(chatIconData = chatIconData)
                )
            }
            is AltAccoLandingAction.HandleEmperiaData -> {
                HotelLandingUiDataHelper.updateWishlistData(
                    action.emperiaData,
                    altAccoViewState.value,
                    hotelLandingTracker
                )
            }
            is AltAccoLandingAction.ShowErrorOnUi -> {
                val currentFunnelData = altAccoViewState.value.altAccoSearchModifyState

                currentFunnelData.let {
                    val updatedFunnelData = it.copy(
                        locationBoxUiData = it.locationBoxUiData.copy(isShowingError = action.isLocationError),
                        roomAndPaxInfo = it.roomAndPaxInfo.copy(isShowingError = action.isRoomAndPaxError),
                        timeInfo = it.timeInfo?.copy(isShowingError = action.isTimeError),
                        bookingForUiData = it.bookingForUiData?.copy(isShowingError = action.isBookingForError),
                        dateInfoUiData = it.dateInfoUiData.copy(
                            checkinDate = it.dateInfoUiData.checkinDate?.copy(isShowingError = action.isDateError),
                            checkoutDate = it.dateInfoUiData.checkoutDate?.copy(isShowingError = action.isDateError)
                        )
                    )
                    altAccoViewState.value.copy(
                        altAccoSearchModifyState = updatedFunnelData
                    )
                }
            }
        }
    }

    private fun getAltAccoSearchModifyUiData(searchRequest: SearchRequest?) : HotelLandingSearchModifyUiState {
        val userSearchData = searchRequest?.userSearchData
        val currentFunnelData = altAccoViewState.value.altAccoSearchModifyState
        if (userSearchData != null) {
            val petSelectedText = HotelSharedPrefUtil.instance.getObject<PetFriendlyFilter>(
                    SharedPrefKeys.PET_FRIENDLY_FILTER, PetFriendlyFilter::class.java)?.selectionText
                    ?: ResourceProvider.instance.getString(R.string.htl_travelling_with_pets_subtext)
            val locationBoxUiData = currentFunnelData.locationBoxUiData.copy(
                    title = userSearchData.displayName,
                    subtitle = HotelLandingUiDataHelper.getSecondaryDisplay(userSearchData, HotelUtil.getHotelFunnel(userSearchData.funnelSrc)),
                    isEnabled = true
            )
            val dateInfoUiData = currentFunnelData.dateInfoUiData.copy(
                    checkinDate = DateInfo(
                            date = HotelLandingUiDataHelper.createDate(userSearchData.checkInDate, dayWithMonthName),
                            suffixText = HotelLandingUiDataHelper.createDate(userSearchData.checkInDate, dayNameWithYear),
                            hintText = ResourceProvider.instance.getString(R.string.str_check_in)
                    ),
                    checkoutDate = DateInfo(
                            date = HotelLandingUiDataHelper.createDate(userSearchData.checkOutDate, dayWithMonthName),
                            suffixText = HotelLandingUiDataHelper.createDate(userSearchData.checkOutDate, dayNameWithYear),
                            hintText = ResourceProvider.instance.getString(R.string.str_check_out)
                    ),
                    noOfNightsText = HotelLandingUiDataHelper.getNoOfNightsText(userSearchData),
                    daySelectionTabs = HotelLandingUiDataHelper.getDaySelectionTabs(HotelUtil.getHotelFunnel(userSearchData.funnelSrc), userSearchData.checkInDate),
            )

            val roomAndPaxInfo = currentFunnelData.roomAndPaxInfo.copy(
                    title = HotelLandingUiDataHelper.getAltAccoGuestText(userSearchData.occupancyData),
                    subtitle = if (searchRequest.roomStayCandidate?.firstOrNull()?.travellingWithPets == true) {
                        petSelectedText
                    } else EMPTY_STRING
            )
            val collapsedSearchData = CollapsedSearchData(
                    title = userSearchData.displayName
                            ?: resources.getString(R.string.htl_hint_city_area),
                    subtitle = HotelLandingUiDataHelper.getCollapsedSearchBoxSubtitle(dateInfoUiData, userSearchData, HotelFunnel.HOMESTAY),
                    editText = resources.getString(R.string.edit),
                    editIcon = R.drawable.htl_ic_listing_edit,
                    hintText = resources.getString(R.string.htl_hint_add_location)
            )
            return currentFunnelData.copy(
                    locationBoxUiData = locationBoxUiData,
                    dateInfoUiData = dateInfoUiData,
                    roomAndPaxInfo = roomAndPaxInfo,
                    collapsedSearchData = collapsedSearchData
            )
        }
        return currentFunnelData
    }

    private fun getDefaultAltAccoState() : AltAccoLandingUiState {
        return AltAccoLandingUiState(
            toolbarUiData = HotelLandingToolbarUiData(
                title = resources.getString(R.string.htl_alt_acco_listing_loader_label),
                currencyString = HotelCurrencyUtil.getSelectedCurrencyCode().uppercase().ifEmpty {  LoginUtils.getFunnelContextCurrencyCodeV1().uppercase() },
                chatIconData = HotelLandingUiDataHelper.getChatIconData(customerUnreadMessageData),
                wishlistIconData = HotelLandingUiDataHelper.getWishlistIconData()
            ),
            altAccoSearchModifyState = HotelLandingUiDataHelper.getLandingSearchModifyUiState(landingData, HotelFunnel.HOMESTAY),
            bottomSheetsData = HotelLandingBottomSheetsData()
        )
    }

}