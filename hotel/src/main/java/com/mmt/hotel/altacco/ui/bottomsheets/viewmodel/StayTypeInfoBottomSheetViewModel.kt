package com.mmt.hotel.altacco.ui.bottomsheets.viewmodel
import com.mmt.hotel.base.viewModel.HotelToolBarViewModel
import com.mmt.hotel.common.util.HotelUtil.getListingActionInfoData
import com.mmt.hotel.listingV2.model.response.hotels.ExtraDetails
import com.mmt.hotel.mobconfig.model.response.ListingActionDetails
import com.mmt.network.utils.GsonUtils.covertToJSONObject
import javax.inject.Inject

class StayTypeInfoBottomSheetViewModel @Inject constructor(): HotelToolBarViewModel() {

    var listingData: ListingActionDetails? = null
    private var titleString: String? = null

    companion object {
        const val DISMISS_BOTTOM_SHEET = "DISMISS_BOTTOM_SHEET"
    }

    override fun getTitle(): String {
        return titleString?: listingData?.title.toString()
    }

    fun initViewModel(title : String?, actionType: String){
        titleString = title
        listingData = getListingActionInfoData()[actionType]
        if (listingData == null) {
            updateEventStream(DISMISS_BOTTOM_SHEET)
        }
    }

    override fun onHandleBackPress() {
        updateEventStream(DISMISS_BOTTOM_SHEET)
    }

    fun onCrossClick(){
        updateEventStream(DISMISS_BOTTOM_SHEET)
    }

}