package com.mmt.hotel.altacco.ui.listingv2.compose.components

import android.graphics.Paint
import com.mmt.hotel.R
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.animateScrollBy
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.widgets.MMTComposeImageView
import com.mmt.hotel.listingV2.event.HotelListingHeaderEvent
import com.mmt.hotel.listingV2.model.HotelCardItem
import com.mmt.hotel.listingV2.model.HotelCardSection
import com.mmt.hotel.widget.compose.MMTAnnotatedTextView
import com.mmt.hotel.widget.compose.MmtComposeTextView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Composable
fun ScrollableButtonsLayout(
    modifier: Modifier = Modifier,
    cardSection: HotelCardSection?,
    handleEvent : (String, Any?) -> Unit
) {
    val scrollState = rememberScrollState()
    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                handleEvent(HotelListingHeaderEvent.HEADER_PILLS_SWIPED, true)
                return Offset.Zero
            }
        }
    }
    Row(
        modifier = modifier.height(IntrinsicSize.Max),
        verticalAlignment = Alignment.CenterVertically) {
        CollectionPillsTitle(cardSection?.title)
        Row(
            modifier = Modifier
                .padding(start = dimensionResource(id = R.dimen.margin_tiny))
                .height(intrinsicSize = IntrinsicSize.Max)
                .nestedScroll(nestedScrollConnection)
                .horizontalScroll(scrollState),
            horizontalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.margin_medium)),
        ) {
            cardSection?.cardItems?.forEachIndexed { index, item ->
                val marginStart = if(index == 0) R.dimen.margin_medium else R.dimen.htl_empty_dimen
                val marginEnd = if(index == cardSection.cardItems.lastIndex) R.dimen.margin_medium else R.dimen.htl_empty_dimen
                CollectionPill(
                    modifier = Modifier
                        .padding(
                            start = dimensionResource(id = marginStart),
                            end = dimensionResource(id = marginEnd)
                        )
                        .fillMaxHeight(),
                    cardItem = item,
                    scrollState = scrollState,
                    handleEvent = handleEvent
                )
            }
        }
    }
}

@Composable
private fun CollectionPillsTitle(title : String?) {
    Row(modifier = Modifier
        .fillMaxHeight()
        .mmtClickable {
        }, verticalAlignment = Alignment.CenterVertically) {
        Row(modifier = Modifier
            .height(IntrinsicSize.Max)) {
            MmtComposeTextView(
                text = title?: stringResource(id = R.string.htl_collections),
                color = colorResource(id = R.color.white),
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
                lineHeight = spDimensionResource(id = R.dimen.text_line_height_large),
                mmtFontStyle = latoBold,
                modifier = Modifier.padding(start = dimensionResource(id = R.dimen.margin_large)),
                style = TextStyle(fontWeight = FontWeight.Normal,fontSize = 34.sp,lineHeight = 36.sp,letterSpacing = 0.25.sp).copy(
                    shadow = Shadow(
                        color = colorResource(id = R.color.secondary_color),
                        offset = Offset(2f, 4f),
                        blurRadius = 2f
                    )
                )
            )
            Spacer(modifier = Modifier
                .padding(start = dimensionResource(id = R.dimen.margin_medium))
                .width(dimensionResource(id = R.dimen.htl_divider_height))
                .fillMaxHeight()
                .background(color = colorResource(id = R.color.white))
            )
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun CollectionPill(
    modifier: Modifier = Modifier,
    cardItem: HotelCardItem,
    handleEvent: (String, Any?) -> Unit,
    scrollState: ScrollState
) {
    val scope = rememberCoroutineScope()
    Row(modifier = modifier
        .semantics { testTagsAsResourceId = true }
        .testTag("item_collection_collapsed")
        .mmtClickable {
            handleEvent(HotelListingHeaderEvent.COLLECTION_ITEM_CLICKED, cardItem.cardData)
            scope.launch {
                scrollState.scrollTo(0)
            }
        }
        .height(IntrinsicSize.Max)
        .background(
            color = if (cardItem.isSelected) Color.Black.copy(alpha = 0.66f) else Color.Transparent,
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small))
        )
        .border(
            width = dimensionResource(id = R.dimen.htl_divider_height),
            color = colorResource(id = R.color.white),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small))
        )
        .padding(dimensionResource(id = R.dimen.margin_small)),
        verticalAlignment = Alignment.CenterVertically
    ) {
        MmtComposeTextView(
            text = cardItem.cardData.title?:"",
            color = colorResource(id = R.color.white),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            lineHeight = spDimensionResource(id = R.dimen.text_line_height_large),
            mmtFontStyle = if (cardItem.isSelected) latoBold else latoRegular,
            style = TextStyle(fontWeight = FontWeight.Normal,fontSize = 34.sp,lineHeight = 36.sp,letterSpacing = 0.25.sp).copy(
                shadow = Shadow(
                    color = colorResource(id = R.color.secondary_color),
                    offset = Offset(2f, 4f),
                    blurRadius = 2f
                )
            )
        )
        if (cardItem.isSelected) {
            MMTComposeImageView(
                painter = painterResource(id = R.drawable.ic_close_vector_white),
                contentDescription = "",
                Modifier
                    .semantics { testTagsAsResourceId = true }
                    .testTag("iv_cross_collection_item")
                    .padding(start = dimensionResource(id = R.dimen.margin_small_extra))
            )
        }
    }
}
@Composable
fun ImageCardsCorousal(modifier: Modifier = Modifier, cardItems: List<HotelCardItem>, scrollState: LazyListState, handleEvent: (String, Any?) -> Unit) {
    val snapBehavior = rememberSnapFlingBehavior(lazyListState = scrollState)

    val coroutineScope = rememberCoroutineScope()

    LaunchedEffect(key1 = Unit, block = {
        scrollState.animateScrollAndCentralizeItem(cardItems.size*10, coroutineScope)
    })

    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                handleEvent(HotelListingHeaderEvent.HEADER_CARDS_SWIPED, true)
                return Offset.Zero
            }
        }
    }

    LazyRow(
        state = scrollState,
        modifier = modifier.nestedScroll(nestedScrollConnection),
        horizontalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.margin_medium)),
        flingBehavior = snapBehavior
    ) {
        items(Int.MAX_VALUE, key = null, itemContent = {
            val index = it % cardItems.size
            ImageCardItemComposable(cardItem = cardItems[index], handleEvent = handleEvent)
        })
    }
}

fun LazyListState.animateScrollAndCentralizeItem(index: Int, scope: CoroutineScope) {
    val itemInfo = this.layoutInfo.visibleItemsInfo.firstOrNull { it.index == index }
    scope.launch {
        if (itemInfo != null) {
            val center = <EMAIL> / 2
            val childCenter = itemInfo.offset + itemInfo.size / 2
            <EMAIL>((childCenter - center).toFloat())
        } else {
            <EMAIL>(index)
            <EMAIL>(index, scope)
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ImageCardItemComposable(
    cardItem: HotelCardItem,
    handleEvent: (String, Any?) -> Unit
) {
    Box( modifier = Modifier
        .semantics { testTagsAsResourceId = true }
        .testTag("item_collection_expanded")
        .width(dimensionResource(id = R.dimen.alt_acco_listing_collection_card_width))
        .height(dimensionResource(id = R.dimen.alt_acco_listing_collection_card_width))
        .clip(
            RoundedCornerShape(dimensionResource(id = R.dimen.margin_large_extra))
        )
        .mmtClickable {
            handleEvent(HotelListingHeaderEvent.COLLECTION_ITEM_CLICKED, cardItem.cardData)
        },
        contentAlignment = Alignment.BottomCenter) {
        LoadImage(
            modifier = Modifier.fillMaxSize(),
            imageUrl = cardItem.cardData.mediaUrl,
            contentScale = ContentScale.FillBounds,
            placeHolderId = R.drawable.bgdefault_bg
        )
        val titleHeight = dimensionResource(id = R.dimen.htl_filter_item_placeholder_max_height)
        Box(Modifier.height(titleHeight), contentAlignment = Alignment.BottomStart) {
            LoadBlurredImage(mediaUrl = cardItem.cardData.mediaUrl, modifier = Modifier.fillMaxWidth())
            Column(modifier = Modifier
                .fillMaxHeight()
                .padding(
                    horizontal = dimensionResource(id = R.dimen.margin_medium)
                ),
                verticalArrangement = Arrangement.Center
            ) {
                MMTAnnotatedTextView(
                    text = buildAnnotatedString {
                        withStyle(style = SpanStyle(fontWeight = FontWeight.Black)) {
                            append(cardItem.cardData.title?:"")
                        }
                        append(" (${cardItem.cardData.desc?:""})")
                    },
                    fontSize = spDimensionResource(id = R.dimen.text_size_medium),
                    lineHeight = spDimensionResource(id = R.dimen.margin_17dp),
                    color = colorResource(id = R.color.white),
                    maxLines = 2,
                    mmtFontStyle = latoRegular
                )
            }
        }
    }
}

@Composable
private fun LoadBlurredImage(mediaUrl: String?, modifier: Modifier = Modifier) {
    val gradientColor = colorResource(id = R.color.htl_collection)
    LoadImage(
        modifier = modifier
            .fillMaxSize()
            .drawWithCache {
                val gradient = Brush.verticalGradient(
                    colors = listOf(gradientColor.copy(0.5f), gradientColor.copy(0.5f)),
                    startY = 0f,
                    endY = size.height
                )
                onDrawWithContent {
                    drawContent()
                    drawRect(gradient, blendMode = BlendMode.SrcAtop)
                }
            }
            .blur(radius = dimensionResource(id = R.dimen.margin_medium)),
        placeHolderId = R.drawable.bgdefault_bg,
        contentScale = ContentScale.FillBounds,
        imageUrl = mediaUrl
    )
}
