package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.landingV3.viewModel.adapter.PrebookChatViewModel
import com.mmt.hotel.landingV3.viewModel.adapter.RecentSearchCardItemViewModel
import com.mmt.hotel.landingV3.viewModel.adapter.RecentSearchCardViewModel
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun RecentSearchLayout(modifier: Modifier = Modifier, recentSearchViewModel: RecentSearchCardViewModel) {
    Column(
        modifier
            .semantics { testTagsAsResourceId = true }
            .fillMaxWidth()
            .background(colorResource(id = R.color.white))
            .padding(
                start = dimensionResource(id = R.dimen.margin_large),
                top = dimensionResource(id = R.dimen.margin_large),
                bottom = dimensionResource(id = R.dimen.margin_large)
            ))    {
        Row(verticalAlignment = Alignment.CenterVertically) {
            recentSearchViewModel.icon?.let {
                LoadImage(modifier = Modifier.size(dimensionResource(id = R.dimen.image_dimen_xsmall)), imageUrl = it)
            }
            MmtComposeTextView(
                text = recentSearchViewModel.getTitle(),
                fontSize = spDimensionResource(id = R.dimen.htl_text_size_extra_large),
                modifier = Modifier.padding(start = dimensionResource(id = R.dimen.margin_extra_small)).testTag("recent_search_title"),
                mmtFontStyle = latoBlack,
                color = colorResource(id = R.color.htl_grey),
                maxLines = 1
            )
        }
        Row(
            Modifier
                .fillMaxWidth()
                .padding(
                    top = dimensionResource(id = R.dimen.margin_medium)
                )
                .horizontalScroll(rememberScrollState()),
            horizontalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.margin_extra_small))
        ) {
            recentSearchViewModel.items.forEach {
                if (it is RecentSearchCardItemViewModel) {
                    RecentSearchItemLayout(it)
                }
                if (it is PrebookChatViewModel) {
                    PrebookChatLayout(model = it)
                }
            }

        }
    }
}

@Composable
fun RecentSearchItemLayout(recentSearchItem: RecentSearchCardItemViewModel) {
    Column(
        modifier = Modifier
            .mmtClickable {
                recentSearchItem.onClicked()
            }
            .height(dimensionResource(id = R.dimen.htl_recent_search_card_item_height))
            .widthIn(
                max = dimensionResource(id = R.dimen.htl_recent_search_card_item_max_width),
                min = dimensionResource(id = R.dimen.htl_recent_search_card_item_min_width)
            )
            .background(
                color = colorResource(id = R.color.white),
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.radius_small))
            )
            .border(
                width = dimensionResource(id = R.dimen.htl_divider_height),
                color = recentSearchItem.getBorderColorCompose(),
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.radius_small))
            )
            .padding(
                start = dimensionResource(id = R.dimen.margin_medium),
                end = dimensionResource(id = R.dimen.margin_medium),
                top = dimensionResource(id = R.dimen.margin_small_extra)
            )
            .testTag("recent_search_item")
    ) {
        MmtComposeTextView(
            text = recentSearchItem.searchType(),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
            modifier = Modifier.padding(bottom = dimensionResource(id = R.dimen.margin_tiny)).testTag("recent_search_type"),
            mmtFontStyle = latoBlack,
            color = recentSearchItem.getFunnelColorCompose(),
            maxLines = 1
        )
        MmtComposeTextView(
            text = recentSearchItem.displayName,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            modifier = Modifier.padding(bottom = dimensionResource(id = R.dimen.margin_tiny)).testTag("recent_search_display_name"),
            mmtFontStyle = latoBold,
            color = colorResource(id = R.color.htl_grey),
            maxLines = 1
        )
        MmtComposeTextView(
            text = recentSearchItem.guestInfo,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
            modifier = Modifier.padding(bottom = dimensionResource(id = R.dimen.margin_tiny)).testTag("recent_search_guest_info"),
            mmtFontStyle = latoRegular,
            color = colorResource(id = R.color.grey_hotel),
            maxLines = 1
        )
        MmtComposeTextView(
            text = recentSearchItem.date,
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
            mmtFontStyle = latoRegular,
            color = colorResource(id = R.color.htl_light_text_color),
            maxLines = 1,
            modifier = Modifier.testTag("recent_search_date")
        )
    }
}

