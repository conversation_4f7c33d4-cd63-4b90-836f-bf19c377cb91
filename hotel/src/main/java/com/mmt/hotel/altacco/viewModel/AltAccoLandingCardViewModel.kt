package com.mmt.hotel.altacco.viewModel

import androidx.databinding.ObservableField
import androidx.lifecycle.viewModelScope
import com.mmt.core.util.executeIfCast
import com.mmt.hotel.altacco.helper.AltAccoLandingResponseConverter
import com.mmt.hotel.altacco.model.ui.AltAccoChatData
import com.mmt.hotel.altacco.model.ui.OffersDataItemModel
import com.mmt.hotel.altacco.repository.AltAccoLandingRepositoryImp
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.extensions.convertTo
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.landingV3.dataModel.LandingResponseWrapper
import com.mmt.hotel.landingV3.event.LandingEvents
import com.mmt.hotel.landingV3.model.EmperiaEventData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.viewModel.adapter.RecentSearchCardViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AltAccoLandingCardViewModel @Inject constructor(
    private val repository: AltAccoLandingRepositoryImp,
    private val converter: AltAccoLandingResponseConverter,
) : HotelViewModel() {

    private var chatData: AltAccoChatData? = null
    var recentItems = listOf<AbstractRecyclerItem>()
    var searchRequest: SearchRequest? = null

    fun fetchRecentSearchItems(chatData: AltAccoChatData?): Flow<List<AbstractRecyclerItem>> {
        return repository.loadRecentSearch()
            .map {
                if (it.isEmpty() && chatData == null) {
                    emptyList()
                } else {
                    listOf(converter.convert(it, chatData, eventStream))
                }
            }.flowOn(Dispatchers.Default)
    }

    fun handleCollectionResponse(landingResponseWrapper: LandingResponseWrapper) {
        searchRequest = landingResponseWrapper.searchRequest
        val widgetSearchRequest = searchRequest ?: return
        viewModelScope.launch (Dispatchers.IO){
            updateEventStream(SHOW_COLLECTION_CARDS, converter.createCards(landingResponseWrapper.originalResponse, widgetSearchRequest, eventStream))
        }
    }

    fun handleEmperiaData(emperiaEventData: EmperiaEventData) {
        emperiaEventData.response?.let {
            chatData = converter.fetchChatData(it)
            chatData?.let { chatData ->
                updateRecentItemsChatData(chatData)
                HotelMigratorHelper.instance.setCustomerUnreadMessageData(chatData.chatData.convertTo())
            }
        }
    }

    private fun updateRecentItemsChatData(chatData: AltAccoChatData) {
        val recentSearchViewModel = recentItems.firstOrNull()
        recentSearchViewModel?.executeIfCast<RecentSearchCardViewModel> {
            updateChatData(chatData)
        } ?: kotlin.run {
            recentItems = listOf(converter.convert(emptyList(), chatData, eventStream))
            updateEventStream(LandingEvents.RECENT_SEARCH_ITEMS, recentItems)
        }
    }

    fun initializeRecentItemsFetch() {
        viewModelScope.launch {
            fetchRecentSearchItems(chatData).catch { it.printStackTrace() }.collect {
                recentItems = it
                updateEventStream(LandingEvents.RECENT_SEARCH_ITEMS, recentItems)
            }
        }
    }

    /**
     * currently used only in tracking, so added a default userSearchData
     */
    fun getUserSearchData(searchRequest: SearchRequest?): UserSearchData {
        return searchRequest?.userSearchData
            ?: UserSearchData(countryCode = HotelConstants.COUNTRY_CODE_INDIA, funnelSrc = HotelFunnel.HOMESTAY.funnelValue, id = null,
                occupancyData = OccupancyData(roomCount = 1, adultCount = 1)
            )
    }

    companion object {
        const val SHOW_COLLECTION_CARDS = "SHOW_COLLECTION_CARDS"
    }

}