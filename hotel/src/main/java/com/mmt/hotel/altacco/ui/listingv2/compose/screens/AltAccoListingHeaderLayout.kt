package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.listingv2.compose.AltAccoListingItemsMapper
import com.mmt.hotel.altacco.ui.listingv2.compose.components.DateGuestEditComposable
import com.mmt.hotel.altacco.ui.listingv2.compose.components.AltAccoFilterPillsUi
import com.mmt.hotel.altacco.ui.listingv2.compose.components.ImageCardsCorousal
import com.mmt.hotel.altacco.ui.listingv2.compose.components.ScrollableButtonsLayout
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.common.util.isHtlMultiCurrencyEnabled
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.widgets.MMTComposeImageView
import com.mmt.hotel.listingV2.event.HotelListingClickEvents
import com.mmt.hotel.listingV2.event.HotelListingHeaderEvent
import com.mmt.hotel.listingV2.helper.HotelListingConstants
import com.mmt.hotel.listingV2.model.HotelListingHeaderUiState
import com.mmt.hotel.listingV2.model.ui.HotelListingFilterUiState
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun AltAccoListingHeaderLayout(
    headerUiData: HotelListingHeaderUiState,
    filterScrollState: LazyListState,
    handleEvent: (String, Any?) -> Unit
) {
    val baseHeaderData = headerUiData.hotelHeaderUiData
    val altAccoExtraHeaderData = headerUiData.altAccoExtraHeaderUiData ?: return
    val isExpanded = altAccoExtraHeaderData.isExpanded
    val canExpand = altAccoExtraHeaderData.canExpand

    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val iconId = HotelListingConstants.getListingDrawable(altAccoExtraHeaderData.topIconType)
        TopNavBarLayout(
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_xHuge2)),
            iconId = iconId,
            iconType = altAccoExtraHeaderData.topIconType,
            title = baseHeaderData.locationText,
            dateText = baseHeaderData.dateGuestText,
            isExpanded = isExpanded && canExpand,
            handleEvent = handleEvent,
            selectedCurrency = headerUiData.hotelHeaderUiData.selectedCurrency
        )

        AnimatedVisibility(
            visible = isExpanded && canExpand,
            enter = expandVertically(animationSpec = tween(500)) + fadeIn(animationSpec = tween(500)),
            exit = shrinkVertically(animationSpec = tween(500)) + fadeOut(animationSpec = tween(500))
        ) {
            Column(modifier = Modifier
                    .width(IntrinsicSize.Max)
                    .padding(
                            horizontal = dimensionResource(
                                    id = R.dimen.margin_large
                            )
                    ), horizontalAlignment = Alignment.CenterHorizontally) {
                TitleText(
                        Modifier
                                .semantics { testTagsAsResourceId = true }
                                .testTag("tvLocationTitle")
                                .padding(
                                        top = dimensionResource(id = R.dimen.margin_xxLarge),
                                        start = dimensionResource(id = R.dimen.margin_small)
                                ),
                    locationTitle = baseHeaderData.locationText,
                    fontSize = spDimensionResource(id = R.dimen.htl_text_size_xHuge)
                )
                DescText(desc = altAccoExtraHeaderData.desc?:"", fontSize = spDimensionResource(id = R.dimen.htl_text_size_small))
                Spacer(
                    modifier = Modifier
                            .padding(top = dimensionResource(id = R.dimen.margin_small))
                            .height(dimensionResource(id = R.dimen.htl_divider_height))
                            .fillMaxWidth()
                            .background(color = colorResource(id = R.color.divider_color))
                )

                DateGuestEditComposable(
                    modifier = Modifier
                            .padding(top = dimensionResource(id = R.dimen.margin_small))
                            .mmtClickable {
                                handleEvent(HotelListingHeaderEvent.EDIT_CLICKED, null)
                            },
                    dateGuestText = baseHeaderData.dateGuestText,
                    dateFont = spDimensionResource(id = R.dimen.htl_text_size_medium),
                    mmtFontStyle = latoBold
                )
            }
        }

        val scrollState = rememberLazyListState()

        if(altAccoExtraHeaderData.cardSection?.cardItems != null && altAccoExtraHeaderData.cardSection.cardItems.isNotEmpty()) {
            AnimatedVisibility(
                isExpanded && canExpand,
                enter = expandVertically(tween(500)) + fadeIn(tween(500)),
                exit = shrinkVertically(tween(500)) + fadeOut(tween(500))
            ) {
                ImageCardsCorousal(
                    modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_xHuge2)),
                    altAccoExtraHeaderData.cardSection.cardItems,
                    scrollState,
                    handleEvent
                )
            }
            AnimatedVisibility(
                visible =!isExpanded || !canExpand,
                enter = expandVertically(tween(500)) + fadeIn(tween(500)),
                exit = shrinkVertically(tween(500)) + fadeOut(tween(500))
            ) {
                ScrollableButtonsLayout(
                    modifier = Modifier
                            .padding(top = dimensionResource(id = R.dimen.margin_large))
                            .fillMaxWidth(),
                    cardSection = altAccoExtraHeaderData.cardSection,
                    handleEvent = handleEvent
                )
            }
        }

        val filterPills = when(headerUiData.filterUiState) {
            is HotelListingFilterUiState.Success -> headerUiData.filterUiState.filterPills
            is HotelListingFilterUiState.Loading -> headerUiData.filterUiState.loadingPills
            is HotelListingFilterUiState.Error ->   listOf()
        }

        Column(modifier = Modifier
                .padding(top = dimensionResource(id = R.dimen.margin_small))
                .fillMaxWidth()
                .clip(
                        shape = if (canExpand && isExpanded && altAccoExtraHeaderData.cardSection?.cardItems.isNullOrEmpty()) {
                            RoundedCornerShape(
                                    topStart = dimensionResource(id = R.dimen.htl_empty_dimen),
                                    topEnd = dimensionResource(id = R.dimen.htl_empty_dimen)
                            )
                        } else {
                            RoundedCornerShape(
                                    topStart = dimensionResource(id = R.dimen.margin_medium),
                                    topEnd = dimensionResource(id = R.dimen.margin_medium)
                            )
                        }
                )
                .background(
                        color = colorResource(
                                id = if ((canExpand && isExpanded))
                                    R.color.htl_color_transparent
                                else
                                    R.color.white
                        )
                )
        ) {
            val topFade = Brush.verticalGradient(0f to Color.Transparent, 0.8f to Color.White)
            if (filterPills.isNotEmpty()) {
                Box(modifier = Modifier.background(brush = topFade)) {
                    AltAccoFilterPillsUi(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                top = if (canExpand && isExpanded && altAccoExtraHeaderData.cardSection?.cardItems.isNullOrEmpty())
                                    dimensionResource(id = R.dimen.margin_xHuge)
                                else
                                    dimensionResource(id = R.dimen.margin_medium),
                                bottom = if (canExpand && isExpanded) {
                                    if (headerUiData.stickyCard != null)
                                        dimensionResource(id = R.dimen.margin_small)
                                    else
                                        dimensionResource(id = R.dimen.htl_empty_dimen)
                                } else dimensionResource(id = R.dimen.margin_medium)
                            ),
                        filterPills = filterPills, handleEvent = handleEvent,
                        filterScrollState = filterScrollState
                    )
                }
            }
            headerUiData.stickyCard?.let {
                AltAccoListingItemsMapper(
                    item = it,
                    index = 0,
                    showBorder = false,
                    scrollBottomBoxMPersuasions = false,
                    handleEvent = handleEvent,
                    autoSwipeImageCardIndex = -1
                )
            }
        }
        if (!canExpand || !isExpanded) {
            Spacer(
                modifier = Modifier
                        .fillMaxWidth()
                        .height(dimensionResource(id = R.dimen.htl_divider_height))
                        .background(color = colorResource(id = R.color.htl_divider_grey))
            )
        }
    }
}

@Composable
private fun currencyIconImage(handleEvent: (String, Any?) -> Unit, selectedCurrency: String?){
    Row(modifier = Modifier
            .padding(
                    top = dimensionResource(id = R.dimen.margin_tiny),
                    end = dimensionResource(id = R.dimen.margin_large)
            )
            .clickable { handleEvent(HotelListingClickEvents.OPEN_CURRENCY_BOTTOMSHEET, null) }){
        MmtComposeTextView(selectedCurrency, fontSize = spDimensionResource(id = R.dimen.htl_text_size_small), color = colorResource(id = R.color.white), mmtFontStyle = latoBold)
        MMTComposeImageView(
            modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .width(dimensionResource(id = R.dimen.margin_large_extra))
                    .height(dimensionResource(id = R.dimen.margin_large_extra))
                    .padding(
                            dimensionResource(id = R.dimen.margin_extra_xtiny)
                    ),
            painter = painterResource(id = R.drawable.arrow_down_24),
            colorFilter = ColorFilter.tint(color = colorResource(id = R.color.white)),
            contentDescription = null
            )
    }
}

@Composable
private fun NavIconImage(@DrawableRes iconId: Int, iconType: HotelListingConstants.HotelListingHeaderIconType, handleEvent: (String, Any?) -> Unit) {
    MMTComposeImageView(
        painter = painterResource(id = iconId),
        contentDescription = null,
        modifier = Modifier
                .padding(start = dimensionResource(id = R.dimen.margin_large))
                .mmtClickable {
                    when (iconType) {
                        HotelListingConstants.HotelListingHeaderIconType.BACK -> handleEvent(
                                HotelListingHeaderEvent.BACK_PRESSED,
                                null
                        )

                        HotelListingConstants.HotelListingHeaderIconType.CROSS -> {
                            handleEvent(
                                    HotelListingHeaderEvent.CROSS_CLICKED,
                                    null
                            )
                        }
                    }
                }
    )
}

@Composable
private fun TopNavBarLayout(
    modifier: Modifier = Modifier,
    @DrawableRes iconId: Int,
    iconType: HotelListingConstants.HotelListingHeaderIconType,
    title: String,
    dateText: String,
    isExpanded: Boolean,
    handleEvent: (String, Any?) -> Unit,
    selectedCurrency: String?
) {
    Row(modifier = modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Absolute.SpaceBetween
    ) {
        NavIconImage(iconId = iconId, iconType = iconType, handleEvent = handleEvent)
        if(isExpanded) {
            Spacer(modifier = Modifier.weight(1f).fillMaxWidth())
        }
        AnimatedVisibility(
                modifier = Modifier.weight(1f).fillMaxWidth().align(Alignment.CenterVertically),
                visible = !isExpanded,
                enter = expandVertically(expandFrom = Alignment.Top, animationSpec = tween(500)) + fadeIn(animationSpec = tween(500)),
                exit = shrinkVertically(shrinkTowards = Alignment.Top, animationSpec = tween(500)) + fadeOut(animationSpec = tween(500))
        ) {
            Column(
                    modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = dimensionResource(id = R.dimen.margin_xHuge2), end = dimensionResource(id = R.dimen.margin_xHuge2)),
                    horizontalAlignment = Alignment.CenterHorizontally
            ) {
                TitleText(
                        locationTitle = title,
                        fontSize = spDimensionResource(id = R.dimen.htl_text_size_large)
                )
                DateGuestEditComposable(
                        modifier = Modifier
                                .padding(top = dimensionResource(id = R.dimen.margin_small))
                                .mmtClickable {
                                    handleEvent(HotelListingHeaderEvent.EDIT_CLICKED, null)
                                },
                        dateGuestText = dateText,
                        dateFont = spDimensionResource(id = R.dimen.htl_text_size_small),
                        mmtFontStyle = latoRegular
                )
            }
        }

        if(isHtlMultiCurrencyEnabled()) {
            // align at end of row
            currencyIconImage(
                    handleEvent,
                    selectedCurrency
            )
        }
    }
}

@Composable
private fun TitleText(modifier: Modifier = Modifier, locationTitle: String, fontSize: TextUnit) {
    MmtComposeTextView(
        text = locationTitle,
        fontSize = fontSize,
        modifier = modifier,
        textAlign = TextAlign.Center,
        maxLines = 1,
        color = colorResource(id = R.color.white),
        mmtFontStyle = latoBlack,
        style = TextStyle(fontWeight = FontWeight.Normal,fontSize = 34.sp,lineHeight = 36.sp,letterSpacing = 0.25.sp).copy(
            shadow = Shadow(
                color = colorResource(id = R.color.secondary_color),
                offset = Offset(2f, 4f),
                blurRadius = 2f
            )
        ),
    )
}

@Composable
private fun DescText(desc: String, fontSize: TextUnit) {
    MmtComposeTextView(
        text = desc,
        fontSize = fontSize,
        modifier = Modifier.padding(top = dimensionResource(id = R.dimen.margin_tiny)),
        textAlign = TextAlign.Center,
        color = colorResource(id = R.color.white),
        maxLines = 1,
        mmtFontStyle = latoRegular,
        style = TextStyle(fontWeight = FontWeight.Normal,fontSize = 34.sp,lineHeight = 36.sp,letterSpacing = 0.25.sp).copy(
            shadow = Shadow(
                color = colorResource(id = R.color.secondary_color),
                offset = Offset(2f, 4f),
                blurRadius = 2f
            )
        ),
    )
}

