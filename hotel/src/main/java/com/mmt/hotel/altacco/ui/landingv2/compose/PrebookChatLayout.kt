package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.landingV3.viewModel.adapter.PrebookChatViewModel
import com.mmt.hotel.widget.compose.MmtComposeTextView

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun PrebookChatLayout(model : PrebookChatViewModel) {
    Column(
        Modifier
            .semantics { testTagsAsResourceId = true}
            .mmtClickable { model.onClick() }
            .widthIn(min = dimensionResource(id = R.dimen.htl_recent_search_card_item_min_width))
            .height(dimensionResource(id = R.dimen.htl_recent_search_card_item_height))
            .background(
                color = if (model.getBadgeCount() != null)
                    colorResource(id = R.color.tint_blue_eaf5ff)
                else Color.White,
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_small))
            )
            .border(
                width = dimensionResource(id = R.dimen.htl_divider_height),
                color = colorResource(id = R.color.htl_color_008cff),
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_small))
            )
            .padding(
                start = dimensionResource(id = R.dimen.margin_medium),
                top = dimensionResource(id = R.dimen.margin_small_extra),
                end = dimensionResource(id = R.dimen.margin_medium),
                bottom = dimensionResource(id = R.dimen.margin_medium)
            )
            .testTag("prebook_chat_card")){
        Row(horizontalArrangement = Arrangement.SpaceBetween) {
            PrebookChatHeader(text = model.getHeader())
            model.data.chatData.lastMsgTimeStr?.let {
                PrebookChatTime(text = it)
            }
        }
        Row {
            Box {
                model.getIconUrlBottom()?.let {
                    PrebookChatIconBottom(imageUrl = it)
                }
                model.getIconUrlTop()?.let {
                    PrebookChatIconTop(topImageUrl = it, borderColor = R.color.htl_color_008cff)
                }
            }
            Column(Modifier.padding(start = dimensionResource(id = R.dimen.margin_extra_small), end = dimensionResource(id = R.dimen.margin_extra_small))) {
                MmtComposeTextView(
                    text = model.data.chatData.title ?: "",
                    fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
                    mmtFontStyle = latoBlack,
                    color = colorResource(id = R.color.htl_grey),
                    maxLines = 1,
                    modifier = Modifier.widthIn(max = dimensionResource(id = R.dimen.htl_chat_sub_title_max_width))
                )
                model.data.chatData.subtitle?.let {
                    MmtComposeTextView(
                        text = it,
                        fontSize = spDimensionResource(id = R.dimen.htl_text_size_tiny),
                        mmtFontStyle = latoRegular,
                        color = colorResource(id = R.color.textColor_gray),
                        maxLines = 2,
                        modifier = Modifier.widthIn(max = dimensionResource(id = R.dimen.htl_chat_sub_title_max_width)))
                }

            }
            model.getBadgeCount()?.let {
                PrebookChatBadgeCount(count = it)
            }
        }
    }
}

@Composable
fun PrebookChatHeader(text : String) {
    MmtComposeTextView(
        text = text,
        fontSize = spDimensionResource(id = R.dimen.detail_page_text_size_tiny),
        mmtFontStyle = latoBlack,
        color = colorResource(id = R.color.azure),
        maxLines = 1,
        modifier = Modifier
            .padding(bottom = dimensionResource(id = R.dimen.margin_extra_small),
                end = dimensionResource(id = R.dimen.margin_small_extra))
    )
}

@Composable
fun PrebookChatTime(text : String) {
    MmtComposeTextView(
        text = text,
        fontSize = spDimensionResource(id = R.dimen.detail_page_text_size_tiny),
        mmtFontStyle = latoBlack,
        color = colorResource(id = R.color.textColor_gray),
        maxLines = 1,
        modifier = Modifier
            .padding(bottom = dimensionResource(id = R.dimen.margin_tiny))
    )
}

@Composable
fun PrebookChatIconBottom(imageUrl: String) {
        LoadImage(
            modifier = Modifier.size(dimensionResource(id = R.dimen.image_dimen_xmedium)).clip(shape = RoundedCornerShape(50)).testTag("iv_chat_icon"),
            imageUrl = imageUrl, contentScale = ContentScale.FillBounds
        )
}

@Composable
fun PrebookChatIconTop(topImageUrl: String, borderColor: Int) {
    LoadImage(
        modifier = Modifier
            .padding(start = dimensionResource(id = R.dimen.margin_tiny))
            .size(dimensionResource(id = R.dimen.htl_top_icon_dimen))
            .clip(shape = RoundedCornerShape(50))
            .border(
                width = dimensionResource(id = R.dimen.margin_extra_xtiny),
                color = colorResource(id = borderColor),
                shape = RoundedCornerShape(50)
            )
            .testTag("iv_chat_icon_top"),
        imageUrl = topImageUrl,
        contentScale = ContentScale.FillBounds
    )
}

@Composable
fun PrebookChatBadgeCount(count : String) {
    MmtComposeTextView(
        text = count,
        fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
        mmtFontStyle = latoBlack,
        color = colorResource(id = R.color.white),
        maxLines = 1,
        modifier = Modifier
            .background(
                colorResource(id = R.color.red_eb2026),
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small_x))
            ).padding(
                top = dimensionResource(id = R.dimen.margin_extra_tiny),
                start = dimensionResource(id = R.dimen.margin_extra_small),
                end = dimensionResource(id = R.dimen.margin_extra_small),
                bottom = dimensionResource(id = R.dimen.margin_extra_tiny)
            ))
}
