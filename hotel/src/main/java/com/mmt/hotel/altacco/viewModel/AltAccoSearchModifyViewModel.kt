package com.mmt.hotel.altacco.viewModel

import android.widget.Toast
import androidx.databinding.ObservableInt
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.di.NamedConstants
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.util.HotelStringsProvider

import com.mmt.hotel.landingV3.helper.GuestCountChangeListener
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.repository.SearchModifyRepository
import com.mmt.hotel.landingV3.viewModel.LandingSearchModifyViewModel
import com.mmt.hotel.landingV3.viewModel.MAX_GUEST_COUNT
import com.mmt.hotel.thankyou.SPACE
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import javax.inject.Named

@HiltViewModel
class AltAccoSearchModifyViewModel @Inject constructor(
    @Named(NamedConstants.ALTACCO_SEARCH_MODIFY_REPO)
    repository: SearchModifyRepository,
    private val tracker: AltAccoTracker
) : LandingSearchModifyViewModel(
    repository,tracker) {

    private var isDataPopulated = false
    val adultChildContainerTextColor = ObservableInt(R.color.black)

    override fun populateFields(request: SearchRequest) {
        if(noOfAdults.get() == 0 && !isDataPopulated) {
            setPaxItemsColor(titleColor = R.color.color_008cff)
            isDataPopulated = !isDataPopulated
        }
    }

    fun setPaxItemsColor(titleColor: Int? = null) {
        titleColor?.let {
            adultChildContainerTextColor.set(it)
        }
    }

    override fun setRoomAndGuestText() {

        val roomsText = noOfRooms.get()?.let { rooms ->
            (HotelStringsProvider.getFormattedQuantityString(R.plurals.htl_bedrooms_count, rooms, rooms)) + CoreConstants.COMMA + SPACE
        } ?: CoreConstants.EMPTY_STRING

        if (noOfAdults.get() ==0){
            guestText.set(null)
        } else if (noOfChild.get() > 0) {
            guestText.set(roomsText + noOfAdults.get().toString() + SPACE + resources.getQuantityString(R.plurals.ADULT_TEXT, noOfAdults.get())
                    + CoreConstants.COMMA + SPACE + noOfChild.get().toString() + SPACE + resources.getQuantityString(R.plurals.htl_landing_child_subtext, noOfChild.get()))
        } else {
            guestText.set(roomsText + noOfAdults.get().toString() + SPACE + resources.getQuantityString(R.plurals.ADULT_TEXT, noOfAdults.get()))
        }
    }
    override fun getUserSearchData(): UserSearchData {
        return getSearchRequest()?.userSearchData
            ?: UserSearchData(id = null,
                countryCode = HotelConstants.COUNTRY_CODE_INDIA,
                funnelSrc = HotelFunnel.HOMESTAY.funnelValue,
                occupancyData = OccupancyData(roomCount = 1, adultCount = 1)
            )
    }

    fun setButtonText(areaContainerEnabled: Boolean, inputMandatory: Boolean?) {
        if(!areaContainerEnabled && inputMandatory != true) {
            buttonCtaText.set(ResourceProvider.instance.getString(R.string.htl_modify_hotel))
        } else {
            buttonCtaText.set(ResourceProvider.instance.getString(R.string.htl_search))
        }
    }

}