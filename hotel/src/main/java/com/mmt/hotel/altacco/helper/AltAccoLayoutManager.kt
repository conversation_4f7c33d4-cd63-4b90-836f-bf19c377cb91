package com.mmt.hotel.altacco.helper

import android.view.View
import androidx.databinding.ViewStubProxy
import com.mmt.hotel.R
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.common.ui.persuasion.view.InputDataValidity
import com.mmt.hotel.databinding.LayoutHotelSearchModifyAltAccoV2Binding
import com.mmt.hotel.landingV3.helper.SearchModifyLayoutManger

class AltAccoLayoutManager (viewStubProxy: ViewStubProxy,
                            viewModel: HotelViewModel?,)
    : SearchModifyLayoutManger<LayoutHotelSearchModifyAltAccoV2Binding>(
    viewStubProxy,
    viewModel,
    R.layout.layout_hotel_search_modify_alt_acco_v2) {

    override val areaContainer: View?
        get() = viewBinding?.areaContainer

    override fun checkAllFiledsFilled(): Bo<PERSON>an {
        if (viewBinding == null) return false
        val allFields = with(viewBinding!!) {
            listOf(areaContainer, checkInContainer,roomGuestContainer)
        }
        var allValid = true
        allFields.forEach {
            val isValid = (it as InputDataValidity).isDataFilled()
            if (!isValid) {
                allValid = false
            }
        }
        return allValid
    }
}