package com.mmt.hotel.altacco.ui.landingv2.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import com.mmt.hotel.R
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBold
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.landingV3.viewModel.adapter.CardItemViewModel
import com.mmt.hotel.widget.compose.MmtComposeTextView

@Composable
fun SquareCardItem(modifier: Modifier = Modifier, cardItem: CardItemViewModel) {
    Column(modifier = modifier.width(dimensionResource(id = R.dimen.aa_trending_card_width)).mmtClickable { cardItem.onCardClicked() }) {
        SquareBoxItemLayout(cardItem = cardItem)
        BottomShadow(modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.margin_small))
            .fillMaxWidth()
            .background(
                color = colorResource(id = R.color.color_b9b5af),
                shape = RoundedCornerShape(
                    bottomStart = dimensionResource(id = R.dimen.htl_radius_xsmall),
                    bottomEnd = dimensionResource(id = R.dimen.htl_radius_xsmall)
                )
            )
        )
        BottomShadow(modifier = Modifier
            .padding(horizontal = dimensionResource(id = R.dimen.margin_large))
            .fillMaxWidth()
            .background(
                color = colorResource(id = R.color.color_e7e2db),
                shape = RoundedCornerShape(
                    bottomStart = dimensionResource(id = R.dimen.htl_radius_xsmall),
                    bottomEnd = dimensionResource(id = R.dimen.htl_radius_xsmall)
                )
            )
        )
    }
}

@Composable
private fun BottomShadow(modifier: Modifier) {
    Spacer(modifier = modifier
        .fillMaxWidth()
        .height(dimensionResource(id = R.dimen.aa_shadow_height)))
}

@Composable
fun SquareBoxItemLayout(cardItem: CardItemViewModel) {
    Box(
        Modifier
            .width(dimensionResource(id = R.dimen.aa_trending_card_width))
            .height(dimensionResource(id = R.dimen.aa_suggest_for_card_heigth))
            .clip(
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.radius_small))
            )){
        LoadImage(modifier = Modifier.matchParentSize(), imageUrl = cardItem.card.imageUrl, contentScale = ContentScale.FillBounds)
        BottomViewWithShadow(modifier = Modifier.align(Alignment.BottomCenter), title = cardItem.card.description)
    }
}

@Composable
private fun BottomViewWithShadow(modifier: Modifier = Modifier, title: String?) {
    Box(modifier = modifier
        .height(dimensionResource(id = R.dimen.htl_action_card_image_dimen))
        .fillMaxWidth()
        .background(color = colorResource(id = R.color.black).copy(alpha = 0.75f))
        .padding(horizontal = dimensionResource(id = R.dimen.margin_small)), contentAlignment = Alignment.CenterStart) {
        MmtComposeTextView(
            text = title ?: "",
            color = colorResource(id = R.color.grey_bg),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_medium),
            maxLines = 2,
            mmtFontStyle = latoBold
        )
    }
}