package com.mmt.hotel.altacco.ui.listingv2.compose.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.BlurredEdgeTreatment
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.unit.dp
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.listingv2.compose.VideoPlayer
import com.mmt.hotel.common.util.compose.LoadAnimatedImage
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.compose.resources.setShimmerBackground
import com.mmt.hotel.listingV2.model.ui.HotelListingUiState

@Composable
fun AltAccoListingHeaderBg(isExpanded: Boolean,
                           mediaUrl: String?, mediaType: String?) {
    ImageWithBottomGradient(imageUrl = mediaUrl,isExpanded,mediaType)
}
@Composable
fun ImageWithBottomGradient(imageUrl: String?,isExpanded: Boolean, mediaType: String?) {
    val gradientColor2 = colorResource(id = R.color.grey_11)

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(dimensionResource(id = R.dimen.htl_listing_header_height))
            .drawWithCache {
                val gradient = Brush.verticalGradient(
                    colors =
                    if(isExpanded){
                    listOf(
                        Color.White,
                        Color.White.copy(alpha = 0.2f),
                        Color.White.copy(alpha = 0.3f),
                        gradientColor2.copy(alpha = 0.5f),
                        gradientColor2.copy(alpha = 0.7f),
                        gradientColor2.copy(alpha = 1.0f),
                        gradientColor2.copy(alpha = 1.0f),
                        gradientColor2.copy(alpha = 1.0f),
                        gradientColor2.copy(alpha = 1.0f),
                        gradientColor2.copy(alpha = 1.0f),
                        gradientColor2.copy(alpha = 1.0f))

                    }
        else
                        listOf(
                            Color.Transparent,
                            Color.White.copy(alpha = .3f),
                            Color.White.copy(alpha = 0.6f),
                            Color.White.copy(alpha = 1.0f),
                            Color.White.copy(alpha = 1.0f),
                            Color.White.copy(alpha = 1.0f))
                        ,
                    startY =  0f ,
                    endY =   if(isExpanded) size.height else size.height/2
                )
                onDrawWithContent {
                    drawContent()
                    drawRect(gradient, blendMode = BlendMode.SrcOver)
                }
            }
    ) {
        var showVideoLoading by remember {
            mutableStateOf(true)
        }
        when(mediaType) {
            "IMAGE" -> {
                LoadImage(
                    modifier = Modifier.fillMaxWidth()
                        .height(dimensionResource(id = R.dimen.htl_listing_header_height)),
                    placeHolderId = R.drawable.bgdefault_bg,
                    contentScale = ContentScale.Crop,
                    imageUrl = imageUrl
                )
            }
            "VIDEO" -> {
                VideoPlayer(url = imageUrl, modifier = Modifier.fillMaxWidth()
                    .height(dimensionResource(id = R.dimen.htl_listing_header_height))
                    .height(dimensionResource(id = R.dimen.htl_listing_header_height))
                    .fillMaxWidth(), onVideoStarted = {
                    showVideoLoading = false
                })
                if (showVideoLoading) {
                    Spacer(
                        modifier = Modifier
                            .fillMaxSize()
                            .setShimmerBackground(
                                shape = RoundedCornerShape(bottomEnd = dimensionResource(id = R.dimen.htl_empty_dimen)),
                                colors = listOf(
                                    colorResource(id = R.color.htl_button_disabled),
                                    colorResource(id = R.color.htl_emi_option_button_gradient_start_color)
                                )
                            )
                    )
                }
            }
            "GIF" -> {
                LoadAnimatedImage(
                    modifier = Modifier.fillMaxWidth()
                        .height(dimensionResource(id = R.dimen.htl_listing_header_height)),
                    url = imageUrl,
                    scale = ContentScale.Crop,
                    placeHolderId = R.drawable.bgdefault_bg
                )            }
            else -> {
                LoadImage(
                    modifier = Modifier.fillMaxWidth()
                        .height(dimensionResource(id = R.dimen.htl_listing_header_height)),
                    placeHolderId = R.drawable.bgdefault_bg,
                    contentScale = ContentScale.Crop,
                    imageUrl = imageUrl
                )
            }
        }
    }
}