package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.listingv2.compose.AltAccoListingItemsMapper
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.PropertyLoadingCardItem
import com.mmt.hotel.compose.base.ui.HotelsLazyColumn
import com.mmt.hotel.listingV2.event.HotelListingClickEvents
import com.mmt.hotel.listingV2.event.HotelListingHeaderEvent
import com.mmt.hotel.listingV2.model.HotelListingHeaderUiState
import com.mmt.hotel.listingV2.model.ui.HotelListingContentUiData
import com.mmt.hotel.listingV2.model.ui.HotelListingUiState
import kotlinx.coroutines.launch

@Composable
fun AltAccoListingSuccessView(
    uiState: State<HotelListingUiState>,
    handleEvent: (String, Any?) -> Unit
) {
    val propertyLoadingItemsCount = 3
    var headerUiData : HotelListingHeaderUiState? = null
    var contentUiData : HotelListingContentUiData? = null
    val listingItems : SnapshotStateList<AbstractRecyclerItem> = SnapshotStateList()
    val scrollState = rememberLazyListState()
    val filterScrollState = rememberLazyListState()

    when(uiState.value) {
        is HotelListingUiState.Success -> {
            headerUiData = (uiState.value as HotelListingUiState.Success).uiData.headerUiData
            contentUiData = (uiState.value as HotelListingUiState.Success).uiData.contentUiData
            listingItems.addAll(contentUiData.listingItems)
            LaunchedEffect(Unit) {
                if (headerUiData?.altAccoExtraHeaderUiData?.isExpanded == false)
                    scrollState.scrollToItem(scrollState.firstVisibleItemScrollOffset, 1)
                else
                    scrollState.scrollToItem(scrollState.firstVisibleItemScrollOffset)
            }
        }
        is HotelListingUiState.PropertyLoading -> {
            headerUiData = (uiState.value as HotelListingUiState.PropertyLoading).headerUiData
            repeat(propertyLoadingItemsCount) {
                listingItems.add(PropertyLoadingCardItem())
            }
            LaunchedEffect(Unit) {
                if (headerUiData.altAccoExtraHeaderUiData?.cardSection?.cardItems?.firstOrNull()?.isSelected == true)
                    scrollState.scrollToItem(0)
                else if (headerUiData.altAccoExtraHeaderUiData?.isExpanded != true)
                    scrollState.scrollToItem(0, 1)
                else
                    scrollState.scrollToItem(0)
                filterScrollState.scrollToItem(0)
            }
        }
        else -> {

        }
    }

    val headerData = headerUiData?.altAccoExtraHeaderUiData

    var prevLastVisibleIndex by remember { mutableIntStateOf(0) }

    var lastVisibleItemIndex by remember {mutableIntStateOf(0)}

    LaunchedEffect(scrollState) {

        snapshotFlow { scrollState.layoutInfo.visibleItemsInfo.isNotEmpty() && scrollState.layoutInfo.visibleItemsInfo.last().index == listingItems.lastIndex}
            .collect {
                lastVisibleItemIndex = listingItems.lastIndex
            }
    }

    if (prevLastVisibleIndex < lastVisibleItemIndex) {
        prevLastVisibleIndex = lastVisibleItemIndex
        handleEvent(HotelListingClickEvents.UPDATE_LAST_VISIBLE_ITEM_INDEX, lastVisibleItemIndex)
    }

    LaunchedEffect(scrollState) {
        snapshotFlow { scrollState.firstVisibleItemIndex == 0 && scrollState.firstVisibleItemScrollOffset in (0 until 1)}
            .collect {
                if (it)
                    handleEvent(HotelListingHeaderEvent.HEADER_SECTION_EXPANDED, true)
                else
                    handleEvent(HotelListingHeaderEvent.HEADER_SECTION_EXPANDED, false)
            }
    }

    LaunchedEffect(scrollState) {
        snapshotFlow {
            if (scrollState.layoutInfo.visibleItemsInfo.isNotEmpty())
                scrollState.layoutInfo.visibleItemsInfo.first().index to scrollState.layoutInfo.visibleItemsInfo.last().index
            else Pair(-1, -1)
        }.collect {
            handleEvent(HotelListingClickEvents.VISIBLE_RANGE_INDICES, it)
        }
    }

    val isLastItemIndexVisible = remember {
        derivedStateOf {
            scrollState.isScrollInProgress &&
                    scrollState.layoutInfo.visibleItemsInfo.last().index + 1 == scrollState.layoutInfo.totalItemsCount
        }
    }

    if (isLastItemIndexVisible.value) {
        handleEvent(HotelListingClickEvents.LISTING_PAGE_LAST_ITEM_VISIBLE, listingItems.lastIndex)
    }

    LaunchedEffect(Unit) {
        handleEvent(HotelListingClickEvents.LISTING_RENDERED_SUCCESSFULLY, null)
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {

        headerUiData?.let {
            CollectionListingHeaderLayout(headerUiData, filterScrollState, handleEvent)
//            AltAccoListingHeaderLayout(headerUiData, filterScrollState, handleEvent)
        }
        AltAccoListing(
            listingItems = listingItems,
            scrollState = scrollState,
            isScrollEnabled = headerData?.isExpanded == false,
            scrollBottomBoxMPersuasions = contentUiData?.shouldScrollBlackPlusLongstayPersuasion == true,
            autoSwipeImageCardIndex = contentUiData?.enableAutoSwipeOfImagesCardIndex,
            handleEvent
        )
    }
}

@Composable
private fun AltAccoListing(
    listingItems: SnapshotStateList<AbstractRecyclerItem>,
    scrollState: LazyListState,
    isScrollEnabled: Boolean,
    scrollBottomBoxMPersuasions: Boolean,
    autoSwipeImageCardIndex: Int?,
    handleEvent: (String, Any?) -> Unit
) {
    val scope = rememberCoroutineScope()
    HotelsLazyColumn(
        list = listingItems,
        state = scrollState,
        modifier = Modifier
            .fillMaxWidth()
            .background(color = colorResource(id = R.color.grey_11))
            .pointerInput(Unit) {
                detectVerticalDragGestures { change, dragAmount ->
                    if (scrollState.firstVisibleItemIndex == 0) {
                        if (dragAmount < 0) {
                            handleEvent(HotelListingHeaderEvent.HEADER_SECTION_EXPANDED, false)
                            scope.launch {
                                scrollState.scrollToItem(0, 1)
                            }
                        } else
                            handleEvent(HotelListingHeaderEvent.HEADER_SECTION_EXPANDED, true)
                    }
                }
            },
        userScrollEnabled = isScrollEnabled,
        verticalArrangement = Arrangement.spacedBy(dimensionResource(id = R.dimen.margin_large))
    ) { item, index ->
        val marginBottom = if (index == listingItems.lastIndex) R.dimen.margin_large else R.dimen.htl_empty_dimen
        val marginTop = if (index == 0) R.dimen.margin_large else R.dimen.htl_empty_dimen
        AltAccoListingItemsMapper(
            modifier = Modifier
                .padding(
                    top = dimensionResource(id = marginTop),
                    bottom = dimensionResource(id = marginBottom),
                    start = dimensionResource(id = R.dimen.margin_large),
                    end = dimensionResource(id = R.dimen.margin_large)
                ),
            item = item,
            index = index,
            showBorder = true,
            scrollBottomBoxMPersuasions = scrollBottomBoxMPersuasions,
            autoSwipeImageCardIndex = autoSwipeImageCardIndex ?: -1,
            handleEvent = handleEvent
        )
    }
}
