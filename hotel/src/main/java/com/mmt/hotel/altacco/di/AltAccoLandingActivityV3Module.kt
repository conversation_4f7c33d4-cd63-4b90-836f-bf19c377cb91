package com.mmt.hotel.altacco.di

import com.mmt.hotel.altacco.repository.AltAccoLandingRepositoryImp
import com.mmt.hotel.altacco.tracking.AltAccoTracker
import com.mmt.hotel.altacco.viewModel.AltAccoLandingViewModel
import com.mmt.hotel.altacco.viewModel.AltAccoLandingViewModelV3
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmt.hotel.base.viewModel.ViewModelKey
import com.mmt.hotel.common.di.NamedConstants
import com.mmt.hotel.landingV3.helper.HomeStayLandingConfig
import com.mmt.hotel.landingV3.helper.HotelLandingConfig
import com.mmt.hotel.landingV3.helper.LandingResponseConverter
import com.mmt.hotel.landingV3.model.HotelLandingDataV3
import com.mmt.hotel.landingV3.repository.HotelLandingRepositoryImpl
import com.mmt.hotel.landingV3.repository.LandingRepository
import com.mmt.hotel.landingV3.tracking.HotelLandingTracker
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ActivityComponent
import dagger.multibindings.IntoMap
import javax.inject.Named

@Module
@InstallIn(ActivityComponent::class)
class AltAccoLandingActivityV3Module {
    @IntoMap
    @ViewModelKey(AltAccoLandingViewModel::class)
    @Provides
    fun provideViewModel(config: HotelLandingConfig): HotelViewModel {
        return AltAccoLandingViewModel(config)
    }

    @IntoMap
    @ViewModelKey(AltAccoLandingViewModelV3::class)
    @Provides
    fun provideViewModelV3(config: HotelLandingConfig, bundleData: HotelLandingDataV3,
                           repository: LandingRepository,
                           converter: LandingResponseConverter,
                           hotelLandingTracker: HotelLandingTracker,
                           altAccoTracker: AltAccoTracker,
                           altAccoRepo: AltAccoLandingRepositoryImp,
                           repositoryImpl: HotelLandingRepositoryImpl
    ): HotelViewModel {
        return AltAccoLandingViewModelV3(config, bundleData, repository, converter, hotelLandingTracker,altAccoTracker,altAccoRepo, repositoryImpl)
    }

    @Named(NamedConstants.HOMESTAY_LANDING_CONFIG)
    @Provides
    fun provideConfig(): HotelLandingConfig {
        return HomeStayLandingConfig()
    }
}