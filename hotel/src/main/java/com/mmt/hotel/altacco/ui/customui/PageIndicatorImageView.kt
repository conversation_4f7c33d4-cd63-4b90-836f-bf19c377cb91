package com.mmt.hotel.altacco.ui.customui

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.mmt.hotel.R

/**
 * Created by <PERSON><PERSON><PERSON> on 08/07/21.
 */
class PageIndicatorImageView @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private val selectAnimator = ValueAnimator.ofFloat(context.resources.getDimension(R.dimen.aa_unselected_indicator_size),
            resources.getDimension(R.dimen.aa_selected_indicator_size))

    private val unSelectAnimator = ValueAnimator.ofFloat(resources.getDimension(R.dimen.aa_selected_indicator_size),
            resources.getDimension(R.dimen.aa_unselected_indicator_size))

    private var selectedIndicator: Drawable? = null
    private var unselectedIndicator: Drawable? = null

    init {
        val typeArray = getContext().obtainStyledAttributes(attrs, R.styleable.PageIndicatorImageView, 0, defStyleAttr)
        selectedIndicator = typeArray.getDrawable(R.styleable.PageIndicatorImageView_selectedIndicator)
        unselectedIndicator = typeArray.getDrawable(R.styleable.PageIndicatorImageView_unSelectedIndicator)
        typeArray.recycle()
        setImageDrawable(unselectedIndicator)
    }

    fun setSelectedIndicator(drawable: Drawable?){
        selectedIndicator = drawable
    }

    fun setUnSelectedIndicator(drawable: Drawable?){
        unselectedIndicator = drawable
        setImageDrawable(unselectedIndicator)
    }

    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)

        setImageDrawable(if (selected) selectedIndicator else unselectedIndicator)

        val animator = if (selected) selectAnimator else unSelectAnimator
        animator.addUpdateListener {
            layoutParams = layoutParams.apply {
                height = (it.animatedValue as Float).toInt()
                width = (it.animatedValue as Float).toInt()
            }
        }

        animator.start()

    }

}