package com.mmt.hotel.altacco.ui.listingv2.compose

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import com.mmt.hotel.R
import com.mmt.hotel.altacco.ui.listingv2.compose.components.HeaderCard
import com.mmt.hotel.altacco.ui.listingv2.compose.screens.AltAccoListingPropertyLoadingCard
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.PropertyLoadingCardItem
import com.mmt.hotel.listingV2.adapter.ListingAdapterTypes
import com.mmt.hotel.listingV2.viewModel.adapter.hotel.ListingSectionViewModel
import com.mmt.hotel.listingV3.ui.itemsMapper.HotelListingItemsMapper

/**
 * This list contains cards which already has border and no need add another border
 */
val CARDS_WITH_BORDER = listOf<Int>(
    ListingAdapterTypes.COLLECTIONS_FILTER_CARD
)
@Composable
fun AltAccoListingItemsMapper(
    modifier: Modifier = Modifier,
    item: AbstractRecyclerItem,
    index: Int,
    showBorder: Boolean,
    scrollBottomBoxMPersuasions: Boolean,
    autoSwipeImageCardIndex: Int,
    handleEvent: (String, Any?) -> Unit
) {
    when {
//        item.getItemType() == ListingAdapterTypes.HOTEL_SOLD_OUT-> {
//            (item as? ListingHotelViewModel?)?.let {
//                HotelSoldOutCard(modifier, it, true)
//            }
//        }
        item is ListingSectionViewModel -> HeaderCard(modifier, item)
        item is PropertyLoadingCardItem -> AltAccoListingPropertyLoadingCard(modifier)
        else -> {
            HotelListingItemsMapper(
                modifier = modifier,
                item = item,
                index = index,
                showOldCards = false,
                showBorder = showBorder,
                scrollBottomBoxMPersuasions = scrollBottomBoxMPersuasions,
                handleEvent = handleEvent,
                autoSwipeImageCardIndex = autoSwipeImageCardIndex
            )
        }
    }
}

@Composable
fun ListingComposeCard(modifier: Modifier = Modifier, showBorder : Boolean, item: AbstractRecyclerItem, CardComposable :@Composable ()->Unit) {
    if (CARDS_WITH_BORDER.contains(item.getItemType()) || !showBorder) {
        CardComposable()
    } else {
        Box(
            modifier = modifier.border(
                width = dimensionResource(id = R.dimen.htl_stroke_width_extra_tiny),
                color = colorResource(id = R.color.grey_d8d8d8),
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.htl_radius_xxlarge))
            )
        ) {
            CardComposable()
        }
    }
}



