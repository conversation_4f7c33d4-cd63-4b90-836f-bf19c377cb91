package com.mmt.hotel.altacco.ui.listingv2.compose.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.unit.Dp
import com.mmt.hotel.R
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.common.constants.HotelConstants.HOTEL_LISTING_MAX_IMAGE_COUNT
import com.mmt.hotel.common.util.compose.LoadImage
import com.mmt.hotel.common.util.compose.latoBlack
import com.mmt.hotel.common.util.compose.latoRegular
import com.mmt.hotel.common.util.compose.spDimensionResource
import com.mmt.hotel.compose.resources.mmtClickable
import com.mmt.hotel.compose.widgets.MMTComposeImageView
import com.mmt.hotel.listingV2.dataModel.HotelRatingV2
import com.mmt.hotel.listingV2.event.ListingPersuasionPlaceHolder
import com.mmt.hotel.listingV2.model.response.hotels.TemplatePersuasion
import com.mmt.hotel.listingV2.viewModel.adapter.ListingHotelImageViewModel
import com.mmt.hotel.selectRoom.compose.PagerIndicator
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.hotel.widget.compose.persuasion.PersuasionUiBuilder
import com.mmt.hotel.wishlist.viewmodel.WishlistViewModel
import com.mmt.uikit.util.isNotNullAndEmpty
import kotlin.math.min

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun ListingPager(
    modifier: Modifier = Modifier,
    mediaList: List<AbstractRecyclerItem>? = null,
    persuasion : TemplatePersuasion? = null,
    @ListingPersuasionPlaceHolder placeHolderId: String,
    rating: HotelRatingV2?,
    wishListData: WishlistViewModel?,
    onImageIndexChanged : (Int) -> Unit
) {

    Box(modifier = modifier){
        Box(
            modifier = Modifier
                .padding(bottom = dimensionResource(id = R.dimen.margin_medium))
                .fillMaxWidth()
                .semantics { testTagsAsResourceId = true }
        ) {
            val size = min(HOTEL_LISTING_MAX_IMAGE_COUNT, (mediaList?.size ?: 0))
            val pagerState = rememberPagerState(pageCount = {
                size
            })
            LaunchedEffect(pagerState) {
                snapshotFlow { pagerState.currentPage }.collect { page ->
                    onImageIndexChanged(page)
                }
            }



            if (mediaList?.isNotNullAndEmpty() == true) {
                HorizontalPager(
                    state = pagerState,
                    pageSize = PageSize.Fill,
                    modifier = Modifier
                        .clip(
                            shape = RoundedCornerShape(
                                topStart = dimensionResource(id = R.dimen.margin_large),
                                topEnd = dimensionResource(id = R.dimen.margin_large),
                                bottomEnd = dimensionResource(id = R.dimen.htl_section_card_size)
                            )
                        )
                        .height(dimensionResource(id = R.dimen.htl_city_collection_tile_image_width))
                        .fillMaxWidth()
                        .testTag("listing_htl_media")
                ) { pos ->
                    when (val media = mediaList[pos]) {
                        is ListingHotelImageViewModel -> {
                            val defaultBg = R.drawable.bgdefault_bg
                            if (media.media.url.isNotNullAndEmpty()) {
                                LoadImage(
                                    modifier = Modifier.fillMaxSize(),
                                    imageUrl = media.media.url,
                                    contentScale = ContentScale.FillBounds,
                                    placeHolderId = defaultBg
                                )
                            } else {
                                LoadImage(
                                    modifier = Modifier.fillMaxSize(),
                                    resourceId = defaultBg,
                                    contentScale = ContentScale.FillBounds,
                                    placeHolderId = defaultBg
                                )
                            }
                        }
                    }
                }

                if(size > 1){
                    PagerIndicator(
                        Modifier
                            .padding(bottom = dimensionResource(id = R.dimen.margin_large))
                            .align(Alignment.BottomCenter),
                        Modifier
                            .background(
                                color = Color.Black.copy(alpha = 0.8f),
                                RoundedCornerShape(dimensionResource(id = R.dimen.htl_filter_item_placeholder_max_width))
                            )
                            .padding(
                                vertical = dimensionResource(id = R.dimen.margin_extra_small)
                            ),
                        R.color.white,
                        size,
                        pagerState
                    )
                }

            } else {
                LoadImage(
                    modifier = Modifier
                        .clip(
                            shape = RoundedCornerShape(
                                topStart = dimensionResource(id = R.dimen.margin_large),
                                topEnd = dimensionResource(id = R.dimen.margin_large),
                                bottomEnd = dimensionResource(id = R.dimen.htl_section_card_size)
                            )
                        )
                        .height(dimensionResource(id = R.dimen.htl_city_collection_tile_image_width))
                        .fillMaxWidth(),
                    resourceId = R.drawable.bgdefault_bg,
                    contentScale = ContentScale.FillBounds
                )
            }
            persuasion?.let {
                PersuasionUiBuilder(
                    persuasion = persuasion,
                    modifier = Modifier.padding(
                        top = dimensionResource(id = R.dimen.margin_medium)
                    ),
                    placeHolderId = placeHolderId
                )
            }
            HeartIconClickable(modifier = Modifier
                .testTag("wishlist_icon")
                .padding(dimensionResource(id = R.dimen.margin_medium))
                .align(Alignment.TopEnd), wishListData)
        }
        rating?.let {
            if (rating.rating.isNotBlank()) {
                RatingLabel(
                    modifier = Modifier
                        .align(alignment = Alignment.BottomStart)
                        .padding(
                            start = dimensionResource(id = R.dimen.margin_medium),
                            top = dimensionResource(id = R.dimen.margin_large)
                        )
                        .testTag("hotel_rating"),
                    rating = rating
                )
            }
        }
    }
}

@Composable
private fun HeartIconClickable(modifier: Modifier = Modifier, wishListData: WishlistViewModel?) {

    var iconSelectedState by rememberSaveable {mutableStateOf(wishListData?.isSelected?.get()?:false)}

    val iconResId = if (iconSelectedState) R.drawable.htl_selected_wishlist_dark_icon else R.drawable.htl_unselected_wishlist_dark_icon

    Image(
        painter = painterResource(id = iconResId),
        contentDescription = "",
        modifier.mmtClickable {
            iconSelectedState = !iconSelectedState
            wishListData?.onWishistIconClicked()
        }
    )
}

@Composable
fun CircleSmall(isSelected: Boolean, defaultCircleColor: Int) {
    val color: Color =
        if (isSelected) colorResource(id = R.color.htl_select_room_theme_color) else colorResource(
            id = defaultCircleColor
        )
    val size: Dp =
        if (isSelected) dimensionResource(id = R.dimen.htl_dot_indicator_selected_size) else dimensionResource(
            id = R.dimen.htl_dot_indicator_default_size
        )
    Spacer(
        modifier = Modifier
            .size(size = size)
            .background(
                color = color,
                RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny))
            )
    )
}

@Composable
private fun RatingLabel(modifier: Modifier = Modifier, rating : HotelRatingV2) {

    val bgColor = if (rating.isTaRating) R.color.htl_ta_rating_bg_new else R.color.htl_latest_rating
    val textColor = if (rating.isTaRating) R.color.black else R.color.white

    Row(modifier = modifier
        .background(
            color = colorResource(id = bgColor),//rating.ratingBgColor),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_tiny))
        )
        .padding(
            horizontal = dimensionResource(id = R.dimen.margin_extra_small),
            vertical = dimensionResource(id = R.dimen.margin_tiny),
        ),
        verticalAlignment = Alignment.CenterVertically
    ){
        if (rating.isTaRating) {
            MMTComposeImageView(
                painter = painterResource(id = R.drawable.ic_hol_tripad_logo),
                contentDescription = "",
                modifier = Modifier.padding(end = dimensionResource(id = R.dimen.margin_tiny))
            )
        }
        MmtComposeTextView(
            text = rating.rating,
            color = colorResource(id = textColor),
            fontSize = spDimensionResource(id = R.dimen.htl_text_size_small),
            lineHeight = spDimensionResource(id = R.dimen.text_line_height_large),
            mmtFontStyle = latoBlack
        )
        MmtComposeTextView(
            modifier = Modifier.padding(start = dimensionResource(id = R.dimen.htl_dot_indicator_default_size)),
            text = rating.totalReviewsCount,
            color = colorResource(id = textColor),
            fontSize = spDimensionResource(id = R.dimen.text_size_small),
            mmtFontStyle = latoRegular
        )
    }
}