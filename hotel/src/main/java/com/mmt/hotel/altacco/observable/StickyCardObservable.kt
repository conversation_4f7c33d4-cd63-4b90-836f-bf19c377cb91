package com.mmt.hotel.altacco.observable

import android.graphics.drawable.Drawable
import androidx.annotation.StringRes
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.constant.CoreConstants
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.altacco.event.LandingV2DataEvents
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.util.HotelDateUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.landingV3.model.request.SearchRequest
import java.lang.StringBuilder

/**
 * Created by <PERSON><PERSON><PERSON> A<PERSON>wal on 14,June,2021
 */
open class StickyCardObservable(private val eventStream: MutableLiveData<HotelEvent>) {
    val displayName = ObservableField<String>()
    val subTitle = ObservableField<String>()
    var searchRequest: SearchRequest? = null
    val displayNameColor = ObservableInt(R.color.color_4a4a4a)

    fun searchRequestUpdated(searchRequest: SearchRequest) {
        this.searchRequest = searchRequest
        searchRequest.userSearchData?.let {
            if (it.displayName.isNullOrEmpty()) {
                displayName.set(ResourceProvider.instance.getString(R.string.htl_landing_add_location))
                displayNameColor.set(R.color.red_eb2026)
            } else {
                displayName.set(it.displayName)
                displayNameColor.set(R.color.color_4a4a4a)
            }
            subTitle.set(createSubTitle(it))
        }
    }


    fun onSearchClicked() {
        searchRequest?.let {
            eventStream.value = HotelEvent(LandingV2DataEvents.STICKY_SEARCH_CLICKED, it)
        }
    }

    private fun createSubTitle(userSearchData: UserSearchData): String {
        val guestAndRoomCountPair = HotelUtil.getGuestsAndRoomCount(userSearchData)
        if(searchRequest?.userSearchData?.funnelSrc == HotelFunnel.HOTEL.funnelValue) { // add roomCount in case of hotel
            return StringBuilder()
                    .append(getFormattedDates(userSearchData))
                    .append(CoreConstants.PIPE_SEPARATOR_WITH_SPACE)
                    .appendRoomAndGuest(userSearchData.occupancyData,guestAndRoomCountPair)
                    .toString()
        } else {
            return StringBuilder()
                    .append(getFormattedDates(userSearchData))
                    .append(CoreConstants.PIPE_SEPARATOR_WITH_SPACE)
                    .append(getFormattedPax(userSearchData,guestAndRoomCountPair.first))
                    .toString()
        }
    }

    private fun StringBuilder.appendRoomAndGuest(occupancyData: OccupancyData, guestAndRoomCountPair: Pair<String, String>): StringBuilder {
        if (occupancyData.roomCount == 0) {
            append(getErrorText(ResourceProvider.instance.getString(R.string.htl_landing_add_guest)))
        } else {
            append(guestAndRoomCountPair.first)
                .append(CoreConstants.PIPE_SEPARATOR_WITH_SPACE)
                .append(guestAndRoomCountPair.second)
        }
        return this
    }

    private fun getFormattedPax(userSearchData: UserSearchData,paxString: String):String {
        if(userSearchData.occupancyData.adultCount == 0) {
            return getErrorText(ResourceProvider.instance.getString(R.string.htl_landing_add_guest))
        }
        return paxString
    }

    fun getFormattedDates(
        userSearchData: UserSearchData,
        @StringRes errorText: Int = R.string.htl_landing_add_date,
        errorColor: String = "#eb2026"
    ): String {
        return if (HotelUtil.getHotelFunnel(userSearchData.funnelSrc) == HotelFunnel.DAYUSE) {
            if (userSearchData.checkInDate.isEmpty()) {
                getErrorText(ResourceProvider.instance.getString(errorText), errorColor)
            } else {
                val checkInCal = HotelDateUtil.getCalendarDate(
                    userSearchData.checkInDate,
                    HotelConstants.SEARCH_DATE_FORMAT
                )
                HotelDateUtil.convertDateToString(
                    checkInCal.time,
                    HotelConstants.DETAILS_CHECKIN_DATE_FORMAT
                )
            }
        } else {
            if (userSearchData.checkInDate.isEmpty() || userSearchData.checkOutDate.isEmpty()) {
                getErrorText(ResourceProvider.instance.getString(errorText), errorColor)
            } else {
                HotelUtil.getFormattedStayDates(
                    userSearchData,
                    HotelConstants.DETAILS_CHECKIN_DATE_FORMAT,
                    R.string.htl_room_date_format
                )
            }
        }
    }

    fun getErrorText(text:String,color: String = "#eb2026"):String {
        return StringBuilder()
            .append("<font color='$color'>")
            .append(text)
            .append("</font>")
            .toString()
    }

    fun buttonText():String {
        return ResourceProvider.instance.getString(R.string.htl_search)
    }

}