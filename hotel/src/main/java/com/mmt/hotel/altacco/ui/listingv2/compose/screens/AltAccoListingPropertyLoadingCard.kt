package com.mmt.hotel.altacco.ui.listingv2.compose.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import com.mmt.hotel.R
import com.mmt.hotel.compose.resources.setShimmerBackground

@Preview
@Composable
fun AltAccoListingPropertyLoadingCard(modifier :Modifier = Modifier) {
    Column(modifier = modifier
        .fillMaxWidth()
        .clip(shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_large)))
        .background(
            color = colorResource(id = R.color.white)
        )
        .border(
            width = dimensionResource(id = R.dimen.htl_divider_height),
            color = colorResource(id = R.color.htl_grey_d8d8d8),
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_large))
        )
        .padding(bottom = dimensionResource(id = R.dimen.margin_medium))
    ) {
        PagerLoadingShimmer()
        HotelCardLoadingItemShimmer(
            height = dimensionResource(id = R.dimen.text_line_height_xLarge),
            width = dimensionResource(id = R.dimen.my_safety_horizontal_banner_width)
        )
        HotelCardLoadingItemShimmer(
            height = dimensionResource(id = R.dimen.text_line_height_large),
            width = dimensionResource(id = R.dimen.htl_night_count_container_width)
        )
        HotelCardLoadingItemShimmer(
            height = dimensionResource(id = R.dimen.text_line_height_large),
            width = dimensionResource(id = R.dimen.htl_review_sorter_dropdown_width)
        )
        HotelCardLoadingItemShimmer(
            height = dimensionResource(id = R.dimen.htl_luxe_hotel_icon_height),
            width = dimensionResource(id = R.dimen.htl_detail_video_height)
        )
        HotelCardLoadingItemShimmer(
            height = dimensionResource(id = R.dimen.margin_medium_extra),
            width = dimensionResource(id = R.dimen.htl_loading_card_shimmer_item_large)
        )
    }
}

@Composable
private fun HotelCardLoadingItemShimmer(height: Dp, width : Dp) {
    Spacer(modifier = Modifier
        .padding(
            top = dimensionResource(id = R.dimen.margin_tiny),
            start = dimensionResource(id = R.dimen.margin_medium),
        )
        .height(height)
        .width(width)
        .setShimmerBackground(
            shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_small)),
            colors = listOf(
                colorResource(id = R.color.htl_shimmer_bg_color),
                colorResource(id = R.color.htl_shimmer_anim_color)
            )
        )
    )
}

@Composable
private fun PagerLoadingShimmer() {
    Box(
        modifier = Modifier.fillMaxWidth()
    ) {
        Spacer(modifier = Modifier
            .padding(bottom = dimensionResource(id = R.dimen.margin_medium))
            .height(dimensionResource(id = R.dimen.my_safety_vertical_banner_width))
            .fillMaxWidth()
            .setShimmerBackground(
                shape = RoundedCornerShape(bottomEnd = dimensionResource(id = R.dimen.htl_section_card_size)),
                colors = listOf(
                    colorResource(id = R.color.htl_shimmer_bg_color),
                    colorResource(id = R.color.htl_shimmer_anim_color)
                )
            )
        )
        Spacer(modifier = Modifier
            .padding(start = dimensionResource(id = R.dimen.margin_medium))
            .height(dimensionResource(id = R.dimen.margin_huge))
            .width(dimensionResource(id = R.dimen.detail_luxury_card_width))
            .setShimmerBackground(
                shape = RoundedCornerShape(dimensionResource(id = R.dimen.margin_medium)),
                colors = listOf(
                    colorResource(id = R.color.htl_shimmer_bg_color),
                    colorResource(id = R.color.htl_shimmer_anim_color)
                )
            )
            .align(Alignment.BottomStart)
        )
    }
}