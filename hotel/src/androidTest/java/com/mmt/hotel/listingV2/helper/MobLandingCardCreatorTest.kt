package com.mmt.hotel.listingV2.helper

import androidx.core.util.isEmpty
import androidx.lifecycle.MutableLiveData
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.user.auth.LoginUtil
import com.mmt.hotel.app.convertTo
import com.mmt.hotel.common.model.response.persuasionCards.*
import com.mmt.hotel.common.util.isWishlistEnabled
import com.mmt.hotel.filterV2.model.response.FilterV2
import com.mmt.hotel.listingV2.dataModel.MobLandingCards
import com.mmt.hotel.listingV2.model.response.moblanding.HotelListPersonalizationResponseV2
import com.mmt.hotel.listingV2.model.response.moblanding.HotelListingMobLandingResponse
import io.mockk.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

internal class MobLandingCardCreatorTest {

    private lateinit var cardCreator: MobLandingCardCreator

    @BeforeEach
    fun setUp() {
        mockkStatic("com.mmt.hotel.common.util.ExperimentUtils")
        mockkObject(LoginUtils)
        every { LoginUtils.getPreferredRegion().isGlobalEntity() } returns false
        mockkObject(LoginUtil)
        every { LoginUtils.isLoggedIn } returns true
        every { LoginUtil.getTotalWalletAmount() } returns 0.0
        every { isWishlistEnabled() } returns true
        cardCreator = MobLandingCardCreator(LocalCardCreator(mockk(relaxed = true)))
    }

    @AfterEach
    fun tearDown() {
        unmockkAll()
    }

    @Test
    @DisplayName("should check all passed cards are create ")
    fun checkPopUpCards() {
        val mobLandingResponse = mobLandingCardsMockResponse().response
        val cardsData = callProductionCode(mobLandingResponse?.personalizationResponseV2)
        callProductionCode(mobLandingResponse?.personalizationResponseV2)
        assertEquals("htl_bottom_sheet_2", cardsData.popUpCard?.templateId)
        assertEquals(5, cardsData.listingCards.size())
    }

    @Test
    @DisplayName("should not create filter flex cards if filter data is missing")
    fun testCardsNotCreated(){
        val cardData = CardDataV2(createEmptyCardData().copy(index = 0, templateId = "filter_flex_2"),1)
        val cardData2 = CardDataV2(createEmptyCardData().copy(index = 0, templateId = "filter_flex_1"),1)
        val response = HotelListPersonalizationResponseV2(cards = listOf(cardData,cardData2),"",0)
        val cards = callProductionCode(response)
        assertTrue(cards.listingCards.isEmpty())
    }

    @Test
    @DisplayName("should  create filter flex cards if filter data is present")
    fun testFlexCardsAreCreated(){
        val cardPayloadV2 = createEmptyCardPayLoad().copy(contextualFilterData = listOf(
            ContextualFilterDataV2(cardFilters = CardFiltersV2(mapOf("" to listOf(FilterV2(filterValue = "filterValue", filterGroup = "filterGroup")))))))
        val cardData = CardDataV2(createEmptyCardData().copy(templateId = "filter_flex_2", cardPayload = cardPayloadV2),1)
        val cardData2 = CardDataV2(createEmptyCardData().copy(templateId = "filter_flex_1", cardPayload = cardPayloadV2),2)
        val response = HotelListPersonalizationResponseV2(cards = listOf(cardData,cardData2),"",0)
        val cards = callProductionCode(response)
        assertEquals(2,cards.listingCards.size())
    }

    @Test
    @DisplayName("should create card based on promo card")
    fun testPromoCards(){
        val cardPayloadV2 = createEmptyCardPayLoad().copy(altAccoCardData = listOf(mockk(relaxed = true)))
        val cardData = CardDataV2(createEmptyCardData().copy(templateId = "altacco_promo", cardPayload = cardPayloadV2),2)
        val response = HotelListPersonalizationResponseV2(cards = listOf(cardData),"",0)
        val cards = callProductionCode(response)
        assertEquals(1,cards.listingCards.size())
    }

    private fun callProductionCode(personalizationResponseV2: HotelListPersonalizationResponseV2?): MobLandingCards {
       return cardCreator.convert(
           personalizationResponseV2,
           mockk(relaxed = true),
           null,
           MutableLiveData(),
           "INR"
       )
    }

    private fun createEmptyCardData(): CardInfo{
        return CardInfo(0,null,null,null,null,null,null,null,
            null,null,null,null,null,null,null,null,
            null,null,null,null,null,null,null,null, null, null, null, null)
    }

    private fun createEmptyCardPayLoad(): CardPayloadV2 {
        return CardPayloadV2(null,null,null,null,
            null,null,null,null,null,
            null,null,null,null,null,
            null,null,null,null, null,null, null, null)
    }

    private val testOneMockData =
        "{\"response\":{\"listPersonalizationResponse\":{\"cardData\":[{\"sequence\":5,\"cardInfo\":{\"index\":1,\"subType\":\"BUSINESS\",\"id\":\"POLARIS\",\"titleText\":\"Popular Areas to Stay\",\"templateId\":\"POLARIS_WITHOUT_IMAGE\"}},{\"sequence\":6,\"cardInfo\":{\"index\":1,\"subType\":\"BUSINESS\",\"id\":\"POLARIS\",\"titleText\":\"Popular Areas to Stay\",\"templateId\":\"htl_bottom_sheet\"}},{\"sequence\":7,\"cardInfo\":{\"index\":1,\"subType\":\"BUSINESS\",\"id\":\"POLARIS\",\"titleText\":\"Popular Areas to Stay\",\"templateId\":\"htl_bottom_sheet_2\"}},{\"sequence\":8,\"cardInfo\":{\"index\":1,\"subType\":\"BUSINESS\",\"id\":\"POLARIS\",\"titleText\":\"Popular Areas to Stay\",\"templateId\":\"inline_info_banner\"}},{\"sequence\":9,\"cardInfo\":{\"index\":1,\"subType\":\"BUSINESS\",\"id\":\"POLARIS\",\"titleText\":\"Popular Areas to Stay\",\"templateId\":\"collections\"}},{\"sequence\":15,\"cardInfo\":{\"index\":1,\"subType\":\"BUSINESS\",\"id\":\"POLARIS\",\"titleText\":\"Popular Areas to Stay\",\"templateId\":\"package_location\"}},{\"sequence\":16,\"cardInfo\":{\"index\":1,\"subType\":\"BUSINESS\",\"id\":\"POLARIS\",\"titleText\":\"Popular Areas to Stay\",\"templateId\":\"nearby_getaways_popular\"}}],\"experimentId\":1105,\"trackText\":\"BI_AALONGSTAY_S|POLARIS_BUSINESS_S|BLACK_BLACKCARD_S|BLACK_BLACKBOTTOM_S|\"},\"completedRequests\":[\"HOTEL_META\",\"SUGGESTED_FILTERS\",\"PERSONALIZATION\",\"MATCHMAKER\"],\"currentTimeStamp\":1638524724359},\"correlationKey\":\"e1f294be-2769-4dce-90fe-426baeae9223\"}"

    private fun mobLandingCardsMockResponse(): HotelListingMobLandingResponse {
        return testOneMockData.convertTo()
    }
}