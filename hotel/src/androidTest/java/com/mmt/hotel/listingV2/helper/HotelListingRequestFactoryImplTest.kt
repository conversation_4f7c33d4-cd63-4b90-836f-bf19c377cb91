package com.mmt.hotel.listingV2.helper

import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.util.CoreUtil
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.helper.HotelCommonRequestHelper
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.deeplink.constants.HotelDeepLinkKeys
import com.mmt.hotel.listingV2.dataModel.EntrySearchData
import com.mmt.hotel.listingV2.dataModel.ListingData
import com.mmt.hotel.listingV2.wrapper.HotelListingPrefWrapper
import com.mmt.hotel.mock.MockData
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.*

/**
 * Created by Gurtek Singh on 28/09/20.
 */
class HotelListingRequestFactoryImplTest {

    private var factory: HotelListingRequestFactory? = null
    @MockK(relaxed = true) lateinit var pref: HotelListingPrefWrapper

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        mockkObject(CoreUtil)
        mockkObject(HotelSharedPrefUtil.instance)
        mockkStatic(HotelUtil::class)
        every { CoreUtil.getAppVersionName() } returns EMPTY_STRING
        every { HotelUtil.getExperimetsForApiRequest(any()) }returns EMPTY_STRING
        every { HotelSharedPrefUtil.instance.fetchImgSizeFactorFromPref(any()) } returns 1F
        val hotelListingRequestHelper = HotelListingRequestHelper(HotelCommonRequestHelper(), pref)
        factory = HotelListingRequestFactoryImpl(hotelListingRequestHelper)
    }

    @AfterEach
    fun tearDown() {
        factory = null
    }

    @Test
    @DisplayName("should create request with correct values")
    fun shouldCreateRequestWithCorrectaValues() {
        factory?.run {

            val request = mockRequestData()

            val newRequest = createListingRequest(request)
            with(newRequest) {
                /*Search Criteria*/
                Assertions.assertEquals("2020-08-01", searchCriteria.checkIn)
                Assertions.assertEquals("2020-08-02", searchCriteria.checkOut)
                Assertions.assertEquals("", searchCriteria.lastHotelId)
                Assertions.assertEquals(20, searchCriteria.limit)
            }

            val updatedRequest = createListingRequest(request.copy(lastFetchHotelId = "121"))

            Assertions.assertEquals("121", updatedRequest.searchCriteria.lastHotelId)
            Assertions.assertEquals(newRequest.featureFlags, updatedRequest.featureFlags)
            Assertions.assertEquals(newRequest.expData, updatedRequest.expData)
            Assertions.assertEquals(newRequest.imageDetails, updatedRequest.imageDetails)
            with(newRequest.searchCriteria) {
                Assertions.assertEquals(roomStayCandidates.size, 1)
                Assertions.assertEquals(2, roomStayCandidates.first().adultCount)
                Assertions.assertEquals(2, roomStayCandidates.first().childAges?.size)
                assertThat(roomStayCandidates.first().childAges, CoreMatchers.hasItems(4, 1))
            }

        }
    }

    @Test
    @DisplayName("should return value for mobLanding request if listing request not created first")
    fun requestCreateTest() {
        factory?.run {
            val request = mockRequestData()
            val mobLandingRequest = createMobLandingRequest(request)
            val listingRequest = createListingRequest(request)

            Assertions.assertEquals(mobLandingRequest.deviceDetails, listingRequest.deviceDetails)
            Assertions.assertEquals(mobLandingRequest.imageDetails, listingRequest.imageDetails)
            Assertions.assertEquals(mobLandingRequest.searchCriteria, listingRequest.searchCriteria)
        }
    }

    @Test
    @DisplayName("original request should stay same on update userSearchData")
    fun requestUpdateTest() {
      factory?.run {
          val request = mockRequestData()
          request.searchData.userSearchData = mockUserSearchData().copy(locationType = "region")
          Assertions.assertTrue(request.entrySearchData.locationType != request.searchData.userSearchData.locationType)
      }
    }

    @Test
    @DisplayName("should bind correct parameter with webUrl")
    fun testwebUrlAppendTest(){
        factory?.run {
            val request = mockRequestData()
                val url = appendParamsToWebUrl("www.testurl.com",request)
            Assertions.assertTrue(url.contains("${HotelDeepLinkKeys.SEARCH_TEXT}=Dubai"))
        }
    }

    private fun mockRequestData(): ListingData {
    val data =  SearchRequestToListingDataV2Converter.searchRequestToListingSearchDataV2( MockData.searchRequest)!!
        return ListingData(
            data.copy(roomStayCandidate = mockRoomStayCandidates()),
            EntrySearchData(data.userSearchData)
        )
    }

    private fun mockRoomStayCandidates(): List<RoomStayCandidatesV2> {
        return listOf(RoomStayCandidatesV2(2, listOf(1, 4), null))
    }

    private fun mockUserSearchData(): UserSearchData {
        val data = OccupancyData(1, 2, emptyList())
        return UserSearchData(
            EMPTY_STRING,
            HotelFunnel.HOTEL.funnelValue,
            EMPTY_STRING,
            occupancyData = data,
            position = 0,
            travellerType = 0,
            checkOutDate = "08022020",
            checkInDate = "08022020",
            countryCode = "IN",
            selectedCurrency = "INR"
        )
    }
}