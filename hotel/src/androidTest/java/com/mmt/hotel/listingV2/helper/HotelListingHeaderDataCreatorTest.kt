package com.mmt.hotel.listingV2.helper

import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.R
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelsSearchRequestType
import com.mmt.hotel.listingV2.wrapper.HotelListingPrefWrapper
import com.mmt.hotel.mock.MockData
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*

/**
 * Created by <PERSON><PERSON><PERSON> on 31/08/20.
 */

class HotelListingHeaderDataCreatorTest {

    @MockK lateinit var pref: HotelListingPrefWrapper
    private var provider: ResourceProvider? = null

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        every { pref.isCorporateUser() } returns false
        provider = ResourceProvider.instance
    }

    @AfterEach
    fun tearDown() {

        provider = null
        unmockkAll()
    }

    @Test
    @DisplayName("should return correct subTitle")
    fun shouldReturnCorrectSubTitle() {

        val dataCreator = HotelListingHeaderDataCreator(pref, B2CHotelListingConfig())
        val searchRequest = MockData.searchRequest
         searchRequest.userSearchData?.apply {
            checkInDate = "09012020"
            checkOutDate = "09022020"
            locationName = "Dubai"
            funnelSrc = HotelFunnel.HOMESTAY.funnelValue
            searchType = HotelsSearchRequestType.CITY_SEARCH
        }

        val adults = searchRequest.userSearchData?.occupancyData?.adultCount ?:0
        val expectedAdultCount = provider?.getQuantityString(R.plurals.htl_pax_count, adults, adults)
        val expectedSubTitle = provider?.getString(
            R.string.htl_search_criteria_alt_display_format,
                "01 Sep", "02 Sep", expectedAdultCount)
        val data = dataCreator.create(SearchRequestToListingDataV2Converter.searchRequestToListingSearchDataV2(searchRequest)!!)

        assertNotNull(data)
        data.run {
            assertEquals(R.string.htl_search_hostels_homes, title)
            assertEquals(expectedSubTitle, subTitle)
            assertEquals("Dubai", locationName)
            assertTrue(showMapIcon)
            assertEquals("Tue, 2020", data.checkInYear)
            assertEquals("Wed, 2020", data.checkOutYear)
            //Note:- Check in out are assert in subtitle.
        }

    }

    @Test
    @DisplayName("should return correct subTitle on change of date format and funnel")
    fun shouldReturnCorrectSubTitleOnChangeOfDateFormatAndFunnel() {

        val dataCreator = HotelListingHeaderDataCreator(pref, B2CHotelListingConfig())

        val searchRequest = MockData.searchRequest
        searchRequest.userSearchData?.apply {
            checkInDate = "061220"
            checkOutDate = "061320"
            locationName = "Dubai"
            funnelSrc = HotelFunnel.HOTEL.funnelValue
            searchType = HotelsSearchRequestType.CITY_SEARCH
        }

        val rooms = searchRequest.userSearchData?.occupancyData?.roomCount ?:0
        val adults = searchRequest.userSearchData?.occupancyData?.adultCount ?:0
        val expectedAdultCount = provider?.getQuantityString(R.plurals.htl_pax_count, adults, adults)
        val expectedRoomCount = provider?.getQuantityString(R.plurals.HTL_ROOMS, rooms, rooms)
        val expectedSubTitle = provider?.getString(
            R.string.htl_search_criteria_display_format,
                "12 Jun", "13 Jun", expectedRoomCount, expectedAdultCount)
        val data = dataCreator.create(SearchRequestToListingDataV2Converter.searchRequestToListingSearchDataV2(searchRequest)!!)

        assertEquals(R.string.htl_hotel_villa_search, data.title)
        assertEquals(expectedSubTitle, data.subTitle)
    }

    //Note:- Just to confirm no error throw if there is no value
    @Test
    @DisplayName("should not throw any error if hotelRequest has no values")
    fun shouldNotThrowAnyErrorIfHotelRequestHasNoValues() {
        val dataCreator = HotelListingHeaderDataCreator(pref, B2CHotelListingConfig())
        val data = dataCreator.create(mockk(relaxed = true))

        assertEquals(R.string.htl_hotel_villa_search, data.title)
    }

}