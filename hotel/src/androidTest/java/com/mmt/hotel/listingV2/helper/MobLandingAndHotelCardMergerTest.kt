package com.mmt.hotel.listingV2.helper

import android.util.SparseArray
import androidx.lifecycle.MutableLiveData
import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.listingV2.adapter.ListingAdapterTypes
import com.mmt.hotel.listingV2.event.HotelCardType
import com.mmt.hotel.listingV2.event.SectionOrientation
import com.mmt.hotel.listingV2.model.response.hotels.Hotel
import com.mmt.hotel.listingV2.model.response.hotels.Location
import com.mmt.hotel.listingV2.model.response.hotels.LocationDetail
import com.mmt.hotel.listingV2.model.response.hotels.PersonalizedSection
import com.mmt.hotel.listingV2.viewModel.adapter.hotel.ListingSectionViewModel
import com.mmt.hotel.old.util.HotelPriceUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.jupiter.api.*

/**
 * Created by <PERSON><PERSON><PERSON> on 10/12/20.
 */
class MobLandingAndHotelCardMergerTest {

    private var merger: MobLandingAndHotelCardMerger? = null

    @BeforeEach
    fun setUp() {
        mockkStatic(HotelPriceUtil::class)
        every { HotelPriceUtil.getHtlPricingExperiment(any()) } returns "PRN"
        merger = MobLandingAndHotelCardMerger()
    }

    @AfterEach
    fun tearDown() {
        merger?.reset()
    }

    @Test
    @DisplayName("should insert mob landing card at correct position if only cards insert only in first section")
    fun mobLandingPositionTest() {
        val section = createMockSection().copy(name = "RECOMMENDED_HOTELS")
        val mobLandingCards = SparseArray<AbstractRecyclerItem>()
        mobLandingCards.append(1, createMockAbstractCard(ListingAdapterTypes.BLACK_CARD))
        mobLandingCards.append(5, createMockAbstractCard(ListingAdapterTypes.COLLECTION_BANNER))

        with(merger!!) {
            tryMergingMobLandingWithHotels(mobLandingCards)
            val cards = tryMergingHotelsWithMobLanding(listOf(ListingSectionViewModel(
                section,
                0,
                MutableLiveData(),
                currencyCode = "INR"
            )), null)
            Assertions.assertEquals(ListingAdapterTypes.BLACK_CARD, cards[0].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.HEADER, cards[1].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.COLLECTION_BANNER, cards[5].getItemType())
        }
    }

    @Test
    @DisplayName("should insert mob landing card if in some section card insert false")
    fun testMoblandingCardInsert(){
        val section = createMockSection()
        val mockStream = MutableLiveData<HotelEvent>()
        val currencyCode = "INR"
        val sectionViewModels = listOf(
            ListingSectionViewModel(
                section.copy(
                    name = "Recently View",
                    cardInsertionAllowed = false
                ), 0, mockStream, currencyCode = "INR"
            ),
            ListingSectionViewModel(
                section.copy(name = "RECOMMENDED_HOTELS"),
                10,
                mockStream,
                currencyCode = "INR"
            ),
            ListingSectionViewModel(
                section.copy(name = "RECOMMENDED_HOTELS"),
                20,
                mockStream,
                currencyCode = "INR"
            ),
            ListingSectionViewModel(
                section.copy(name = "RECOMMENDED_HOTELS"),
                30,
                mockStream,
                currencyCode = currencyCode
            )
        )

        val mobLandingCards = SparseArray<AbstractRecyclerItem>()
        mobLandingCards.append(1, createMockAbstractCard(ListingAdapterTypes.BLACK_CARD))
        mobLandingCards.append(5, createMockAbstractCard(ListingAdapterTypes.COLLECTION_BANNER))
        mobLandingCards.append(8, createMockAbstractCard(ListingAdapterTypes.COLLECTION_BANNER))
        mobLandingCards.append(14, createMockAbstractCard(ListingAdapterTypes.BLACK_CARD))
        mobLandingCards.append(24, createMockAbstractCard(ListingAdapterTypes.BLACK_CARD))
        with(merger!!) {
            tryMergingMobLandingWithHotels(mobLandingCards)
            val cards = tryMergingHotelsWithMobLanding(sectionViewModels, null)
            Assertions.assertEquals(ListingAdapterTypes.BLACK_CARD, cards[0].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.HEADER, cards[1].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.HEADER, cards[12].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.COLLECTION_BANNER, cards[16].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.COLLECTION_BANNER, cards[19].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.BLACK_CARD, cards[25].getItemType())
            Assertions.assertEquals(ListingAdapterTypes.BLACK_CARD, cards[35].getItemType())

        }
    }

    private fun createMockSection(): PersonalizedSection {
        return PersonalizedSection(
            10,
            (0 until 10).map { createMockHotel() },
            "Recommended",
            "Recommended",
            CoreConstants.EMPTY_STRING,
            SectionOrientation.VERTICAL,
            false,
            true,
            emptyList(),
            null,
            null,
             false,
             HotelCardType.DEFAULT_CARD.cardType,
            null,
            0
        )
    }

    private fun createMockHotel(): Hotel {
        return Hotel(
            emptyList(),
            Location(0.0, 0.0),
            CoreConstants.EMPTY_STRING,
            false,
            CoreConstants.EMPTY_STRING,
            LocationDetail(
                CoreConstants.EMPTY_STRING,
                CoreConstants.EMPTY_STRING,
                CoreConstants.EMPTY_STRING,
                CoreConstants.EMPTY_STRING,
                CoreConstants.EMPTY_STRING
            ),
            listOf(CoreConstants.EMPTY_STRING),
            emptyList(),
            false,
            CoreConstants.EMPTY_STRING,
            mockk(relaxed = true),
            null,
            CoreConstants.EMPTY_STRING,
            null,
            false,
            0,
            CoreConstants.EMPTY_STRING,
            emptyMap(),
            null,
            null,
            null,
            0,
            null,
            null,
            null,
             CoreConstants.EMPTY_STRING,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            false,
            false,
            false,
            false,
            false,
            null,
            null,
            null,
            0,
            null,
            null,
            false,
            false,
            false,
            null,
            null,
            false,
            false,
            null,
            false,
            false,
            null,
            null,
            null,
        )
    }

    private fun createMockAbstractCard(type: Int): AbstractRecyclerItem {
        return object : AbstractRecyclerItem {
            override fun getItemType(): Int {
                return type
            }
        }
    }
}