//package com.mmt.hotel.app
//
//import android.app.Application
//import android.content.Context
//import androidx.test.runner.AndroidJUnitRunner
//import com.mmt.data.model.MMTComm
//import com.mmt.core.MPermission.PermissionManager
//import com.mmt.data.model.util.CommonMigrationHelper
//import com.mmt.data.model.util.ICommonMigrator
//import com.mmt.core.MMTCore
//import com.mmt.hotel.common.util.HotelMigratorHelper
//import com.mmt.network.MMTNetwork
//import com.mmt.uikit.util.UiMigrationHelper
//import io.mockk.every
//import io.mockk.mockk
//
//class HotelAndroidJUnitRunner : AndroidJUnitRunner() {
//
//    override fun newApplication(
//        cl: ClassLoader?,
//        className: String?,
//        context: Context?
//    ): Application {
//        val application = super.newApplication(cl, className, context)
//        MMTCore.initialise(
//            application,
//            mockk(relaxed = true),
//            mockk(relaxed = true)
//        )
//        MMTComm.initialise(application)
//        CommonMigrationHelper.initialize(commanMigrator())
//        MMTNetwork.initialise(application, mockk(relaxed = true))
//        UiMigrationHelper.initialize(mockk(relaxed = true))
//        PdtTracker.initialize(mockk(relaxed = true))
//        HotelMigratorHelper.initialize(mockk(relaxed = true))
//        return application
//    }
//
//    private fun commanMigrator(): ICommonMigrator {
//        return mockk<ICommonMigrator>(relaxed = true).also {
//            every { it.getPermissionManager() }returns PermissionManager()
//        }
//    }
//
//}