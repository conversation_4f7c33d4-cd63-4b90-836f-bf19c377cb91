package com.mmt.hotel.landingV3.helper

import com.mmt.core.user.prefs.FunnelContext
import com.mmt.hotel.common.constants.FunnelType
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.mock.MockData
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

internal class LocusUtilsV2Test {

    @AfterEach
    fun tearDown(){
        LocusUtilsV2.clearSavedSearchRequest(FunnelType.HOTEL_FUNNEL,FunnelContext.INDIA)
        unmockkAll()
    }

    @Test
    fun testGettingSearchRequestWithoutSaveReturnNull() {
        mockkObject(HotelMigratorHelper.instance)
        every { LocusUtilsV2.getSaveSearchRequest(any()) }returns null
        val request = LocusUtilsV2.getSaveSearchRequest(FunnelType.HOTEL_FUNNEL)
        assertNull(request)
    }

    @Test
    fun testSavingLastSearchInPrefIsSuccessful(){
        LocusUtilsV2.saveSearchRequest(MockData.searchRequest, FunnelType.HOTEL_FUNNEL)
        val request = LocusUtilsV2.getSaveSearchRequest(FunnelType.HOTEL_FUNNEL)
        assertNotNull(request)
        assertEquals(MockData.searchRequest,request)
    }
}