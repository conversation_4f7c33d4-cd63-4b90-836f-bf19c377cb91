package com.mmt.hotel.landingV3.ui

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.testing.FragmentScenario
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.runner.AndroidJUnit4
import com.google.gson.reflect.TypeToken
import com.mmt.data.model.network.NetworkUtil
import com.mmt.core.util.CoreUtil
import com.mmt.hotel.R
import com.mmt.hotel.autoSuggest.model.response.HotelAutoSuggestResponseItem
import com.mmt.hotel.autoSuggest.viewModel.HotelAutoSuggestActivityViewModel
import com.mmt.hotel.base.viewModel.HotelEventSharedViewModel
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.util.isGroupBookingEnabled
import com.mmt.hotel.filterV2.model.response.FilterCategory
import com.mmt.hotel.filterV2.model.response.FilterV2
import com.mmt.hotel.mock.MockData
import com.mmt.network.NetworkHelper
import com.mmt.network.model.NetworkResponse
import io.mockk.*
import kotlinx.coroutines.flow.flowOf
import org.hamcrest.Matchers.allOf
import org.hamcrest.Matchers.not
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.runner.RunWith

/**
 * These test should be done in HotelLandingActivityV3 as acitivy is in mobile module so
 * writing test for fragment only
 * Below are the pending test
 * More Filter click
 * Room Count click and count change test
 * */
@RunWith(AndroidJUnit4::class)
internal class HotelLandingSearchModifyFragmentTest {

    private lateinit var scenario: FragmentScenario<HotelLandingSearchModifyFragment>

    @BeforeEach
    fun setUp() {
        mockkStatic("com.mmt.hotel.common.util.ExperimentUtils")
        every {isGroupBookingEnabled(HotelFunnel.HOTEL.funnelValue) } returns false
        mockkStatic(ViewModelProviders::class)
        val mockProvider = mockk<ViewModelProvider>()
        every { mockProvider.get(HotelEventSharedViewModel::class.java) } returns HotelEventSharedViewModel()
        every { mockProvider.get(HotelAutoSuggestActivityViewModel::class.java) } returns HotelAutoSuggestActivityViewModel()
        every { ViewModelProviders.of(any<FragmentActivity>(), any()) } returns mockProvider
        val args = Bundle()
        args.putParcelable(
            HotelLandingSearchModifyFragment.REQUEST_GENERATED,
            MockData.searchRequest
        )
        args.putBoolean(HotelLandingSearchModifyFragment.IS_AREA_EDITABLE, true)
        scenario = FragmentScenario.launchInContainer(
            HotelLandingSearchModifyFragment::class.java,
            args,
            R.style.Theme_CosmosTheme
        )
    }

    @AfterEach
    fun tearDown() {
        unmockkAll()
    }

    @Test
    @DisplayName("should bind correct values when default search request passed")
    fun testDefaultValuesSetOnUi() {
        scenario.onFragment{
            it.viewModel.setSearchRequest(MockData.searchRequest)
        }
        onView(allOf(withId(R.id.text), isDescendantOfA(withId(R.id.areaContainer))))
            .check(matches(withText("Dubai")))

        onView(allOf(withId(R.id.text), isDescendantOfA(withId(R.id.checkInContainer))))
            .check(matches(withText("01 Aug")))

        onView(allOf(withId(R.id.text), isDescendantOfA(withId(R.id.checkOutContainer))))
            .check(matches(withText("02 Aug")))

        onView(allOf(withId(R.id.text), isDescendantOfA(withId(R.id.room_guest_container))))
            .check(matches(withText("1 Room, 2 Adults")))

    }

    @Test
    @DisplayName("Change City Name on Selection")
    fun testChangeLocationOnSelection() {
        mockkObject(NetworkHelper)
        mockkObject(NetworkUtil)
        mockkObject(CoreUtil)
        every { CoreUtil.getAppVersionName() } returns ""
        every { NetworkUtil.getInterceptorsForHttpUtils() } returns emptyList()
        val item = HotelAutoSuggestResponseItem(
            cityName = "Delhi",
            displayName = "Delhi",
            htype = "City",
            placeId = "1",
            maxAltAccoPropertyType = "1"
        )
        every {
            NetworkHelper.makeFlowRequest(
                any(),
                object : TypeToken<List<HotelAutoSuggestResponseItem>>() {},
                any()
            )
        } returns flowOf(
            NetworkResponse(listOf(item), null, emptyMap(), 200, null, null)
        )
        onView(withId(R.id.areaContainer)).perform(click())
        onView(withText("Delhi")).perform(click())
        onView(allOf(withId(R.id.text), isDescendantOfA(withId(R.id.areaContainer))))
            .check(matches(withText("Delhi")))
    }


    @Test
    @DisplayName("should update filters on ui")
    fun testIsFilterChangedOnUpdate() {
        onView(withId(R.id.filters_recyclerview)).check(matches(not(isDisplayed())))
        onView(withId(R.id.more)).check(matches(not(isDisplayed())))
        scenario.onFragment {
            val mockFilter = FilterV2(filterGroup = "Free", filterValue = "Free", filterUiTitle = "Free")
            it.viewModel.updateOptionalFilters(
                listOf(mockFilter),
                listOf(FilterCategory("Free",filters = listOf(mockFilter))),
                null,
                "INR"
            )
        }
        onView(withId(R.id.filters_recyclerview)).check(matches(isDisplayed()))
        onView(withId(R.id.more)).check(matches(isDisplayed()))
    }

}