package com.mmt.hotel.mock

import com.mmt.core.constant.CoreConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.PageContext
import com.mmt.hotel.common.model.OccupancyData
import com.mmt.hotel.common.model.UserSearchData
import com.mmt.hotel.common.model.request.RoomStayCandidatesV2
import com.mmt.hotel.landingV3.model.request.ListingSearchData
import com.mmt.hotel.landingV3.model.request.SearchRequest

object MockData {

    val searchRequest
    get() = getMockData()


    private fun getMockData(): SearchRequest {
        return SearchRequest(
            pageContext = PageContext.HOTEL_LANDING.name,
            userSearchData = getUserSearchData(),
            listingSearchData = getListingSearchData(),
            roomStayCandidate = getRoomStayCandidatesList()
        )
    }

    private fun getUserSearchData(): UserSearchData {
        return UserSearchData(
            CoreConstants.EMPTY_STRING,
            HotelFunnel.HOTEL.funnelValue,
            CoreConstants.EMPTY_STRING,
            locationName = "Dubai",
            checkInDate = "08012020",
            checkOutDate = "08022020",
            position = 0,
            travellerType = 0,
            occupancyData = getOccupancyData(),
            displayName = "Dubai",
            selectedCurrency = "AED"
        )
    }

    private fun getListingSearchData(): ListingSearchData {
        return ListingSearchData()
    }

    private fun getRoomStayCandidatesList(): MutableList<RoomStayCandidatesV2> {
        val roomStayCandidatesV2 = RoomStayCandidatesV2(adultCount = 2, childAges = emptyList(), rooms = 1)
        return mutableListOf(roomStayCandidatesV2)
    }

    private fun getOccupancyData(): OccupancyData {
        return OccupancyData(roomCount = 1, adultCount = 2, childAges = emptyList())
    }
}