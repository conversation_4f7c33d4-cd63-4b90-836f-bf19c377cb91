package com.mmt.hotel.mobconfig

import android.app.Application
import android.content.Intent
import android.os.ResultReceiver
import androidx.test.core.app.ApplicationProvider
import androidx.test.rule.ServiceTestRule
import com.mmt.data.model.util.SharedPreferenceUtils
import com.mmt.core.util.CoreUtil
import com.mmt.hotel.app.mockkCoroutine
import com.mmt.hotel.app.unMockkCoroutine
import com.mmt.hotel.common.HotelSharedPrefUtil
import com.mmt.hotel.common.constants.SharedPrefKeys
import com.mmt.hotel.landingV3.model.response.GroupBookingConfig
import com.mmt.hotel.old.model.hotelconfig.ErrorInfo
import com.mmt.hotel.mobconfig.model.response.AddOnConfig
import com.mmt.hotel.mobconfig.model.response.Config
import com.mmt.hotel.mobconfig.model.response.HotelMobConfigResponse
import com.mmt.hotel.mobconfig.model.response.HotelMobConfigResponseData
import com.mmt.hotel.mobconfig.repository.MobConfigRepositoryImpl
import io.mockk.*
import kotlinx.coroutines.flow.flowOf
import org.junit.Rule
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class HotelConfigServiceTest {

    @Rule
    val mServiceRule = ServiceTestRule()

    @Rule
    val mServiceRuleTwo = ServiceTestRule()

    private lateinit var intent: Intent

    @BeforeEach
    fun setUp() {
        mockkCoroutine()
        mockkStatic("com.mmt.hotel.common.util.ExperimentUtils")
        mockkObject(CoreUtil)
        every { CoreUtil.getAppVersionCode() } returns 231
        mockkConstructor(MobConfigRepositoryImpl::class)
        every { anyConstructed<MobConfigRepositoryImpl>().retrieveMobConfigResponse(any()) } returns flowOf(
            mockResponse()
        )
        intent = Intent(
            ApplicationProvider.getApplicationContext<Application>(),
            HotelConfigService::class.java
        ).apply {
            putExtra(HotelConfigService.IS_APP_UPDATED, true)
            putExtra(HotelConfigService.IS_VARIANT_CHANGED, true)
        }
    }

    @AfterEach
    fun tearDown() {
        mServiceRule.unbindService()
        unMockkCoroutine()
        unmockkAll()
    }

    @Test
    fun testResponseReceivedAndSavedInPrefCorrectly() {
        val binder = mServiceRule.bindService(intent)
        (binder as HotelConfigService.ConfigBinder).startSync()
        val pref = SharedPreferenceUtils.getInstance()
        val hotelPref = HotelSharedPrefUtil.instance
        assertFalse(pref.getBoolean(SharedPrefKeys.HC_SYNC_IN_PROGRESS))
        assertEquals("0.1", pref.getString(SharedPrefKeys.HC_API_VERSION))
        assertEquals(setOf("DEL", "GOA"), pref.getStringSet(SharedPrefKeys.MATCH_MAKER_CITIES))
        assertTrue(hotelPref.getString(SharedPrefKeys.GROUP_BOOKING_CONFIG).contains("\"adultRequired\":2"))
    }

    @Test
    fun testResultReceiverAddedAndRemovedCorrectly() {
        val receiverOne = ResultReceiver(mockk(relaxed = true))
        val receiverTwo = ResultReceiver(mockk(relaxed = true))

        val binder1 = mServiceRule.bindService(intent)
        val binder2 = mServiceRuleTwo.bindService(intent)

        (binder1 as HotelConfigService.ConfigBinder).addResultReceiver(receiverOne)
        (binder2 as HotelConfigService.ConfigBinder).addResultReceiver(receiverTwo)
        binder1.startSync()
        binder2.startSync()

        assertEquals(2, binder1.currentResultReceiverCount())
        assertEquals(2, binder2.currentResultReceiverCount())

        binder2.removeResultReceiver(receiverTwo)
        mServiceRuleTwo.unbindService()
        assertEquals(1, binder1.currentResultReceiverCount())
        binder2.removeResultReceiver(receiverOne)
        mServiceRule.unbindService()
        assertEquals(0, binder1.currentResultReceiverCount())
    }

    private fun mockResponse(): HotelMobConfigResponse {
        val configJson = HotelMobConfigResponseData(
            config = Config(AddOnConfig(4, 8)),
            checkoutApiErrorCodes = hashMapOf("401" to ErrorInfo()),
            ecoFriendlyDetailPageMsg = "Message",
            matchMakerIncludedCities = listOf("DEL", "GOA"),
            groupBookingConfig=GroupBookingConfig(
                2, 1, "", "",
                "", "", "", "", "", listOf("DEL")
            ),
            numberOfCoupons = 0,
            reviewCouponCount=0,
            filterBannerString = "Filter Banner",
            manualRatingMessage = null,
            charityAddonTitle = null,
            mbgDetailTitle = null,
            mbgDetailString = null,
            mmtBlackWebUrl = null,
            hotelThankYouConfigStrings = null,
            imgSizeFactor = hashMapOf("DETAIL" to 3F),
            hotelsReqCount=  null,
            appUpdateModel =  null,
            flexibleCheckinEducationInfo = null


        )
        return HotelMobConfigResponse(true, "0.1", true, "IN", configJson)
    }

}