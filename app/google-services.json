{"project_info": {"project_number": "365487944636", "firebase_url": "https://manifest-device-657.firebaseio.com", "project_id": "manifest-device-657", "storage_bucket": "manifest-device-657.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:365487944636:android:b088520de2b1c37f", "android_client_info": {"package_name": "com.makemytrip"}}, "oauth_client": [{"client_id": "365487944636-04tpbfmjm1dq55ei4b74gmrj2hofeigg.apps.googleusercontent.com", "client_type": 3}, {"client_id": "365487944636-q943oq1rje9ugcbscagg2vre5om9maj0.apps.googleusercontent.com", "client_type": 3}, {"client_id": "365487944636-p24thu6soekhubo3ndkensb7p7q06hp1.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.makemytrip", "certificate_hash": "ae652c85b8ed0b1ea1f8e7525c9137c3cb64068f"}}, {"client_id": "365487944636-m6crti3onae6glei1bdgghvfr9re5lun.apps.googleusercontent.com", "client_type": 3}, {"client_id": "365487944636-1pg02vhjem1snkek5pampuvq7c1tg531.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.makemytrip", "certificate_hash": "222bf44a30dbf4e91bfc9f82d02461ae0511b538"}}, {"client_id": "365487944636-ch4g9l38nks9403ss1mqt826c37vk33d.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCxWG6Ap7w2DRdE4pH1UFoCaQHB_9tF-Hk"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 2, "other_platform_oauth_client": [{"client_id": "365487944636-04tpbfmjm1dq55ei4b74gmrj2hofeigg.apps.googleusercontent.com", "client_type": 3}, {"client_id": "365487944636-3s34dmb5hh7enf6alddgjirqjbuuodit.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.Iphone.MMT", "app_store_id": "https://itunes.apple.com/in/app/makemytrip-flights-hotels/id530488359?mt=8"}}]}, "ads_service": {"status": 2}}}], "configuration_version": "1"}