# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in D:\Android/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

#DONT OBFUSCATE CLASS HAVING SERIALIZABLE BEHAVIOR
-keep class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
-keepattributes *Annotation*,EnclosingMethod,JavascriptInterface
-keepattributes SourceFile,LineNumberTable

-keepnames public class * extends android.app.Activity
-keepnames public class * extends android.app.Fragment
-keepnames public class * extends android.app.Application
-keepnames public class * extends android.app.Service
-keepnames public class * extends android.content.BroadcastReceiver
-keepnames public class * extends android.preference.Preference
-keepclassmembers class **.model.**{<fields>;}
-keepclassmembers class **.model.** { *; }
-keepclassmembers class **.baseModels.** { *; }
-keepclassmembers class **.models.** { *; }
-keepclassmembers class **.dataModel.**{<fields>;}
-keepclassmembers class **.models.**{<fields>;}
-keepclassmembers class **.baseModels.**{<fields>;}
-keepclassmembers class **.dataModel.** { *; }
# As PaymentsFetchIntermediateResponse is using reflection for getters
-keepclassmembers class com.mmt.travel.app.visa.dto.** {*;}
-keepclassmembers class **.model.deeplink.** { *; }
-keepclassmembers class in.juspay.** {*;}
#-keepclassmembers class com.crashlytics.** { *; }
-keepclassmembers class com.freshdesk.hotline.** { *; }
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

-keepclassmembers class **.data.**{<fields>;}
-keepclassmembers class com.mmt.travel.app.hotel.corporate.dataModel.**{<fields>;}
-keepclassmembers class com.mmt.hotel.userReviews.collection.generic.** { *; }
-keepclassmembers class com.mmt.hotel.landingV3.dataModel.** {*;}
-keepclassmembers class com.mmt.core.base.thankyou.** {*;}
-keepclassmembers class com.mmt.core.currency.** {*;}
-keep class com.mmt.core.gcm.** {*;}
#Branch proguard settings
-keep class com.facebook.applinks.** { *; }
-keepclassmembers class com.facebook.applinks.** { *; }
#-keep class com.facebook.FacebookSdk { *; }
-keep public class com.google.android.gms.ads.identifier.** { *; }
-keep public class com.google.android.gms.internal.ads.** { *; }
#-keep public class com.google.android.gms.gcm.** { *; }
#-keep public class com.google.android.gms.common.** { *; }

-keepattributes *Annotation*
-keep enum com.mmt.hotel.thankyou.CancellationPolicyType { *; }
#end tune proguard setting

#-keepattributes InnerClasses

-keepclasseswithmembers class * {
public <init>(android.content.Context, android.util.AttributeSet, int);
}


-keep public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
    @com.google.android.gms.common.annotation.KeepName *;
}

-keepnames class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

-keep interface android.support.design.widget.** { *; }

-dontwarn com.flurry.**
-dontwarn okio.**
-dontwarn oauth.signpost.signature.**
-dontwarn com.nhaarman.**
-dontwarn com.squareup.**
-dontwarn com.google.android.gms.**
-dontwarn com.androidquery.auth.**
-dontwarn javax.annotation.Nullable
-dontwarn javax.annotation.CheckReturnValue
-dontwarn android.support.design.**
-dontwarn com.crashlytics.**
-dontwarn io.intercom.**
-dontwarn com.freshdesk.hotline.**
-dontwarn okhttp3.**
-dontwarn com.squareup.okhttp.internal.**
-dontwarn com.google.**
-dontwarn org.npci.**
-dontwarn in.org.npci.**



#-keep class com.google.common.collect.** {*;}
#-keep class com.google.common.io.** {*;}

#-keep class android.support.v4.view.ViewPager
#-keepclassmembers class android.support.v4.view.ViewPager$LayoutParams { *; }

#-keep class com.mixpanel.android.mpmetrics.MixpanelAPI { *;}
#-keep class com.google.android.gms.analytics.Tracker { *; }
#-keep class com.google.analytics.tracking.android.Tracker { *; }
#-keep class com.flurry.android.FlurryAgent { *; }
#-keep class com.omniture.AppMeasurementBase { *;}
#-keep class com.adobe.adms.measurement.ADMS_Measurement { *;}
#-keep class  * implements com.google.gson {*; }

#-keep class com.google.gson.examples.android.model.** { <fields>; }

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

#-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

#-keep class com.newrelic.** { *; }
-dontwarn com.newrelic.**
-keepattributes Exceptions, Signature, InnerClasses, LineNumberTable

-keepattributes Exceptions, Signature, InnerClasses, LineNumberTable

#-keep class okhttp3.** { *; }
#-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**

-dontwarn io.realm.**

# ButterKnife 7

-keep class butterknife.** { *; }
-dontwarn butterknife.internal.**
-keep class **$$ViewBinder { *; }

-keepclasseswithmembernames class * {
    @butterknife.* <fields>;
}

-keepclasseswithmembernames class * {
    @butterknife.* <methods>;
}


# config annotation class
-keep class androidx.annotation.Keep {*;}
-keep public enum com.makemytrip.AbElement$** {
    <fields>;
        public static **[] values();
        public static ** valueOf(java.lang.String);
}

-keepclassmembers class * extends android.webkit.WebChromeClient {
     public void openFileChooser(...);
     public void onShowFileChooser(...);
}

#FIREBASE

#-keep class com.firebase.** { *; }
#-keep class org.apache.** { *; }
-keepnames class com.fasterxml.jackson.** { *; }
-keepnames class javax.servlet.** { *; }
-keepnames class org.ietf.jgss.** { *; }
-dontwarn org.w3c.dom.**
-dontwarn org.joda.time.**
-dontwarn org.shaded.apache.**
-dontwarn org.ietf.jgss.**

# Only necessary if you downloaded the SDK jar directly instead of from maven.
#-keep class com.shaded.fasterxml.jackson.** { *; }

-keepclasseswithmembers class com.mmt.travel.app.homepagev2.data.entity.** { *; }

## React native Fast Image changes
-keep public class com.dylanvann.fastimage.* {*;}
-keep public class com.dylanvann.fastimage.** {*;}
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

#-keep class android.support.v7.widget.** { *; }

# TextLayoutBuilder uses a non-public Android constructor within StaticLayout.
# See libs/proxy/src/main/java/com/facebook/fbui/textlayoutbuilder/proxy for details.
-dontwarn android.text.StaticLayout

-keep class com.mmt.react_native_ota.domain.** { *; }


# from hermes vm
#-keep class com.facebook.hermes.unicode.* { *; }
#-keep class com.facebook.jni.** { *; }

-keep class org.npci.** {*;}
-keep class in.org.npci.** {*;}

# Explicitly preserve all serialization members. The Serializable interface
# is only a marker interface, so it wouldn't save them.
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Preserve the special static methods that are required in all enumeration classes.
-keepclassmembers enum * {
    public *;
    **[] $VALUES;
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# For Retrofit
-keepclassmembernames interface * {
    @retrofit.http.* <methods>;
}

-keepclassmembernames interface * {
    @retrofit.http.* <methods>;
}
#react-native-svg module for react native
-keep public class com.horcrux.svg.** {*;}

#GSM notification
-keep class com.mmt.core.gcm.** { *; }

#SDK has been obfuscated and compressed to avoid class not found error due to re-obfuscation.
-keep class com.xiaomi.**

#If the compiling Android version you are using is 23, you can prevent getting a false warning which makes it impossible to compile.
-dontwarn com.xiaomi.push.**

#Minkasupay Sdk
-keep class minkasu2fa.** { *; }
-keep class com.minkasu.android.twofa.** { *; }

#Requisition Model
-keep class com.mmt.travel.app.homepagex.corp.tripdetails.repository.model.**{*;}
-keep class com.mmt.travel.app.homepagex.corp.repository.model.response.**{*;}
-keep class com.mmt.hotel.common.model.response.**{*;}

# With R8 full mode generic signatures are stripped for classes that are not
 # kept. Suspend functions are wrapped in continuations where the type argument
 # is used.
 -keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

 # R8 full mode strips generic signatures from return types if not kept.
 -if interface * { @retrofit2.http.* public *** *(...); }
 -keep,allowoptimization,allowshrinking,allowobfuscation class <3>

 # With R8 full mode generic signatures are stripped for classes that are not kept.
 -keep,allowobfuscation,allowshrinking class retrofit2.Response


#AppsFlyer
-keep class com.appsflyer.** { *; }
-keep public class com.android.installreferrer.** { *; }

-keep class com.google.android.gms.internal.consent_sdk**


-keep class com.gommt.pay.core.base.response.**{*;}
-keep class com.gommt.upi.upi_enroll.domain.request.**{*;}
-keep class com.gommt.upi.**.request.**{*;}
-keep class com.gommt.upi.**.response.**{*;}

-dontwarn java.lang.invoke.StringConcatFactory
-dontwarn com.simpl.android.fingerprint.SimplFingerprint
-dontwarn com.simpl.android.fingerprint.SimplFingerprintListener
-dontwarn in.juspay.hyperapayupi.APayUPIBridge
-dontwarn in.juspay.hyperlottie.LottieAnimation
-dontwarn in.juspay.hyperupi.UPIBridge
-dontwarn in.juspay.hypervsc.VSCBridge
-dontwarn in.juspay.hypervsc.VSCHelper

-dontwarn amazonpay.silentpay.**
-dontwarn com.amazon.**

-keepattributes Signature
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault
-keep class com.haroldadmin.cnradapter.** { *; }

-if class com.google.gson.reflect.TypeToken
-keep,allowobfuscation class com.google.gson.reflect.TypeToken
#-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken
-keep class sun.misc.** { *; }

-keep,allowobfuscation,allowoptimization @com.google.gson.annotations.JsonAdapter class *
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.Expose <fields>;
  @com.google.gson.annotations.JsonAdapter <fields>;
  @com.google.gson.annotations.Since <fields>;
  @com.google.gson.annotations.Until <fields>;
}

-keepclassmembers class * extends com.google.gson.TypeAdapter {
  <init>();
}
-keepclassmembers class * implements com.google.gson.TypeAdapterFactory {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonSerializer {
  <init>();
}
-keepclassmembers class * implements com.google.gson.JsonDeserializer {
  <init>();
}

# Prevent R8 from leaving Data object members always null

-keep public class * implements java.lang.reflect.Type
-if class *
-keepclasseswithmembers,allowobfuscation class <1> {
  @com.google.gson.annotations.SerializedName <fields>;
}
-if class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keepclassmembers,allowobfuscation,allowoptimization class <1> {
  <init>();
}


#-if class *
#-keepclasseswithmembers class <1> {
#    <init>(...);
#    @com.google.gson.annotations.SerializedName <fields>;
#}

-dontwarn com.mmt.core.interfaces.BaseDialogInterface

-dontwarn com.mmt.core.interfaces.BaseDialogInterface
