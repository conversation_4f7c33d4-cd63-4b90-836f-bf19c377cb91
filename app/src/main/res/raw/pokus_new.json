{"perLobMap": {"PAYMENT": {"expDetails": ["1726|22|3615", "2306|1|4960", "2263|4|4863", "2307|4|4962", "2380|2|5140", "2262|6|4862", "2292|2|4928", "2305|4|4958", "2457|1|5315", "1967|35|4166", "2222|8|4767", "2028|20|4309"], "expDetailsV1": [{"exp_experiment_id": 2305, "exp_experiment_version": 4, "exp_experiment_variant_id": 4958}, {"exp_experiment_id": 2306, "exp_experiment_version": 1, "exp_experiment_variant_id": 4960}, {"exp_experiment_id": 2307, "exp_experiment_version": 4, "exp_experiment_variant_id": 4962}, {"exp_experiment_id": 2380, "exp_experiment_version": 2, "exp_experiment_variant_id": 5140}, {"exp_experiment_id": 2457, "exp_experiment_version": 1, "exp_experiment_variant_id": 5315}, {"exp_experiment_id": 2222, "exp_experiment_version": 8, "exp_experiment_variant_id": 4767}, {"exp_experiment_id": 1967, "exp_experiment_version": 35, "exp_experiment_variant_id": 4166}, {"exp_experiment_id": 1726, "exp_experiment_version": 22, "exp_experiment_variant_id": 3615}, {"exp_experiment_id": 2262, "exp_experiment_version": 6, "exp_experiment_variant_id": 4862}, {"exp_experiment_id": 2263, "exp_experiment_version": 4, "exp_experiment_variant_id": 4863}, {"exp_experiment_id": 2028, "exp_experiment_version": 20, "exp_experiment_variant_id": 4309}, {"exp_experiment_id": 2292, "exp_experiment_version": 2, "exp_experiment_variant_id": 4928}], "metadataValues": {"tokenizationType": {"value": 1, "expDetails": "1726|22|3615", "trackingKey": "2sf"}, "isNewEWalletEnabled": {"value": false, "expDetails": "2263|4|4863", "trackingKey": "3r3"}, "showInsuranceOnBottom": {"value": false, "expDetails": "2028|20|4309", "trackingKey": "3bp"}, "isNewEMIEnabled": {"value": true, "expDetails": "2292|2|4928", "trackingKey": "3sw"}, "udirStatusCtaEnable": {"value": true, "expDetails": "2380|2|5140", "trackingKey": "3ys"}, "checkCredAppToShow": {"value": false, "expDetails": "2306|1|4960", "trackingKey": "3ts"}, "newSavedCardFlowEnableLobs": {"value": "\"BUS,Cabs,Hotel,Flight,Domestic Bus,HotelIntl,Holidays,QC,RAIL,VISA,IntlFlight\"", "expDetails": "1967|35|4166", "trackingKey": "37q"}, "isUPIDirectDisable": {"value": false, "expDetails": "2305|4|4958", "trackingKey": "3tq"}, "isNewNBEnabled": {"value": true, "expDetails": "2262|6|4862", "trackingKey": "3r2"}, "isLobSummaryEnabled": {"value": true, "expDetails": "2222|8|4767", "trackingKey": "3of"}, "isNewCouponFlow": {"value": false, "expDetails": "2307|4|4962", "trackingKey": "3tu"}, "isNewUPIEnabled": {"value": true, "expDetails": "2457|1|5315", "trackingKey": "43n"}, "OTPOnBankPage": {"value": false}}, "variantKey": ""}, "BUS": {"expDetails": ["2396|6|5173", "2321|5|4992", "2382|12|5144", "2424|6|5236"], "expDetailsV1": [{"exp_experiment_id": 2321, "exp_experiment_version": 5, "exp_experiment_variant_id": 4992}, {"exp_experiment_id": 2382, "exp_experiment_version": 12, "exp_experiment_variant_id": 5144}, {"exp_experiment_id": 2396, "exp_experiment_version": 6, "exp_experiment_variant_id": 5173}, {"exp_experiment_id": 2424, "exp_experiment_version": 6, "exp_experiment_variant_id": 5236}], "metadataValues": {"Li_RTC_Change": {"value": true, "expDetails": "2321|5|4992", "trackingKey": "3uo"}, "Re_NoGST": {"value": false, "expDetails": "2424|6|5236", "trackingKey": "41g"}, "Li_Ad_Banner": {"value": false, "expDetails": "2396|6|5173", "trackingKey": "3zp"}, "Li_Filter_Cards": {"value": false, "expDetails": "2382|12|5144", "trackingKey": "3yw"}}, "variantKey": ""}, "ACME": {"expDetails": ["2101|4|4484"], "expDetailsV1": [{"exp_experiment_id": 2101, "exp_experiment_version": 4, "exp_experiment_variant_id": 4484}], "metadataValues": {"newAcmeBannerEXP": {"value": true, "expDetails": "2101|4|4484", "trackingKey": "3gk"}}, "variantKey": "3gk"}, "HOTEL_INT": {"expDetails": ["2327|3|5004"], "expDetailsV1": [{"exp_experiment_id": 2327, "exp_experiment_version": 3, "exp_experiment_variant_id": 5004}], "metadataValues": {"htlSingleRatingColor": {"value": true, "expDetails": "2327|3|5004", "trackingKey": "3v0"}}, "variantKey": "3v0"}, "CAB": {"expDetails": ["2048|7|4349", "1871|5|3941", "2231|5|4792", "2156|8|4610", "2237|8|4806", "2425|1|5239", "2014|5|4277", "1991|4|4218", "2105|11|4493", "2092|8|4456", "2093|6|4459", "2366|3|5102", "1054|11|2121", "2120|4|4533", "1851|14|3898", "2106|6|4495", "1857|12|3910", "1889|7|3979", "1986|14|4208", "2013|8|4274", "1979|16|4191"], "expDetailsV1": [{"exp_experiment_id": 2048, "exp_experiment_version": 7, "exp_experiment_variant_id": 4349}, {"exp_experiment_id": 1054, "exp_experiment_version": 11, "exp_experiment_variant_id": 2121}, {"exp_experiment_id": 2092, "exp_experiment_version": 8, "exp_experiment_variant_id": 4456}, {"exp_experiment_id": 2093, "exp_experiment_version": 6, "exp_experiment_variant_id": 4459}, {"exp_experiment_id": 2105, "exp_experiment_version": 11, "exp_experiment_variant_id": 4493}, {"exp_experiment_id": 2106, "exp_experiment_version": 6, "exp_experiment_variant_id": 4495}, {"exp_experiment_id": 1851, "exp_experiment_version": 14, "exp_experiment_variant_id": 3898}, {"exp_experiment_id": 2366, "exp_experiment_version": 3, "exp_experiment_variant_id": 5102}, {"exp_experiment_id": 1857, "exp_experiment_version": 12, "exp_experiment_variant_id": 3910}, {"exp_experiment_id": 2120, "exp_experiment_version": 4, "exp_experiment_variant_id": 4533}, {"exp_experiment_id": 1871, "exp_experiment_version": 5, "exp_experiment_variant_id": 3941}, {"exp_experiment_id": 1889, "exp_experiment_version": 7, "exp_experiment_variant_id": 3979}, {"exp_experiment_id": 2156, "exp_experiment_version": 8, "exp_experiment_variant_id": 4610}, {"exp_experiment_id": 2425, "exp_experiment_version": 1, "exp_experiment_variant_id": 5239}, {"exp_experiment_id": 2231, "exp_experiment_version": 5, "exp_experiment_variant_id": 4792}, {"exp_experiment_id": 1979, "exp_experiment_version": 16, "exp_experiment_variant_id": 4191}, {"exp_experiment_id": 2237, "exp_experiment_version": 8, "exp_experiment_variant_id": 4806}, {"exp_experiment_id": 1986, "exp_experiment_version": 14, "exp_experiment_variant_id": 4208}, {"exp_experiment_id": 1991, "exp_experiment_version": 4, "exp_experiment_variant_id": 4218}, {"exp_experiment_id": 2013, "exp_experiment_version": 8, "exp_experiment_variant_id": 4274}, {"exp_experiment_id": 2014, "exp_experiment_version": 5, "exp_experiment_variant_id": 4277}], "metadataValues": {"Blusmart_Listing": {"value": 1, "expDetails": "2092|8|4456", "trackingKey": "3fs"}, "LandingPage_SnackBar": {"value": true, "expDetails": "2425|1|5239", "trackingKey": "41j"}, "SKU_Pricing_Format": {"value": false, "expDetails": "1889|7|3979", "trackingKey": "32j"}, "Review_Page_Luggage_Info": {"value": true, "expDetails": "1851|14|3898", "trackingKey": "30a"}, "Listing_Coupon_Persuasion": {"value": false, "expDetails": "2105|11|4493", "trackingKey": "3gt"}, "Review_Full_Price": {"value": false, "expDetails": "2048|7|4349", "trackingKey": "3ct"}, "Ryde_listing_swap": {"value": true, "expDetails": "2014|5|4277", "trackingKey": "3at"}, "Mysafety_Switch": {"value": true, "expDetails": "2156|8|4610", "trackingKey": "3k2"}, "Review_UX_RBYB": {"value": true, "expDetails": "2237|8|4806", "trackingKey": "3pi"}, "Ride_Now_Switch": {"value": true, "expDetails": "1871|5|3941", "trackingKey": "31h"}, "Multicity_switch": {"value": false, "expDetails": "2013|8|4274", "trackingKey": "3aq"}, "HR_New_Icon": {"value": true, "expDetails": "2093|6|4459", "trackingKey": "3fv"}, "Ryde_Upsell_Switch": {"value": false, "expDetails": "2231|5|4792", "trackingKey": "3p4"}, "Ryde_AT_Listing": {"value": true, "expDetails": "1857|12|3910", "trackingKey": "30m"}, "BNPL_switch": {"value": true, "expDetails": "1991|4|4218", "trackingKey": "396"}, "b2c_hourly_rentals": {"value": true, "expDetails": "1979|16|4191", "trackingKey": "38f"}, "Listing_Part_Payment": {"value": false, "expDetails": "2106|6|4495", "trackingKey": "3gv"}, "cab_sort_filter": {"value": true, "expDetails": "1054|11|2121", "trackingKey": "1mx"}, "Hotel_Mytrips_AB": {"value": true, "expDetails": "2366|3|5102", "trackingKey": "3xq"}, "HR_Landing": {"value": true, "expDetails": "2120|4|4533", "trackingKey": "3hx"}, "mybiz_hourly_rentals_new": {"value": true, "expDetails": "1986|14|4208", "trackingKey": "38w"}}, "variantKey": ""}, "MYPARTNER_CORE": {"expDetails": ["2327|3|5004"], "expDetailsV1": [{"exp_experiment_id": 2327, "exp_experiment_version": 3, "exp_experiment_variant_id": 5004}], "metadataValues": {"htlSingleRatingColor": {"value": true, "expDetails": "2327|3|5004", "trackingKey": "3v0"}}, "variantKey": "3v0"}, "POST_SALES": {"expDetails": ["2276|4|4892", "1809|11|3810", "1835|6|3866", "1838|10|3872", "1816|15|3824", "2226|3|4780", "2275|2|4890", "2440|2|5276"], "expDetailsV1": [{"exp_experiment_id": 1809, "exp_experiment_version": 11, "exp_experiment_variant_id": 3810}, {"exp_experiment_id": 1816, "exp_experiment_version": 15, "exp_experiment_variant_id": 3824}, {"exp_experiment_id": 1835, "exp_experiment_version": 6, "exp_experiment_variant_id": 3866}, {"exp_experiment_id": 1838, "exp_experiment_version": 10, "exp_experiment_variant_id": 3872}, {"exp_experiment_id": 2440, "exp_experiment_version": 2, "exp_experiment_variant_id": 5276}, {"exp_experiment_id": 2226, "exp_experiment_version": 3, "exp_experiment_variant_id": 4780}, {"exp_experiment_id": 2275, "exp_experiment_version": 2, "exp_experiment_variant_id": 4890}, {"exp_experiment_id": 2276, "exp_experiment_version": 4, "exp_experiment_variant_id": 4892}], "metadataValues": {"mytripsCSATApp": {"value": true, "expDetails": "2226|3|4780", "trackingKey": "3os"}, "EscalateMyTrips": {"value": false, "expDetails": "2275|2|4890", "trackingKey": "3ru"}, "holDocumentUpload": {"value": true, "expDetails": "1835|6|3866", "trackingKey": "2ze"}, "mbmc": {"value": 1, "expDetails": "2440|2|5276", "trackingKey": "42k"}, "TalkToUsMyTrips": {"value": false, "expDetails": "2276|4|4892", "trackingKey": "3rw"}, "Planningpresales": {"value": true, "expDetails": "1809|11|3810", "trackingKey": "2xu"}, "stillNeedHelpSection": {"value": true, "expDetails": "1816|15|3824", "trackingKey": "2y8"}, "mmtp": {"value": 1, "expDetails": "1838|10|3872", "trackingKey": "2zk"}}, "variantKey": ""}, "HOTEL": {"expDetails": ["2378|23|5135", "1498|37|4078", "2462|1|5324", "1953|14|4129", "1938|8|4088", "2374|22|5123", "2056|13|4568", "1682|28|3518", "2086|6|4437", "2185|19|4683", "2301|6|4947", "2157|10|4612", "2417|3|5222", "2472|1|5348", "1478|56|3067", "1542|29|3211", "2055|22|4364", "1270|20|2605", "2135|4|4564", "2304|2|4956", "1407|18|2903", "1930|27|4068", "2131|6|4554", "2329|2|5007", "837|24|1644", "1584|76|3298", "2075|6|4408", "2458|1|5317", "2386|17|5152", "2327|3|5004"], "expDetailsV1": [{"exp_experiment_id": 2304, "exp_experiment_version": 2, "exp_experiment_variant_id": 4956}, {"exp_experiment_id": 1542, "exp_experiment_version": 29, "exp_experiment_variant_id": 3211}, {"exp_experiment_id": 2055, "exp_experiment_version": 22, "exp_experiment_variant_id": 4364}, {"exp_experiment_id": 2056, "exp_experiment_version": 13, "exp_experiment_variant_id": 4568}, {"exp_experiment_id": 2327, "exp_experiment_version": 3, "exp_experiment_variant_id": 5004}, {"exp_experiment_id": 2329, "exp_experiment_version": 2, "exp_experiment_variant_id": 5007}, {"exp_experiment_id": 2075, "exp_experiment_version": 6, "exp_experiment_variant_id": 4408}, {"exp_experiment_id": 2086, "exp_experiment_version": 6, "exp_experiment_variant_id": 4437}, {"exp_experiment_id": 1584, "exp_experiment_version": 76, "exp_experiment_variant_id": 3298}, {"exp_experiment_id": 837, "exp_experiment_version": 24, "exp_experiment_variant_id": 1644}, {"exp_experiment_id": 2374, "exp_experiment_version": 22, "exp_experiment_variant_id": 5123}, {"exp_experiment_id": 2378, "exp_experiment_version": 23, "exp_experiment_variant_id": 5135}, {"exp_experiment_id": 2386, "exp_experiment_version": 17, "exp_experiment_variant_id": 5152}, {"exp_experiment_id": 2131, "exp_experiment_version": 6, "exp_experiment_variant_id": 4554}, {"exp_experiment_id": 2135, "exp_experiment_version": 4, "exp_experiment_variant_id": 4564}, {"exp_experiment_id": 2157, "exp_experiment_version": 10, "exp_experiment_variant_id": 4612}, {"exp_experiment_id": 2417, "exp_experiment_version": 3, "exp_experiment_variant_id": 5222}, {"exp_experiment_id": 1407, "exp_experiment_version": 18, "exp_experiment_variant_id": 2903}, {"exp_experiment_id": 2185, "exp_experiment_version": 19, "exp_experiment_variant_id": 4683}, {"exp_experiment_id": 1930, "exp_experiment_version": 27, "exp_experiment_variant_id": 4068}, {"exp_experiment_id": 1682, "exp_experiment_version": 28, "exp_experiment_variant_id": 3518}, {"exp_experiment_id": 1938, "exp_experiment_version": 8, "exp_experiment_variant_id": 4088}, {"exp_experiment_id": 2458, "exp_experiment_version": 1, "exp_experiment_variant_id": 5317}, {"exp_experiment_id": 2462, "exp_experiment_version": 1, "exp_experiment_variant_id": 5324}, {"exp_experiment_id": 1953, "exp_experiment_version": 14, "exp_experiment_variant_id": 4129}, {"exp_experiment_id": 2472, "exp_experiment_version": 1, "exp_experiment_variant_id": 5348}, {"exp_experiment_id": 1478, "exp_experiment_version": 56, "exp_experiment_variant_id": 3067}, {"exp_experiment_id": 1498, "exp_experiment_version": 37, "exp_experiment_variant_id": 4078}, {"exp_experiment_id": 1270, "exp_experiment_version": 20, "exp_experiment_variant_id": 2605}, {"exp_experiment_id": 2301, "exp_experiment_version": 6, "exp_experiment_variant_id": 4947}], "metadataValues": {"dsH2HSimilarityAlgoId": {"value": "mmt_shs_ctx_mab", "expDetails": "1498|37|4078", "trackingKey": "35a"}, "HubAndSpokeCard": {"value": "A", "expDetails": "2374|22|5123", "trackingKey": "3yb"}, "htlLandingSkyRefresh": {"value": 2, "expDetails": "2304|2|4956", "trackingKey": "3to"}, "searchInCity": {"value": true}, "htlNewPropertyLayout": {"value": true, "expDetails": "2458|1|5317", "trackingKey": "43p"}, "homeStayFilterBottomSheet": {"value": "C", "expDetails": "1953|14|4129", "trackingKey": "36p"}, "showIndiannness": {"value": true, "expDetails": "2462|1|5324", "trackingKey": "43w"}, "HomePageLayoutConfig_B2C": {"value": {"layout": "INB2C_BOOKABILITY", "variant": "3982"}, "expDetails": "2378|23|5135", "trackingKey": "3yn"}, "groupBookingEnabled": {"value": true, "expDetails": "1584|76|3298", "trackingKey": "2jm"}, "htlSingleRatingColor": {"value": true, "expDetails": "2327|3|5004", "trackingKey": "3v0"}, "htlListingCalendarAction": {"value": true, "expDetails": "2157|10|4612", "trackingKey": "3k4"}, "disableBNPL": {"value": false}, "filterBottomSheet": {"value": "C", "expDetails": "1930|27|4068", "trackingKey": "350"}, "soldOutCard": {"value": true, "expDetails": "2157|10|4612", "trackingKey": "3k4"}, "htlLandingAvailabilityCalendar": {"value": false, "expDetails": "2417|3|5222", "trackingKey": "412"}, "newAboutPropertyCard": {"value": true, "expDetails": "837|24|1644", "trackingKey": "19o"}, "reCaptchaVariant": {"value": 0, "expDetails": "2056|13|4568", "trackingKey": "3iw"}, "groupBookingLandingEnabled": {"value": true, "expDetails": "2055|22|4364", "trackingKey": "3d8"}, "showLoadMoreCard": {"value": false, "expDetails": "2135|4|4564", "trackingKey": "3is"}, "LandingInput": {"value": "Purpose", "expDetails": "2472|1|5348", "trackingKey": "44k"}, "hotelDetailCardOrderV3": {"value": "{\"topItems\":[\"rc\",\"ccc\",\"dlc\",\"vsc\",\"pl\",\"pci\",\"gq\",\"aic\"],\"sections\": [{\"id\": \"0\",\"title\": \"Overview\",\"items\": [\"gbrc\",\"gbh\",\"pdc\",\"ap\",\"cr\", \"hbc\", \"dac\",\"hfc\", \"so\",\"qbrc\",\"ex\"]},{\"id\": \"1\",\"title\": \"Location\",\"items\": [\"lcn\"]},{\"id\": \"2\",\"title\": \"Reviews\",\"items\":[\"rr\", \"gc\", \"ta\", \"man\"]}],\"bottomItems\":[\"cc\",\"cfc\"]}", "expDetails": "1478|56|3067", "trackingKey": "2d7"}, "highlightPackagesSR": {"value": 1}, "listingIconPadding": {"value": true, "expDetails": "2086|6|4437", "trackingKey": "3f9"}, "travellerPhoto": {"value": 1, "expDetails": "1270|20|2605", "trackingKey": "20d"}, "hotelBaseCardOrderV3": {"value": "{\"topItems\":[\"rc\",\"ccc\",\"dlc\",\"ptc\",\"vsc\",\"pl\",\"ex\",\"alpd\"],\"sections\":[{\"id\":\"0\",\"title\":\"Overview\",\"items\":[\"gbrc\",\"pci\",\"pdc\",\"ap\",\"aic\",\"cr\",\"gbh\",\"hbc\",\"dac\",\"hfc\",\"so\",\"qbrc\",\"hrc\",\"cp\"]},{\"id\":\"1\",\"title\":\"Location\",\"items\":[\"lcn\"]},{\"id\":\"2\",\"title\":\"Reviews\",\"items\":[\"rr\",\"gc\",\"gr\",\"ta\",\"man\"]}],\"bottomItems\":[\"aga\",\"cc\",\"cfc\"]}", "expDetails": "1542|29|3211", "trackingKey": "2h7"}, "reviewDetailCardOrderB2C": {"value": "pd,dbc,pr,bpb,ppc,cd,af,td,tdc,tmbc,rtb,bc,bi,sr,pbi,ins,tc,tcv2,ca,pb", "expDetails": "1938|8|4088", "trackingKey": "35k"}, "hourlyHotelsEnabled": {"value": true, "expDetails": "2378|23|5135", "trackingKey": "3yn"}, "starRatingPill": {"value": true, "expDetails": "2075|6|4408", "trackingKey": "3eg"}, "showRevampedRatePlan": {"value": true, "expDetails": "2329|2|5007", "trackingKey": "3v3"}, "bhfData": {"value": "A"}, "htlInsuranceAddOn": {"value": true, "expDetails": "1407|18|2903", "trackingKey": "28n"}, "repeatExpHotel": {"value": false, "expDetails": "2301|6|4947", "trackingKey": "3tf"}, "newL2R": {"value": true, "expDetails": "2185|19|4683", "trackingKey": "3m3"}, "htlAvailabilityCalendar": {"value": true, "expDetails": "2157|10|4612", "trackingKey": "3k4"}, "newHotelWidget": {"value": true, "expDetails": "2301|6|4947", "trackingKey": "3tf"}, "filterTypes": {"value": "B", "expDetails": "1682|28|3518", "trackingKey": "2pq"}, "roomImagesForward": {"value": false, "expDetails": "2131|6|4554", "trackingKey": "3ii"}, "htlFilterContext": {"value": false, "expDetails": "2386|17|5152", "trackingKey": "3z4"}}, "variantKey": "3to,3v0,3yb,3z4,3ii,3k4,412,43p,3tf"}, "HOSTING": {"expDetails": ["2360|10|5090", "2388|5|5156"], "expDetailsV1": [{"exp_experiment_id": 2360, "exp_experiment_version": 10, "exp_experiment_variant_id": 5090}, {"exp_experiment_id": 2388, "exp_experiment_version": 5, "exp_experiment_variant_id": 5156}], "metadataValues": {"helpCentreBDMScreen": {"value": false, "expDetails": "2388|5|5156", "trackingKey": "3z8"}, "is_hosting_video_variant": {"value": true, "expDetails": "2360|10|5090", "trackingKey": "3xe"}}, "variantKey": ""}, "OTHERS": {"expDetails": ["2176|12|4654"], "expDetailsV1": [{"exp_experiment_id": 2176, "exp_experiment_version": 12, "exp_experiment_variant_id": 4654}], "metadataValues": {"helpCentreBDMScreen": {"value": false}, "fltbkapilatency": {"value": 1, "expDetails": "2176|12|4654", "trackingKey": "3la"}, "is_hosting_video_variant": {"value": false}}, "variantKey": ""}, "MYBIZ_CORE": {"expDetails": ["1787|14|3765", "2327|3|5004"], "expDetailsV1": [{"exp_experiment_id": 2327, "exp_experiment_version": 3, "exp_experiment_variant_id": 5004}, {"exp_experiment_id": 1787, "exp_experiment_version": 14, "exp_experiment_variant_id": 3765}], "metadataValues": {"htlSingleRatingColor": {"value": true, "expDetails": "2327|3|5004", "trackingKey": "3v0"}, "mmsme": {"value": 1, "expDetails": "1787|14|3765", "trackingKey": "2wl"}}, "variantKey": "3v0,2wl"}, "FLIGHT": {"expDetails": ["1043|17|2098", "1129|18|2296", "1140|16|2318", "2416|1|5220", "1267|18|2598", "979|17|1965", "2123|21|4539", "1051|30|2116", "1628|36|3390", "2178|20|4658", "1984|81|4200", "1466|34|3037", "1392|18|2867", "1926|12|4059", "648|16|1231", "1464|18|3033", "1766|19|3712", "544|21|1014", "794|31|2705", "1999|9|4241", "981|12|1969", "2190|15|4692", "2001|9|4245", "2081|3|4423", "304|35|1084", "958|16|1920", "413|16|741", "1755|37|3683", "741|26|1437", "2368|5|5106", "1722|18|3606", "81|11|216", "2102|15|4487", "1163|13|2366", "2271|15|4882", "1848|8|3893", "1482|33|3082", "900|90|1790", "1873|14|3946", "1325|20|2722", "2095|7|4467", "868|15|1719", "2426|14|5305", "2277|2|4894", "1068|28|2155", "846|19|1672", "1423|12|2946", "2091|11|4454", "1038|40|4406", "1007|19|2026", "847|18|1675", "1987|14|4210"], "expDetailsV1": [{"exp_experiment_id": 1038, "exp_experiment_version": 40, "exp_experiment_variant_id": 4406}, {"exp_experiment_id": 1043, "exp_experiment_version": 17, "exp_experiment_variant_id": 2098}, {"exp_experiment_id": 794, "exp_experiment_version": 31, "exp_experiment_variant_id": 2705}, {"exp_experiment_id": 1051, "exp_experiment_version": 30, "exp_experiment_variant_id": 2116}, {"exp_experiment_id": 544, "exp_experiment_version": 21, "exp_experiment_variant_id": 1014}, {"exp_experiment_id": 2081, "exp_experiment_version": 3, "exp_experiment_variant_id": 4423}, {"exp_experiment_id": 2091, "exp_experiment_version": 11, "exp_experiment_variant_id": 4454}, {"exp_experiment_id": 1068, "exp_experiment_version": 28, "exp_experiment_variant_id": 2155}, {"exp_experiment_id": 1325, "exp_experiment_version": 20, "exp_experiment_variant_id": 2722}, {"exp_experiment_id": 2095, "exp_experiment_version": 7, "exp_experiment_variant_id": 4467}, {"exp_experiment_id": 304, "exp_experiment_version": 35, "exp_experiment_variant_id": 1084}, {"exp_experiment_id": 2102, "exp_experiment_version": 15, "exp_experiment_variant_id": 4487}, {"exp_experiment_id": 1848, "exp_experiment_version": 8, "exp_experiment_variant_id": 3893}, {"exp_experiment_id": 2368, "exp_experiment_version": 5, "exp_experiment_variant_id": 5106}, {"exp_experiment_id": 2123, "exp_experiment_version": 21, "exp_experiment_variant_id": 4539}, {"exp_experiment_id": 846, "exp_experiment_version": 19, "exp_experiment_variant_id": 1672}, {"exp_experiment_id": 847, "exp_experiment_version": 18, "exp_experiment_variant_id": 1675}, {"exp_experiment_id": 81, "exp_experiment_version": 11, "exp_experiment_variant_id": 216}, {"exp_experiment_id": 1873, "exp_experiment_version": 14, "exp_experiment_variant_id": 3946}, {"exp_experiment_id": 1628, "exp_experiment_version": 36, "exp_experiment_variant_id": 3390}, {"exp_experiment_id": 868, "exp_experiment_version": 15, "exp_experiment_variant_id": 1719}, {"exp_experiment_id": 1129, "exp_experiment_version": 18, "exp_experiment_variant_id": 2296}, {"exp_experiment_id": 1392, "exp_experiment_version": 18, "exp_experiment_variant_id": 2867}, {"exp_experiment_id": 2416, "exp_experiment_version": 1, "exp_experiment_variant_id": 5220}, {"exp_experiment_id": 1140, "exp_experiment_version": 16, "exp_experiment_variant_id": 2318}, {"exp_experiment_id": 2426, "exp_experiment_version": 14, "exp_experiment_variant_id": 5305}, {"exp_experiment_id": 2178, "exp_experiment_version": 20, "exp_experiment_variant_id": 4658}, {"exp_experiment_id": 900, "exp_experiment_version": 90, "exp_experiment_variant_id": 1790}, {"exp_experiment_id": 1926, "exp_experiment_version": 12, "exp_experiment_variant_id": 4059}, {"exp_experiment_id": 648, "exp_experiment_version": 16, "exp_experiment_variant_id": 1231}, {"exp_experiment_id": 1163, "exp_experiment_version": 13, "exp_experiment_variant_id": 2366}, {"exp_experiment_id": 2190, "exp_experiment_version": 15, "exp_experiment_variant_id": 4692}, {"exp_experiment_id": 1423, "exp_experiment_version": 12, "exp_experiment_variant_id": 2946}, {"exp_experiment_id": 413, "exp_experiment_version": 16, "exp_experiment_variant_id": 741}, {"exp_experiment_id": 1464, "exp_experiment_version": 18, "exp_experiment_variant_id": 3033}, {"exp_experiment_id": 1466, "exp_experiment_version": 34, "exp_experiment_variant_id": 3037}, {"exp_experiment_id": 1722, "exp_experiment_version": 18, "exp_experiment_variant_id": 3606}, {"exp_experiment_id": 958, "exp_experiment_version": 16, "exp_experiment_variant_id": 1920}, {"exp_experiment_id": 1984, "exp_experiment_version": 81, "exp_experiment_variant_id": 4200}, {"exp_experiment_id": 1987, "exp_experiment_version": 14, "exp_experiment_variant_id": 4210}, {"exp_experiment_id": 1482, "exp_experiment_version": 33, "exp_experiment_variant_id": 3082}, {"exp_experiment_id": 1999, "exp_experiment_version": 9, "exp_experiment_variant_id": 4241}, {"exp_experiment_id": 2001, "exp_experiment_version": 9, "exp_experiment_variant_id": 4245}, {"exp_experiment_id": 979, "exp_experiment_version": 17, "exp_experiment_variant_id": 1965}, {"exp_experiment_id": 981, "exp_experiment_version": 12, "exp_experiment_variant_id": 1969}, {"exp_experiment_id": 1755, "exp_experiment_version": 37, "exp_experiment_variant_id": 3683}, {"exp_experiment_id": 2271, "exp_experiment_version": 15, "exp_experiment_variant_id": 4882}, {"exp_experiment_id": 741, "exp_experiment_version": 26, "exp_experiment_variant_id": 1437}, {"exp_experiment_id": 2277, "exp_experiment_version": 2, "exp_experiment_variant_id": 4894}, {"exp_experiment_id": 1766, "exp_experiment_version": 19, "exp_experiment_variant_id": 3712}, {"exp_experiment_id": 1007, "exp_experiment_version": 19, "exp_experiment_variant_id": 2026}, {"exp_experiment_id": 1267, "exp_experiment_version": 18, "exp_experiment_variant_id": 2598}], "metadataValues": {"WCM": {"value": 1, "expDetails": "846|19|1672", "trackingKey": "1ag"}, "PRE": {"value": 2, "expDetails": "2081|3|4423", "trackingKey": "3ev"}, "INSGHT": {"value": 1, "expDetails": "1392|18|2867", "trackingKey": "27n"}, "cnpn": {"value": 1, "expDetails": "81|11|216", "trackingKey": "60"}, "CPS": {"value": 0, "expDetails": "1999|9|4241", "trackingKey": "39t"}, "OTP": {"value": 1, "expDetails": "979|17|1965", "trackingKey": "1il"}, "mnthn": {"value": false}, "STP": {"value": 1, "expDetails": "1722|18|3606", "trackingKey": "2s6"}, "SimpF": {"value": 0}, "mstp": {"value": 0}, "FSA": {"value": 3, "expDetails": "1038|40|4406", "trackingKey": "3ee"}, "PFL": {"value": 1, "expDetails": "2426|14|5305", "trackingKey": "43d"}, "AME": {"value": 1, "expDetails": "868|15|1719", "trackingKey": "1br"}, "CYT": {"value": 1, "expDetails": "1464|18|3033", "trackingKey": "2c9"}, "mema": {"value": 1, "expDetails": "1766|19|3712", "trackingKey": "2v4"}, "NSF": {"value": 0, "expDetails": "1984|81|4200", "trackingKey": "38o"}, "mbfc": {"value": 1, "expDetails": "1873|14|3946", "trackingKey": "31m"}, "BNTD": {"value": 1, "expDetails": "1423|12|2946", "trackingKey": "29u"}, "mbrt": {"value": 1, "expDetails": "1051|30|2116", "trackingKey": "1ms"}, "mras": {"value": 1, "expDetails": "1267|18|2598", "trackingKey": "206"}, "BCG": {"value": 1, "expDetails": "1628|36|3390", "trackingKey": "2m6"}, "bntdp": {"value": 0, "expDetails": "1423|12|2946", "trackingKey": "29u"}, "IFFLK": {"value": 1, "expDetails": "2178|20|4658", "trackingKey": "3le"}, "INSBTM": {"value": 1, "expDetails": "1325|20|2722", "trackingKey": "23m"}, "mbfsme": {"value": 1, "expDetails": "1848|8|3893", "trackingKey": "305"}, "DGT": {"value": 3, "expDetails": "304|35|1084", "trackingKey": "u4"}, "FCN": {"value": true, "expDetails": "648|16|1231", "trackingKey": "y7"}, "HCP": {"value": 0, "expDetails": "2368|5|5106", "trackingKey": "3xu"}, "DTD": {"value": 2, "expDetails": "794|31|2705", "trackingKey": "235"}, "IFPLK": {"value": 1, "expDetails": "2277|2|4894", "trackingKey": "3ry"}, "ZCD": {"value": 0, "expDetails": "2190|15|4692", "trackingKey": "3mc"}, "SEM": {"value": 0}, "ZCE": {"value": 1, "expDetails": "981|12|1969", "trackingKey": "1ip"}, "CABF": {"value": 1, "expDetails": "847|18|1675", "trackingKey": "1aj"}, "mics": {"value": 0}, "INSV3": {"value": 1, "expDetails": "2416|1|5220", "trackingKey": "410"}, "ANP": {"value": 1, "expDetails": "544|21|1014", "trackingKey": "s6"}, "AFI": {"value": 1, "expDetails": "1987|14|4210", "trackingKey": "38y"}, "mctwb": {"value": 1, "expDetails": "2095|7|4467", "trackingKey": "3g3"}, "dgi": {"value": 0, "expDetails": "900|90|1790", "trackingKey": "1dq"}, "INP": {"value": 1, "expDetails": "2102|15|4487", "trackingKey": "3gn"}, "RTM": {"value": 1, "expDetails": "900|90|1790", "trackingKey": "1dq"}, "HPR": {"value": 0, "expDetails": "1755|37|3683", "trackingKey": "2ub"}, "CFAR": {"value": 1, "expDetails": "2123|21|4539", "trackingKey": "3i3"}, "PLK": {"value": 1, "expDetails": "1482|33|3082", "trackingKey": "2dm"}, "RTS": {"value": 1, "expDetails": "2271|15|4882", "trackingKey": "3rm"}, "FFBEN": {"value": 3, "expDetails": "2001|9|4245", "trackingKey": "39x"}, "LLS": {"value": 1, "expDetails": "741|26|1437", "trackingKey": "13x"}, "mrtp": {"value": 0}, "PDF": {"value": 1, "expDetails": "1466|34|3037", "trackingKey": "2cd"}, "SRT": {"value": 1, "expDetails": "958|16|1920", "trackingKey": "1hc"}, "BNPL": {"value": 1, "expDetails": "1926|12|4059", "trackingKey": "34r"}, "QBK": {"value": 0, "expDetails": "2426|14|5305", "trackingKey": "43d"}, "msfn": {"value": 1, "expDetails": "1163|13|2366", "trackingKey": "1tq"}, "mgsf": {"value": 1, "expDetails": "1140|16|2318", "trackingKey": "1se"}, "mntf": {"value": 0}, "ADDONM": {"value": 1, "expDetails": "1007|19|2026", "trackingKey": "1ka"}, "FAA": {"value": 1, "expDetails": "1068|28|2155", "trackingKey": "1nv"}, "PIF": {"value": 1, "expDetails": "2091|11|4454", "trackingKey": "3fq"}, "INSNEW": {"value": 1, "expDetails": "1325|20|2722", "trackingKey": "23m"}, "GYOLO": {"value": 0}, "BAGR": {"value": 1, "expDetails": "413|16|741", "trackingKey": "kl"}, "MCS": {"value": 1, "expDetails": "1129|18|2296", "trackingKey": "1rs"}}, "variantKey": "3fq,3g3,3gn,3xu,3i3,43d,3le,38y,2v4"}, "RAIL": {"expDetails": ["1326|39|2724", "1959|10|4147", "2121|14|4535", "2430|1|5252"], "expDetailsV1": [{"exp_experiment_id": 1326, "exp_experiment_version": 39, "exp_experiment_variant_id": 2724}, {"exp_experiment_id": 2121, "exp_experiment_version": 14, "exp_experiment_variant_id": 4535}, {"exp_experiment_id": 2430, "exp_experiment_version": 1, "exp_experiment_variant_id": 5252}, {"exp_experiment_id": 1959, "exp_experiment_version": 10, "exp_experiment_variant_id": 4147}], "metadataValues": {"railsConfirmationGuaranteeOption": {"value": 1, "expDetails": "1326|39|2724", "trackingKey": "23o"}, "LTS_Native_RN_migrations": {"value": 2, "expDetails": "1959|10|4147", "trackingKey": "377"}, "Rails_NU_LP_Banner": {"value": false, "expDetails": "2430|1|5252", "trackingKey": "41w"}, "railsAppVernacV2": {"value": true, "expDetails": "2121|14|4535", "trackingKey": "3hz"}, "railsShowFCStampOnListing": {"value": true, "expDetails": "1326|39|2724", "trackingKey": "23o"}}, "variantKey": ""}, "HOLIDAY": {"expDetails": ["1573|16|3274", "1913|5|4032", "1363|28|3071", "1894|7|3992", "2358|11|5086", "1490|19|3100", "2057|7|4367", "2445|5|5286", "1488|36|3095", "2021|13|4291", "2058|8|4370", "1257|53|2579", "1250|23|2565", "1775|29|3732", "500|13|921", "2094|5|4460", "2394|1|5170", "2274|8|4888", "1472|22|3053", "2119|10|4531", "925|69|1847", "1934|7|4077", "2253|11|4840", "2079|4|4419"], "expDetailsV1": [{"exp_experiment_id": 2057, "exp_experiment_version": 7, "exp_experiment_variant_id": 4367}, {"exp_experiment_id": 2058, "exp_experiment_version": 8, "exp_experiment_variant_id": 4370}, {"exp_experiment_id": 2079, "exp_experiment_version": 4, "exp_experiment_variant_id": 4419}, {"exp_experiment_id": 1573, "exp_experiment_version": 16, "exp_experiment_variant_id": 3274}, {"exp_experiment_id": 2094, "exp_experiment_version": 5, "exp_experiment_variant_id": 4460}, {"exp_experiment_id": 2358, "exp_experiment_version": 11, "exp_experiment_variant_id": 5086}, {"exp_experiment_id": 2119, "exp_experiment_version": 10, "exp_experiment_variant_id": 4531}, {"exp_experiment_id": 1363, "exp_experiment_version": 28, "exp_experiment_variant_id": 3071}, {"exp_experiment_id": 2394, "exp_experiment_version": 1, "exp_experiment_variant_id": 5170}, {"exp_experiment_id": 1894, "exp_experiment_version": 7, "exp_experiment_variant_id": 3992}, {"exp_experiment_id": 1913, "exp_experiment_version": 5, "exp_experiment_variant_id": 4032}, {"exp_experiment_id": 2445, "exp_experiment_version": 5, "exp_experiment_variant_id": 5286}, {"exp_experiment_id": 1934, "exp_experiment_version": 7, "exp_experiment_variant_id": 4077}, {"exp_experiment_id": 925, "exp_experiment_version": 69, "exp_experiment_variant_id": 1847}, {"exp_experiment_id": 1472, "exp_experiment_version": 22, "exp_experiment_variant_id": 3053}, {"exp_experiment_id": 2253, "exp_experiment_version": 11, "exp_experiment_variant_id": 4840}, {"exp_experiment_id": 1488, "exp_experiment_version": 36, "exp_experiment_variant_id": 3095}, {"exp_experiment_id": 1490, "exp_experiment_version": 19, "exp_experiment_variant_id": 3100}, {"exp_experiment_id": 1250, "exp_experiment_version": 23, "exp_experiment_variant_id": 2565}, {"exp_experiment_id": 2274, "exp_experiment_version": 8, "exp_experiment_variant_id": 4888}, {"exp_experiment_id": 2021, "exp_experiment_version": 13, "exp_experiment_variant_id": 4291}, {"exp_experiment_id": 1257, "exp_experiment_version": 53, "exp_experiment_variant_id": 2579}, {"exp_experiment_id": 1775, "exp_experiment_version": 29, "exp_experiment_variant_id": 3732}, {"exp_experiment_id": 500, "exp_experiment_version": 13, "exp_experiment_variant_id": 921}], "metadataValues": {"phoenixReviewSectionsExpanded": {"value": false, "expDetails": "2057|7|4367", "trackingKey": "3db"}, "enablePhoenixUi": {"value": true, "expDetails": "925|69|1847", "trackingKey": "1fb"}, "enableCarouselViewDetail": {"value": true, "expDetails": "1573|16|3274", "trackingKey": "2iy"}, "HolidaysCtaData": {"value": {"HolidaysCtaData": [{"pages": ["LISTING", "DETAILS"], "ctaIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Mobile%20Query.png", "ctaText": "Need Help", "chatIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/f36cec4f_icons_fab_chat_with_us.png", "chatText": "Chat with <PERSON><PERSON>", "callIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/bcd43e78_icons_fab_call%201.png", "callText": "Call Us", "webQueryIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/02382bc2_icons_fab_query.png", "webQueryText": "Customize your Plan", "branchLocatorText": "Find a branch", "branchLocatorIconURL": "https://jsak.mmtcdn.com/holidays/rn/images/d1af3f39_icons_fab_branch_locator.webp"}, {"pages": ["ALL"], "ctaIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Mobile%20Query.png", "ctaText": "Need Help", "chatIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/f36cec4f_icons_fab_chat_with_us.png", "chatText": "Chat with <PERSON><PERSON>", "callIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/bcd43e78_icons_fab_call%201.png", "callText": "Call Us", "webQueryIconUrl": "https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/02382bc2_icons_fab_query.png", "webQueryText": "Customize your Plan", "branchLocatorText": "Find a branch", "branchLocatorIconURL": "https://jsak.mmtcdn.com/holidays/rn/images/d1af3f39_icons_fab_branch_locator.webp"}]}, "expDetails": "2021|13|4291", "trackingKey": "3b7"}, "phoenixreviewsectionsorder": {"value": "0,1,2,3,4", "expDetails": "2058|8|4370", "trackingKey": "3de"}, "showQueryFormV2": {"value": true, "expDetails": "1488|36|3095", "trackingKey": "2dz"}, "showHECardonDetail": {"value": true, "expDetails": "1250|23|2565", "trackingKey": "1z9"}, "showNewDetailV3": {"value": true, "expDetails": "1257|53|2579", "trackingKey": "1zn"}, "enablePhoenixReview": {"value": true, "expDetails": "1775|29|3732", "trackingKey": "2vo"}, "showHotelCollectionCard": {"value": true, "expDetails": "1934|7|4077", "trackingKey": "359"}, "cuesConfig": {"value": {"landing": [], "listing": [], "map": [], "detail": [], "detailV2": [{"key": "editSearch", "text": "Edit your travel date and number of travelers from here"}, {"key": "variantFDSelection", "text": "View and Edit Package Features from here"}, {"key": "changeOrRemove", "text": "Change/Remove flight, hotels, transfers or activites in simple steps"}, {"key": "query", "text": "Our Travel Expert will get in touch with you to help plan your holiday"}, {"key": "summaryTab", "text": "Check out the Package Summary from here"}, {"key": "customizationWidget", "type": "dynamic", "text": "Easily view your customizations from here"}], "grouping": [{"key": "query", "text": "You can easily connect with us using call and chat options."}, {"key": "modifySearch", "text": "Edit your Travel Dates & Destination from here"}, {"key": "filter", "text": "Easily change your Budget, Duration & Hotel Category from here"}], "fph": [{"key": "hotel", "text": "Choose your preferred hotel by clicking here"}, {"key": "traveller", "text": "Fill in travellers' details here"}, {"key": "policy", "text": "Check out your cancellation policies for the combo"}, {"key": "coupon", "text": "Check out special discount offered on the combo"}], "psm": [{"key": "optionHeader", "text": "You can switch between the options here"}, {"key": "modifyItinerary", "text": "You can easily modify your itinerary or connect with your travel expert "}], "psmModify": [{"key": "changeOrRemove", "text": "Change/Remove flights, hotels, transfers or activities in simple steps"}, {"key": "customizationWidget", "text": "Easily view and modify your package from here"}, {"key": "continue", "text": "You can easily modify your itinerary or connect with your travel expert "}]}, "expDetails": "1363|28|3071", "trackingKey": "2db"}, "showActivityCollectionCard": {"value": true, "expDetails": "1934|7|4077", "trackingKey": "359"}, "showQuotesCompare": {"value": true, "expDetails": "1894|7|3992", "trackingKey": "32w"}, "enablePhoenixListing": {"value": true, "expDetails": "1490|19|3100", "trackingKey": "2e4"}, "showPriceChangeDetails": {"value": true, "expDetails": "2094|5|4460", "trackingKey": "3fw"}, "showNewPhoenixCard": {"value": true, "expDetails": "1472|22|3053", "trackingKey": "2ct"}, "showOvernightFlightsFilterNew": {"value": true, "expDetails": "2079|4|4419", "trackingKey": "3er"}, "coachmarkDaysDelayHol": {"value": 30, "expDetails": "500|13|921", "trackingKey": "pl"}, "enableHolidaysGroupingSection": {"value": "COM,DEST,UGCS", "expDetails": "2253|11|4840", "trackingKey": "3qg"}, "enablePhoenixSearchPage": {"value": true, "expDetails": "2394|1|5170", "trackingKey": "3zm"}, "enableGeoLoc": {"value": true, "expDetails": "2445|5|5286", "trackingKey": "42u"}, "enableGroupingGalleryWidget": {"value": true, "expDetails": "2274|8|4888", "trackingKey": "3rs"}, "landingSearchFilterNew": {"value": {"searchV2": true, "menuList": true, "filter": true}, "expDetails": "2445|5|5286", "trackingKey": "42u"}, "phoenixVarinatType": {"value": "others", "expDetails": "2119|10|4531", "trackingKey": "3hv"}, "excludedInterventionTypes": {"value": "FILTER, ROADBLOCK", "expDetails": "2358|11|5086", "trackingKey": "3xa"}, "Offertimer_Mobile": {"value": true, "expDetails": "1913|5|4032", "trackingKey": "340"}}, "variantKey": "3fw,3xa,3hv,42u,359,3qg,2dz,3rs,3b7,2vo,pl"}, "COMMON": {"expDetails": ["2099|11|4480", "2378|23|5135", "1794|8|3779", "2018|9|4850", "2031|10|4315", "1840|6|3876", "2121|14|4535", "1577|23|3283", "2093|6|4459", "1199|19|2449", "2281|7|4903", "1805|8|3801", "2455|4|5310", "1804|5|3800", "2459|1|5318", "1773|14|3743", "576|24|1081", "1906|11|4018", "2422|1|5233", "1986|14|4208", "1979|16|4191", "1247|19|2559"], "expDetailsV1": [{"exp_experiment_id": 1794, "exp_experiment_version": 8, "exp_experiment_variant_id": 3779}, {"exp_experiment_id": 1804, "exp_experiment_version": 5, "exp_experiment_variant_id": 3800}, {"exp_experiment_id": 1805, "exp_experiment_version": 8, "exp_experiment_variant_id": 3801}, {"exp_experiment_id": 1577, "exp_experiment_version": 23, "exp_experiment_variant_id": 3283}, {"exp_experiment_id": 2093, "exp_experiment_version": 6, "exp_experiment_variant_id": 4459}, {"exp_experiment_id": 1840, "exp_experiment_version": 6, "exp_experiment_variant_id": 3876}, {"exp_experiment_id": 2099, "exp_experiment_version": 11, "exp_experiment_variant_id": 4480}, {"exp_experiment_id": 576, "exp_experiment_version": 24, "exp_experiment_variant_id": 1081}, {"exp_experiment_id": 2121, "exp_experiment_version": 14, "exp_experiment_variant_id": 4535}, {"exp_experiment_id": 2378, "exp_experiment_version": 23, "exp_experiment_variant_id": 5135}, {"exp_experiment_id": 1906, "exp_experiment_version": 11, "exp_experiment_variant_id": 4018}, {"exp_experiment_id": 2422, "exp_experiment_version": 1, "exp_experiment_variant_id": 5233}, {"exp_experiment_id": 2455, "exp_experiment_version": 4, "exp_experiment_variant_id": 5310}, {"exp_experiment_id": 2459, "exp_experiment_version": 1, "exp_experiment_variant_id": 5318}, {"exp_experiment_id": 1199, "exp_experiment_version": 19, "exp_experiment_variant_id": 2449}, {"exp_experiment_id": 1979, "exp_experiment_version": 16, "exp_experiment_variant_id": 4191}, {"exp_experiment_id": 1986, "exp_experiment_version": 14, "exp_experiment_variant_id": 4208}, {"exp_experiment_id": 1247, "exp_experiment_version": 19, "exp_experiment_variant_id": 2559}, {"exp_experiment_id": 2018, "exp_experiment_version": 9, "exp_experiment_variant_id": 4850}, {"exp_experiment_id": 2281, "exp_experiment_version": 7, "exp_experiment_variant_id": 4903}, {"exp_experiment_id": 1773, "exp_experiment_version": 14, "exp_experiment_variant_id": 3743}, {"exp_experiment_id": 2031, "exp_experiment_version": 10, "exp_experiment_variant_id": 4315}], "metadataValues": {"hotelWishlist": {"value": true, "expDetails": "1906|11|4018", "trackingKey": "33m"}, "mecb": {"value": 0}, "blueBGHotelSearchForm": {"value": false, "expDetails": "1199|19|2449", "trackingKey": "1w1"}, "ratingVarient_in_PERSONAL_hotelty_dom_ANDROID": {"value": "IARP", "expDetails": "1805|8|3801", "trackingKey": "2xl"}, "newHomeUserAction": {"value": true, "expDetails": "1247|19|2559", "trackingKey": "1z3"}, "ratingVarient_in_PERSONAL_hotelty_intl_ANDROID": {"value": "UIR", "expDetails": "1804|5|3800", "trackingKey": "2xk"}, "railsAppVernacV2": {"value": true, "expDetails": "2121|14|4535", "trackingKey": "3hz"}, "HomePageLayoutConfig_B2B": {"value": {}}, "HomePageLayoutConfig_B2C": {"value": {"layout": "INB2C_BOOKABILITY", "variant": "3982"}, "expDetails": "2378|23|5135", "trackingKey": "3yn"}, "accountCardOrdering": {"value": "LIN1,PC2,B<PERSON>H3,DD4,MT5,WL6,HS7,SVC8,CT9,DD10,MW11,MGC12,SC13,UPI14,DD15,RP16,LOG17", "expDetails": "1773|14|3743", "trackingKey": "2vz"}, "HR_New_Icon": {"value": true, "expDetails": "2093|6|4459", "trackingKey": "3fv"}, "ratingVarient_in_PERSONAL_flightty_dom_ANDROID": {"value": "IARP", "expDetails": "1794|8|3779", "trackingKey": "2wz"}, "SpinAndWin_MMT0": {"value": false}, "splashMaxDelayInMillis": {"value": 3000, "expDetails": "2459|1|5318", "trackingKey": "43q"}, "hotelLandingFilters": {"value": true, "expDetails": "1199|19|2449", "trackingKey": "1w1"}, "imageAnimationEnabled": {"value": "\"VILLA\"", "expDetails": "2018|9|4850", "trackingKey": "3qq"}, "Hotels_Landing_Adtech_Sequence": {"value": "Adtech4"}, "socialLoginAndroid": {"value": true, "expDetails": "2099|11|4480", "trackingKey": "3gg"}, "personlised_offers_app": {"value": "hidebankoffers_newuser"}, "mybiz_hourly_rentals_new": {"value": true, "expDetails": "1986|14|4208", "trackingKey": "38w"}, "NotificationOmnitureLogging": {"value": true, "expDetails": "2455|4|5310", "trackingKey": "43i"}, "newHotelLandingPage": {"value": true, "expDetails": "1199|19|2449", "trackingKey": "1w1"}, "HubbleFeedFullpageAd": {"value": true, "expDetails": "2281|7|4903", "trackingKey": "3s7"}, "hp_background": {"value": "null"}, "offer_entry_topfold2": {"value": "hide_offer_topfold"}, "hourlyHotelsEnabled": {"value": true, "expDetails": "2378|23|5135", "trackingKey": "3yn"}, "localDropOffNotifications": {"value": true, "expDetails": "1577|23|3283", "trackingKey": "2j7"}, "hubbleShopperDestIntent": {"value": false, "expDetails": "2031|10|4315", "trackingKey": "3bv"}, "b2c_hourly_rentals": {"value": true, "expDetails": "1979|16|4191", "trackingKey": "38f"}, "HubbleDestBookingOptionsExpand": {"value": false, "expDetails": "2422|1|5233", "trackingKey": "41d"}, "ratingVarient_in_PERSONAL_personalization_ANDROID": {"value": "UIR", "expDetails": "1840|6|3876", "trackingKey": "2zo"}}, "variantKey": "2j7,33m,3qq"}, "TRIPMONEY": {"expDetails": [], "expDetailsV1": [], "metadataValues": {"forex_bmf_funnel": {"value": false}}}, "FLIGHT_INT": {"expDetails": ["648|16|1231", "1766|19|3712", "1068|28|2155", "81|11|216", "544|21|1014", "1423|12|2946", "1140|16|2318", "2091|11|4454", "2416|1|5220", "2102|15|4487", "1163|13|2366", "1267|18|2598", "1038|40|4406", "1848|8|3893", "2001|9|4245", "2123|21|4539", "1628|36|3390", "2178|20|4658", "1873|14|3946", "2081|3|4423", "413|16|741", "741|26|1437", "1325|20|2722", "2095|7|4467"], "expDetailsV1": [{"exp_experiment_id": 1038, "exp_experiment_version": 40, "exp_experiment_variant_id": 4406}, {"exp_experiment_id": 544, "exp_experiment_version": 21, "exp_experiment_variant_id": 1014}, {"exp_experiment_id": 2081, "exp_experiment_version": 3, "exp_experiment_variant_id": 4423}, {"exp_experiment_id": 2091, "exp_experiment_version": 11, "exp_experiment_variant_id": 4454}, {"exp_experiment_id": 1068, "exp_experiment_version": 28, "exp_experiment_variant_id": 2155}, {"exp_experiment_id": 1325, "exp_experiment_version": 20, "exp_experiment_variant_id": 2722}, {"exp_experiment_id": 2095, "exp_experiment_version": 7, "exp_experiment_variant_id": 4467}, {"exp_experiment_id": 2102, "exp_experiment_version": 15, "exp_experiment_variant_id": 4487}, {"exp_experiment_id": 1848, "exp_experiment_version": 8, "exp_experiment_variant_id": 3893}, {"exp_experiment_id": 2123, "exp_experiment_version": 21, "exp_experiment_variant_id": 4539}, {"exp_experiment_id": 81, "exp_experiment_version": 11, "exp_experiment_variant_id": 216}, {"exp_experiment_id": 1873, "exp_experiment_version": 14, "exp_experiment_variant_id": 3946}, {"exp_experiment_id": 1628, "exp_experiment_version": 36, "exp_experiment_variant_id": 3390}, {"exp_experiment_id": 2416, "exp_experiment_version": 1, "exp_experiment_variant_id": 5220}, {"exp_experiment_id": 1140, "exp_experiment_version": 16, "exp_experiment_variant_id": 2318}, {"exp_experiment_id": 2178, "exp_experiment_version": 20, "exp_experiment_variant_id": 4658}, {"exp_experiment_id": 648, "exp_experiment_version": 16, "exp_experiment_variant_id": 1231}, {"exp_experiment_id": 1163, "exp_experiment_version": 13, "exp_experiment_variant_id": 2366}, {"exp_experiment_id": 1423, "exp_experiment_version": 12, "exp_experiment_variant_id": 2946}, {"exp_experiment_id": 413, "exp_experiment_version": 16, "exp_experiment_variant_id": 741}, {"exp_experiment_id": 2001, "exp_experiment_version": 9, "exp_experiment_variant_id": 4245}, {"exp_experiment_id": 741, "exp_experiment_version": 26, "exp_experiment_variant_id": 1437}, {"exp_experiment_id": 1766, "exp_experiment_version": 19, "exp_experiment_variant_id": 3712}, {"exp_experiment_id": 1267, "exp_experiment_version": 18, "exp_experiment_variant_id": 2598}], "metadataValues": {"PRE": {"value": 2, "expDetails": "2081|3|4423", "trackingKey": "3ev"}, "INSV3": {"value": 1, "expDetails": "2416|1|5220", "trackingKey": "410"}, "ANP": {"value": 1, "expDetails": "544|21|1014", "trackingKey": "s6"}, "mctwb": {"value": 1, "expDetails": "2095|7|4467", "trackingKey": "3g3"}, "cnpn": {"value": 1, "expDetails": "81|11|216", "trackingKey": "60"}, "INP": {"value": 1, "expDetails": "2102|15|4487", "trackingKey": "3gn"}, "CFAR": {"value": 1, "expDetails": "2123|21|4539", "trackingKey": "3i3"}, "FFBEN": {"value": 3, "expDetails": "2001|9|4245", "trackingKey": "39x"}, "LLS": {"value": 1, "expDetails": "741|26|1437", "trackingKey": "13x"}, "FSA": {"value": 3, "expDetails": "1038|40|4406", "trackingKey": "3ee"}, "msfn": {"value": 1, "expDetails": "1163|13|2366", "trackingKey": "1tq"}, "mgsf": {"value": 1, "expDetails": "1140|16|2318", "trackingKey": "1se"}, "mema": {"value": 1, "expDetails": "1766|19|3712", "trackingKey": "2v4"}, "mbfc": {"value": 1, "expDetails": "1873|14|3946", "trackingKey": "31m"}, "BNTD": {"value": 1, "expDetails": "1423|12|2946", "trackingKey": "29u"}, "mras": {"value": 1, "expDetails": "1267|18|2598", "trackingKey": "206"}, "FAA": {"value": 1, "expDetails": "1068|28|2155", "trackingKey": "1nv"}, "BCG": {"value": 1, "expDetails": "1628|36|3390", "trackingKey": "2m6"}, "PIF": {"value": 1, "expDetails": "2091|11|4454", "trackingKey": "3fq"}, "bntdp": {"value": 0, "expDetails": "1423|12|2946", "trackingKey": "29u"}, "IFFLK": {"value": 1, "expDetails": "2178|20|4658", "trackingKey": "3le"}, "INSBTM": {"value": 1, "expDetails": "1325|20|2722", "trackingKey": "23m"}, "mbfsme": {"value": 1, "expDetails": "1848|8|3893", "trackingKey": "305"}, "FCN": {"value": true, "expDetails": "648|16|1231", "trackingKey": "y7"}, "INSNEW": {"value": 1, "expDetails": "1325|20|2722", "trackingKey": "23m"}, "BAGR": {"value": 1, "expDetails": "413|16|741", "trackingKey": "kl"}}, "variantKey": "3fq,3g3,3gn,3i3,3le,2v4"}}}