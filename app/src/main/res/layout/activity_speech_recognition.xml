<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="24dp">

    <!-- Container for the mic and wave animation -->
    <FrameLayout
        android:id="@+id/iv_mic_container"

        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center">
    </FrameLayout>


    <ImageView
            android:id="@+id/iv_mic"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@android:drawable/ic_btn_speak_now"
            android:contentDescription="Microphone button" />

    <TextView
        android:id="@+id/tv_speech_to_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:textSize="24sp"
        android:text="Tap mic to speak" />
</LinearLayout> 