<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/ll_images_dummy"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="@dimen/margin_large"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imageView_1"
            android:layout_width="@dimen/dimen_my_trip_card_item_height"
            android:layout_height="@dimen/dimen_my_trip_card_item_height"
            android:background="@drawable/htl_ugc_placeholder_bg"
            android:layout_marginEnd="@dimen/margin_large" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imageView_2"
            android:layout_width="@dimen/dimen_my_trip_card_item_height"
            android:layout_height="@dimen/dimen_my_trip_card_item_height"
            android:background="@drawable/htl_ugc_placeholder_bg"
            android:layout_marginEnd="@dimen/margin_large"/>

        <TextView
            android:id="@+id/tv_reload_images"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/margin_large"
            android:text="Reload Images"
            android:gravity="center"
            android:textColor="@color/white"
            android:background="@drawable/blue_button_rectangle_background"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>