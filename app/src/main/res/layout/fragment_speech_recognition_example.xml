<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="24dp">

    <ImageView
        android:id="@+id/speech_mic_button"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:src="@android:drawable/ic_btn_speak_now"
        android:contentDescription="Microphone button" />

    <TextView
        android:id="@+id/speech_result_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:textSize="20sp"
        android:text="Tap microphone to speak"
        android:textAlignment="center" />

</LinearLayout> 