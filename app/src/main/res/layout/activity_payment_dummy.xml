<?xml version="1.0" encoding="utf-8"?>

<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingBottom="@dimen/margin_xhuge">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/header_height"
            android:background="@color/white"
            android:elevation="@dimen/elevation_normal"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/image_dimen_xmedium"
                android:layout_height="@dimen/image_dimen_xmedium"
                android:layout_margin="@dimen/margin_large"
                android:padding="@dimen/margin_tiny"
                android:src="@drawable/ic_htl_back" />

            <com.mmt.uikit.MmtTextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/lato_black"
                android:gravity="center_vertical"
                android:text="Dummy Payment Page"
                android:textColor="@color/black"
                android:textSize="@dimen/htl_text_size_medium" />
        </LinearLayout>

       <TextView
           android:layout_width="match_parent"
           android:layout_height="0dp"
           android:gravity="center"
           android:layout_weight="1"
           android:textSize="@dimen/margin_xhuge"
           android:text="Kya karoge payment krke.... \nJao maaf kiya !"/>

        <TextView
            android:id="@+id/tv_go_to_thank_you_page"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Go To Thankyou Page"
            android:background="@drawable/bg_button_gradient"
            android:padding="@dimen/margin_medium"
            android:layout_gravity="center_horizontal"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:textSize="@dimen/margin_large"
            android:layout_margin="@dimen/margin_large"/>

    </LinearLayout>

</layout>