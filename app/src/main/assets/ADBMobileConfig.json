{"lastModified": "2017-10-17T14:16:51.001Z", "marketingCloud": {"org": "1E0D22CE527845790A490D4D@AdobeOrg"}, "target": {"clientCode": "", "timeout": 5}, "audienceManager": {"server": "", "analyticsForwardingEnabled": false, "timeout": 5}, "acquisition": {"server": "c00.adobe.com", "appid": "f51dc1d3-d8bd-4943-8403-24e1f7e6a2e8"}, "analytics": {"rsids": "mmtmobileapp", "server": "metrics.makemytrip.com", "ssl": true, "offlineEnabled": false, "charset": "UTF-8", "lifecycleTimeout": 300, "privacyDefault": "<PERSON>in", "batchLimit": 0, "timezone": "IST", "timezoneOffset": 330, "referrerTimeout": 0, "backdateSessionInfo": false, "poi": []}, "messages": [{"messageId": "224975dc-5879-45dd-8352-85e8e82d497b", "payload": {"templateurl": "http://pixel.everesttech.net/{a.acquisition.custom.amo.userid}/t/amstracking?event={a.action}&ev_idfa={%adid%}&ev_droid_id={%adid%}&ev_ckid={a.acquisition.custom.amo.clickid}&ev_sid={a.acquisition.custom.amo.sid}&ev_ln={a.acquisition.custom.amo.ln}&ev_lx={a.acquisition.custom.amo.targetid}&ev_crx={a.acquisition.custom.amo.crx}&ev_mt={a.acquisition.custom.amo.mt}&ev_n={a.acquisition.custom.amo.n}&ev_ltx={a.acquisition.custom.amo.ltx}&ev_pl={a.acquisition.custom.amo.pl}&ev_pos={a.acquisition.custom.amo.pos}&ev_dvc={a.acquisition.custom.amo.dvc}&ev_dvm={a.acquisition.custom.amo.dvm}&ev_phy={a.acquisition.custom.amo.phy}&ev_loc={a.acquisition.custom.amo.loc}&ev_cx={a.acquisition.custom.amo.cx}&ev_ax={a.acquisition.custom.amo.ax}&ev_clktime={%timestampu%}", "templatebody": "", "contenttype": "", "timeout": 2}, "showOffline": true, "showRule": "always", "endDate": 2524730400, "startDate": 0, "template": "callback", "audiences": [{"key": "a.acquisition.custom.amo.userid", "matches": "ex", "values": null}], "triggers": [{"key": "pev2", "matches": "eq", "values": ["AMACTION:Purchase", "AMACTION:event2", "AMACTION:event3", "AMACTION:mob:funnel:domestic flights:listing", "AMACTION:mob:funnel:domestic hotels:interstitial", "AMACTION:mob:funnel:domestic flights:interstitial", "AMACTION:mob:funnel:domestic hotels:listing", "AMACTION:mob:funnel:intl hotels:listing", "AMACTION:event"]}]}, {"messageId": "095098f7-cb22-4d23-8c00-f1255ce4b4da", "payload": {"templateurl": "http://pixel.everesttech.net/{a.acquisition.custom.amo.userid}/t/amstracking?event=install&ev_idfa={%adid%}&ev_droid_id={%adid%}&ev_ckid={a.acquisition.custom.amo.clickid}&ev_sid={a.acquisition.custom.amo.sid}&ev_ln={a.acquisition.custom.amo.ln}&ev_lx={a.acquisition.custom.amo.targetid}&ev_crx={a.acquisition.custom.amo.crx}&ev_mt={a.acquisition.custom.amo.mt}&ev_n={a.acquisition.custom.amo.n}&ev_ltx={a.acquisition.custom.amo.ltx}&ev_pl={a.acquisition.custom.amo.pl}&ev_pos={a.acquisition.custom.amo.pos}&ev_dvc={a.acquisition.custom.amo.dvc}&ev_dvm={a.acquisition.custom.amo.dvm}&ev_phy={a.acquisition.custom.amo.phy}&ev_loc={a.acquisition.custom.amo.loc}&ev_cx={a.acquisition.custom.amo.cx}&ev_ax={a.acquisition.custom.amo.ax}&ev_clktime={%timestampu%}", "templatebody": "", "contenttype": "", "timeout": 2}, "showOffline": true, "showRule": "always", "endDate": 2524730400, "startDate": 0, "template": "callback", "audiences": [{"key": "a.acquisition.custom.amo.userid", "matches": "ex", "values": null}], "triggers": [{"key": "<PERSON><PERSON>", "matches": "ex", "values": null}]}, {"messageId": "ee264c2f-d398-4151-a50c-aaf41dcab2cb", "payload": {"templateurl": "https://docs.google.com/forms/d/e/1FAIpQLSc8E0pdMNv20FKKeaNsAZjvH-wFgNQiPV7WX2p0JSA7uhWN7A/formResponse", "templatebody": "ZW50cnkuMjA3Mzc3Mzg3OD17YS5hY3Rpb259JmVudHJ5LjIwMzM3MjgwNjk9eyVhZGlkJX0mZW50cnkuMTAxMjMyNjg1ND17JWFkaWQlfSZlbnRyeS44MzI4NDQxNDM9e2EuYWNxdWlzaXRpb24uY3VzdG9tLmFtby5jbGlja2lkfSZlbnRyeS4xNDIyNDU2OTgyPXthLmFjcXVpc2l0aW9uLmN1c3RvbS5hbW8uc2lkfSZlbnRyeS40ODUyNDU3MjY9e2EuYWNxdWlzaXRpb24uY3VzdG9tLmFtby5sbn0mZW50cnkuMjE0MTk3ODcwNz17YS5hY3F1aXNpdGlvbi5jdXN0b20uYW1vLnRhcmdldGlkfSZlbnRyeS4xNDEyNzUyNDgxPXthLmFjcXVpc2l0aW9uLmN1c3RvbS5hbW8uY3J4fSZlbnRyeS40MzU0Mjk0NjM9e2EuYWNxdWlzaXRpb24uY3VzdG9tLmFtby5tdH0mZW50cnkuMTIxMTg1MzU4Nz17YS5hY3F1aXNpdGlvbi5jdXN0b20uYW1vLm59JmVudHJ5LjUzODM3MzAwNj17YS5hY3F1aXNpdGlvbi5jdXN0b20uYW1vLmx0eH0mZW50cnkuMTk0MDMwMDgzPXthLmFjcXVpc2l0aW9uLmN1c3RvbS5hbW8ucGx9JmVudHJ5LjQzNDY0MzAwMj17YS5hY3F1aXNpdGlvbi5jdXN0b20uYW1vLnBvc30mZW50cnkuNTE0NzkxNTE2PXthLmFjcXVpc2l0aW9uLmN1c3RvbS5hbW8uZHZjfSZlbnRyeS4xNzE5MTA0NTM2PXthLmFjcXVpc2l0aW9uLmN1c3RvbS5hbW8uZHZtfSZlbnRyeS42NTA1Mzk4NTU9e2EuYWNxdWlzaXRpb24uY3VzdG9tLmFtby5waHl9JmVudHJ5LjU2MjUzODc4ND17YS5hY3F1aXNpdGlvbi5jdXN0b20uYW1vLmxvY30mZW50cnkuNzA0OTgwMDgyPXthLmFjcXVpc2l0aW9uLmN1c3RvbS5hbW8uY3h9JmVudHJ5LjExNjY3MzM2ODM9e2EuYWNxdWlzaXRpb24uY3VzdG9tLmFtby5heH0mZW50cnkuMTE2NjczMzY4Mz17JXRpbWVzdGFtcHUlfQ==", "contenttype": "application/x-www-form-urlencoded", "timeout": 2}, "showOffline": true, "showRule": "always", "endDate": 2524730400, "startDate": 0, "template": "callback", "audiences": [{"key": "a.acquisition.custom.amo.userid", "matches": "ex", "values": null}], "triggers": [{"key": "pev2", "matches": "eq", "values": ["AMACTION:event2", "AMACTION:event3", "AMACTION:mob:funnel:domestic hotels:interstitial", "AMACTION:mob:funnel:domestic flights:listing", "AMACTION:mob:funnel:domestic hotels:listing", "AMACTION:mob:funnel:domestic flights:interstitial", "AMACTION:mob:funnel:intl hotels:listing", "AMACTION:Purchase", "AMACTION:erent2", "AMACTION:event\""]}]}], "remotes": {"analytics.poi": "https://assets.adobedtm.com/b213090c5204bf94318f4ef0539a38b487d10368/scripts/satellite-54fea8803437640019330700.json", "messages": "https://assets.adobedtm.com/b213090c5204bf94318f4ef0539a38b487d10368/scripts/satellite-54fea8806331310016f30500.json"}}