<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />


    <application
        android:name=".HotelApplication"
        android:icon="@drawable/icon_hs"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@drawable/icon_hs"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="supportsRtl">
        <profileable
            android:shell="true"
            tools:targetApi="q" />
        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/IDS_GOOGLE_MAPS_API_KEY" />

        <meta-data
            android:name="firebase_performance_logcat_enabled"
            android:value="${firebasePerfLogcat}" />

        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-8823029799075194~8510884212" />
        <!--     Optimize Google Mobile Ads SDK initialization    -->
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true" />
        <!--     Optimize Google Mobile Ad Loading    -->
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true" />
        <service
            android:name="com.mmt.hotel.service.AnimationDownloadJobService"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <activity
            android:name="com.mmthoteldev.hotelapp.ui.TestXMLActivity"
            android:exported="true"
            android:launchMode="singleTask" />

        <activity
            android:name=".launch.HotelAppActivity"
            android:allowTaskReparenting="false"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="mmt.intent.action.LAUNCH_HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="mmyt" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <data android:scheme="mmyt" />
                <data android:host="open" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="applinks.makemytrip.com" />
                <data
                    android:scheme="https"
                    android:host="makemytrip-alternate.app.link" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="https"
                    android:host="applinks.makemytrip.com" />
                <data
                    android:scheme="https"
                    android:host="makemytrip-alternate.app.link" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="mmyt.onelink.me"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="app.mmyt.co"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="mmyt.app"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter><action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />
                <data android:host="mybiz.makemytrip.com" />
                <data android:pathPattern="/.*" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!--this is intentionally done for specific path handling don't remove this , Avoid below warning-->
                <data
                    android:host="mmyt.com"
                    android:path="/dl"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:path="/"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:path="/"
                    android:scheme="https" />

                <data
                    android:host="tripmoney.makemytrip.com"
                    android:pathPrefix="/"
                    android:scheme="https" />

                <data
                    android:host="tripmoney.makemytrip.com"
                    android:pathPrefix="/"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.ae"
                    android:path="/"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.ae"
                    android:path="/"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:path=""
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:path=""
                    android:scheme="https" />


                <data
                    android:host="supportz.makemytrip.com"
                    android:pathPrefix="/MyAccount/Communication/param"
                    android:scheme="http" />

                <data
                    android:host="supportz.makemytrip.com"
                    android:pathPrefix="/MyAccount/Communication/param"
                    android:scheme="https" />
                <data
                    android:host="flyfish.makemytrip.com"
                    android:path="/flyfish/feedbackform"
                    android:scheme="https" />
                <data
                    android:host="mybiz.makemytrip.com"
                    android:path="/userverify"
                    android:scheme="https" />
                <data
                    android:host="mybiz.makemytrip.com"
                    android:path="/invite/"
                    android:scheme="https" />

            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_HOTEL_LANDING_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/rightstay"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/rightstay"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_HOTEL_LANDING_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Accepts URIs that begin with "http://www.makemytrip.com/" -->
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/hotels"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/hotels"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/homestays"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/homestays"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/hotel-listing"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/hotel-details"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/hotel-listing"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/hotel-details"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/site/hotels/detail"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/site/hotels/detail"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/site/hotels/search"
                    android:scheme="http" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/site/hotels/search"
                    android:scheme="https" />

            </intent-filter>
            <intent-filter android:autoVerify="true">
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.SEND" />

                <category android:name="android.intent.category.BROWSABLE" />
                <!--DOM url filter -->
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flights"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/international-flights"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flights"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/international-flights"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_FLIGHT_LISTING_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/search"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/search"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/sem/search"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/sem/search"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/review"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/review"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/inc_ind"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/flight/inc_ind"
                    android:scheme="https" />

            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_FLIGHT_LISTING_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/cabs"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/car-rental"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_FLIGHT_LISTING_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="cabs.makemytrip.com"
                    android:pathPrefix="/"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_FLIGHT_LISTING_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="mbus.makemytrip.com"
                    android:pathPrefix="/"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/bus-tickets"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_FLIGHT_LISTING_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="rails.makemytrip.com"
                    android:pathPattern=".*"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/MAKEMYTRIP">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="payments.makemytrip.com"
                    android:pathPattern="/ui/easypay/"
                    android:scheme="https" />
                <data
                    android:host="payments.makemytrip.com"
                    android:pathPattern="/ui/easypay"
                    android:scheme="https" />
                <data
                    android:host="payments.makemytrip.com"
                    android:pathPattern="/easypay/"
                    android:scheme="https" />
                <data
                    android:host="payments.makemytrip.com"
                    android:pathPattern="/easypay"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_VISA_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="visa.makemytrip.com"
                    android:pathPattern=".*"
                    android:scheme="http" />

                <data
                    android:host="visa.makemytrip.com"
                    android:pathPattern=".*"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_ACME_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="experiences.makemytrip.com"
                    android:pathPattern=".*"
                    android:scheme="http" />

                <data
                    android:host="experiences.makemytrip.com"
                    android:pathPattern=".*"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_RAILS_LINK">

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/rails"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/rails"
                    android:scheme="http" />
            </intent-filter>

            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_HOLIDAY_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/india.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/india.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays-india.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays-india.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/international.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/international.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays-international.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays-international.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/sendQuery.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/sendQuery.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/reArchBookingReviewAndPaymentAction.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/reArchBookingReviewAndPaymentAction.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/chat.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/chat.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/referAndEarn.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/referAndEarn.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/shortlistedPackages.*"
                    android:scheme="http" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/shortlistedPackages.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/sme.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/psm.*"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter
                android:autoVerify="true"
                android:label="@string/INTENT_HOLIDAY_LINK">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays-india.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays-india.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/india.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/india.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays-international.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays-international.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/international.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/international.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/sendQuery.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/sendQuery.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/reArchBookingReviewAndPaymentAction.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/reArchBookingReviewAndPaymentAction.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/chat.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/chat.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/referAndEarn.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/referAndEarn.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/shortlistedPackages.*"
                    android:scheme="http" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/shortlistedPackages.*"
                    android:scheme="https" />
                <data
                    android:host="holidayz.makemytrip.com"
                    android:pathPattern="/holidays/psm.*"
                    android:scheme="https" />
                <data
                    android:host="www.makemytrip.com"
                    android:pathPattern="/holidays/sme.*"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/hotels"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/homestays"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/hotel-listing"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/hotel-details"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/site/hotels/detail"
                    android:scheme="https" />

                <data
                    android:host="www.makemytrip.com"
                    android:pathPrefix="/pwa/site/hotels/search"
                    android:scheme="https" />

                <data
                    android:host="flyfish.makemytrip.com"
                    android:path="/flyfish/feedbackform"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.SpeechRecognitionActivity"
            android:exported="false"
            android:theme="@style/Theme.CosmosTheme">
<!--            <intent-filter>-->
<!--                <action android:name="mmt.intent.action.HOTEL_DUMMY_THANK_YOU" />-->

<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
        </activity>

        <activity
            android:name=".ui.DummyThankYouActivity"
            android:exported="false"
            android:theme="@style/Theme.CosmosTheme">
            <intent-filter>
                <action android:name="mmt.intent.action.HOTEL_DUMMY_THANK_YOU" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.DummyPaymentActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:theme="@style/PaymentActivityTheme.Base"
            android:windowSoftInputMode="stateVisible|adjustResize">
            <intent-filter>
                <action android:name="mmt.intent.action.PAYMENT_HOME_V2" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

    </application>

</manifest>