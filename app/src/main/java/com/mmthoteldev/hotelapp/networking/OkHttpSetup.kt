package com.mmthoteldev.hotelapp.networking

import com.gommt.network.IOkHttpSetup
import com.mmt.network.utils.NetworkMigrationHelper
import kotlinx.coroutines.runBlocking

class OkHttpSetup: IOkHttpSetup {
    override fun getDefaultHeaders(url: String): Map<String, String> {
        return (NetworkMigrationHelper.instance.getDefaultHeaders() ?: hashMapOf<String, String>()) as Map<String, String>
    }

    private suspend fun refreshToken() {
    }

    override fun flavouredUrl(url: String): String {
        return url
    }
}