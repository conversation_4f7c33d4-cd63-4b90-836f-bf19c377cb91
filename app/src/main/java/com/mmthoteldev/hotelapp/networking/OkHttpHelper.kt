package com.mmthoteldev.hotelapp.networking

import com.gommt.network.fillClientDefaults
import com.mmt.data.model.network.NetworkUtil
import okhttp3.OkHttpClient

object OkHttpHelper {
    val okHttpClient: OkHttpClient by lazy {
        val okHttpClientBuilder = createClientBuilder()
        okHttpClientBuilder.fillClientDefaults(OkHttpSetup())
        NetworkUtil.getDefaultInterceptors().forEach {
            okHttpClientBuilder.addInterceptor(it)
        }
////        okHttpClientBuilder.addInterceptor(OkhttpBotManInterceptor())
//        // putting this at end so that it can log everything
//        if (true) {
//            val logging = HttpLoggingInterceptor { message -> Log.d("Networking_Logs", message) }
//            logging.setLevel(HttpLoggingInterceptor.Level.BODY)
//            okHttpClientBuilder.addInterceptor(logging)
//            okHttpClientBuilder.addInterceptor(AuthSdkInterceptor("aPpufwsYjdkZ9ox"))
//        }
        okHttpClientBuilder.build()
    }

    private fun createClientBuilder(): OkHttpClient.Builder {
        // No timeouts by default
        //            .connectTimeout(30, TimeUnit.SECONDS)
//            .readTimeout(30, TimeUnit.MILLISECONDS)
//            .writeTimeout(30, TimeUnit.MILLISECONDS)
//        val cacheSize = 10 * 1024 * 1024L // 10 Mo
//        val cacheDirectory = File(MMTCore.mContext.cacheDir, "http-cache")
//        val cache = Cache(cacheDirectory, cacheSize)
        return OkHttpClient.Builder()
    }
}