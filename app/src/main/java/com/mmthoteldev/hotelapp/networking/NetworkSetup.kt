package com.mmthoteldev.hotelapp.networking

import com.gommt.network.INetworkSetup
import com.mmthoteldev.hotelapp.networking.OkHttpHelper.okHttpClient
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import retrofit2.CallAdapter
import retrofit2.Converter
import retrofit2.converter.gson.GsonConverterFactory

class NetworkSetup: INetworkSetup {
    override fun getBaseUrl(): String = "https://usersettings.makemytrip.com"


    override fun getOkHttpClient(): OkHttpClient = okHttpClient

    override fun getSupportedCallAdapters(): List<CallAdapter.Factory> {
        val list = mutableListOf<CallAdapter.Factory>()
        return list
    }

    override fun getSupportedConverters(): List<Converter.Factory> {
        val list = mutableListOf<Converter.Factory>()
        list.add(GsonConverterFactory.create())
        return list
    }
}