package com.mmthoteldev.hotelapp.ui

import android.os.Bundle
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.fragment.app.Fragment
import com.mmt.core.base.MmtBaseActivity
import com.mmt.core.base.thankyou.BookingStateData
import com.mmt.core.base.thankyou.ThankYouActivityLobInteractionListener
import com.mmt.hotel.bookingreview.model.ReviewToThankyouTrackingData
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.model.response.persuasionCards.CardDataV2
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.thankyou_v2.ui.HotelThankyouV2Fragment
import com.mmt.travel.app.hotel.thankyou.ui.FragmentHotelThankYou
import com.mmt.travel.app.hotel.thankyou.ui.OnActivityInteraction
import com.mmthoteldev.hotelapp.R
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DummyThankYouActivity : MmtBaseActivity(), OnActivityInteraction,
    ThankYouActivityLobInteractionListener {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_thank_you_dummy)

        findViewById<AppCompatImageView>(R.id.iv_back)?.setOnClickListener {
            finish()
        }

        val fragInstance = getFragInstance()
        supportFragmentManager.beginTransaction()
            .add(R.id.container, fragInstance, HotelThankyouV2Fragment.TAG)
            .commitAllowingStateLoss()
        supportFragmentManager.executePendingTransactions()
    }

    private fun getFragInstance(): Fragment {
        val transactionKey = "ABC"
        val countryCode = HotelConstants.COUNTRY_CODE_UNKNOWN
        val funnelSrc = HotelFunnel.HOTEL.funnelValue
        val thankYouTrackingData = ReviewToThankyouTrackingData(null,null,null,null)


        // if  we don't have a transactionKey due to any xyz reason, an error page will get loaded
        val bundle = Bundle().also { bundle ->
            bundle.putString(HotelThankyouV2Fragment.BUNDLE_KEY_TRANSACTION_KEY, transactionKey)
            bundle.putString(HotelThankyouV2Fragment.BUNDLE_KEY_COUNTRY_CODE, countryCode)
            bundle.putInt(HotelThankyouV2Fragment.BUNDLE_KEY_FUNNEL_SRC, funnelSrc)
            bundle.putParcelable(HotelThankyouV2Fragment.BUNDLE_TRACKING_DATA_FROM_REVIEW_PAGE,thankYouTrackingData)
        }

        return if(ExperimentUtil.thankYouPageRevamp()){
            HotelThankyouV2Fragment().apply { arguments = intent.extras }
        }else{
            // return FragmentHotelThankYou
            FragmentHotelThankYou.getFragmentHotelThankYou(bundle)
        }
    }

    override fun openDialogFragment(cardData: CardDataV2) {
    }

    override fun openWebview(title: String?, url: String?) {
    }

    override fun fetchPersonalizationData() {
    }

    override fun getCytContainer(callBack: (container: LinearLayout?) -> Unit?) {
    }

    override fun updateBookingState(bookingState: BookingStateData) {
    }

}