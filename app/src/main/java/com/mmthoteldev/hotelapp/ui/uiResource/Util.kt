package com.mmthoteldev.hotelapp.ui.uiResource

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Shapes
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

val roundedCornerShapes = Shapes()

fun Modifier.roundedCornerBorder(color: Color = Color.Black): Modifier =
    composed {
        this.border(1.dp, color, roundedCornerShapes.large)
    }

fun Modifier.whiteBackground(): Modifier =
    composed {
        this.background(Color.White, roundedCornerShapes.large)
    }

fun Modifier.horizontalPadding(padding: Dp = 20.dp): Modifier =
    composed {
        this.padding(start = padding, end = padding)
    }

fun Modifier.verticalPadding(padding: Dp = 20.dp): Modifier =
    composed {
        this.padding(top = padding, bottom = padding)
    }