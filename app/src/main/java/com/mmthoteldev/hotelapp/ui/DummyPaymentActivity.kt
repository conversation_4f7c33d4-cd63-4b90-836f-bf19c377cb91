package com.mmthoteldev.hotelapp.ui

import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.mmt.core.MMTCore
import com.mmt.core.base.MmtBaseActivity
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmthoteldev.hotelapp.R

class DummyPaymentActivity : MmtBaseActivity(){

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentView(R.layout.activity_payment_dummy)

        findViewById<AppCompatImageView>(R.id.iv_back)?.setOnClickListener {
            finish()
        }

        findViewById<TextView>(R.id.tv_go_to_thank_you_page)?.setOnClickListener {
            if(ExperimentUtil.thankYouPageRevamp())
                HotelUtil.openDummyThankYouActivity(this)
            else
                Intent("mmt.intent.action.HOTEL_DUMMY_THANK_YOU").apply {
                    setPackage(MMTCore.mContext.packageName)
                    startActivity(this)
                }
        }


    }



}