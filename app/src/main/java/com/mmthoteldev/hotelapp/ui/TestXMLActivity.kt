package com.mmthoteldev.hotelapp.ui

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.mmthoteldev.hotelapp.R
import com.squareup.picasso.MemoryPolicy
import com.squareup.picasso.NetworkPolicy
import com.squareup.picasso.Picasso

/**
 * This shows how to create a simple activity with streetview
 */
class TestXMLActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_test_v2)
        loadImage()
        val cta = findViewById<View>(R.id.tv_reload_images)
        cta.setOnClickListener {
            loadImage()
        }

    }

    fun loadImage(){
        val url = "https://platform-ugc.s3.ap-south-1.amazonaws.com/dev-ugc/images/01JE8Q7J2ZW6CHT2FKE4WPETS7.jpg"
        val imageview1 = findViewById<AppCompatImageView>(R.id.imageView_1)
        val imageview2 = findViewById<AppCompatImageView>(R.id.imageView_2)

        // load image with picasso
        Picasso.get()
            .load(url)
            .placeholder(R.drawable.htl_ugc_placeholder_bg)
            .memoryPolicy(MemoryPolicy.NO_CACHE, MemoryPolicy.NO_STORE)
            .networkPolicy(NetworkPolicy.NO_CACHE)
            .into(imageview1)

        Glide.with(this)
            .asBitmap()
            .load(url)
            .placeholder(R.drawable.htl_ugc_placeholder_bg)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(true)
            .into(imageview2)

    }
}