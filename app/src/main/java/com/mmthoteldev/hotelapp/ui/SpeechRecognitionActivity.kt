package com.mmthoteldev.hotelapp.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.unit.dp
import com.mmt.hotel.handsfree.ui.widget.SmoothWaveBarAnimation
import com.mmt.hotel.handsfree.util.SpeechRecognitionHelper
import com.mmthoteldev.hotelapp.R

class SpeechRecognitionActivity : AppCompatActivity(), SpeechRecognitionHelper.SpeechRecognitionListener {

    private lateinit var microphoneButton: ImageView
    private lateinit var textView: TextView
    private lateinit var waveComposeView: ComposeView
    private lateinit var speechRecognitionHelper: SpeechRecognitionHelper
    
    // Sound level state that will be updated during recognition
    private var currentSoundLevel by mutableStateOf(0f)
    
    // Animation visibility state
    private var isWaveVisible by mutableStateOf(false)
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_speech_recognition)

        microphoneButton = findViewById(R.id.iv_mic)
        textView = findViewById(R.id.tv_speech_to_text)
        val iv_mic_container = findViewById<FrameLayout>(R.id.iv_mic_container)
        
        // Initialize the ComposeView for wave animation
        waveComposeView = ComposeView(this).apply {
            layoutParams = iv_mic_container.layoutParams
            visibility = View.VISIBLE // Always keep the ComposeView visible
        }
        // Add ComposeView to the layout
        (microphoneButton.parent as? android.view.ViewGroup)?.addView(waveComposeView)
        
        // Set up the wave animation - initially hidden
        waveComposeView.setContent {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
                    .padding(0.dp)
            ) {
                SmoothWaveBarAnimation(
                    modifier = Modifier.fillMaxWidth().height(80.dp),
                    soundLevel = currentSoundLevel,
                    useGradient = true,
                    centerFocus = true,
                    centerFocusIntensity = 0.7f,
                    expandingBarHeight = 10f,
                    useDotsWhileExpanding = true,
                    isVisible = isWaveVisible,
                    animationDuration = 500 // Reveal animation duration in ms
                )
            }
        }

        // Initialize speech recognition helper
        speechRecognitionHelper = SpeechRecognitionHelper(this)
        speechRecognitionHelper.setListener(this)

        // Check if speech recognition is available
        if (!SpeechRecognitionHelper.isRecognitionAvailable(this)) {
            textView.text = "Speech recognition is not available on this device"
            microphoneButton.isEnabled = false
            return
        }

        microphoneButton.setOnClickListener {
            // Start speech recognition when mic button is clicked
            speechRecognitionHelper.checkPermissionAndStart(this)
        }
    }

    private fun showWaveAnimation(show: Boolean) {
        // Toggle animation visibility state
        isWaveVisible = show
        
        // Hide/show mic button
        microphoneButton.visibility = if (show) View.INVISIBLE else View.VISIBLE
        
        // Reset sound level when starting
        if (show) {
            currentSoundLevel = 0f
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // Let the helper handle permission results
        speechRecognitionHelper.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        // Let the helper handle activity results
        speechRecognitionHelper.onActivityResult(requestCode, resultCode, data)
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clean up resources
        speechRecognitionHelper.destroy()
    }

    // SpeechRecognitionListener implementations
    override fun onListeningStarted() {
        showWaveAnimation(true)
        textView.text = "Listening..."
    }

    override fun onSpeechStarted() {
        // User started speaking, could adjust UI here
    }

    override fun onSpeechEnded() {
        // User stopped speaking
        // Keep animation visible until results are processed
    }

    override fun onSoundLevelChanged(rmsdB: Float) {
        // Update the sound level to change the wave animation
        currentSoundLevel = rmsdB
    }

    override fun onResults(results: ArrayList<String>) {
        showWaveAnimation(false)
        if (results.isNotEmpty()) {
            textView.text = results[0]
        }
    }

    override fun onPartialResults(results: ArrayList<String>) {
        if (results.isNotEmpty()) {
            textView.text = "${results[0]}"
        }
    }

    override fun onError(errorMessage: String) {
        showWaveAnimation(false)
    }
} 