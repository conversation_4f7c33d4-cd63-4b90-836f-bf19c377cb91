package com.mmthoteldev.hotelapp.di

import android.app.Application
import android.content.Context
import com.mmt.data.model.hotel.localnotification.NotificationDTO
import com.mmt.network.logging.latency.ThreadSafeObjectQueue
import com.mmthoteldev.hotelapp.HotelApplication
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Module class for Dagger DI Framework <br></br>
 * Any custom models for Injection e.g. List of singletons.
 * can be initialized here.
 * Created by mmt5997 on 03/05/16.
 */
@InstallIn(SingletonComponent::class)
@Module
class ApplicationModule {
    @Provides
    @Singleton
    fun provideApplicationContext(mmtApplication: Application): Context {
        return mmtApplication
    }


    @Provides
    @Singleton
    fun providesNotificationQueue(context: Context?): ThreadSafeObjectQueue<NotificationDTO> {
        return ThreadSafeObjectQueue(context, "htlNotificationQueueFile")
    }


}