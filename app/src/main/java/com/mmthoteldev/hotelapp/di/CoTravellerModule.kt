package com.mmthoteldev.hotelapp.di

import com.mmt.profile.cotraveller.trackingevents.CoTravellerTrackingHelper
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class CoTravellerModule {

    @Binds
    internal abstract fun provideCoTravellerTrackingHelper(coTravellerTrackingHelperImpl: CoTravellerTrackingHelperImpl): CoTravellerTrackingHelper
}

class CoTravellerTrackingHelperImpl @Inject constructor() : CoTravellerTrackingHelper {

    override fun postCoTravellersLoadedEvent(size: Int) {

    }
}