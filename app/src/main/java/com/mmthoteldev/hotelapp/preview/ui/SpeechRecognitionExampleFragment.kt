package com.mmthoteldev.hotelapp.preview.ui

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import com.mmt.hotel.handsfree.ui.widget.SmoothWaveBarAnimation
import com.mmt.hotel.handsfree.util.SpeechRecognitionHelper
import com.mmthoteldev.hotelapp.R

/**
 * Example fragment showing how to use SpeechRecognitionHelper in a Fragment context.
 * This can be included in any activity to add speech recognition functionality.
 */
class SpeechRecognitionExampleFragment : Fragment(), SpeechRecognitionHelper.SpeechRecognitionListener {

    private lateinit var micButton: ImageView
    private lateinit var resultTextView: TextView
    private lateinit var waveComposeView: ComposeView
    private lateinit var speechRecognitionHelper: SpeechRecognitionHelper
    
    // Sound level state that will be updated during recognition
    private var currentSoundLevel by mutableStateOf(0f)
    
    // Animation visibility state
    private var isWaveVisible by mutableStateOf(false)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate a simple layout with a mic button and text view
        return inflater.inflate(R.layout.fragment_speech_recognition_example, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize views
        micButton = view.findViewById(R.id.speech_mic_button)
        resultTextView = view.findViewById(R.id.speech_result_text)
        
        // Create and add ComposeView for the wave animation
        waveComposeView = ComposeView(requireContext()).apply {
            layoutParams = micButton.layoutParams
            visibility = View.VISIBLE // Always keep the ComposeView visible
        }
        (micButton.parent as? ViewGroup)?.addView(waveComposeView)
        
        // Set up the wave animation - initially hidden
        waveComposeView.setContent {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
                    .padding(0.dp)
            ) {
                SmoothWaveBarAnimation(
                    modifier = Modifier.fillMaxWidth().height(80.dp),
                    soundLevel = currentSoundLevel,
                    useGradient = true,
                    centerFocus = true,
                    centerFocusIntensity = 0.7f,
                    expandingBarHeight = 10f,
                    useDotsWhileExpanding = true,
                    isVisible = isWaveVisible,
                    animationDuration = 500 // Reveal animation duration in ms
                )
            }
        }
        
        // Initialize SpeechRecognitionHelper
        speechRecognitionHelper = SpeechRecognitionHelper(requireContext())
        speechRecognitionHelper.setListener(this)
        
        // Check if speech recognition is available
        if (!SpeechRecognitionHelper.isRecognitionAvailable(requireContext())) {
            resultTextView.text = "Speech recognition is not available on this device"
            micButton.isEnabled = false
            return
        }
        
        // Set click listener for mic button
        micButton.setOnClickListener {
            speechRecognitionHelper.checkPermissionAndStart(this)
        }
    }
    
    private fun showWaveAnimation(show: Boolean) {
        // Toggle animation visibility state
        isWaveVisible = show
        
        // Hide/show mic button
        micButton.visibility = if (show) View.INVISIBLE else View.VISIBLE
        
        // Reset sound level when starting
        if (show) {
            currentSoundLevel = 0f
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int, 
        permissions: Array<out String>, 
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // Let the helper handle permission results
        speechRecognitionHelper.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        // Let the helper handle activity results
        speechRecognitionHelper.onActivityResult(requestCode, resultCode, data)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // Release resources
        speechRecognitionHelper.destroy()
    }
    
    // SpeechRecognitionListener implementations
    override fun onListeningStarted() {
        showWaveAnimation(true)
        resultTextView.text = "Listening..."
    }

    override fun onSpeechStarted() {
        // User started speaking, could adjust UI here
    }

    override fun onSpeechEnded() {
        // User stopped speaking
        // Keep animation visible until results are processed
    }

    override fun onSoundLevelChanged(rmsdB: Float) {
        // Update the sound level to change the wave animation
        currentSoundLevel = rmsdB
    }

    override fun onResults(results: ArrayList<String>) {
        showWaveAnimation(false)
        if (results.isNotEmpty()) {
            resultTextView.text = results[0]
        }
    }

    override fun onPartialResults(results: ArrayList<String>) {
        if (results.isNotEmpty()) {
            resultTextView.text = "Hearing: ${results[0]}"
        }
    }

    override fun onError(errorMessage: String) {
        showWaveAnimation(false)
        resultTextView.text = "Error: $errorMessage"
    }
} 