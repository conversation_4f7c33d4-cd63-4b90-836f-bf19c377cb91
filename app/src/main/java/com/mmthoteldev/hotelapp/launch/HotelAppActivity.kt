package com.mmthoteldev.hotelapp.launch

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.NavHostController
import com.mmt.core.MMTCore
import com.mmt.core.user.auth.LoginUtil
import com.mmt.data.model.util.GenericUtils
import com.mmt.hotel.altacco.ui.AltAccoLandingActivity
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.common.constants.HotelConstants
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.HotelPageActionName
import com.mmt.hotel.common.util.ExperimentUtil
import com.mmt.hotel.common.util.HotelScreenIntentUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.deeplink.HotelDeeplinkActivity
import com.mmt.hotel.service.LottieDownloadHelper
import com.mmt.hotel.staycation.ui.StayCationActivityV2
import com.mmt.hotel.userReviews.collection.generic.model.UserReviewModel
import com.mmthoteldev.hotelapp.R
import com.mmthoteldev.hotelapp.ui.TestXMLActivity
import com.mmthoteldev.hotelapp.ui.SpeechRecognitionActivity
import com.mmthoteldev.hotelapp.util.AppLaunchActivityEvent
import com.mmthoteldev.hotelapp.util.HotelPokusHelper

class HotelAppActivity : AppCompatActivity() {

    companion object {
        const val LAUNCH_SCREEN = "LaunchScreen"
        const val EXPERIMENT_SCREEN = "ExperimentScreen"
        const val TEST_SCREEN = "TestScreen"
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LottieDownloadHelper.scheduleDownloadService(this)
        setContent {
            HotelAppUI(window, ::handleEvents)
        }
        launchDeeplinkActivity(intent)
    }

    fun handleEvents(event: HotelEvent){
        android.util.Log.i("HotelApp","Event ID: ${event.eventID}")
        when(event.eventID){
            AppLaunchActivityEvent.OPEN_HOTEL_LANDING_ACTIVITY -> openHotelLanding()
            AppLaunchActivityEvent.OPEN_ALT_ACCO_LANDING_ACTIVITY -> openAltAccoLanding()
            AppLaunchActivityEvent.OPEN_SHORT_STAY_LANDING_ACTIVITY -> openShortStays()
            AppLaunchActivityEvent.OPEN_STAYCATION_LANDING_ACTIVITY -> openStaycation()
            AppLaunchActivityEvent.OPEN_TREELS -> openTreelScreen()
            AppLaunchActivityEvent.OPEN_DUMMY_THANK_YOU -> openDummyThankyou()
            AppLaunchActivityEvent.OPEN_CORP_APPROVAL -> openCorpApproval()
            AppLaunchActivityEvent.OPEN_FLYFISH_REVIEW -> openFlyfishReview()
            AppLaunchActivityEvent.OPEN_POKUS -> openExperiments(event.data as NavHostController)
            AppLaunchActivityEvent.UPDATE_POKUS -> updateExperiments(event.data as Pair<NavHostController,MutableMap<String, Any>>)
            AppLaunchActivityEvent.TEST_SCREEN -> openTestScreen(event.data as NavHostController)
        }
    }

    private fun openHotelLanding() {
        val intent = HotelScreenIntentUtil.getHotelLandingIntent()
        startActivity(intent)
    }

    private fun openAltAccoLanding() {
        HotelScreenIntentUtil.getAltAccoScreenIntent().apply {
            putExtra(AltAccoLandingActivity.IS_FROM_APP_LANDING, true)
            putExtra(AltAccoLandingActivity.AREA_FIELD_EDITABLE, true)
            putExtra(AltAccoLandingActivity.REQUEST_TRANSPARENT_BACKGROUND, false)
            setPackage(MMTCore.mContext.packageName)
            startActivity(this)
        }
    }

    private fun openShortStays() {
        startActivity(HotelUtil.getHotelLandingActivityIntent(HotelFunnel.SHORT_STAYS))
    }

    private fun openStaycation() {
        Intent(MMTCore.mContext, StayCationActivityV2::class.java).apply {
            startActivity(this)
        }
    }

    private fun openTreelScreen() {
        val intent = HotelScreenIntentUtil.getHotelTreelIntent()
        startActivity(intent)
    }


    private fun openDummyThankyou() {
        if(ExperimentUtil.thankYouPageRevamp())
            HotelUtil.openDummyThankYouActivity(this)
        else
            Intent("mmt.intent.action.HOTEL_DUMMY_THANK_YOU").apply {
                setPackage(MMTCore.mContext.packageName)
                startActivity(this)
            }
    }

    private fun openCorpApproval() {
        val intent = Intent(HotelPageActionName.CORP_APPROVAL_ACTIVITY_V2).setPackage(MMTCore.mContext.packageName)
        intent.putExtra(HotelConstants.APPROVAL_ID, "b01f6102-417b-400d-87f5-0d24c73b0d80")
        startActivity(intent)
    }

    private fun openFlyfishReview() {
        // replace token & hotelId as per need
        val  userReviewModel = UserReviewModel(token = "ce8ea407181fa5bd236ac97d35782916deb2193497950ee250db0e58e91edb39",
            bookingId="",hotelId = "202201281716089621", source = "Android", metaSrc = "Android", uuid = LoginUtil.getUUID())
        val bundle: Bundle = Bundle().apply { putParcelable(HotelConstants.BUNDLE_USER_REVIEW_GENERATION, userReviewModel) }
        val redirectIntent: Intent = Intent(HotelPageActionName.FLYFISH_REVIEW_V2).apply {
            putExtras(bundle)
        }
        GenericUtils.startActivityInternal(MMTCore.mContext, redirectIntent)
    }

    private fun openExperiments(navController: NavHostController) {
        navController.navigate(EXPERIMENT_SCREEN)
    }

    private fun updateExperiments(data: Pair<NavHostController, MutableMap<String, Any>>) {
        HotelPokusHelper.updateEditedExps(data.second)
        data.first.popBackStack()
    }

    private fun openTestScreen(navController: NavHostController) {
        startActivity(Intent(this, TestXMLActivity::class.java))
//        navController.navigate(TEST_SCREEN)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        launchDeeplinkActivity(intent)

    }

    private fun launchDeeplinkActivity(intent: Intent?){
        intent?.let {
            if(it.data == null){
                return
            }
            val redirectIntent = Intent(this, HotelDeeplinkActivity::class.java)
            redirectIntent.action = Intent.ACTION_VIEW
            redirectIntent.data = it.data
            startActivity(redirectIntent)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Add speech recognition option
        menu.add(Menu.NONE, R.id.menu_speech, Menu.NONE, "Speech Recognition")
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menu_speech -> {
                startActivity(Intent(this, SpeechRecognitionActivity::class.java))
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}