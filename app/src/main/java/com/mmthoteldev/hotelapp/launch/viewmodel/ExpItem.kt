package com.mmthoteldev.hotelapp.launch.viewmodel

import android.text.InputType
import androidx.lifecycle.MutableLiveData
import com.mmt.hotel.base.AbstractRecyclerItem
import com.mmt.hotel.base.events.HotelEvent
import java.lang.Exception

class ExpItem(var key: String, var value: Any?, var classType: Class<Any>) : AbstractRecyclerItem {
    override fun getItemType(): Int {
        return when(classType){
            java.lang.Boolean::class.java -> ExperimentItemType.EXP_BOOL
            java.lang.Integer::class.java -> ExperimentItemType.EXP_INT
            java.lang.String::class.java -> ExperimentItemType.EXP_STR
            else -> ExperimentItemType.EXP_STR
        }
    }

    fun getItemVal(value: Any): Any{
        return if (getItemType() == ExperimentItemType.EXP_INT) {
            try {
                when (value) {
                    is Double -> value.toInt()
                    else -> value.toString().toInt()
                }
            } catch (e: Exception) {
                0
            }
        } else {
            value
        }
    }
}

object ExperimentItemType{
    const val EXP_BOOL = 1
    const val EXP_INT = 2
    const val EXP_STR = 3
}