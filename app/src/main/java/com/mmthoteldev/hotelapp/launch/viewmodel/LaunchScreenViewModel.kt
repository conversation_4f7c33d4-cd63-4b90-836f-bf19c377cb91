package com.mmthoteldev.hotelapp.launch.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.databinding.ObservableField
import androidx.lifecycle.MutableLiveData
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.util.ResourceProvider
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.base.viewModel.HotelViewModel
import com.mmthoteldev.hotelapp.R
import com.mmthoteldev.hotelapp.util.AppLaunchActivityEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class LaunchScreenViewModel @Inject constructor() : HotelViewModel() {

    var currentFunnelContext = ObservableField(FunnelContext.INDIA)

    val mainFunnelList = listOf(
        Pair(first = null, second = Triple("Hotels", R.drawable.icon_hotels, AppLaunchActivityEvent.OPEN_HOTEL_LANDING_ACTIVITY)),
        Pair(first = FunnelContext.INDIA, second = Triple("Homestays & Villas", R.drawable.icon_hs, AppLaunchActivityEvent.OPEN_ALT_ACCO_LANDING_ACTIVITY)),
        Pair(first = FunnelContext.INDIA, second = Triple("Nearby Staycations", R.drawable.icon_staycation, AppLaunchActivityEvent.OPEN_SHORT_STAY_LANDING_ACTIVITY)),
        Pair(first = FunnelContext.GCC, second = Triple("GCC Staycation", R.drawable.icon_staycation, AppLaunchActivityEvent.OPEN_STAYCATION_LANDING_ACTIVITY)),
        Pair(first = FunnelContext.INDIA, second = Triple("Treels", R.drawable.icon_treels, AppLaunchActivityEvent.OPEN_TREELS)))

    var buttonList = listOf(
        Pair("OPEN CORP APPROVAL", AppLaunchActivityEvent.OPEN_CORP_APPROVAL),
        Pair("OPEN CORP APPROVAL XML", AppLaunchActivityEvent.OPEN_CORP_APPROVAL_XML),
        Pair("OPEN THANKYOU", AppLaunchActivityEvent.OPEN_DUMMY_THANK_YOU),
        Pair("OPEN FLYFISH REVIEW", AppLaunchActivityEvent.OPEN_FLYFISH_REVIEW),
        Pair("EDIT POKUS EXP", AppLaunchActivityEvent.OPEN_POKUS),
    )
}


