package com.mmthoteldev.hotelapp.launch.screen

import android.view.Window
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.staggeredgrid.LazyHorizontalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridItemScope
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import com.mmt.hotel.R
import androidx.navigation.NavHostController
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.bookingreview.model.response.price.HotelCloudData
import com.mmt.hotel.compose.utils.parseHtmlString
import com.mmt.hotel.detail.dataModel.DiffUtilRecycleItem
import com.mmt.hotel.detail.dataModel.MyBizCardListItemData
import com.mmt.hotel.detail.dataModel.MyBizCardUIData
import com.mmt.hotel.detail.compose.model.AboutPropertyCardData
import com.mmt.hotel.detail.compose.model.AboutPropertyUiData
import com.mmt.hotel.detail.compose.model.FoodAndDiningCardData
import com.mmt.hotel.detail.compose.model.FoodAndDiningUiData
import com.mmt.hotel.detail.compose.model.InclusionOfferItemData
import com.mmt.hotel.detail.compose.model.DetailIconInfoCardItemUIDataV2
import com.mmt.hotel.detail.compose.model.DetailIconInfoCardUIDataV2
import com.mmt.hotel.detail.compose.model.DetailIconInfoCardItem
import com.mmt.hotel.detail.compose.model.DetailIconInfoCardData
import com.mmt.hotel.detail.compose.model.InfoData
import com.mmt.hotel.detail.compose.model.PaymentInfoCardData
import com.mmt.hotel.detail.compose.model.PrimaryOfferCardData
import com.mmt.hotel.detail.compose.model.PropertyCertificateCardData
import com.mmt.hotel.detail.model.PropertyCertificatesUIData
import com.mmt.hotel.detail.compose.model.PropertyHighlightCardData
import com.mmt.hotel.detail.compose.model.StoryCardData
import com.mmt.hotel.detail.compose.model.StoryCardUIDataV2
import com.mmt.hotel.detail.compose.model.WhatsAnOfferCardData
import com.mmt.hotel.detail.model.response.ContextRules
import com.mmt.hotel.detail.model.response.PropertyHighlightDetails
import com.mmt.hotel.detail.model.response.PropertyHighlights
import com.mmt.hotel.detail.compose.ui.cards.AboutPropertyCardUi
import com.mmt.hotel.detail.compose.ui.cards.DiscoverLuxuryCardUi
import com.mmt.hotel.detail.compose.ui.cards.FoodAndDiningCardUi
import com.mmt.hotel.detail.compose.ui.cards.PrimaryOfferCardUI
import com.mmt.hotel.detail.compose.ui.cards.PropertyCertificateCardUi
import com.mmt.hotel.detail.compose.ui.cards.WhatsAnOfferCardUi
import com.mmt.hotel.detail.compose.ui.util.DetailAttributes
import com.mmt.hotel.detail.compose.ui.util.LocalDetailAttributesProvider
import com.mmt.hotel.detail.model.response.CommonHighlightItem
import com.mmt.hotel.detail.viewModel.cardsViewModel.DetailCommonSubItemVM
import com.mmt.hotel.detail.viewModel.cardsViewModel.DiscoverLuxuryItemViewModel
import com.mmt.hotel.detail.viewModel.cardsViewModel.HotelMyBizNonBizCardAdapterModel
import com.mmt.hotel.detail.viewModel.cardsViewModel.LuxuryItemClick
import com.mmt.hotel.listingV2.dataModel.StoryCardItemUIData
import com.mmt.hotel.selectRoom.model.response.PaymentCardInfo
import com.mmt.hotel.selectRoom.model.response.PrimaryOffer
import com.mmt.hotel.selectRoom.model.response.Style
import com.mmthoteldev.hotelapp.ui.uiResource.HotelAppTheme


@Composable
fun TestScreen(window: Window, navController: NavHostController, handleEvent: (HotelEvent) -> Unit) {
    HotelAppTheme {
        HotelCollageView(listOf("", "", "", ""))
        SideEffect {
//            window.statusBarColor = Color.Transparent.toArgb()
//            WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightStatusBars = true
//            window.setFlags(
//                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
//                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
//            )

        }
//        CompositionLocalProvider(LocalDetailAttributesProvider provides DetailAttributes(isLuxScreen = true)) {
//
//            val items = getItems()
//            HotelDetailTestScreen(window, HotelDetailTestData(items))
//
//        }
    }
}

@Composable
fun HotelCollageView(urls: List<String>) {
    val size = urls.size
    val screenWidth = (LocalConfiguration.current.screenWidthDp.toInt() / 4)
    LazyHorizontalStaggeredGrid(rows = StaggeredGridCells.Fixed(2),
            modifier = Modifier.fillMaxWidth().height(200.dp)) {
        items(count = size, span = { index ->
            if (index == 0) {
                StaggeredGridItemSpan.FullLine
            } else {
                StaggeredGridItemSpan.SingleLane
            }
        }) { index ->
            Box(modifier = Modifier
                    .padding(4.dp)
                    .size(100.dp)
                    .background(Color.Gray))

        }
    }
}


@Composable
fun TestNidhi() {
    val data = PropertyCertificatesUIData("Certified 5-Star (Without Alcohol) Certified 5-Star (Without Alcohol)",
            "Certified 5-Star (Without Alcohol) Certified 5-Star (Without Alcohol)", "", R.dimen.htl_empty_dimen, false)
    val cardData = PropertyCertificateCardData(data, {})
    PropertyCertificateCardUi(cardData)
}

@Composable
fun TestOfferCard() {
    val inclusionOfferItemData = InclusionOfferItemData(
            imageUrl = "https://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/202307041722498581-7261270-819af978-81dc-4a93-8063-41a58641c303.jpg?&output-quality=75&output-format=jpg",
            text = "Movie under the Stars",
            desc = "A 200 Seater, surround sound savy, open air theatre is hightlight of this.  air theatre is hightlight of this property.   air theatre is hightlight of this property",
            ratePlanCode = ""
    ) {

    }
    val inclusionOfferItemDatav2 = InclusionOfferItemData(
            imageUrl = "https://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/202307041722498581-7261270-819af978-81dc-4a93-8063-41a58641c303.jpg?&output-quality=75&output-format=jpg",
            text = "Movie under the Stars",
            desc = "A 200 Seater, surround sound savy, ",
            ratePlanCode = ""
    ) {

    }
    val cardData = WhatsAnOfferCardData(listOf(inclusionOfferItemData, inclusionOfferItemDatav2, inclusionOfferItemData, inclusionOfferItemDatav2))
    WhatsAnOfferCardUi(cardData)
}

private fun createDiscoverLuxuryModel(): DiscoverLuxuryItemViewModel {
    val uiData = StoryCardItemUIData(
            titleText = "Poolside Loung Grab your",
            subText = "Grab your favourite fizzy drink & ease off on the poolside loungers.",
            cardWidthResId = R.dimen.detail_luxury_card_width,
            cardHeightResId = R.dimen.detail_luxury_card_height,
            imageUrl = "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/202307041722498581-7261270-819af978-81dc-4a93-8063-41a58641c303.jpg?&output-quality=75&output-format=jpg",
            cornerRadiusRes = R.dimen.margin_large,
            topMarginOfCircleRes = R.dimen.margin_small,
            maxLineInTitleText = 2,
            titleTextColor = R.color.white,
            subTextColor = R.color.white,
            titleTextSizeRes = R.dimen.htl_text_size_tiny,
            storiesCount = 5,
            itemEndSpacingRes = R.dimen.margin_small
    )

    return DiscoverLuxuryItemViewModel(uiData, 1, object : LuxuryItemClick {
        // do something
        override fun onItemClick(position: Int) {
        }
    })
}

@Composable
fun TestPrimaryOfferCardUI() {
    val data = PrimaryOffer(
            expiry = System.currentTimeMillis() + (12 * 60 * 60 * 1000),
            iconUrl = "https://example.com",
            title = "",
            description = "Book now and save 525 on this property. Limited time only!",
            type = "PrimaryOffer",
            style = Style(bgColor = "#80B8F5E0")
    )
    val primaryOfferCardData = PrimaryOfferCardData(data) {}

    PrimaryOfferCardUI(primaryOfferCardData)
}

@Composable
fun PreviewAboutPropertyCardUi() {
    val mockData = AboutPropertyUiData(
            title = AnnotatedString("About The Lalit Golf &amp; Spa Resort dj cdj cndj cdsj ncds"),
            subTitle = AnnotatedString("Luxe rooms, a private beach &amp; a double 'T' 9-hole links golf course await you at this 5-star resort in Canacona."),
            categoryList = listOf(
                    DetailCommonSubItemVM("Mock Category 1") {},
                    DetailCommonSubItemVM("Mock Category 2") {}
            ),
            showReadMore = false,
            null
    )

    val mockCardData = AboutPropertyCardData(mockData) {}

    CompositionLocalProvider(LocalDetailAttributesProvider provides DetailAttributes(isLuxScreen = true)) {
        AboutPropertyCardUi(cardData = mockCardData)
    }
}

@Composable
fun PreviewDiscoverLuxuryCardUi() {
    val storyCardItemUIData = DiscoverLuxuryItemViewModel(StoryCardItemUIData(
            titleText = "Poolside Loung Grab your",
            subText = "",
            cardWidthResId = R.dimen.detail_luxury_card_width,
            cardHeightResId = R.dimen.detail_luxury_card_height,
            imageUrl = "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/202307041722498581-7261270-819af978-81dc-4a93-8063-41a58641c303.jpg?&output-quality=75&output-format=jpg",
            cornerRadiusRes = R.dimen.margin_large,
            topMarginOfCircleRes = R.dimen.margin_small,
            maxLineInTitleText = 2,
            titleTextColor = R.color.white,
            subTextColor = R.color.white,
            titleTextSizeRes = R.dimen.htl_text_size_tiny,
            storiesCount = 5,
            itemEndSpacingRes = R.dimen.margin_small
    ), 0, object : LuxuryItemClick {
        override fun onItemClick(position: Int) {
        }
    })

    val storyCardData = StoryCardData(
            data = StoryCardUIDataV2(
                    title = AnnotatedString("Discover Luxury"),
                    storyCardItemUIDataList = listOf(storyCardItemUIData, storyCardItemUIData, storyCardItemUIData, storyCardItemUIData, storyCardItemUIData)
            ), {})

    CompositionLocalProvider(LocalDetailAttributesProvider provides DetailAttributes(isLuxScreen = true)) {
        DiscoverLuxuryCardUi(storyCardData)
    }
}

@Composable
fun PreviewFoodAndDiningCardUiSummaryOnly() {
    val highlights = emptyList<CommonHighlightItem>()
    val summary = listOf(InfoData(AnnotatedString("Private kitchen is available for cooking")), InfoData(AnnotatedString("summary2")))
    val category = emptyList<InfoData>()
    FoodAndDiningCardUi(cardData = FoodAndDiningCardData(data = FoodAndDiningUiData("Food and Dining", highlights, summary, category, null, null), eventLambda = {}))
}

private fun getItems(): List<DiffUtilRecycleItem> {
    val list = mutableListOf<DiffUtilRecycleItem>()

    val contextRules = ContextRules(title = "Couple, Bachelor Rules", tag = "", ruleIcon = "",
            desc = "This property does not allow unmarried couples.s", category = "")
  //  val houseRulesData = HouseRulesDataV2(title = "House Rules", contextRules = contextRules,
  //          checkInTime = "2:00 PM - 3:30 PM", checkOutTime = "11:00 AM - 11:30 AM",
  //          rulesItems = listOf(HouseRulesItemUiData(title = "Rule 1", rules = listOf(InfoData(AnnotatedString("Rule 1 description"))), images = listOf(TravellerImageEntity()), imageText = "7 Photos", showViewAll = true, eventLambda = { /* Handle event here */ })), showViewAll = true, showTimeRange = true, languageHeader = "English", language = "Languages Spoken at the Property : Hindi, English, Turkey")

//    val houseRuleCardData = HouseRuleCardData(houseRulesData) { /* Handle event here */ }
 //   list.add(houseRuleCardData)
//
    val details = listOf(PropertyHighlightDetails(
            title = "Free Wi-Fi",
            desc = "High-speed internet available",
            icon = "https://example.com/icon.png"
    ), PropertyHighlightDetails(
            title = "Free Wi-Fi",
            desc = "High-speed internet available",
            icon = "https://example.com/icon.png"
    ), PropertyHighlightDetails(
            title = "Free Wi-Fi",
            desc = "High-speed internet available",
            icon = "https://example.com/icon.png"
    ))

    val propertycardData = PropertyHighlightCardData(
            data = PropertyHighlights(
                    icon = "https://example.com/icon.png",
                    title = "Property Highlights",
                    details = details
            )
    ) {

    }
    list.add(propertycardData)
    val highlights = emptyList<CommonHighlightItem>()
    val summary = listOf(InfoData(AnnotatedString("Private kitchen is available for cooking")), InfoData(AnnotatedString("summary2")))
    val category = emptyList<InfoData>()
    list.add(FoodAndDiningCardData(data = FoodAndDiningUiData("Food and Dining", highlights, summary, category, null, null), eventLambda = {}))

    val storyCardItemUIData = DiscoverLuxuryItemViewModel(StoryCardItemUIData(
            titleText = "Poolside Loung Grab your",
            subText = "",
            cardWidthResId = R.dimen.detail_luxury_card_width,
            cardHeightResId = R.dimen.detail_luxury_card_height,
            imageUrl = "//r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/202307041722498581-7261270-819af978-81dc-4a93-8063-41a58641c303.jpg?&output-quality=75&output-format=jpg",
            cornerRadiusRes = R.dimen.margin_large,
            topMarginOfCircleRes = R.dimen.margin_small,
            maxLineInTitleText = 2,
            titleTextColor = R.color.white,
            subTextColor = R.color.white,
            titleTextSizeRes = R.dimen.htl_text_size_tiny,
            storiesCount = 5,
            itemEndSpacingRes = R.dimen.margin_small
    ), 0, object : LuxuryItemClick {
        override fun onItemClick(position: Int) {
        }
    })

    val storyCardData = StoryCardData(
            data = StoryCardUIDataV2(
                    title = AnnotatedString("Discover Luxury"),
                    storyCardItemUIDataList = listOf(storyCardItemUIData, storyCardItemUIData, storyCardItemUIData, storyCardItemUIData, storyCardItemUIData)
            ), {})
    list.add(storyCardData)

    val mockData = AboutPropertyUiData(
            title = AnnotatedString("About The Lalit Golf &amp; Spa Resort dj cdj cndj cdsj ncds"),
            subTitle = AnnotatedString("Luxe rooms, a private beach &amp; a double 'T' 9-hole links golf course await you at this 5-star resort in Canacona."),
            categoryList = listOf(
                    DetailCommonSubItemVM("Mock Category 1") {},
                    DetailCommonSubItemVM("Mock Category 2") {}
            ),
            showReadMore = false,
            null
    )

    val mockCardData = AboutPropertyCardData(mockData) {}
    list.add(mockCardData)

    val data = PrimaryOffer(
            expiry = System.currentTimeMillis() + (12 * 60 * 60 * 1000),
            iconUrl = "https://example.com",
            title = "",
            description = "Book now and save 525 on this property. Limited time only!",
            type = "PrimaryOffer",
            style = Style(bgColor = "#80B8F5E0")
    )
    val primaryOfferCardData = PrimaryOfferCardData(data) {}
    list.add(primaryOfferCardData)

    val inclusionOfferItemData = InclusionOfferItemData(
            imageUrl = "https://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/202307041722498581-7261270-819af978-81dc-4a93-8063-41a58641c303.jpg?&output-quality=75&output-format=jpg",
            text = "Movie under the Stars",
            desc = "A 200 Seater, surround sound savy, open air theatre is hightlight of this.  air theatre is hightlight of this property.   air theatre is hightlight of this property",
            ratePlanCode = ""
    ) {

    }
    val inclusionOfferItemDatav2 = InclusionOfferItemData(
            imageUrl = "https://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/room-imgs/202307041722498581-7261270-819af978-81dc-4a93-8063-41a58641c303.jpg?&output-quality=75&output-format=jpg",
            text = "Movie under the Stars",
            desc = "A 200 Seater, surround sound savy, ",
            ratePlanCode = ""
    ) {

    }
    val cardData = WhatsAnOfferCardData(listOf(inclusionOfferItemData, inclusionOfferItemDatav2, inclusionOfferItemData, inclusionOfferItemDatav2))
    list.add(cardData)

    val nidhiData = PropertyCertificatesUIData("Certified 5-Star (Without Alcohol) Certified 5-Star (Without Alcohol)",
            "Certified 5-Star (Without Alcohol) Certified 5-Star (Without Alcohol)", "", R.dimen.htl_empty_dimen, false)
    val nidhiCardData = PropertyCertificateCardData(nidhiData, {})
    list.add(nidhiCardData)

    val item = DetailIconInfoCardItem(
            DetailIconInfoCardItemUIDataV2(
                    titleText = ("Indian Food <i>Available</i> On-site".parseHtmlString()),
                    subText = AnnotatedString("Unmarried couples are not allowed"),
                    iconType = "Mock Icon Type",
                    imageUrl = "Mock Image URL"), true
    )
    val sampleData = DetailIconInfoCardData(
            data = DetailIconInfoCardUIDataV2(
                    title = "Why do Indians book this property?",
                    titleIconUrl = "https://sampleurl.com/icon.png",
                    items = listOf(item, item, item),
                    deeplink = "https://sampleurl.com",
                    actionText = "",
                    maxItemToShow = 3,
                    isIconInfoV2 = false,
                    subType = ""
            ),
            eventLambda = { /* Handle click event here */ }
    )
    list.add(sampleData)

    val bizdata = MyBizCardListItemData(text = "Highly Rated by Business Travellers")
    val hotelCloudData = HotelCloudData(title = null, sectionFeatures = null,
            persuasionIcon = "", persuasionText = "This property is highly rated by business travellers")
    val myBizCardUIData = MyBizCardUIData(iconUrl = "https://example.com/image.png",
            title = "This Property is myBiz assured", itemDataList = listOf(bizdata, bizdata, bizdata, bizdata), hotelCloudData = hotelCloudData)
    val hotelMyBizNonBizCardAdapterModel = HotelMyBizNonBizCardAdapterModel(myBizCardUIData)
    list.add(hotelMyBizNonBizCardAdapterModel)

    list.add(PaymentInfoCardData(PaymentCardInfo("Pay only 50% now to confirm your booking and the rest before 7 Feb, 12:00 pm",
            "Learn More", "https://", "cta"), {}))

    return list
}

