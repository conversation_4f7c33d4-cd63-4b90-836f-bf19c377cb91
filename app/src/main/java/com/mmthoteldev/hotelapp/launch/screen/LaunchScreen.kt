package com.mmthoteldev.hotelapp.launch.screen

import android.annotation.SuppressLint
import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.But<PERSON><PERSON><PERSON>aults
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import com.mmt.auth.login.MMTAuth
import com.mmt.auth.login.model.LoginPageExtra
import com.mmt.auth.login.util.AuthMigrationHelper
import com.mmt.auth.login.util.LoginUtils
import com.mmt.auth.logout.LogoutManager
import com.mmt.auth.logout.network.LogoutRepo
import com.mmt.core.MMTCore
import com.mmt.core.constant.LoginControllerConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.CountryCodeRepository
import com.mmt.core.user.prefs.FunnelContext
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.data.model.util.SharedPreferenceUtils
import com.mmt.hotel.R
import com.mmt.hotel.base.events.HotelEvent
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.pokus.PokusV2Helper
import com.mmt.skywalker.config.SWConfigConstants
import com.mmthoteldev.hotelapp.implementations.DummyNetworkHeadersImpl
import com.mmthoteldev.hotelapp.launch.viewmodel.LaunchScreenViewModel
import com.mmthoteldev.hotelapp.ui.uiResource.Grey90
import com.mmthoteldev.hotelapp.ui.uiResource.HotelAppTheme
import com.mmthoteldev.hotelapp.ui.uiResource.horizontalPadding
import com.mmthoteldev.hotelapp.ui.uiResource.roundedCornerBorder
import com.mmthoteldev.hotelapp.ui.uiResource.roundedCornerShapes
import com.mmthoteldev.hotelapp.ui.uiResource.verticalPadding
import com.mmthoteldev.hotelapp.util.AppLaunchActivityEvent
import com.mmthoteldev.hotelapp.util.ProfileSwitch
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun LaunchScreen(navController: NavHostController, handleEvent: (HotelEvent) -> Unit) {

    val viewModel: LaunchScreenViewModel = viewModel()

    val currentFunnel = rememberSaveable { mutableStateOf(viewModel.currentFunnelContext.get()) }
    HotelAppTheme() {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .verticalScroll(rememberScrollState())
                .background(color = Grey90)
                .padding(16.dp)
        ) {
            RegionSelection(currentFunnel)
            if (currentFunnel.value == FunnelContext.INDIA) {
                Row {
                    ProfileSwitch(Modifier.weight(1f))
                    ExperienceSwitch(Modifier.weight(1f))
                }
            }
            if(currentFunnel.value == FunnelContext.GCC) {
                GlobalUrlSwitch()
            }
            MainFunnels(currentFunnel, viewModel.mainFunnelList, handleEvent)
            OtherOptions(viewModel.buttonList, navController, handleEvent)
            Login()
        }
    }
}

@Composable
fun GlobalUrlSwitch() {
    //write a composable which contains a switch to enable/disable global url
    //it will contain a text and a switch
    //on switch change, it will call a function to enable/disable global url
    //this function will be in the viewmodel
    Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(vertical = 10.dp)) {
        MmtComposeTextView(
            text = "Global URL",
            modifier = Modifier
                .padding(bottom = dimensionResource(R.dimen.margin_large))
                .padding(
                    all = dimensionResource(R.dimen.margin_small)
                ),
            color = Color.Black
        )
        Spacer(modifier = Modifier.width(10.dp))
        var isGlobalUrlEnabled by remember { mutableStateOf(DummyNetworkHeadersImpl.isGlobalDomainEnabled) }
        Switch(
            checked = isGlobalUrlEnabled,
            onCheckedChange = {
                isGlobalUrlEnabled = it
                DummyNetworkHeadersImpl.isGlobalDomainEnabled = it
            }
        )
    }
}

//generate a preview for the above composable
@Preview
@Composable
fun GlobalUrlSwitchPreview() {
    GlobalUrlSwitch()
}

@Composable
fun RegionSelection(currentFunnel: MutableState<FunnelContext?>) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .roundedCornerBorder(Color.White),
        color = Color.White,
        shape = roundedCornerShapes.large,
        shadowElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .horizontalPadding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("Region :", color = Color.Black)

            val isIndiaSelected = (currentFunnel.value == FunnelContext.INDIA)

            RadioButton(
                selected = isIndiaSelected,
                onClick = {
                    currentFunnel.value = FunnelContext.INDIA
                    LoginUtils.setPreferredRegionFromCountry(CountryCodeRepository.getCountryIndia())
                    PokusV2Helper.initPokus(true)
                    LoginUtils.setPreferredRegionFromCountry(CountryCodeRepository.getCountryIndia())
                }
            )
            Text(text = "India")

            RadioButton(
                selected = !isIndiaSelected,
                onClick = {
                    currentFunnel.value = FunnelContext.GCC
                    LoginUtils.setPreferredRegionFromCountry(CountryCodeRepository.getCountryUae())
                    PokusV2Helper.initPokus(true)
                    LoginUtils.setPreferredRegionFromCountry(CountryCodeRepository.getCountryUae())
                }
            )
            Text(text = "UAE")
        }
    }
}

@Composable
fun MainFunnels(
    currentFunnel: MutableState<FunnelContext?>,
    mainFunnelList: List<Pair<FunnelContext?, Triple<String, Int, String>>>,
    handleEvent: (HotelEvent) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .verticalPadding(16.dp)
    ) {
        mainFunnelList.forEach { funnelData ->
            if(funnelData.first == null || funnelData.first == currentFunnel.value) {
                Box(
                    modifier = Modifier
                        .weight(1f)
                ) {
                    FunnelSurface(funnelData, handleEvent)
                }
                Spacer(modifier = Modifier.width(5.dp))
            }
        }
    }
}

@Composable
fun FunnelSurface(
    data: Pair<FunnelContext?, Triple<String, Int, String>>,
    handleEvent: (HotelEvent) -> Unit
){
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .roundedCornerBorder(Color.White),
        color = Color.White,
        shape = roundedCornerShapes.large,
        shadowElevation = 4.dp,
        onClick = { handleEvent(HotelEvent(data.second.third)) }
    ) {
        Column {
            Image(
                painter = painterResource(data.second.second),
                contentDescription = "",
                Modifier
                    .width(70.dp)
                    .height(70.dp)
                    .align(Alignment.CenterHorizontally)
            )
            Text(
                modifier = Modifier
                    .padding(4.dp)
                    .fillMaxWidth()
                    .height(50.dp),
                text = data.second.first,
                color = Color.Black,
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                textAlign = TextAlign.Center
            )
        }
    }
}


@SuppressLint("RememberReturnType")
@Composable
fun OtherOptions(
    buttonList: List<Pair<String, String>>,
    navController: NavHostController?,
    handleEvent: (HotelEvent) -> Unit
) {

    Column() {
        buttonList.forEach { buttonData ->
            ButtonWithColor(
                text = buttonData.first,
                onClicked = {
                    handleEvent(HotelEvent(buttonData.second, navController))
                }
            )
            Spacer(modifier = Modifier.height(10.dp))
        }
    }


    ButtonWithColor(
        text = "Test",
        onClicked = { handleEvent(HotelEvent(AppLaunchActivityEvent.TEST_SCREEN,navController)) }
    )
    Spacer(modifier = Modifier.height(10.dp))
}

@Composable
fun ExperienceSwitch(modifier: Modifier) {

    val experienceMode = remember { mutableStateOf(SharedPreferenceUtils.getInstance().getString(SWConfigConstants.USER_CURRENT_STORE)) }
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        MmtComposeTextView(
            text = if (experienceMode.value == SWConfigConstants.LUXE_STORE) "Premium" else "Normal",
            modifier = Modifier
                .padding(bottom = dimensionResource(R.dimen.margin_large))
                .clickable {
                    experienceMode.value =
                        if (experienceMode.value == SWConfigConstants.LUXE_STORE) {
                            SWConfigConstants.MMT_STORE
                        } else {
                            SWConfigConstants.LUXE_STORE
                        }
                    SharedPreferenceUtils
                        .getInstance()
                        .putString(SWConfigConstants.USER_CURRENT_STORE, experienceMode.value)
                }
                .background(Color.Blue, RoundedCornerShape(dimensionResource(R.dimen.radius_small)))
                .padding(
                    all = dimensionResource(R.dimen.margin_small)
                ),
            color = Color.White
        )
    }

}

@Composable
fun ButtonWithColor(text: String, onClicked: () -> Unit) {
    Button(
        onClick = onClicked, shape = roundedCornerShapes.large, elevation = ButtonDefaults.buttonElevation(),
        colors = ButtonDefaults.buttonColors(containerColor = Color.White),
        modifier = Modifier.fillMaxWidth()
    )
    {
        Text(
            text = text,
            color = Color.Black,
            fontSize = 18.sp,
            fontFamily = FontFamily.SansSerif,
            textAlign = TextAlign.Center
        )
    }
}


@Composable
fun Login() {
    var buttonText by remember { mutableStateOf(loginText()) }

    val intent = MMTAuth.getIntentForLoginActivity(MMTCore.mContext, LoginPageExtra("Hotel App Login").apply {
        isVerifyMobile = false
        isShowAsBottomSheet = true
    })

    val activityLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        android.util.Log.i("Ankur","ActivityResultExample: $result ${result.resultCode}")
        buttonText = loginText()
    }


    ButtonWithColor(
        text = buttonText,
        onClicked = {
            if (LoginUtil.isLoggedIn()) {
                LogoutManager.logoutAll()
                buttonText = loginText()
            } else {
                if (intent != null) {
                    activityLauncher.launch(intent)
                }
            }
        }
    )
}

fun loginText(): String{
    return if(LoginUtil.isLoggedIn()) "Logout" else "Login"
}


