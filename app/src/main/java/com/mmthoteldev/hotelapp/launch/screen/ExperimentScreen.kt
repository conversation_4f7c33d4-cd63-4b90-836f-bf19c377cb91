package com.mmthoteldev.hotelapp.launch.screen


import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import com.mmt.hotel.base.events.HotelEvent
import com.mmthoteldev.hotelapp.launch.viewmodel.ExperimentScreenViewModel
import com.mmthoteldev.hotelapp.ui.uiResource.Grey90
import com.mmthoteldev.hotelapp.ui.uiResource.HotelAppTheme
import com.mmthoteldev.hotelapp.ui.uiResource.roundedCornerShapes
import com.mmthoteldev.hotelapp.util.AppLaunchActivityEvent.UPDATE_POKUS


@Composable
fun ExperimentScreen(navController: NavHostController, handleEvent: (HotelEvent) -> Unit) {
    val viewModel: ExperimentScreenViewModel = viewModel()

    val data = viewModel.displayData

    HotelAppTheme {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .background(color = Grey90)
        ) {
            // Create references for the composables to constrain
            val (search, list, button) = createRefs()

            SearchToolbar(
                onSearch = viewModel::filter,
                modifier = Modifier.constrainAs(search) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    height = Dimension.wrapContent
                }
            )

            LazyColumn(modifier = Modifier
                .constrainAs(list) {
                    top.linkTo(search.bottom, margin = 10.dp)
                    bottom.linkTo(button.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.matchParent
                    height = Dimension.fillToConstraints
                }
                .background(color = Grey90)) {
                items(data.value) { item ->
                    when (item.classType) {
                        java.lang.Boolean::class.java -> {
                            BooleanExperiment(
                                item = item,
                                viewModel = viewModel
                            )
                        }
                        Integer::class.java -> {
                            TextExperiment(
                                item = item,
                                viewModel = viewModel
                            )
                        }
                        String::class.java -> {
                            TextExperiment(
                                item = item,
                                viewModel = viewModel
                            )
                        }
                    }
                }
            }

            Button(modifier = Modifier
                .constrainAs(button) {
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    height = Dimension.wrapContent
                }
                .padding(6.dp)
                .wrapContentWidth(align = Alignment.CenterHorizontally),
                shape = roundedCornerShapes.extraSmall,
                onClick = {handleEvent(HotelEvent(UPDATE_POKUS,data = Pair(navController, viewModel.changedExps)))},
                colors = ButtonDefaults.buttonColors(containerColor = Color.Blue)) {
                Text(
                    text = "UPDATE",
                    color = Color.White,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Center
                )
            }
        }

    }
}


