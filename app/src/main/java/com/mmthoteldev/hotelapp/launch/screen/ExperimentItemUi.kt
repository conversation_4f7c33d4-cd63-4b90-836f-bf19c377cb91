package com.mmthoteldev.hotelapp.launch.screen


import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.mmthoteldev.hotelapp.launch.viewmodel.ExpItem
import com.mmthoteldev.hotelapp.launch.viewmodel.ExperimentScreenViewModel
import com.mmthoteldev.hotelapp.ui.uiResource.*

@Composable
fun BooleanExperiment(item: ExpItem, viewModel: ExperimentScreenViewModel) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .roundedCornerBorder(Color.White),
        color = Color.White,
        shape = roundedCornerShapes.large,
        shadowElevation = 1.dp
    ) {
        Row(modifier = Modifier.padding(6.dp)) {
            Text(
                text = item.key,
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 4.dp, end = 4.dp)
                    .align(Alignment.CenterVertically)
            )
            Switch(
                modifier = Modifier.padding(start = 4.dp, end = 4.dp),
                checked = item.value as Boolean,
                onCheckedChange = {newValue ->
                    viewModel.update(item, newValue)
                })
        }
    }
    Spacer(modifier = Modifier.height(8.dp))
}


@Composable
fun TextExperiment(item: ExpItem, viewModel: ExperimentScreenViewModel) {
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .roundedCornerBorder(Color.White),
        color = Color.White,
        shape = roundedCornerShapes.large,
        shadowElevation = 1.dp
    ) {
        Column(modifier = Modifier.padding(6.dp)) {
            Text(
                text = item.key,
                modifier = Modifier.padding(4.dp)
            )
            TextField(
                value = item.getItemVal(item.value!!).toString(),
                onValueChange = {newValue ->
                    viewModel.update(item, newValue)
                },
                modifier = Modifier.padding(4.dp)
            )
        }
    }
    Spacer(modifier = Modifier.height(8.dp))
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchToolbar(
    onSearch: (String) -> Unit,
    modifier: Modifier,
) {
    var searchExpanded by remember { mutableStateOf(false) }
    var searchText by remember { mutableStateOf("") }

    val appBarWidth by animateDpAsState(
        targetValue = if (searchExpanded) 0.dp else 56.dp,
        animationSpec = tween(300)
    )

    val focusRequester = remember { FocusRequester() }

    LaunchedEffect(searchExpanded) {
        if (searchExpanded) {
            focusRequester.requestFocus()
        }
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.CenterStart
    ) {
        TopAppBar(
            modifier = Modifier
                .fillMaxWidth()
                .shadow(4.dp),
            title = {
                Box(
                    Modifier.fillMaxSize(),
                    contentAlignment = Alignment.CenterStart
                ) {
                    AnimatedVisibility(
                        visible = searchExpanded,
                        enter = slideInHorizontally(initialOffsetX = { it }) + fadeIn(),
                        exit = slideOutHorizontally(targetOffsetX = { it }) + fadeOut()
                    ) {
                        TextField(value = searchText,
                            onValueChange = { newText ->
                                searchText = newText
                                onSearch(newText)
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .width(appBarWidth)
                                .padding(end = 16.dp)
                                .focusRequester(focusRequester),
                            singleLine = true,
                            placeholder = { Text("Search Exp...", color = Color.Gray) })
                    }
                    AnimatedVisibility(
                        visible = !searchExpanded,
                        enter = slideInHorizontally(initialOffsetX = { -it }) + fadeIn(),
                        exit = slideOutHorizontally(targetOffsetX = { -it }) + fadeOut()
                    ) {
                        Text(
                            "Experiments",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black
                        )
                    }
                }
            },
            actions = {
                IconButton(
                    onClick = { searchExpanded = !searchExpanded }
                ) {
                    Icon(
                        imageVector = Icons.Filled.Search,
                        contentDescription = "Search"
                    )
                }
            }
        )
    }
}


