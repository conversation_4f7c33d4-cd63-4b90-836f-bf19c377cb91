package com.mmthoteldev.hotelapp.launch

import android.view.Window
import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.mmt.hotel.base.events.HotelEvent
import com.mmthoteldev.hotelapp.launch.HotelAppActivity.Companion.EXPERIMENT_SCREEN
import com.mmthoteldev.hotelapp.launch.HotelAppActivity.Companion.LAUNCH_SCREEN
import com.mmthoteldev.hotelapp.launch.HotelAppActivity.Companion.TEST_SCREEN
import com.mmthoteldev.hotelapp.launch.screen.ExperimentScreen
import com.mmthoteldev.hotelapp.launch.screen.LaunchScreen
import com.mmthoteldev.hotelapp.launch.screen.TestScreen


@Composable
fun HotelAppUI(window: Window,handleEvent: (HotelEvent) -> Unit) {
    val navController = rememberNavController()

    NavHost(navController = navController, startDestination = LAUNCH_SCREEN) {
        composable(LAUNCH_SCREEN) {
            LaunchScreen(navController, handleEvent)
        }
        composable(EXPERIMENT_SCREEN) {
            ExperimentScreen(navController, handleEvent)
        }
        composable(TEST_SCREEN) {
            TestScreen(window, navController, handleEvent)
        }
    }
}


