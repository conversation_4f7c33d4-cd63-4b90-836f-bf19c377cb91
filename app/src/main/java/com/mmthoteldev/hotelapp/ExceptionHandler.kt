package com.mmthoteldev.hotelapp

import android.util.Log

/***
 * class for logging exceptions
 */
object ExceptionHandler {
    fun setupExceptionHandler() {
        val defaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            val message = "Uncaught exception in thread ${thread.name}:\n"
            Log.e("HotelApplication", message, throwable)
            defaultUncaughtExceptionHandler?.uncaughtException(thread, throwable)
        }
    }
}