package com.mmthoteldev.hotelapp.implementations

import android.app.Activity
import androidx.appcompat.app.AppCompatActivity
import com.mmt.core.model.webview.WebViewBundle
import com.mmt.data.model.interfaces.CommonNavigationInterface

class DummyNavigationImpl: CommonNavigationInterface {
    override fun openWebView(activity: Activity?, webViewBundle: WebViewBundle) {
    }

    override fun openAccountDeletionWebView(activity: Activity?, webViewBundle: WebViewBundle) {
    }

    override fun openMyWalletWebView(
        activity: AppCompatActivity,
        url: String,
        showActionBar: Boolean,
        sendMmtAuth: Boolean,
        isFinishOnBack: Boolean
    ) {

    }

    override fun launchHomeOnLangChange(activity: Activity?) {
    }

    override fun openWebView(activity: Activity?, webViewUrl: String?, webViewTitle: String?) {
    }
}