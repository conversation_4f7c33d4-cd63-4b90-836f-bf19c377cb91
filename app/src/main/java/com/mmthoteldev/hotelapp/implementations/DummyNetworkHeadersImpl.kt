package com.mmthoteldev.hotelapp.implementations

import android.content.Context
import android.os.Build
import android.text.TextUtils
import com.mmt.core.user.auth.LoginUtil
import com.mmt.data.model.network.NetworkConstants
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.CoreSharedPreferenceUtil
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.LOBS
import com.mmt.network.INetworkHeaders
import com.mmt.analytics.omnitureclient.OmnitureTracker
import com.mmt.core.MMTCore
import com.mmt.core.constant.CommonConstants.MMT_AUTH_HEADER
import com.mmt.core.currency.CurrencyUtil
import com.mmt.core.user.prefs.RegionEntity
import com.mmt.core.util.LocaleHelper
import com.mmt.core.util.StringUtil
import com.mmt.data.model.network.NetworkUtil

object DummyNetworkHeadersImpl: INetworkHeaders {
    var isGlobalDomainEnabled : Boolean = false
    private const val USER_CURRENCY_HEADER = "user-currency"
    private const val USER_COUNTRY_HEADER = "user-country"
    private const val ENTITY_NAME_HEADER = "entity-name"
    private const val GDPR_CONSENT_HEADER = "GDPR_C"
    private const val USER_PLATFORM = "platform"
    private const val USER_TENANT = "tenant"
    private const val USER_APP_VERSION = "appVersion"
    private const val USER_AUTH = "auth"

    private const val VALUE_TENANT = "MMT"
    private const val VALUE_PLATFORM = "mobile"
    override fun getDefaultHeaders(context: Context): Map<String, String?> {
        val headers = HashMap<String, String?>()
        headers[NetworkConstants.HEADER_DEVICE_OS] = "Android " + Build.VERSION.RELEASE
        headers[NetworkConstants.HEADER_APP_VERSION] = CoreUtil.getAppVersionNameWithoutRc()
        headers[NetworkConstants.HEADER_USER_AGENT] = CoreUtil.getUserAgent()
        headers[NetworkConstants.HEADER_ORG] = "mmt"
        //Omniture data
        val visitorId = OmnitureTracker.getVisitorID()

        if (visitorId != null) {
            headers[NetworkConstants.HEADER_VISITOR_ID] = visitorId
        }

        headers[NetworkConstants.HEADER_MCID] = OmnitureTracker.getMcid()
        headers[NetworkConstants.HEADER_TID] = CoreUtil.getUniqueDeviceId()

        // Set mmtAuth cookie if the user is logged-in
        val mmtAuth = LoginUtils.mmtAuth

        if (StringUtil.isNotNullAndEmpty(mmtAuth)) {
            headers[NetworkConstants.MMT_AUTH_HEADER] = mmtAuth
            headers[NetworkConstants.HEADER_COOKIE_NAME] = LoginUtil.getAuthCookieInHeaderFormat(mmtAuth)
            headers[NetworkConstants.HEADER_BACKUP_AUTH] = LoginUtil.getAuthCookieInHeaderFormat(mmtAuth)
        }

        headers[NetworkConstants.HEADER_DEVICE_ID] = CoreUtil.getDeviceId()
        headers[NetworkConstants.HEADER_PRIMARY_EMAIL] = CoreUtil.getPrimaryEmail()
            headers[NetworkConstants.HEADER_REGION] =
                LoginUtils.getPreferredRegionCode().lowercase()

        headers[NetworkConstants.HEADER_LANGUAGE] = LocaleHelper.getNetworkApiLanguage(LOBS.HOTEL.lob)
        headers[NetworkConstants.HEADER_CURRENCY] = LoginUtils.getFunnelContextCurrencyCodeV1()
        if(CurrencyUtil.isMultiCurrencyEnabled() && !LoginUtils.isCorporateUser){
            headers[USER_CURRENCY_HEADER] = LoginUtils.getPreferredCurrency().code
        }

        headers[USER_COUNTRY_HEADER] = LoginUtils.getUserCountry().countryCode

        if (isGlobalDomainEnabled) {
            headers[ENTITY_NAME_HEADER] = LoginUtils.getCurrentEntity()
        }
        MMTCore.coreInterface.getGDPRConsent()?.let {
            headers[GDPR_CONSENT_HEADER] = it
        }
        headers[USER_PLATFORM] = VALUE_PLATFORM
        headers[USER_TENANT] = VALUE_TENANT
        headers[USER_APP_VERSION] = CoreUtil.getAppVersionNameWithoutRc()
        headers[USER_AUTH] = mmtAuth.orEmpty()

        return headers
    }
}