package com.mmthoteldev.hotelapp.implementations

import android.content.Context
import android.text.Html
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.widget.TextView
import androidx.databinding.ViewDataBinding
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.model.webview.WebViewBundle
import com.mmt.core.util.CoreUtil
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.uikit.util.UiMigrator

class DummyUiMigrationImpl : UiMigrator {
    override fun getPokusValue(): Boolean {
        return true
    }

    override fun getPrimaryContact(): String? {
        TODO("Not yet implemented")
    }

    override fun isCorporateUser(): Boolean {
        return LoginUtils.isCorporateUser
    }

    override fun isValidUserLoggedIn(): Boolean {
        TODO("Not yet implemented")
    }

    override fun isAssetDirExists(lottieJsonPath: String): Boolean {
        return CoreUtil.isAssetDirExists(lottieJsonPath)
    }

    override fun isAssetExists(lottieJsonPath: String): Boolean {
        return CoreUtil.isAssetExists(lottieJsonPath)
    }

    override fun vibrateForDuration(context: Context, vibrateDuration: Long) {
        //AppUtils.vibrateForDuration(context, vibrateDuration)
    }

    override fun openWebView(context: Context, webViewBundle: WebViewBundle) {
       // AppUtils.openWebView(context, webViewBundle)
    }

    override fun setTextViewHTML(context: Context, textView: TextView, text: String) {
        //This is dummy implementation
        val sequence: CharSequence = Html.fromHtml(text)
        val strBuilder = SpannableStringBuilder(sequence)
        textView.text = strBuilder
        textView.movementMethod = LinkMovementMethod.getInstance()
    }

    override fun convertDpToPixel(dp: Float): Float {
        return CoreUtil.convertDpToPixel(dp)
    }

    override fun appendImageDownSizeParam(url: String, width: String, height: String): String {
        return HotelUtil.appendImageDownSizeParam(url)
    }

    override fun appendImageDownSizeParam(url: String, width: Int, height: Int): String {
        return HotelUtil.appendImageDownSizeParam(url, width, height)
    }

    override fun setDataVariable(binding: ViewDataBinding, entry: Any?) {
        binding.setVariable(com.mmt.hotel.BR.data, entry)
    }

    override fun getBindingResModel(): Int {
        return com.mmt.hotel.BR.model
    }

    override fun getCurrentAuth(): String? {
        TODO("Not yet implemented")
    }

    override fun getLoggedInUserEmail(): String? {
        TODO("Not yet implemented")
    }
}