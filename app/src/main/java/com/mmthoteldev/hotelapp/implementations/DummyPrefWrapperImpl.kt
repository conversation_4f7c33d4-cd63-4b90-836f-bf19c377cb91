package com.mmthoteldev.hotelapp.implementations

import com.mmt.hotel.common.constants.ScreenName
import com.mmt.hotel.listingV2.wrapper.HotelListingPrefWrapper
import com.mmt.hotel.common.HotelSharedPrefUtil

/**
 * Created by <PERSON><PERSON><PERSON> on 01/09/20.
 */
class DummyPrefWrapperImpl : HotelListingPrefWrapper {
    override fun isCorporateUser(): Boolean {
        return com.mmt.auth.login.util.LoginUtils.isCorporateUser
    }

    override fun getImageSizeFactor(): Float {
        return HotelSharedPrefUtil.instance.fetchImgSizeFactorFromPref(ScreenName.LIST)
    }

    override fun getNumberOfCoupons(): Int {
        return 2
    }

    override fun getNumberOfAddsOn(): Int {
        return 0
    }

    override fun searchRequestUpdate(isChanged: Boolean) {
    }

    /*Linked to old code to avoid duplicate code can be replaced here.*/
    override fun getNotificationCoupon(cityCode: String): String? {
        return null
    }

}