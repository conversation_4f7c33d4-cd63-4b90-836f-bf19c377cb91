package com.mmthoteldev.hotelapp.implementations

import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.country.CountryInfoHelper
import com.mmt.core.location.LocationUtil
import com.mmt.core.user.auth.AuthConstants
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.*
import com.mmt.core.util.DeviceInfo.screenDensityName
import com.mmt.data.model.network.NetworkUtil
import com.pdt.coreModels.DeviceDetails
import com.pdt.coreModels.DomainDetails
import com.pdt.coreModels.EntityInfo
import com.pdt.coreModels.PDTLocation
import com.pdt.coreModels.UserDetails
import com.pdt.interfaces.IPDTAnalytics
import com.pdt.pdtDataLogging.PDTAnalyticsUtil
import okhttp3.Interceptor

object DummyAnalyticsImpl : IPDTAnalytics {
    override fun isNotificationEnabled(): Boolean {
        return false
    }

    //Analytics callbacks
    override fun scheduledAlarmForBucketFlush(scheduleTimeInSeconds: Int, isPersonalized: Boolean) {
    }

    override fun isAlarmUpForBucketFlush(isPersonalized: Boolean): Boolean {
        return true
    }

    override fun cancelAlarmForPDTBucketFlush(isPersonalized: Boolean) {

    }

    override fun setIsSyncing(isSyncing: Boolean) {
    }

    override fun updateSyncInformation() {
    }

    override fun getUserDetails(): UserDetails {
        val userDetails = UserDetails(LoginUtils.isLoggedIn)
        userDetails.address = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getString(LocationUtil.HOME_ADDRESS)
        userDetails.city = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getString(LocationUtil.CITY)
        userDetails.state = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getString(LocationUtil.STATE)
        userDetails.country = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getString(LocationUtil.COUNTRY_NAME)

        userDetails.userMobileVerified = LoginUtils.isAnyNumberVerified
        userDetails.userEmailVerified = LoginUtils.isUserEmailVerified
        userDetails.userProfileType = LoginUtils.profileType
        userDetails.userPrimaryCity = LoginUtils.primaryCity
        userDetails.uuid = LoginUtils.uuid
        userDetails.userWalletAmount = MMTCore.iAuth.getTotalWalletAmount()
        userDetails.isCorporateUser = LoginUtils.isCorporateUser
        userDetails.hasCorporateUser = LoginUtils.isCorporateUser
        userDetails.userLoginChannel = LoginUtils.loginTypeForAnalytics

        userDetails.userOrganisationId = LoginUtils.orgId
        userDetails.isCorpUserAdmin = LoginUtils.isCorpUserAdmin
        userDetails.employeeStatus = LoginUtils.employeeStatus
        return userDetails
    }

    override fun getDomainDetails(): DomainDetails {
        val domainDetails = DomainDetails()
        val selectedRegion = LoginUtils.getPreferredRegion()
        val domainRegionCode = selectedRegion.code
        val domainCurrency = LoginUtils.getRegionCurrencyCode()
        domainDetails.hydraSegment = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getString(CoreSharedPreferenceUtil.HYDRA_USER_SEGMENT)
        domainDetails.trafficType = CoreConstants.EMPTY_STRING
        domainDetails.domainSbu = LoginUtils.getFunnelContextValue()
        domainDetails.domainCountry = domainRegionCode
        domainDetails.domainCurrency = domainCurrency
        domainDetails.domainLanguage = LocaleHelper.getCurrentApiLanguage()
        domainDetails.countrySim = CountryInfoHelper.countrySim
        domainDetails.countryNetwork = CountryInfoHelper.networkCountry
        domainDetails.countryUserLoginCode = CountryInfoHelper.userLoginCode()
        domainDetails.countryTimeZone = CountryInfoHelper.countryTimeZone
        domainDetails.countryTimeZoneId = CountryInfoHelper.countryTimeZoneId

        return domainDetails
    }

    override fun getEntityInfo(): EntityInfo {
        return EntityInfo()
    }

    override fun getIdentities(): Map<String, String?> {
        return mutableMapOf()
    }

    override fun getDeviceDetails(): DeviceDetails {
        val deviceDetails = DeviceDetails()
        deviceDetails.deviceID = CoreUtil.getDeviceId()
        deviceDetails.sessionID = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getInt(CoreSharedPreferenceUtil.VISITOR_NUMBER)
        deviceDetails.appVersionName = CoreUtil.getAppVersionName()
        deviceDetails.appVersionCode = CoreUtil.getAppVersionCode().toString()
        deviceDetails.legacyVisitorID = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getString("omniture_visitor_id", "vid")
        deviceDetails.deviceManufacturer = DeviceUtil.getDeviceManufacturer()
        deviceDetails.deviceLanguage = DeviceUtil.getDeviceLanguage()
        deviceDetails.deviceModel = DeviceUtil.getDeviceModel()
        deviceDetails.totalRAMinGB =
            Utils.getDoubleFromString(DeviceUtil.getTotalRAMinGB(MMTCore.mContext))
        deviceDetails.deviceArchitecture = DeviceUtil.getDeviceArchitecture()
        deviceDetails.screenResolution = DeviceUtil.getScreenResolution(MMTCore.mContext)
        deviceDetails.screenDimensionInInches =
            DeviceUtil.getScreenDimensionInInches(MMTCore.mContext)
        deviceDetails.processorMaxFrequency =
            Utils.getIntFromString(DeviceUtil.getProcessorMaxFrequency())
        deviceDetails.screenDensityName = screenDensityName
        deviceDetails.installDateInGMT = CoreUtil.getInstallDateInGMT()
        deviceDetails.grantedPermissions = CoreUtil.getGrantedPermissions()

        deviceDetails.totalMemoryDeviceInGB = DeviceUtil.getTotalMemoryDeviceInGB()
        deviceDetails.totalExternalMemoryInGB =
            Utils.getDoubleFromString(DeviceUtil.getTotalExternalMemoryInGB())
        deviceDetails.marketingCloudId = CoreSharedPreferenceUtil.getInstance(MMTCore.mContext)
            .getString("marketing_cloud_id", "mcid")
        deviceDetails.daysSinceInstall = Utils.getIntFromString(CoreUtil.getDaysSinceInstall())
        deviceDetails.userAgent = CoreConstants.ANDROID + "_" + CoreUtil.getAppVersionName()

        return deviceDetails
    }

    override fun getUserLoyalityStatus(): Int {
        val loyaltyStatus: String = LoginUtil.getLoyaltyStatus()
        var status = PDTAnalyticsUtil.BlackStatus.INACTIVE
        when (loyaltyStatus) {
            AuthConstants.LoyaltyStatuses.BLACKLIST -> status =
                PDTAnalyticsUtil.BlackStatus.BLACKLIST
            AuthConstants.LoyaltyStatuses.ACTIVE -> status = PDTAnalyticsUtil.BlackStatus.ACTIVE
            AuthConstants.LoyaltyStatuses.INACTIVE -> status = PDTAnalyticsUtil.BlackStatus.INACTIVE
        }
        return status
    }

    override fun getUserPrimeStatus(): String {
        return LoginUtil.getBlackUserTierName()
    }

    override fun getPullNotificationUrl(): String? {
        TODO("Not yet implemented")
    }

    override fun getCompleteDataLoggingStatus(): Boolean {
        TODO("Not yet implemented")
    }

    override fun getLocation(): PDTLocation? {
        val locationWrapper = LocationUtil.getLastKnownLocationFromCacheInObj()
        locationWrapper ?: return null
        val location = PDTLocation(locationWrapper.lat, locationWrapper.lng)
        return location
    }

    override fun getDefaultHeaders(): Map<String, String?> {
        return DummyNetworkHeadersImpl.getDefaultHeaders(MMTCore.mContext)
    }

    override fun getDefaultInterceptors(): List<Interceptor> {
        return NetworkUtil.getDefaultInterceptors()
    }
}