package com.mmthoteldev.hotelapp.implementations

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.widget.Toast
import com.gommt.gommt_auth.v2.common.helpers.UserServiceErrorHelper
import com.gommt.gommt_auth.v2.common.utils.TermsAndConditionUtil
import com.mmt.analytics.omnitureclient.Events
import com.mmt.auth.login.helper.LoginNetworkHelper
import com.mmt.auth.login.helper.LoginTrackingHelper
import com.mmt.auth.login.helper.UserServiceRequestHelper
import com.mmt.auth.login.model.LoginRequestModel
import com.mmt.auth.login.model.login.DomainType
import com.mmt.auth.login.model.login.User
import com.mmt.auth.login.model.response.otpResponse.OTPResponse
import com.mmt.auth.login.model.userservice.MobileNumber
import com.mmt.auth.login.model.userservice.Traveller
import com.mmt.auth.login.model.userservice.UserIdentifier
import com.mmt.auth.login.util.*
import com.mmt.auth.login.util.LoginDBHelper.getIntentForgotPwd
import com.mmt.auth.login.verification.helper.VerificationHelper
import com.mmt.core.MMTCore
import com.mmt.core.constant.BaseLoginConstants
import com.mmt.core.constant.CommonConstants
import com.mmt.core.constant.LoginControllerConstants
import com.mmt.core.user.prefs.CountryCodeRepository
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ApiLanguage
import com.mmt.core.util.ResourceProvider
import com.mmt.core.util.RxUtils
import com.mmt.core.util.StringUtil
import com.mmt.data.model.util.SharedPreferenceUtils
import com.mmt.network.model.HttpRequest
import com.mmt.network.model.HttpResponseException
import com.mmt.network.model.NetworkRequest
import com.mmt.profile.ProfileModule
import com.mmt.profile.interfaces.ProfileLoginInterface
import com.mmt.profile.tracking.MyAccountTrackingHelper
import com.mmt.profile.ui.MyAccountFragment
import com.mmt.profile.utils.ProfileConstants
import com.mmthoteldev.hotelapp.R
import io.reactivex.disposables.CompositeDisposable

class ProfileLoginImpl: ProfileLoginInterface {
    override fun getLoggedInUser():   User? {
        return LoginUtils.loggedInUser
    }

    override fun isLoggedIn(): Boolean {
        return LoginUtils.isLoggedIn
    }

    override fun isCorporateUser(): Boolean {
        return LoginUtils.isCorporateUser
    }

    override fun isPersonalUser(): Boolean {
        return LoginUtils.isPersonalUser
    }

    override fun getCurrentAuth(): String? {
        return LoginUtils.mmtAuth
    }

    override fun getUUID(): String? {
        return LoginUtils.uuid ?: ""
    }

    override fun getCorporateUser():   User? {
        return LoginUtils.getCorporateUser()
    }

    override fun getPersonalUser():   User? {
        return LoginUtils.getPersonalUser()
    }

    override fun isHostUser(): Boolean {
        return LoginUtils.isHostUser
    }

    override fun getLoggedInUserPrimaryContact(): MobileNumber? {
        return LoginUtils.loggedInUserPrimaryContact
    }

    override fun updateProfileCompletion(completionScore: String?, isCorp: Boolean) {
        LoginUtils.updateProfileCompletion(completionScore,isCorp, if(LoginUtils.isIndiaFunnelContext()) DomainType.INDIA else DomainType.GLOBAL)
    }

    override fun logout(activity: Activity) {
        /*val intent = Intent(activity, HomeBroadcastReceiver::class.java)
        if (LoginUtils.isCorporateUser) {
            intent.action = LoginConstants.ACTION_LOGOUT_CORPORATE
            HomeIconConfigRepository.deleteLobIconResponse(true) // For Deleting Lob Icons

        } else {
            intent.action = LoginConstants.ACTION_LOGOUT_PERSONAL
            HomeIconConfigRepository.deleteLobIconResponse(false) // For Deleting Lob Icons

        }
        activity.sendBroadcast(intent)*/
        MyAccountTrackingHelper.handleComponentClickTacking(
            Events.EVENT_MYACCOUNT_LANDING,
            "My_Account_Logout"
        )
        SharedPreferenceUtils.getInstance().removePreference(ProfileConstants.CHAT_CONVERSATION_ID)
        SharedPreferenceUtils.getInstance().removePreference(ProfileConstants.CHAT_STATE_ID)

    }

    override fun resetPassword(
        activity: Activity?,
        emailId: String?,
        mobileNumberPrimary: MobileNumber?,
        compositeDisposable: CompositeDisposable,
        changeVisibility: ((visible: Boolean) -> Unit)?
    ) {
        val loginRequestModel = LoginRequestModel()

        if (StringUtil.isNotNullAndEmpty(emailId)) {
            loginRequestModel.loginIdentifier = emailId
        } else {
            loginRequestModel.loginIdentifier = mobileNumberPrimary?.mobileNumber
            loginRequestModel.countryCode = mobileNumberPrimary?.countryCode
        }
        val loginHelper = LoginNetworkHelper()
        compositeDisposable.add(loginHelper.getOTPResponseFor(
            BaseLoginConstants.SEND_FORGET_PASSWORD_OTP,
            loginRequestModel,
            ProfileModule.loginInterface.isCorporateUser()
        )
            .compose(RxUtils.applyNetworkExecutor())
            .subscribe(
                { otpResponse ->
                    handleSendMobileOTPResponse(activity, otpResponse, changeVisibility)
                },
                { throwable: Throwable ->
                    handleSendMobileOTPFailed(activity, throwable, changeVisibility)
                }
            ))
    }

    private fun handleSendMobileOTPFailed(
        activity: Activity?,
        throwable: Throwable,
        changeVisibility: ((visible: Boolean) -> Unit)?
    ) {
        changeVisibility?.invoke(false)
        var otpResponse: OTPResponse? = OTPResponse()
        if (throwable is HttpResponseException) {
            otpResponse = throwable.getErrorResponseBody(OTPResponse::class.java)
            if (otpResponse == null) {
                otpResponse = OTPResponse()
            }
            otpResponse.httpCode = throwable.errorCode
            otpResponse.message =
                UserServiceErrorHelper.getForgotPwdSendOTPAPIErrorMsg(
                    otpResponse
                )
        } else {
            otpResponse?.message = UserServiceErrorHelper.getDefaultErrorMsg()
        }
        handleSendMobileOTPResponse(activity, otpResponse, changeVisibility)
    }

    private fun handleSendMobileOTPResponse(
        activity: Activity?,
        otpResponse: OTPResponse?,
        changeVisibility: ((visible: Boolean) -> Unit)?
    ) {
        changeVisibility?.invoke(false)
        if (otpResponse == null) {
            ResourceProvider.instance.showToast(
                R.string.htl_SOMETHING_WENT_WRONG,
                Toast.LENGTH_SHORT
            )
            return
        }
        if (!otpResponse.isSuccess) {
            ResourceProvider.instance.showToast(otpResponse.message, Toast.LENGTH_SHORT)
            return
        }
        val loginRequestModel = LoginRequestModel()
        if (StringUtil.isNotNullAndEmpty(LoginUtils.loggedInUserEmail)) {
            loginRequestModel.loginIdentifier = LoginUtils.loggedInUserEmail
        } else {
            val mobileNumber: MobileNumber? =
                ProfileModule.loginInterface.getLoggedInUserPrimaryContact()
            if (mobileNumber != null) {
                loginRequestModel.countryCode = mobileNumber.countryCode
                loginRequestModel.loginIdentifier = mobileNumber.mobileNumber
            }
        }
        val intent = getIntentForgotPwd(
            activity,
            otpResponse,
            ProfileModule.loginInterface.isCorporateUser()
        )
        activity?.startActivityForResult(intent, MyAccountFragment.REQUEST_FORGOT_PWD)
    }

    override fun getSendOTPDisplayErrorForCode(
        exception: HttpResponseException,
        identifier: String?,
        pageName: Events?
    ): String? {
        return VerificationHelper().getSendOTPDisplayErrorForCode(exception, identifier, pageName)
    }

    override fun getLoginControllerRequest(requestId: Int, data: Any?): HttpRequest? {
        return LoginController().getRequest(requestId, data)
    }

    override fun getLoginControllerNetworkRequest(requestId: Int, data: Any?): NetworkRequest? {
        return LoginController().getNetworkRequest(requestId, data)
    }

    override fun getAuthWithUserIdentifierHeaderMap(): MutableMap<String, String?> {
        return LoginController.authWithUserIdentifierHeaderMap
    }

    override fun getCurrentUserIdentifier(): UserIdentifier? {
        return LoginController.getUserIdentifier(
            LoginControllerConstants.MMT_AUTH,
            LoginUtils.mmtAuth
        )
    }

    override fun getCoTravellerModificationRequest(user: User, coTraveller: Traveller): String? {
        return UserServiceRequestHelper().getCoTravellerModificationRequest(user, coTraveller)
    }

    override fun deleteCoTravellerWithId(deletedTravellers: List<Int>): Boolean {
        return LoginDBHelper.deleteCoTravellerWithId(deletedTravellers)
    }

    override fun getHeaderForImageUpload(user:   User): MutableMap<String, String> {
        return LoginController.getHeaderForImageUpload(user)
    }

    override fun privacyPolicyClicked(pageName: Events) {
        LoginTrackingHelper.privacyPolicyClicked(pageName)
        val privacyUrl = LoginConstants.MMT_PRIVACY_POLICY_URL
        /*CommonMigrationHelper.instance.openWebView(
            MMTCore.mContext,
            privacyUrl,
            CommonConstants.MAKEMYTRIP
        )*/
    }

    override fun userAgreementClicked(pageName: Events) {
        LoginTrackingHelper.userAgreementClicked(pageName)
        val userAgreementUrl = TermsAndConditionUtil.getTncUrls(
            LoginConstants.MMT_USER_AGREEMENT,
            CountryCodeRepository.getCountryIndia().nameCode, ApiLanguage.API_ENGLISH_LOCALE.lang
        )
        /*CommonMigrationHelper.instance.openWebView(
            MMTCore.mContext, userAgreementUrl,
            ResourceProvider.instance.getString(R.string.htl_REVIEW_USER_AGREEMENT)
        )*/
    }

    override fun userTnCClicked(pageName: Events) {
        LoginTrackingHelper.termsConditionClicked(pageName)
        val tncUrl = TermsAndConditionUtil.getTncUrls(
            LoginConstants.MMT_TNC_URL,
            CountryCodeRepository.getCountryIndia().nameCode,
            ApiLanguage.API_ENGLISH_LOCALE.lang
        )
        /*CommonMigrationHelper.instance.openWebView(
            MMTCore.mContext, tncUrl,
            ResourceProvider.instance.getString(R.string.htl_TERMS_OF_SERVICE)
        )*/
    }

    override fun hitUpdateContactInfo(context: Context) {
        LoginUtils.hitUpdateContactInfo(context)
    }

    override fun getChangePasswordRequestData(oldPassword: String, newPassword: String): String {
        return UserServiceRequestHelper().getChangePwdRequestData(
            oldPassword, newPassword
        )
    }

    override fun checkValidMobileNumber(loginIdentifier: String?, countryCode: String?): Boolean {
        return AuthMigrationHelper.instance.checkMobileNumber(
            loginIdentifier, countryCode
        )
    }
}