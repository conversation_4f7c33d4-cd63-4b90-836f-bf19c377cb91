package com.mmthoteldev.hotelapp.implementations

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.gommt.travelplex.bridge.ChatPageData
import com.gommt.travelplex.bridge.TravelplexChatViewModel
import com.mmt.travelplex.ITravelPlexViewWrapper

class TravelPlexViewWrapper : ITravelPlexViewWrapper {

    override fun initTravelPlexChatWindow(context: Context): ViewGroup? {
        return null
    }

    override fun initTravelPlexChatWindow(context: Context, attrs: AttributeSet?): ViewGroup? {
        return null
    }

    override fun initTravelPlexChatWindow(context: Context, attrs: AttributeSet?, defStyleAttr: Int): ViewGroup? {
        return null
    }

    override fun initTravelPlexChatWindow(
            context: Context, attrs: AttributeSet?,
            defStyleAttr: Int,
            defStyleRes: Int,
    ): ViewGroup? {
        return null
    }

    override fun showChatBot(instanceId: Int, chatPageData: ChatPageData, initialMessage: String?) {
//        travelPlexChatBotView?.getChatViewModel()?.showChatBot(instanceId, chatPageData, initialMessage)
    }

    override fun hideChatBot(instanceId: Int, shouldCollapse: Boolean) {
//        travelPlexChatBotView?.getChatViewModel()?.hideChatBot(instanceId)
    }

    override fun invalidateChatData(instanceId: Int, chatPageData: ChatPageData) {
//        travelPlexChatBotView?.getChatViewModel()?.invalidateChatData(instanceId, chatPageData)
    }

    override fun invalidate() {
//        travelPlexChatBotView?.invalidate()
    }

    override fun observerVisibility(): LiveData<Boolean> {
        return MutableLiveData(false)
    }

    override fun getTravelPlexView(): ViewGroup? {
        return null
    }

    override fun getTravelPlexViewModel(): TravelplexChatViewModel? {
        return null//travelPlexChatBotView?.getChatViewModel()
    }

    override fun getInstanceId(): Int? {
        return 0//travelPlexChatBotView?.getInstanceId()
    }
}