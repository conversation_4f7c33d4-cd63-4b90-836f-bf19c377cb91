package com.mmthoteldev.hotelapp.implementations

import android.database.sqlite.SQLiteDatabase
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.country.models.Region
import com.mmt.core.currency.Currency
import com.mmt.core.currency.CurrencyV1
import com.mmt.core.interfaces.IAuth
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.selectedCurrency

object DummyAuthImpl : IAuth {

    override fun getPreferredRegion(): Region {
        return LoginUtils.getPreferredRegion()
    }

    override fun isMultiCurrencyEnabled(): Boolean {
        return  false
    }

    override fun getSelectedCurrency(): CurrencyV1 {
        return selectedCurrency
    }

    override fun getSelectedCurrencyCode(): String {
        return com.mmt.core.util.getSelectedCurrencyCode()
    }

    override fun putIntoHotelSharedPref(key: String, value: String) {
    }

    override fun getStringFromHotelSharedPref(key: String, default: String?): String? {
        return null
    }

    override fun getAuthForDatabaseEncryption(db: SQLiteDatabase): String? {
        return LoginUtils.getOldMmtAuthForUser(db)
    }

    fun getDeviceId(): String? {
        return CoreUtil.getDeviceId()
    }

    override fun getHtlSelectedCurrency(): CurrencyV1? {
        return null
    }

    override fun isHtlMultiCurrencyEnabled(): Boolean {
        return false
    }

    override fun getPrimaryEmail(): String? {
        return LoginUtils.loggedInUserEmail
    }

    override fun getPrimaryContactCountryCode(): String? {
        return LoginUtils.loggedInUserPrimaryContact?.countryCode
    }

    override fun getMmtAuth(): String? {
        return LoginUtils.mmtAuth
    }

    override fun isCorporateUser(): Boolean {
        return LoginUtils.isCorporateUser
    }

    override fun isLoggedIn(): Boolean {
        return LoginUtils.isLoggedIn
    }

    override fun getUUID(): String? {
        return LoginUtils.uuid
    }

    override fun getOrgId(): String? {
        return LoginUtils.orgId
    }

    override fun getLoginType(): String? {
        val user = LoginUtils.loggedInUser
        return user?.loginType
    }

    override fun getPolicyId(): String? {
        return LoginUtils.policyId
    }

    override fun getPreferredCurrency(): Currency {
        return LoginUtils.getPreferredCurrency()
    }

    override fun isCorpUserAdmin(): Boolean {
        return LoginUtils.isCorpUserAdmin
    }

    override fun getLoyaltyStatus(): String {
        //todo vivek
        return "AppUtils.getLoyaltyStatus()"
    }

    override fun getTotalWalletAmount(): Double? {
        return 0.0
    }

    override fun getBlackUserTierName(): String {
        return ""
    }

}