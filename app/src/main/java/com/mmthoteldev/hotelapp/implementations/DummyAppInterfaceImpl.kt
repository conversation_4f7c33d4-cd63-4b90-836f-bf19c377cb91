package com.mmthoteldev.hotelapp.implementations

import android.content.Intent
import android.widget.Toast
import androidx.fragment.app.FragmentActivity
import com.mmt.core.country.models.RegionData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.currency.Currency
import com.mmt.core.interfaces.AppInterface
import com.mmt.core.util.LOBS
import com.mmt.core.util.ResourceProvider

class DummyAppInterfaceImpl : AppInterface {
    override fun getCachedOrBundledCountriesList(): List<RegionData> {
        return emptyList()
    }

    override fun getCurrencyInfo(currencyCode: String?) : Currency? {
        return null
    }

    override fun getPaymentIntent(
        pokusLobName: LOBS,
        checkoutId: String,
        thankYouIntentAction: String,
        lob: String?,
        qcMeta: Any?
    ): Intent {
        val intent = Intent()
        intent.action = CoreConstants.ACTION_LAUNCH_PAYMENT_HOME_V2
        return intent
    }

    override fun openCurrencySelector(
        fragmentActivity: FragmentActivity,
        onCurrencySelected: (Currency) -> Unit
    ) {
        ResourceProvider.instance.showToast("Build Mobile app for MultiCurrency feature", Toast.LENGTH_LONG)
    }
}