package com.mmthoteldev.hotelapp.implementations

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.widget.Button
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import com.mmt.auth.login.model.login.User
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.auth.login.model.userservice.ExtendedUser
import com.mmt.auth.login.model.userservice.MobileNumber
import com.mmt.auth.login.model.userservice.Traveller
import com.mmt.auth.login.util.LoginDBHelper
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.MPermission.PermissionManager
import com.mmt.core.MPermission.PermissionUtil
import com.mmt.core.base.BaseActivity
import com.mmt.core.constant.CoreConstants
import com.mmt.core.model.AnimationData
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.util.CoreSharedPreferenceUtil
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.DeviceUtil
import com.mmt.core.util.SerializerUtil
import com.mmt.data.model.homepage.personalizationSequenceAPI.response.locationdata.LocationData
import com.mmt.data.model.location.LocationCallback
import com.mmt.data.model.model.GccTabType
import com.mmt.data.model.ui.ProgressBarGreenTint
import com.mmt.data.model.util.ICommonMigrator
import com.mmt.network.logging.latency.LatencyTrackingUtils
import com.mmt.network.model.HttpRequest
import com.mmt.network.model.NetworkRequest
import com.mmt.pokus.PokusV2Helper
import com.mmt.uikit.util.UiUtil
import com.mmthoteldev.hotelapp.HotelApplication
import okhttp3.Headers
import okhttp3.Request
import java.io.File
import java.io.Serializable
import android.app.Fragment as DeprecatedFragmentType

class DummyCommonMigratorImpl : ICommonMigrator {

    override fun getLocation(
        needHighAccuracy: Boolean?,
        locationCallback: LocationCallback?,
        timeOut: Int?
    ) {
    }

    override fun getRequestCheckSetting(): Int? {
        return null
    }

    override fun handleInteractiveNotification(context: Context?, intent: Intent?) {
    }

    override fun handleCustomNotificationOpened(intent: Intent?) {
    }

    override fun isShouldUseBusReactNative(): Boolean? {
        return false
    }

    override fun writeToClipboard(mDealCode: String?) {
    }

    override fun copyCodeAndShowToast(context: Context?, mDealCode: String?) {
    }

    override fun getLastKnownLocation(): String? {
        return null
    }

    override fun isLocationEnabled(): Boolean? {
        return true
    }

    override fun openDeepLink(link: String?, context: Context?): Boolean? {
        return true
    }

    override fun openActionViewFor(activity: Activity?, url: String?) {
    }

    override fun checkIfBottomBarMoreMenuIsOpen(activity: BaseActivity?): Boolean? {
        return false
    }

    override fun handleHolidayOpenNative(url: String?, activity: Activity?): Boolean? {
        return false
    }

    override fun handleProtocolBasedDeepLink(
        baseWebViewActivity: Activity?,
        url: String?,
        b: Boolean?
    ): Boolean? {
        return false
    }

    override fun shouldHandleInternally(uri: Uri?): Boolean? {
//        return uri?.let {
//            DeepLinkImpl().shouldHandleInternally(uri)
//        }
        return false
    }

    override fun handleActionNotificationAirpush(bundle: Bundle?, mButton: Button?) {

    }

    override fun refreshPokus(onBackGround: Boolean) {
        PokusV2Helper.refreshPokus(true)
    }

    override fun callGetConfigAPI() {
    }


    override fun getUserAgent(): String {
        return CoreUtil.getUserAgent()
    }

    override fun insertOrUpdateCoTravellerData(coTravellerList: List<Any>, mAction: Int) {
    }

    override fun openAppSettings(activity: Activity?) {

    }

    override fun startWishListWorker(data: List<Pair<String?, String?>>) {
    }

    override fun fetchPersonalizationData(event: String) {
    }

    override fun fetchDataFromDB() {
    }

    override fun updateRecentSearch(lob: String?, searchContext: Any?, datasetName: String?) {
    }

    override fun loadGccHomepageLandingFragmentByTabType(
        gccTabType: GccTabType,
        bundle: Bundle
    ): Fragment? {
        return null
    }

    override fun logFBEvent(eventName: String, parameters: Bundle) {
        //TODO("Not yet implemented")
    }

    override fun scrollToTop(fragment: Fragment, tabType: GccTabType) {
    }

    override fun makeEmperiaCall(fragment: Fragment, tabType: GccTabType) {
    }

    override fun shouldShowGSTNWidget(): Boolean {
        return false
    }

    override fun showOfferBottomCTA(mUrl: String, mLob: String): Boolean? {
        return false
    }

    override fun refreshUserData() {
    }

    override fun updateHighIntentDismissPdt(data: Any?) {
    }

//    override fun getRequestCheckSetting(): Int? {
//        return null
//        //TODO("Not yet implemented")
//    }
}