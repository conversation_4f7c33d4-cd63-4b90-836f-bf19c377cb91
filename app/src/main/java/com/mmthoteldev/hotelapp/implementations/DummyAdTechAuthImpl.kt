package com.mmthoteldev.hotelapp.implementations

import android.content.Context
import com.gommt.adtech.utils.IAdTechAuth
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.user.prefs.RegionEntity
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.LocaleHelper

class DummyAdTechAuthImpl(private val context: Context) : IAdTechAuth {

    companion object {
        fun get(): DummyAdTechAuthImpl {
            val context = MMTCore.mContext
            return DummyAdTechAuthImpl(context)
        }
    }

    override fun getAuthToken(): String {
        return LoginUtils.mmtAuth.toString()
    }

    override fun getLanguage(): String {
        return LocaleHelper.getCurrentApiLanguage()
    }

    override fun getRegion(): String {
        return LoginUtils.getPreferredRegionCode()
    }

    override fun getEntityName(): String {
        return getEntity()
    }

    private fun getEntity(): String {
        return if (LoginUtils.getPreferredRegionCode().equals(RegionEntity.IN.code, true)) {
            "india"
        } else {
            "global"
        }
    }
}
