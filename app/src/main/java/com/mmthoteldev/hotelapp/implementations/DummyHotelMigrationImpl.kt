package com.mmthoteldev.hotelapp.implementations

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.net.Uri
import android.os.Bundle
import android.os.IBinder
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.Composable
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.mmt.analytics.omnitureclient.OmnitureTrackingHelper
import com.mmt.analytics.pdtclient.PdtActivityName
import com.mmt.analytics.pdtclient.PdtPageName
import com.mmt.auth.login.model.LoginPageExtra
import com.mmt.core.MMTCore
import com.mmt.core.extensions.ActivityResultLifeCycleObserver
import com.mmt.core.model.webview.WebViewBundle
import com.mmt.core.util.GsonUtils
import com.mmt.data.model.homepage.empeiria.cards.CardTemplateData
import com.mmt.data.model.homepage.empeiria.cards.adtech.v1.AdTechCardData
import com.mmt.data.model.homepage.empeiria.cards.recentsearches.RecentSearchesCardData
import com.mmt.data.model.homepage.empeiria.interfaces.HotelEmperiaRequestData
import com.mmt.data.model.homepage.empeiria.interfaces.IHomePersonalizationRepo
import com.mmt.data.model.homepage.empeiria.response.EmpeiriaResponse
import com.mmt.data.model.homepage.searchevent.PageSearchType
import com.mmt.data.model.homepage.searchevent.SearchEventLob
import com.mmt.data.model.homepage.wrapper.CustomerUnreadMessageData
import com.mmt.data.model.homepagex.skywalker.multiviewlist.TemplateViewModel
import com.mmt.hotel.common.constants.HotelFunnel
import com.mmt.hotel.common.constants.ScreenName
import com.mmt.hotel.common.model.HotelSuggestionData
import com.mmt.hotel.common.model.MyraPreBookChatData
import com.mmt.hotel.common.model.response.HotelWishlistResponse
import com.mmt.hotel.common.util.HotelUtil
import com.mmt.hotel.common.util.IHotelMigrator
import com.mmt.hotel.landingV3.dataModel.HotelCardListFragmentData
import com.mmt.hotel.landingV3.model.EmperiaEventData
import com.mmt.hotel.landingV3.model.request.SearchRequest
import com.mmt.hotel.landingV3.ui.compose.HotelLandingCardsVMInterface
import com.mmt.hotel.listingV2.dataModel.ListingData
import com.mmt.hotel.wishlist.model.WishlistBottomSheetData
import com.mmt.hotel.wishlist.repository.HotelWishlistRepository
import com.mmt.network.model.NetworkResponse
import com.mmt.network.model.ResponseWrapper
import com.mmt.pokus.model.PokusResponseV2
import com.mmt.skywalker.ui.gommt.EmptyItemMapper
import com.mmt.skywalker.ui.mapper.IModelTemplateMapper
import com.mmthoteldev.hotelapp.R
import com.mmthoteldev.hotelapp.util.HotelPokusHelper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean


class DummyHotelMigrationImpl : IHotelMigrator {

    /**
     * Used for tracking
     */
    override fun updateSearchEvents(@ScreenName screenName: String, cityName: String?, hotelId: String?) {
    }

    override fun openDeepLink(redirectUrl: String, backToParent: Boolean,context: Context, flags:Int?) {
    }

    override fun initAppLaunchService(context: Context, appLaunchServiceConnection: ServiceConnection) {
    }

    override fun hitPersonalizationApiAppLaunchService(binder: IBinder?) {
    }

    override fun getCustomerUnreadMessageData(): LiveData<CustomerUnreadMessageData?> {
        return MutableLiveData()
    }

    override fun setCustomerUnreadMessageData(data: CustomerUnreadMessageData?) {
    }

    override fun postCustomerUnreadMessageData(context: Context, data: CustomerUnreadMessageData?) {
    }

    override fun isLobTrackingTrue(isDom: Boolean): Boolean {
        return false
    }

    override fun getPdtMapperTemplateId(key: String): String? {
        return null
    }

    override fun isMMTPrime(): Boolean {
        return false
    }

    override fun isAudioEnabled(): Boolean {
        return false
    }

    override fun setAudioStatus(isEnabled: Boolean){
    }

    override fun openMyraActivity(context: Activity?, myraPreBookChatData: MyraPreBookChatData?){
    }

    override fun saveLobRecentSearch(hotelSuggestionData: HotelSuggestionData) {
    }

    override fun logAppTrail(activityName: PdtActivityName, pageName: PdtPageName) {
    }

    override fun logOutAccount(activity: Activity) {
    }

    override fun isMapRequirementFulfilled(): Boolean {
        return true
    }

    override fun generateHotelSectorKey(cityName: String?): String? {
        return null
    }

    override fun checkIfSectorExists(sectorKey: String?): Boolean {
        return false
    }


    override fun updateSearchHistory(lob: SearchEventLob, map: MutableMap<SearchEventLob, Any>, page: PageSearchType) {
    }

    override fun updateSectorInfo(key: String?, lob: SearchEventLob, page: PageSearchType, productIds: List<String>?) {
    }

    override fun launchLoginActivityForResult(activity: Activity, loginPageExtra: LoginPageExtra, requestCode: Int) {
        val intent = HotelUtil.getLoginActivityIntent(loginPageExtra)
        if (activity !is Activity) {
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        activity.startActivityForResult(intent, requestCode)
        activity.overridePendingTransition(
            R.anim.top_bottom_enter_anim,
            R.anim.top_bottom_exit_anim
        )
    }

    override fun launchLoginActivityForResult(
        fragment: Fragment,
        loginPageExtra: LoginPageExtra,
        requestCode: Int
    ) {
        val intent = HotelUtil.getLoginActivityIntent(loginPageExtra)
        fragment.startActivityForResult(intent,requestCode)
    }

    override fun launchLoginActivityForResult(
        activity: AppCompatActivity,
        loginPageExtra: LoginPageExtra,
        requestCode: Int,
        activityResultObserver: ActivityResultLifeCycleObserver
    ) {
        val intent = HotelUtil.getLoginActivityIntent(loginPageExtra)
        activityResultObserver.startActivityForResult(intent,requestCode)
    }

    override fun openHotelCardFragment(
        supportFragmentManager: FragmentManager,
        data: HotelCardListFragmentData) {
    }

    override fun getHotelCardFragment(data: HotelCardListFragmentData): Fragment {
        return Fragment()
    }

    @Composable
    override fun HotelCardListComposable(
        emperiaEventData: EmperiaEventData?,
        funnel: HotelFunnel,
        hotelLandingCardsVM: HotelLandingCardsVMInterface
    ) {

    }

    override fun getHotelLandingCardsVM(
        hotelCardListFragmentData: HotelCardListFragmentData,
        activity: AppCompatActivity
    ): HotelLandingCardsVMInterface {
        return object : HotelLandingCardsVMInterface() {
            override fun onCleared() {
            }
        }
    }

    override fun onAppLaunchServiceConnected(service: IBinder?, data: ListingData) {
    }

    override fun getAppLaunchService(activity: Activity): Intent? {
        return null
    }

    override fun getImagePath(context: Context?, uri: Uri?): String? {
        return null
    }

    override fun getHotelMyItineraryIntent(activity: Activity?, queryParams: List<String>): Intent? {
        return null
    }

    override fun getDeeplinkAction(schemaArr: String): String {
        return ""
    }

    override fun trackDeepLinkCMPOrZCPData(schema: String, schemaArr: List<String>) {}

    override fun getTrafficSource(schema: String, schemaArr: List<String>): String {
        return ""
    }

    override fun trackAdTechCard(
        trackPosition: Int,
        adTechCardData: AdTechCardData?,
        canShow: AtomicBoolean,
        fragment: Fragment
    ) {
       //NA

    }

    override fun trackAdTechCard(
        trackPosition: Int,
        adTechCardData: AdTechCardData?,
        canShow: AtomicBoolean,
        vm: HotelLandingCardsVMInterface
    ) {
    }

    override fun updateScrollStateForTracking(fragment: Fragment) {
    }

    override fun updateScrollStateForTracking(vm: HotelLandingCardsVMInterface) {
    }

    override fun getHomePageAdapter(): RecyclerView.Adapter<RecyclerView.ViewHolder>? {
        return null
    }

    override fun updateHomePageAdapterItems(
        recyclerView: RecyclerView,
        adapter: RecyclerView.Adapter<RecyclerView.ViewHolder>,
        item: TemplateViewModel,
        activity: Activity
    ) {
        //do nothing
    }

    override fun getSkywalkerMapper(): IModelTemplateMapper {
        return EmptyItemMapper()
    }

    override fun notifySearchRequestUpdate(
        supportFragmentManager: FragmentManager,
        searchRequest: SearchRequest
    ) {

    }

    override fun notifyEmperiaResponse(
        supportFragmentManager: FragmentManager,
        emperiaEventData: EmperiaEventData?
    ) {
    }

    override fun notifyEmperiaResponse(fragment: Fragment, emperiaEventData: EmperiaEventData?) {

    }

    override fun notifyEmperiaResponse(
        vm: HotelLandingCardsVMInterface,
        emperiaEventData: EmperiaEventData?
    ) {
    }

    override fun getHomePersonalizationRepo(): IHomePersonalizationRepo<ResponseWrapper<EmpeiriaResponse>> {
        return object : IHomePersonalizationRepo<ResponseWrapper<EmpeiriaResponse>>{
            override suspend fun getPersonalizeResponsesForHotelLanding(emperiaRequestData: HotelEmperiaRequestData): Flow<ResponseWrapper<EmpeiriaResponse>> {
                return flow {
                    val emperiaResponse : EmpeiriaResponse = GsonUtils.getInstance().deserializeJSON(
                        HotelPokusHelper.getJSONFromRaw(
                            MMTCore.mContext,
                            R.raw.skywalker_hotellanding
                        ), EmpeiriaResponse::class.java)
                    emit(ResponseWrapper(NetworkResponse(
                        emperiaResponse,
                        null, emptyMap(), 200, null, null), false))
                }
            }

        }
    }

    override fun logPurchaseEvent(purchaseAmount: BigDecimal, currency: Currency, parameters: Bundle) {

    }

    override fun updateBookingEvent(bookingDetails: MutableMap<String, Any>) {

    }

    override fun isBlackInvitedAndNotBlackOrDbRegistered(): Boolean {
        return false
    }

    override fun addRevenueDetailsForTracking(
        eventParams: MutableMap<String?, Any?>,
        productId: String
    ) {

        try {
            eventParams[OmnitureTrackingHelper.OEPK_REVENUE_Products] = ";$productId"
        } catch (e: Exception) {
        }
    }

    override fun getMyraWebViewBundle(
        context: Context?,
        queryParamMap: MutableMap<String, String>
    ) {

    }

    override fun trackRenderedCardsInPdt(model: RecentSearchesCardData) {
    }

    override fun openWebView(context: Context?, webViewBundle: WebViewBundle) {

    }

    override fun createWishlistRepository(): HotelWishlistRepository {
        return object : HotelWishlistRepository {
            override fun makeWishlistRequest(
                hotelId: String,
                hotelName: String,
                checkIn: String,
                checkOut: String,
                isWishlisted: Boolean
            ): Flow<HotelWishlistResponse> {
                return flow {
                     emit(HotelWishlistResponse())
                }
            }

        }
    }

    override fun openWishlistActivity(
        context: Context?,
        observer: ActivityResultLifeCycleObserver?
    ) {
    }

    override fun handleWishlistRequestCode(intent: Intent?): HotelWishlistResponse? {
        return null
    }

    override fun openWishlistBottomSheet(data: WishlistBottomSheetData) {
    }

    override fun notifyWishlistUpdated() {
    }

    override fun isGDPRRegion(): Boolean {
        return false
    }


    override fun onCardsListScrolled(activity: Activity?,dx: Int, dy: Int){

    }

    override fun isTravelPlexEnabled(): Boolean {
        return true
    }
}