package com.mmthoteldev.hotelapp.implementations

import android.content.pm.PackageInfo
import android.os.Build
import android.text.TextUtils
import com.google.gson.JsonObject
import com.mmt.auth.login.util.LoginUtils
import com.mmt.network.logging.LatencyEventManager
import com.mmt.network.logging.latency.LatencyTrackingUtils
import com.mmt.network.utils.INetworkMigrator
import com.mmt.core.MMTCore
import com.mmt.data.model.network.NetworkConstants
import com.mmt.data.model.network.NetworkUtil
import com.mmt.core.database.DatabaseHelper
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.CollectionUtil
import com.mmt.core.util.DeviceUtil
import com.mmt.network.model.HttpRequest
import com.mmt.network.MMTNetwork
import com.mmt.network.connectivity.MobileNetworkUtils
import com.mmt.network.logging.latency.LatencyEvent
import com.mmt.network.model.NetworkRequest
import okhttp3.Interceptor

class DummyNetworkMigratorImpl : INetworkMigrator {
    override fun getLatencyEventManager(): LatencyEventManager?{
        return null
    }

    override fun getAccountEmail(): String? {
        var emailId:String?= null;
        val user = LoginUtils.loggedInUser?:return null
        if (user.isLoggedIn && !TextUtils.isEmpty(user.emailId)) {
            emailId = user.emailId
        }
        return emailId;
    }

    override fun getCurrentInternetConnectionAndApproxSpeed(): String? {
        return ""
    }

    override fun getDeviceId(): String? {
         return CoreUtil.getDeviceId()
    }

    override fun getCompleteUrlForGetRequest(intialURL: String?, paramsMap: Map<String?, String?>?): String? {
        return intialURL?.let {
            paramsMap?.let { it1->
                getCompleteUrlForGetRequestTemp(it,it1)
            }
           }
    }

    fun getCompleteUrlForGetRequestTemp(url: String, paramsMap: Map<String?, String?>): String {
        val sb = StringBuilder(url)
        if (CollectionUtil.isNotEmptyMap(paramsMap)) {
            if (!url.contains(NetworkConstants.QUE_MARK)) {
                sb.append(NetworkConstants.QUE_MARK)
            } else if (url[url.length - 1] != NetworkConstants.AMP) {
                sb.append(NetworkConstants.AMP)
            }
            for ((key, value) in paramsMap) {
                sb.append(key)
                sb.append(NetworkConstants.EQUI)
                sb.append(value)
                sb.append(NetworkConstants.AMP)
            }
            if (sb[sb.length - 1] == NetworkConstants.AMP) {
                sb.setLength(sb.length - 1)
            }
        }
        return sb.toString()
    }

    override fun getLatencyTrackingUtils(): LatencyTrackingUtils? {
        return null
    }

    override fun getTag(httpRequest: Any?): Int{
        return 0
    }

    override fun getDefaultHeaders(): Map<String, String?> {
        return MMTNetwork.iNetworkHeaders.getDefaultHeaders(MMTCore.mContext)
    }

    override fun getInt(key: String?, defaultValue: Int?): Int? {
        return 0
    }

    override fun getKey(url: String?, tag: Int?, requestType: Int?, cacheKey: String?): String? {
        return ""
    }

    override fun getAllExperiments(param: Any?): String? {
        return null
    }

    override fun getNetworkOperatorName(): String? {
        return MobileNetworkUtils.getNetworkOperatorName()
    }

    override fun getScreenResolution(): String? {
        return DeviceUtil.getScreenResolution(MMTCore.mContext)
    }

    override fun getAppVersionCode(): Int? {
        return CoreUtil.getAppVersionCode()
    }

    override fun getApplicationDisplayName(): String? {
        return "mmt"
    }

    override fun getAppVersionNameWithoutRc(): String? {
        return CoreUtil.getAppVersionNameWithoutRc()
    }

    override fun getAppVersionName(): String? {
        val packageInfo: PackageInfo? = CoreUtil.getPackageInfo(0)
        return if (packageInfo != null) {
            packageInfo.versionName
        } else {
            ""
        }
    }

    override fun makeDeviceName(): String? {
        val manufacturer = Build.MANUFACTURER
        val model = Build.MODEL
        return if (model.startsWith(manufacturer)) {
            capitalize(model)
        } else {
            capitalize(manufacturer) + " " + model
        }
    }
    fun capitalize(s: String?): String? {
        if (s == null || s.isEmpty()) {
            return ""
        }
        val first = s[0]
        return if (Character.isUpperCase(first)) {
            s
        } else {
            Character.toUpperCase(first).toString() + s.substring(1)
        }
    }

    override fun getRequestTag(httpRequest: HttpRequest?): Int? {
        return 0
    }

    override fun getNetworkRequestTag(networkRequest: NetworkRequest?): Int? {
        return 0
    }

    override fun logDownloadImageSize(path: String, size: Long) {
        //No nothing
    }

    override fun logDownloadImageCounter(size: Long) {
        //Do nothing
    }

    override fun isUserInfoAvailable(): Boolean {
        return false
    }

    override fun getUserPrimaryContact(): String? {
        return ""
    }

    override fun getUserEmailId(): String? {
        return ""
    }

    override fun getUserLoginType(): String? {
        return ""
    }

    override fun getDefaultInterceptor(): List<Interceptor> {
        return NetworkUtil.getDefaultInterceptors()
    }

    override fun getInterceptorsForHttpUtils(): List<Interceptor> {
        return NetworkUtil.getInterceptorsForHttpUtils()
    }

    override fun getTrackingEndpoint(): String {
        return ""
    }

    override fun isTrackingEnabled(): Boolean {
        return false
    }

    override fun parseLatencyEventsIntoPdtTrackingInfo(latencyEvents: List<LatencyEvent>): List<JsonObject> {
        return emptyList()
    }

    override fun getContentProviderUri(): String {
        return DatabaseHelper.CONTENT_URI.toString() + "/" + DatabaseHelper.CACHING_RESPONSE_TABLE
    }
}