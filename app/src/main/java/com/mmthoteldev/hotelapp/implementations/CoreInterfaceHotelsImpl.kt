package com.mmthoteldev.hotelapp.implementations

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.IBinder
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import com.mmt.auth.login.model.login.User
import com.mmt.auth.login.model.userservice.CoTraveller
import com.mmt.auth.login.model.userservice.Traveller
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.MPermission.PermissionManager
import com.mmt.core.MPermission.PermissionUtil
import com.mmt.core.base.BaseActivity
import com.mmt.core.base.SuperBaseService
import com.mmt.core.base.thankyou.BookingStateData
import com.mmt.core.constant.CoreConstants
import com.mmt.core.interfaces.CoreInterface
import com.mmt.core.model.AnimationData
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.util.*
import com.mmt.data.model.homepage.personalizationSequenceAPI.response.locationdata.LocationData
import com.mmt.data.model.location.LocationCallback
import com.mmt.data.model.model.GccTabType
import com.mmt.network.logging.latency.LatencyTrackingUtils
import com.mmt.network.model.HttpRequest
import com.mmt.network.model.NetworkRequest
import com.mmthoteldev.hotelapp.BuildConfig
import com.mmthoteldev.hotelapp.HotelApplication
import com.mmthoteldev.hotelapp.launch.HotelAppActivity
import okhttp3.Headers
import okhttp3.Request
import java.io.File
import java.io.Serializable

class CoreInterfaceHotelsImpl: CoreInterface {
    override fun launchHomeWithFlags(
        activity: Activity?,
        bundle: Bundle?,
        launchflags: List<Int>?,
        isFinish: Boolean,
        animData: AnimationData?
    ) {
        val intent = Intent(activity, HotelAppActivity::class.java)
        launchflags?.forEach { intent.addFlags(it) }
        activity?.run {
            startActivity(intent)
            if (isFinish) finish()
        }
    }

    override fun registerLifecycle(lifecycle: Lifecycle?) {
    }

    override fun isDeviceRooted(): Boolean {
        return false
    }

    override fun getBuildFlavor(): String {
        return BuildConfig.FLAVOR
    }
    override fun cleanUpCrashlyticsKeys() {
    }

    override fun setCurrentActivityName(simpleName: String?) {
    }

    override fun notifyIfAppInBackground() {
    }


    override fun setPermissionDialogClosed(close: Boolean?) {
    }


    override fun getFlightSearchAction(): String? {
        return "mmt.intent.action.PERSONAL_FLIGHT_BOOK_NEW"
    }

    override fun getGDPRConsent(): String? {
        return null
    }


    override fun isReactRunning(): Boolean? {
        return false
    }

    override fun getLatencyTrackingUtils(): LatencyTrackingUtils? {
        return null
    }

    override fun generateResponseHeaderBundle(headers: Headers?): Bundle? {
        return null
    }

    override fun clearReferences(activityName: String?) {
    }

    override fun getPermissionManager(): PermissionManager? {
        val application = MMTCore.mContext as HotelApplication
        return application.permissionManager!!
    }

    override fun checkIfCloseBottomBar(activity: BaseActivity?): Boolean? {
        return null
    }

    override fun getTag(tag: Any?): Int? {
        return null
    }

    override fun getRequestTag(request: HttpRequest?): Int? {
        return null
    }

    override fun getRequestTag(request: Request?): Int? {
        return null
    }

    override fun getNetworkRequestTag(request: NetworkRequest?): Int? {
        return null
    }

    override fun isBackHomeRequired(activity: Activity?): Boolean? {
        return null
    }

    override fun returnToHomeScreen(activity: Activity?) {
    }

    override fun isActivityActive(baseActivity: Activity?): Boolean? {
        return true
    }
    override fun isRequestTag(request: Request?): Boolean {
        return false
    }

    override fun isRequestTag(request: Object?): Boolean {
        return false
    }

    override fun deleteUniversalSearchHintData() {

    }

    override fun deletePersonalizationDataFromDB() {

    }
    override fun isFragmentUIActive(baseFragmentWithLatencyTracking: android.app.Fragment?): Boolean? {
        return false
    }

    override fun isFragmentUIActive(baseFragmentWithLatencyTracking: Fragment?): Boolean {
        return false
    }
    override fun openWebView(mContext: Context?, tncUrl: String?, string: String?) {
    }

    override fun getAppHashKey(): String? {
        return CoreConstants.AUTO_READ_DEBUG_KEY
    }

    override fun openHomeClearTaskSingleTop(context: Context?, bundle: Bundle?) {
    }

    override fun checkGooglePlayServicesAvailableFor(any: Int?): Boolean? {
        return true
    }

    override fun hideFocusedKeyboard(activity: FragmentActivity?) {
    }
    override fun sendPhoneDetailsPDT() {
        //nothing
    }

    override fun sendLocationDetailsPDT() {
        //nothing
    }

    override fun isGdprServiceAvailable(): Boolean {
        return true
    }

    override fun fixLocaleIssue() {
    }

    override fun clearConfigData() {
    }

    override fun setBnplUserDetails() {
    }

    override fun reInitializeAdTechSDK() {
    }

    override fun startActivityTracking(activity: Activity) {
    }

    override fun stopActivityTracking() {
    }

    override fun onLanguageSwitched(
        context: Context,
        newLang: String,
        changeLanguagePayload: ChangeLanguagePayload
    ) {
    }

    override fun trackPermissionPopupEvent(localMap: HashMap<String, Any>) {
    }

    override fun trackSnackBarOmnitureEvent(localMap: HashMap<String, Any>) {
    }

    override fun trackUpfrontPageOmnitureEvent(localMap: HashMap<String, Any>) {
    }

    override fun trackPermissionSwitchOmnitureEvent(localMap: HashMap<String, Any>) {
    }

    override fun getThankYouFragment(): Fragment {
        TODO("Not yet implemented")
    }

    override fun getThankYouFragmentgetCompleteYourTripCardContainer(
        fragment: Fragment,
        callback: (container: LinearLayout?) -> Unit?
    ) {
    }

    override fun updateThankYouFragmentBookingState(
        fragment: Fragment,
        bookingState: BookingStateData
    ) {
    }

    override fun handleOnActivityResult(resultCode: Int, anchorView: View): Boolean {
        return false
    }

    override fun getAppLaunchService(binder: IBinder): SuperBaseService {
        TODO("Not yet implemented")
    }

    override fun getAppLaunchServiceIntent(activity: Activity): Intent {
        return Intent()
    }

    override fun fetchPersonalisationDataFromAppLaunchService(
        service: SuperBaseService,
        event: String
    ) {
    }
}