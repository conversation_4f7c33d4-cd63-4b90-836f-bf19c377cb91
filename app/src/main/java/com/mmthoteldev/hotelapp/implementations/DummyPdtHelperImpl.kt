package com.mmthoteldev.hotelapp.implementations

import com.mmt.core.MPermission.PermissionUtil

import com.mmt.core.user.prefs.FunnelContextHelper.getFunnelContext
import com.mmt.core.util.CoreUtil
import com.mmt.pokus.PokusV2Helper
import com.pdt.pdtDataLogging.PdtHelper
import com.mmthoteldev.hotelapp.BuildConfig

class DummyPdtHelperImpl : PdtHelper {
    override fun getPdtTrackingData(lobName: String): Pair<String, List<Any>>? {
        val pdtTrackingData: Pair<String?, List<Any>?> = PokusV2Helper.getPdtTrackingData(lobName)
                ?: return null
        return pdtTrackingData as Pair<String, List<Any>>
    }

    override fun getSplashLastResumeTime(): Long {
        return 0L
    }

    override fun getKeyAppGcmId(): String? {
        return ""
    }

    override fun getOsNotificationSetting(): String {
        //return if (AppUtils.areNotificationsEnabledFromOS()) AppConstants.NOTIFICATION_ENABLED else AppConstants.NOTIFICATION_DISABLED
        return ""
    }

    override fun getSessionId(): Int {
        //return SharedPreferencesUtils.getInstance().getInt(AppConstants.VISITOR_NUMBER)
        return 0
    }

    override fun getDeviceId(): String {
        return CoreUtil.getDeviceId()
    }

    override fun isLoggedIn(): Boolean {
        return com.mmt.auth.login.util.LoginUtils.isLoggedIn;
    }

    override fun getLoginUuid(): String? {
        return com.mmt.auth.login.util.LoginUtils.loggedInUser?.uuid
    }


    override fun getProfileType(): String? {
        return com.mmt.auth.login.util.LoginUtils?.loggedInUser?.profileType
    }

    override fun getCorpOrgId(): String? {
        return com.mmt.auth.login.util.LoginUtils.getCorporateUser()?.corpData?.employee?.organizationId
    }

    override fun getCityLocusCode(): String? {
        //UserLocationToPlaceRepo.getUserCityCode()?.let { return it } ?: return ""
        return null
    }

    override fun getMobileCountryCode(): String? {
        return getFunnelContext().countryCode
    }

    override fun getVisitSourceLink(): String? {
        //return AppUtils.visitLink
        return null
    }

    override fun getVisitSource(): String? {
        return null
    }

    override fun isNewPdt(): Int {
        return 1
    }

    override fun isReInstall(): Boolean {
        return true
    }

    override fun getUniqueKey(): String? {
       return null
    }

    override fun getAppVersion(): Any {
       return  BuildConfig.VERSION_NAME
    }

    override fun getPermissionsList(): List<String> {
        val permissionList = PermissionUtil.getGrantedPermissionList(
            PermissionUtil.getPermissionsList())
        permissionList.add("notification_enabled = " + false)
        return permissionList
    }

    override fun getManufacturerPushToken(): String? {
        return null
    }

    override fun getIsNotificationEnabled(): Boolean? {
        return true
    }

    override fun getGoogleAdvertisingId(): String? {
        return null
    }

    override fun getPdtTrackingDataV2(lobName: String): Pair<*, *>? {
        return PokusV2Helper.getPdtTrackingDataV2(lobName)
    }
}