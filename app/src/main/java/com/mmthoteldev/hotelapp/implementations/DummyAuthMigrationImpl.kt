package com.mmthoteldev.hotelapp.implementations

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.reflect.TypeToken
import com.mmt.analytics.omnitureclient.Events
import com.mmt.analytics.pdtclient.PdtActivityName
import com.mmt.analytics.pdtclient.PdtPageName
import com.mmt.auth.login.model.LoginPageExtra
import com.mmt.auth.login.model.login.User
import com.mmt.auth.login.model.login.request.OTPRequest
import com.mmt.auth.login.model.userservice.MobileNumber
import com.mmt.auth.login.util.IAuthMigrator
import com.mmt.auth.login.util.LoginConstants
import com.mmt.core.MMTCore
import com.mmt.core.constant.CoreConstants
import com.mmt.core.constant.CoreConstants.EMPTY_STRING
import com.mmt.core.user.auth.LoginUtil
import com.mmt.core.util.CollectionUtil.isEmptyCollection
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.GsonUtils
import com.mmt.network.model.HttpRequest
import com.mmt.network.model.NetworkRequest
import com.mmt.network.utils.NetworkMigrationHelper
import com.google.i18n.phonenumbers.NumberParseException
import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber
import com.mmt.auth.login.MMTAuth
import com.mmt.hotel.common.constants.ExperimentsGrowth
import com.mmt.hotel.common.util.HtlUrlConstants.USER_SERVICE_DOMAIN

class DummyAuthMigrationImpl : IAuthMigrator {

    private val USER_SERVICE_BASE_URL = "$USER_SERVICE_DOMAIN/ext/Android/"
    val USER_SERVICE_VERSION_URL: String = USER_SERVICE_BASE_URL + com.mmt.core.util.CoreUtil.getAppVersionNameWithoutRc()

    override fun isNewAnalytics(): Int {
        return 1
    }

    override fun checkMobileNumber(identifier: String): Boolean {
        return LoginUtil.checkMobileNumber(identifier)
    }

    override fun checkMobileNumber(identifier: String?, countryCode: String?): Boolean {
        return LoginUtil.checkMobileNumber(identifier)
    }

    override fun hideFocusedKeyboard(activity: FragmentActivity?) {

    }

    override fun getFirstNameAndLastName(fullName: String): Pair<String, String?> {
        val list = fullName.trim().split("\\s+".toRegex());
        val listWithCapitals = uppercaseFirstLetterInStringList(list).toMutableList()
        if(listWithCapitals.size == 1){
            return Pair(list[0], null)
        }
        // removing last element from list
        val lastName = listWithCapitals.removeAt(listWithCapitals.size - 1)
        return Pair(listWithCapitals.joinToString(separator = " ") , lastName)
    }

    override fun getFirstNameError(name: String?): String? {
        return ""
    }
    @SuppressLint("DefaultLocale")
    private fun uppercaseFirstLetterInStringList(list: List<String>): List<String> {
        return list.map { word ->
            word.capitalize()
        }
    }


    override fun getUserServiceVersionUrl(): String {
        return USER_SERVICE_VERSION_URL
    }

    override fun getNetworkClass(context: Context?): String {
        return CoreConstants.NetworkType.FOUR_G
    }

    override fun isNetworkAvailable(): Boolean {
        return NetworkMigrationHelper.Companion.instance.isNetworkAvailable()
    }

    override fun checkEmail(loginIdentifier: String?): Boolean {
        return LoginUtil.checkEmail(loginIdentifier)
    }

    fun logOutAccount(activity: FragmentActivity?) {
        try {
            activity?.cacheDir?.deleteRecursively()
            MMTCore.mContext.getSharedPreferences("mmt_prefs", Context.MODE_PRIVATE).edit().clear()
                .apply()
        } catch (e:Exception) {

        }
    }

    override fun logOutAllAccount(activity: FragmentActivity?) {
        TODO("Not yet implemented")
    }

    override fun appInstalledOrNot(whatsAppPackage: String?, mContext: Context?): Boolean {
        return true
    }


    override fun sendProfileUpdateSuccessBroadcast() {
    }

    override fun getDeepLinkSearchQueryMap(query: String?): Map<String, String>? {
        return null
    }


    override fun isValidGSTNumber(gstNumber: String?): Boolean {
        return true
    }

    override fun getDeepLinkSearchQueryMapNullable(query: String?): Map<String?, String?>? {
        //return AppUtils.getDeepLinkSearchQueryMap(query)
        return null
    }

    override fun getBonusMessageForLogin(): String {
        //return AppUtils.getBonusMessageForLogin()
        return ""
    }

    override fun getBookingForPokusValue(): Boolean {
        return ExperimentsGrowth.bookingForShown.getPokusValue()
    }

    override fun getDeviceId(): String {
        return CoreUtil.getDeviceId()
    }

    override fun checkGooglePlayServicesAvailableFor(any: Int?): Boolean? {
        return true
    }

    override fun hitUpdateContactInfo(context: Context?) {

    }

    override fun broadCastLoginSuccess() {
        val applicationContext = MMTCore.mContext
        val loginSuccessBroadcast = Intent()
        loginSuccessBroadcast.action = LoginConstants.ACTION_LOGIN
        LocalBroadcastManager.getInstance(applicationContext).sendBroadcast(loginSuccessBroadcast)
    }

    override fun isValidNumber(phoneNumber: Phonenumber.PhoneNumber?): Boolean? {
        return true
    }

    override fun getPhoneNumber(number: String): Phonenumber.PhoneNumber? {
        try {
            val phoneNumberUtil: PhoneNumberUtil = PhoneNumberUtil.getInstance()

            return phoneNumberUtil.parse(number, "")
        } catch (e: NumberParseException) {
        }
        return null
    }

    override fun getIntentForLoginActivity(context: Context?): Intent? {
        return MMTAuth.getIntentForLoginActivity(context, null)
    }

    override fun getIntentForLoginActivity(context: Context?, loginPageExtra: LoginPageExtra?): Intent? {
        return MMTAuth.getIntentForLoginActivity(context, loginPageExtra)
    }

    override fun getMobileNumberFrom(countryCode: String?, info: String?): MobileNumber {
        return MobileNumber("91","9876543210")
    }

    override fun setRewardData(otpRequest: OTPRequest) {
    }

    override fun logLoginFlowErrors(activitySignup: PdtActivityName?, eventSignupError: PdtPageName?, httpCode: Int, errorMessage: String?) {
    }

    override fun trackRegionCode(regionCode: String?) {
    }

    override fun homeGccTrackCountryChange(selectedCode: String?, previousCode: String?) {
    }

    override fun trackProp33WithPage(eventCommonLoginPage: Events?, cmpID: String?) {
    }

    override fun updateCorpDataAndLaunchBusinessProfile(activity: Activity?) {

    }

    override fun getSSOLOGINIntent(context: Context?, email: String?): Intent {
        return Intent()
    }

    override fun getSwitcherActivityClassName(): Class<*> {
        return DummyAuthImpl.javaClass
    }

    override fun trackProp50WithPage(eventHomepageLanding: Events?, s: String?) {
    }

    override fun trackProp35EventHotelDetailShareCard() {
    }


    override fun onSSOUserDetected(loginId: String?, activity: Activity?) {
    }

    override fun trackAnalyticEvents(eventFltLandingPageLanding: Events?, eventString: String?) {
    }

    override fun logVerificationMethod(activityOtp: PdtActivityName?, eventOtpSuccess: PdtPageName?, trackOtpVrified: String?) {
    }

    override fun notifyUserModuleDataRefresh(mobileVerifiedEvent: String?, writableMap: HashMap<String, Boolean>?) {
    }

    fun getRequestFromIntTag(requestType: Int, any: Any?): HttpRequest {
        return HttpRequest()
    }


    override fun clearHolidayPreferences() {
    }

    override fun performSwitchCleanUp(context: Context?) {
    }



    override fun onLoginCompleted() {
    }


    override fun getRequest(requestTagObj: Any?, any: Any?): HttpRequest? {
        return null
    }

    override fun getNetworkRequest(requestTag: Any?, any: Any?): NetworkRequest? {
        return null
    }

    override fun scheduleSignUpNotification() {
    }

    override fun scheduleLoginCompletedNotification() {
    }

    override fun cancelVerifyMobileNotification() {
    }

    override fun isExistingUser(): Boolean {
        return false
    }

    override fun isEligibleForReferralSuccessMessage(): Boolean {
        return false
    }

    override fun getMasterDeviceId(): String {
        return ""
    }

    private fun getRequestFromRequestTag(requestTagObj: Any?, any: Any?): HttpRequest {
        return HttpRequest()
    }


    override fun openSignUpOnboardingSuccess(
        context: Context,
        bundle: Bundle?,
        isEnableReferralFlow: Boolean,
        referral: String?
    ) {
        //TODO("Not yet implemented")
    }

    override fun updateAppData(user: User?) {
    }

    override fun broadCastLoginSuccessForAdditionalInfo(activity: Activity?, isSignup: Boolean?) {
    }


    override fun scheduleProfileSwitchToPersonal() {
    }

    override fun getAppVersionNameWithoutRc(): String {
        return CoreUtil.getAppVersionNameWithoutRc()
    }

    override fun getTimeZoneString(): String {
        return CoreUtil.getTimeZoneString()
    }

    override fun getOSVersion(): String {
        return CoreUtil.getOSVersion()
    }

    override fun getMobileNumberString(verifiedPhoneNumberList: MutableList<MobileNumber>?): String? {
        if(isEmptyCollection(verifiedPhoneNumberList)){
            return EMPTY_STRING;
        }
        return GsonUtils.getInstance().serializeToJson(verifiedPhoneNumberList,object: TypeToken<ArrayList<MobileNumber>>() {
        }.type)
    }

    override fun getReferralExperimentValue(): Boolean {
        return true
    }


}