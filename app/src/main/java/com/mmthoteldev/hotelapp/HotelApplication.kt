package com.mmthoteldev.hotelapp

import android.widget.ImageView
import androidx.multidex.MultiDexApplication
import com.adtech.AdTech
import com.adtech.ImageLoader
import com.appsflyer.AppsFlyerLib
import com.gommt.adtech.data.model.AdTechOrg
import com.gommt.configstore.ConfigStore
import com.gommt.configstore.ORG
import com.gommt.configstore.init
import com.gommt.gommt_auth.v2.b2c.di.LoginModule.init
import com.gommt.core.GoMMTCore
import com.gommt.logger.LogUtils
import com.gommt.network.Networking
import com.gommt.uicompose.theme.AppThemeInit
import com.gommt.uicompose.theme.GoMmtUIType
import com.mmt.analytics.AnalyticsSDK
import com.mmt.analytics.omnitureclient.OmnitureTracker
import com.mmt.analytics.pdtclient.PdtPagenameConstants
import com.mmt.auth.login.util.AuthMigrationHelper
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.MMTCore
import com.mmt.core.MPermission.PermissionManager
import com.mmt.core.interfaces.AppInterfaceHelper
import com.mmt.core.user.prefs.FunnelContextHelper
import com.mmt.core.util.ChangeLanguagePayload
import com.mmt.core.util.CoreSharedPreferenceUtil
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.LanguageScreenType
import com.mmt.core.util.LanguageSettings
import com.mmt.core.util.LocaleHelper
import com.mmt.core.util.LocaleHelper.getAppCurrentLanguage
import com.mmt.core.util.LocaleHelper.getNewConfiguration
import com.mmt.data.model.network.interceptor.mmtglobal.addLobHostProvider
import com.mmt.data.model.util.CommonMigrationHelper
import com.mmt.hotel.analytics.pdtv2.HotelPdtV2Constants
import com.mmt.hotel.common.constants.ExperimentsHotel
import com.mmt.hotel.common.util.HotelMigratorHelper
import com.mmt.hotel.common.util.HotelUtil.onSessionChange
import com.mmt.hotel.network.HotelGlobalHostListProvider
import com.mmt.network.MMTNetwork
import com.mmt.pokus.PokusModule
import com.mmt.profile.ProfileModule
import com.mmt.travelplex.TravelPlexViewInitializerHelper
import com.mmt.uikit.fonts.FontManager
import com.mmt.uikit.util.UiMigrationHelper
import com.mmthoteldev.hotelapp.implementations.CoreInterfaceHotelsImpl
import com.mmthoteldev.hotelapp.implementations.DummyAdTechAuthImpl
import com.mmthoteldev.hotelapp.implementations.DummyAnalyticsImpl
import com.mmthoteldev.hotelapp.implementations.DummyAppInterfaceImpl
import com.mmthoteldev.hotelapp.implementations.DummyAuthImpl
import com.mmthoteldev.hotelapp.implementations.DummyAuthMigrationImpl
import com.mmthoteldev.hotelapp.implementations.DummyCommonMigratorImpl
import com.mmthoteldev.hotelapp.implementations.DummyDatabaseInterface
import com.mmthoteldev.hotelapp.implementations.DummyHotelMigrationImpl
import com.mmthoteldev.hotelapp.implementations.DummyLoginInterfaceImpl
import com.mmthoteldev.hotelapp.implementations.DummyNetworkHeadersImpl
import com.mmthoteldev.hotelapp.implementations.DummyNetworkMigratorImpl
import com.mmthoteldev.hotelapp.implementations.DummyPdtHelperImpl
import com.mmthoteldev.hotelapp.implementations.DummyUiMigrationImpl
import com.mmthoteldev.hotelapp.implementations.ProfileLoginImpl
import com.mmthoteldev.hotelapp.implementations.TravelPlexViewInitializerImpl
import com.mmthoteldev.hotelapp.networking.NetworkSetup
import com.mmthoteldev.hotelapp.util.ConfigStoreConnector
import com.mmthoteldev.hotelapp.util.HotelPokusHelper
import com.mmthoteldev.hotelapp.util.HotelPokusModuleImpl
import com.pdt.Configuration
import com.pdt.PDTAnalytics
import com.pdt.eagleEye.constants.Lobs
import com.squareup.picasso.Picasso
import dagger.hilt.android.HiltAndroidApp
import io.reactivex.plugins.RxJavaPlugins
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.Locale


@HiltAndroidApp
class HotelApplication: MultiDexApplication() {


    private val TAG = "HotelApplication"
    var permissionManager: PermissionManager? =
        PermissionManager()

    /**
     * to enable PDT Tracking, Set this variable to true -
     * [DO NOT COMMIT THE CHANGE]
     * */
    private val enablePDTTracking  = true

    /**
     * to enable Omniture Tracking, Set this variable to true -
     * [DO NOT COMMIT THE CHANGE]
     * */
    private val enableOmnitureTracking  = true

    public var treelOnboardingStatus = false

    override fun onCreate() {
        super.onCreate()
        GoMMTCore.init(this)
        LogUtils.setEnableLog(BuildConfig.DEBUG)
        initNetworking()
        ExceptionHandler.setupExceptionHandler()
        initNetworking()
        initMigrators()
        initConfigStore()
        onSessionChange()
        initDummyData()
        AppThemeInit.setGlobalUiType(GoMmtUIType.MMT_B2C)

//        updateLanguageInAppContext("ar")
//        TooLargeTool.startLogging(this);
//
//        StrictMode.setVmPolicy(
//                StrictMode.VmPolicy.Builder()
//                        .detectLeakedSqlLiteObjects()
//                        .detectLeakedClosableObjects()
//                        .detectActivityLeaks()
//                        .penaltyLog()
//                        .build()
//        )
//
//        StrictMode.setThreadPolicy(
//                StrictMode.ThreadPolicy.Builder()
//                        .detectCustomSlowCalls()
//                        .detectDiskReads()
//                        .detectDiskWrites()
//                        .detectNetwork()
//                        .penaltyLog()
//                        .build()
//        )

    }

    private fun initNetworking() {
        Networking.init(NetworkSetup())
        addLobHostProvider(HotelGlobalHostListProvider)
    }

 private fun initConfigStore() {
        ConfigStore.init(
            application = this,
            org = ORG.MMT,
            connector = ConfigStoreConnector(),
            isTestingBuild = BuildConfig.DEBUG,
            isStagingEnvEnabled = true
        )
        GlobalScope.launch(Dispatchers.IO) {
            ConfigStore.loadCachedConfigs()
        }

    }

    private fun handleError(e: Throwable) {
        LogUtils.error("HotelApplication", e)
    }

    fun updateLanguageInAppContext(newLanguage: String) {
        Locale.setDefault(Locale(newLanguage))
        MMTCore.mContext.getResources()?.updateConfiguration(
            getNewConfiguration(newLanguage, MMTCore.mContext!!),
            MMTCore.mContext?.getResources()?.getDisplayMetrics()
        )
        val changeLanguagePayload = ChangeLanguagePayload(
            "", true,
            LanguageScreenType.LanguageChangeDialog.screenType
        )
        LanguageSettings.setSelectedLanguage(MMTCore.mContext,newLanguage,changeLanguagePayload)
    }

    fun initMigrators(){
        TravelPlexViewInitializerHelper.initialize(TravelPlexViewInitializerImpl())

        AppInterfaceHelper.initialize(DummyAppInterfaceImpl())
        CommonMigrationHelper.initialize(DummyCommonMigratorImpl())
        MMTCore.initialise(
            this,
            DummyAuthImpl,
            CoreInterfaceHotelsImpl(), DummyDatabaseInterface)
        PDTAnalytics.initialise(this,
            Configuration.Builder().isDebugMode(true).isDebugX(true).companyId("MMT").build(),
            DummyAnalyticsImpl)
        PDTAnalytics.registerLobTemplateId(Lobs.HOTEL,HotelPdtV2Constants.PDT_V2_TEMPLATE_ID,HotelPdtV2Constants.PDT_V2_B2C_TOPIC_ID)
        MMTNetwork.initialise(this, DummyNetworkHeadersImpl, DummyNetworkMigratorImpl())

        AuthMigrationHelper.initialize(DummyAuthMigrationImpl(), BuildConfig.APP_HEADER_KEY)
        init(DummyLoginInterfaceImpl())

        HotelMigratorHelper.initialize(DummyHotelMigrationImpl())

        UiMigrationHelper.initialize(DummyUiMigrationImpl())
        initAnalyticsSdkAsync()
        initAppsFlyer()
        //AnalyticsSDK.initialize(this, null, DummyPdtHelperImpl())
        initOtherComponent()
        RxJavaPlugins.setErrorHandler(this::handleError)

        initMmtAdTechSDK()
        initProfileImpl()
    }

    private fun addAppsFlyerBrandedDomains() {
        AppsFlyerLib.getInstance().setOneLinkCustomDomain("mmyt.app", "app.mmyt.co");
    }
    private fun initAppsFlyer(){
        try {
            addAppsFlyerBrandedDomains()
            AppsFlyerLib.getInstance()
                .init("9MVTkCgs68szjLHjzWCE6R", null, this)
            val additionalParams: MutableMap<String, Any?> = mutableMapOf()
            //if (OmnitureTracker.getMcid() != null && OmnitureTracker.getMcid() != OmnitureTracker.DEFAULT_MCID) {
                additionalParams.put(
                    "marketingCloudID",
                    OmnitureTracker.getMcid()
                )
                additionalParams.put(OmnitureTracker.KEY_OMNITURE_TRACKER_DEVICE_ID,
                    CoreUtil.getDeviceId()
                )
                AppsFlyerLib.getInstance().setAdditionalData(additionalParams)
            /*} else {
                AppsFlyerLib.getInstance().waitForCustomerUserId(true);
            }*/
            AppsFlyerLib.getInstance().start(this);
        }catch (e:Exception){
            LogUtils.error(TAG,e)
        }
    }

    private fun initProfileImpl() {
        ProfileModule.loginInterface = ProfileLoginImpl()
    }

    private fun initOtherComponent(){
        FontManager.initFonts(this)
    }

    private fun initAnalyticsSdkAsync() {
        try {
            PdtPagenameConstants.getInstance()
        } catch (th: Throwable) {
            //LogUtils.error(HotelApplication.TAG, th)
        }
        //Initialise analytics to initialise PDT and Omniture together
        try {
            AnalyticsSDK.initialize(if(enablePDTTracking || enableOmnitureTracking) this else null, {
                if(enableOmnitureTracking){
                    val mcid = AnalyticsSDK.instance.omnitureMarketingCloudId
                    val mvid = AnalyticsSDK.instance.omnitureVisitorId
                    if (mcid != null) {
                        CoreSharedPreferenceUtil.getInstance(this)
                            .putString(OmnitureTracker.SP_MCID, mcid)
                    }
                    if (mvid != null) {
                        CoreSharedPreferenceUtil.getInstance(this)
                            .putString(OmnitureTracker.SP_OMNITURE_VISITOR_ID, mvid)
                    }
                    OmnitureTracker.initialize()
                }
            }, DummyPdtHelperImpl())
        } catch (e: Exception) {
            LogUtils.error(TAG, e)
        }
    }


    private fun initDummyData(){
        ExperimentsHotel
        PokusModule.initialise(HotelPokusModuleImpl())
        HotelPokusHelper.initPokusJson(this)
        PokusModule.pokus.setPokusV2Config(HotelPokusHelper.pokusResponse)
    }

    private fun initMmtAdTechSDK() {
        try {
            val sdkConfig = AdTech.SDKConfig.Builder()
                .setApplication(this)
                .setImageLoader(object : ImageLoader {
                    override fun loadImage(imageUrl: String?, imageView: ImageView?) {
                        Picasso.get().load(imageUrl).into(imageView)
                    }
                })
                .setAuthority(BuildConfig.ADTECH_SDK_AUTHORITY)
                .setAuthorization(BuildConfig.ADTECH_SDK_AUTH)
                .setOrg(BuildConfig.ADTECH_SDK_ORG)
                .setLanguage(LocaleHelper.getCurrentApiLanguage())
                .setRegion(LoginUtils.getPreferredRegionCode())
                .build()
            AdTech.init(sdkConfig)
        } catch (e: java.lang.Exception) {

        }
        initAdTechSDK()
    }

    private fun initAdTechSDK() {
        try {
            val sdkConfig: com.gommt.adtech.AdTech.SDKConfig = com.gommt.adtech.AdTech.SDKConfig.Builder()
                .setApplication(this)
                .setOrg(AdTechOrg.MMT)
                .setAppVersion(CoreUtil.getAppVersionName())
                .setAdTechAuth(DummyAdTechAuthImpl.get())
                .build()
            com.gommt.adtech.AdTech.init(sdkConfig)
        } catch (e: java.lang.Exception) {
            LogUtils.error(TAG, e)
        }
    }
}