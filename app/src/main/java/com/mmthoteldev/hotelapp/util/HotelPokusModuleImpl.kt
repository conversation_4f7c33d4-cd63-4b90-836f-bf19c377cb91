package com.mmthoteldev.hotelapp.util

import com.mmt.analytics.omnitureclient.OmnitureTracker
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.location.LocationUtil
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.LOBS
import com.mmt.core.util.LocaleHelper
import com.mmt.data.model.network.NetworkUtil
import com.mmt.data.model.util.CommonMigrationHelper
import com.mmt.data.model.util.DeviceUtils
import com.mmt.data.model.util.SharedPreferenceUtils
import com.mmt.pokus.model.PokusResponseV2
import com.mmt.pokus.moduleData.PokusInterface
import com.mmt.pokus.moduleData.PokusLocationData
import com.mmt.pokus.moduleData.PokusUserDetails
import okhttp3.Interceptor

class HotelPokusModuleImpl:PokusInterface {
    override fun getUUID(): String? {
        return LoginUtils.uuid
    }

    override fun getDeviceId(): String? {
        return CoreUtil.getDeviceId()
    }

    override fun getAuth(): String? {
        return LoginUtils.mmtAuth
    }

    override fun getLocaleLanguage(): String? {
        return LocaleHelper.getNetworkApiLanguage(LOBS.GROWTH.lob)
    }

    override fun getMcid(): String? {
        return OmnitureTracker.getMcid()
    }

    override fun getVisitorID(): String? {
        return OmnitureTracker.getVisitorID()
    }

    override fun getLocationData(): PokusLocationData {
        val locationData = PokusLocationData()

        locationData.countryName = SharedPreferenceUtils.getInstance().getString(LocationUtil.COUNTRY_NAME)
        locationData.state = SharedPreferenceUtils.getInstance().getString(LocationUtil.STATE)
        locationData.city = SharedPreferenceUtils.getInstance().getString(LocationUtil.CITY)
        locationData.longitude = SharedPreferenceUtils.getInstance().getString(LocationUtil.LONGITUDE)
        locationData.latitude = SharedPreferenceUtils.getInstance().getString(LocationUtil.LAT)

        return locationData
    }

    override fun getUserDetails(): PokusUserDetails {
        val userDetails = PokusUserDetails()
        userDetails.isCorp = LoginUtils.isCorporateUser
        userDetails.deviceModel = DeviceUtils.getDeviceModel()
        userDetails.orgId = LoginUtils.orgId
        userDetails.appVersion = CoreUtil.getAppVersionNameWithoutRc()
        userDetails.userAgent = CommonMigrationHelper.instance.getUserAgent()
        return userDetails
    }

    override fun notifyPokusConfigLoad() {

    }

    override fun setOTAEnabled() {

    }

    override fun getNetworkInterceptors(): List<Interceptor> {
        return NetworkUtil.getInterceptorsForHttpUtils()
    }

    override fun trackOmniture(response: PokusResponseV2) {

    }

    override fun setPokusV2Config(pokusResponse: PokusResponseV2) {
        HotelPokusHelper.updatePokusServerData(pokusResponse)
    }
}