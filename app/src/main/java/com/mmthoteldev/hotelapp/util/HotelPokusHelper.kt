package com.mmthoteldev.hotelapp.util

import android.content.Context
import androidx.annotation.RawRes
import com.mmt.pokus.model.Experiment
import com.mmt.core.util.DateUtil
import com.mmt.core.util.GsonUtils
import com.mmt.hotel.common.util.HotelDateUtil
import com.mmt.pokus.LOB
import com.mmt.pokus.PokusV2Helper
import com.mmt.pokus.model.ExperimentsHelper
import com.mmt.pokus.model.PokusResponseV2
import com.mmthoteldev.hotelapp.R
import java.io.IOException


object HotelPokusHelper {

    // this will hold the pokus response
    lateinit var pokusResponse: PokusResponseV2
    val hotelExperiments = mutableListOf<Experiment<*>>()

    fun initPokusJson(context: Context) {
        //we can use asset pokus also
        pokusResponse = GsonUtils.getInstance().deserializeJSON(getJSONFromRaw(context, R.raw.pokus_new), PokusResponseV2::class.java)
        PokusV2Helper.initPokus(true)
    }

    fun updatePokusServerData(pokusResponse: PokusResponseV2) {
        HotelPokusHelper.pokusResponse = pokusResponse
    }

    public fun getJSONFromRaw(context: Context, @RawRes file: Int): String {
        var json: String = ""
        try {
            val input = context.resources.openRawResource(file)
            val size = input.available()
            val buffer = ByteArray(size)
            input.read(buffer)
            input.close()
            json = String(buffer)
        } catch (ex: IOException) {
            ex.printStackTrace()
        }
        return json
    }

    fun createHotelExperimentsEntries(): List<Experiment<*>> {
        if(hotelExperiments.size == 0) {
            val allExpMap: Map<String, Experiment<*>> = ExperimentsHelper.getExperiments()
            for (experiment in allExpMap.values) {
                if (experiment.lob == LOB.HOTEL) {
                    hotelExperiments.add(experiment)
                }
            }
        }
        hotelExperiments.sortWith { o1, o2 ->
            val date1 = DateUtil.getFormattedDate(
                o1.date,
                HotelDateUtil.DATE_FORMAT_DD_MM_YYYYY
            )
            val date2 = DateUtil.getFormattedDate(
                o2.date,
                HotelDateUtil.DATE_FORMAT_DD_MM_YYYYY
            )
            date2.compareTo(date1)
        }
        return hotelExperiments
    }

    fun updateEditedExps(changedExps: MutableMap<String, Any>) {
        for(exp in changedExps){
            PokusV2Helper.changeExperimentValue("HOTEL",exp.key, exp.value)
        }
    }
}