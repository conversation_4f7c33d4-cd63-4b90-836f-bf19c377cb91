package com.mmthoteldev.hotelapp.util

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.mmt.auth.login.util.LoginConstants
import com.mmt.auth.login.util.LoginUtils
import com.mmt.hotel.R
import com.mmt.hotel.widget.compose.MmtComposeTextView
import com.mmt.pokus.PokusV2Helper

@Composable
fun ProfileSwitch(modifier: Modifier = Modifier) {
    if (!LoginUtils.isLoggedIn) {
        return
    }
    val profileMode = remember { mutableStateOf(getMsg()) }

    val broadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            profileMode.value = getMsg()
        }
    }
    val intentFilter = IntentFilter()
    intentFilter.addAction(LoginConstants.ACTION_LOGIN)

    LocalBroadcastManager.getInstance(LocalContext.current).registerReceiver(broadcastReceiver, intentFilter)
    ProfileSwitchInternal(profileMode.value, modifier)
}

@Composable
private fun ProfileSwitchInternal(loginMsg: String, modifier: Modifier) {
    val mContext = LocalContext.current
    Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        MmtComposeTextView(
            text = loginMsg,
            modifier = Modifier.padding(bottom = dimensionResource(R.dimen.margin_large))
                .clickable {
                    LoginUtils.toggleUser(mContext)
                    PokusV2Helper.initPokus(true)
                }.background(Color.Blue, RoundedCornerShape(dimensionResource(R.dimen.radius_small))).padding(
                    all = dimensionResource(R.dimen.margin_small)
                ),
            color = Color.White
        )
    }
}
private fun getMsg(): String {
    return if ("PERSONAL".equals(LoginUtils.profileType, true)) {
        "Profile in B2C  mode"
    } else {
        "Profile in B2B mode"
    }
}