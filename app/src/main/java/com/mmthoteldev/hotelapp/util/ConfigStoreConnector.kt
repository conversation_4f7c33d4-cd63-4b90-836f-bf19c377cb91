package com.mmthoteldev.hotelapp.util

import android.os.Build
import com.gommt.configstore.IConfigStoreConnector
import com.gommt.configstore.models.ConfigRequestAttributes
import com.gommt.configstore.models.ConfigRequestScope
import com.gommt.configstore.models.ConfigRequestUser
import com.gommt.configstore.models.ConfigStoreRequest
import com.mmt.auth.login.util.LoginUtils
import com.mmt.core.location.LocationUtil
import com.mmt.core.util.CoreUtil
import com.mmt.core.util.LanguageSettings
import com.mmt.data.model.network.NetworkUtil
import com.mmt.data.model.util.SharedPreferenceUtils
import com.mmt.network.OkHttpFactory
import okhttp3.OkHttpClient

class ConfigStoreConnector : IConfigStoreConnector {
    override fun getLanguageCode(): String {
        return LanguageSettings.getCurrentLanguage()
    }

    override fun getOkHttpClient(): OkHttpClient? {
        return OkHttpFactory.getDefaultClient(NetworkUtil.getDefaultInterceptors())
    }

    override fun getRequestBody(lob: String, tags: List<String>): ConfigStoreRequest {
        val attributes = ConfigRequestAttributes(
            appVersion = CoreUtil.getAppVersionNameWithoutRc(),
            country = SharedPreferenceUtils.getInstance().getString(LocationUtil.COUNTRY_NAME, "IN"),
            deviceModel = Build.MODEL,
            deviceOs = "ANDROID",
            deviceType = "Mobile",
            latitude = SharedPreferenceUtils.getInstance().getString(LocationUtil.LAT, ""),
            login = LoginUtils.isLoggedIn,
            longitude = SharedPreferenceUtils.getInstance().getString(LocationUtil.LONGITUDE, ""),
            osVersion = Build.VERSION.SDK_INT.toString(),
            profileType = LoginUtils.profileType ?: "PERSONAL",
            userCity = SharedPreferenceUtils.getInstance().getString(LocationUtil.CITY, "")
        )
        val scope = ConfigRequestScope(
            appVersion = CoreUtil.getAppVersionNameWithoutRc(),
            deviceOS = "ANDROID", lob = lob
        )
        val user = ConfigRequestUser(
            deviceId = CoreUtil.getUniqueDeviceId(),
            profileType = LoginUtils.profileType ?: "PERSONAL",
            userId = LoginUtils.uuid.orEmpty()
        )
        return ConfigStoreRequest(attributes, scope, user)
    }

}