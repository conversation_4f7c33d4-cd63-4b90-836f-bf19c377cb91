package com.mmthoteldev.hotelapp.util

import android.app.ActivityManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import java.io.File

object AppDataClearer {

    // Method to clear app data
    fun clearAppData(context: Context) {
        try {
            // Get the package manager
            val packageManager: PackageManager = context.packageManager

            // Get the package name of the app
            val packageName: String = context.packageName

            // Clear application data
            (context.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager)
                    ?.clearApplicationUserData()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun clearData(context: Context) {
        try {
            // Clear application data
            val cacheDir: File? = context.cacheDir
            val appDir: File? = context.filesDir.parentFile

            cacheDir?.let { deleteDir(it) }
            appDir?.let { deleteDir(it) }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun deleteDir(dir: File) {
        if (dir.isDirectory) {
            val children: Array<String> = dir.list() ?: return
            for (child in children) {
                deleteDir(File(dir, child))
            }
        }
        dir.delete()
    }
}