package com.mmthoteldev.hotelapp.util

object AppLaunchActivityEvent {
    const val OPEN_HOTEL_LANDING_ACTIVITY = "OPEN_HOTEL_LANDING_ACTIVITY"
    const val OPEN_ALT_ACCO_LANDING_ACTIVITY = "OPEN_ALT_ACCO_LANDING_ACTIVITY"
    const val OPEN_STAYCATION_LANDING_ACTIVITY = "OPEN_STAYCATION_LANDING_ACTIVITY"
    const val OPEN_SHORT_STAY_LANDING_ACTIVITY = "OPEN_SHORT_STAY_LANDING_ACTIVITY"
    const val OPEN_DUMMY_THANK_YOU = "OPEN_DUMMY_THANK_YOU"
    const val OPEN_CORP_APPROVAL = "OPEN_CORP_APPROVAL"
    const val OPEN_CORP_APPROVAL_XML = "OPEN_CORP_APPROVAL_XML"

    const val OPEN_FLYFISH_REVIEW = "OPEN_FLYFISH_REVIEW"
    const val OPEN_POKUS = "OPEN_POKUS"
    const val UPDATE_POKUS = "UPDATE_POKUS"
    const val OPEN_DETAIL = "OPEN_DETAIL"
    const val LOG_IN = "LOG_IN"
    const val LOG_OUT = "LOG_OUT"
    const val OPEN_TREELS = "OPEN_TREELS"
    const val OPEN_FILTER = "OPEN_FILTER"
    const val TEST_SCREEN ="TEST_SCREEN"
}