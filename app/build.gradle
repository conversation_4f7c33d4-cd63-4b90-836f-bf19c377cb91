plugins {
    id 'com.android.application'
    id 'kotlin-android'
//    id 'kotlin-android-extensions'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin'
    id 'com.gommt.image-size-check'

}
apply from: "../versions.gradle"
apply from: "../testlogs.gradle"
if(enableFirebasePref){
    apply plugin: 'com.google.gms.google-services'
    apply plugin: 'com.google.firebase.crashlytics'
    apply plugin: 'com.google.firebase.firebase-perf'
}

android {

    compileSdk rootProject.ext.compileSdkVersion
    packagingOptions{
        pickFirst 'META-INF/kotlinx_coroutines_core.version'
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
        tasks.lint.enabled = false
    }

    buildFeatures {
        compose true
        viewBinding true
    }

    composeOptions {
        kotlinCompilerExtensionVersion compose_compiler_version
    }

    defaultConfig {
        applicationId "com.makemytrip"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
        // Enabling multidex support.
        multiDexEnabled true
        // Stops the Gradle plugin’s automatic rasterization of vectors
        vectorDrawables.useSupportLibrary = true

        buildConfigField "String", "ADTECH_SDK_AUTHORITY", "\"adorch.makemytrip.com\""
        buildConfigField "String", "ADTECH_SDK_AUTH", "\"DFjgkXbgFGBvebc\""
        buildConfigField "String", "ADTECH_SDK_ORG", "\"MMT\""
        buildConfigField "String", "APP_HEADER_KEY", "\"${getAppHeaderKey()}\""

        javaCompileOptions.annotationProcessorOptions.arguments['dagger.hilt.disableModulesHaveInstallInCheck'] = 'true'

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        missingDimensionStrategy "default", "mmt"
    }

    signingConfigs {
        release {
            storeFile file("makemytrip.keystore")
            storePassword "000000"
            keyAlias "makemytrip"
            keyPassword "000000"
        }
        debug {
            storeFile file("debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.debug
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [firebasePerfLogcat:"false"]
        }

        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [firebasePerfLogcat:"true"]
        }
        benchmark {
            initWith release
            debuggable false
            proguardFiles 'benchmark-rules.pro'
            matchingFallbacks = ['release']
        }
    }

    productFlavors {
        flavorDimensions "type"
        standard {
            dimension "type"
            buildConfigField 'Boolean', 'Automation', 'false'
        }
        standard_charles {
            dimension "type"
            buildConfigField 'Boolean', 'Automation', 'false'
        }
    }

    compileOptions {
        targetCompatibility JavaVersion.VERSION_17
        sourceCompatibility JavaVersion.VERSION_17
        incremental true
    }

    kotlinOptions {
        // work-runtime-ktx 2.1.0 and above now requires Java 8
        jvmTarget = JavaVersion.VERSION_17
    }

    kapt {
        useBuildCache = true
        correctErrorTypes true

        javacOptions {
            option("-Xmaxerrs", 4000)
        }
        arguments {
            arg("room.incremental", true)
            arg("room.expandProjection", true)
            arg("dagger.fastInit", "enabled")
            arg("dagger.gradle.incremental", true)
        }
    }

    lintOptions {
        disable "Instantiatable"
    }

    dataBinding {
        enabled = true
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }
    namespace 'com.mmthoteldev.hotelapp'
}

def getAppHeaderKey() {
    def value = project.getProperties().get("AppKey")
    return value != null ? value : rootProject.ext.app_header_key
}

dependencies {
    if (submodules[":hotel"] == true) {
        implementation project(path: ':hotel')
        implementation project(path: ':hotel-data')
    } else {
        implementation "com.mmt:mmt-hotel-data:$mmt_hotel_data"
        implementation "com.mmt:mmt-hotels:$mmt_hotels"
    }

    api rootProject.ext.modules.mmt_core
    implementation rootProject.ext.modules.mmt_network
    implementation rootProject.ext.modules.mmt_uikit
    implementation rootProject.ext.modules.mmt_analytics
    implementation rootProject.ext.modules.gommt_auth
    implementation rootProject.ext.modules.mmt_pokus
    implementation gommtModules.gommt.core
    implementation gommtModules.gommt.pdt
    implementation gommtModules.gommt.logger
    implementation(platform("com.gommt:sharedKmp:2.3.0"))
    implementation("com.gommt:configstore-android")
    implementation "com.gommt:adtech:$adTech"
    implementation "com.mmt:toro-exoplayer:1.0.0"
    implementation "com.adtech:mmt:$adTechMmt"
    implementation "com.mmt:mmt-skywalker:$mmt_skywalker"
    implementation "com.gommt:networking:$common_network_version"
    implementation gommtModules.gommt.uicompose

    implementation "androidx.navigation:navigation-compose:$navigation_compose"
    kapt "org.jetbrains.kotlinx:kotlinx-metadata-jvm:0.5.0"
    testImplementation "junit:junit:$junit_version"
    androidTestImplementation "androidx.test.ext:junit:$androidxExtTestJUnit"
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
     implementation "com.googlecode.libphonenumber:libphonenumber:8.13.25"
//    implementation "io.michaelrocks:libphonenumber-android:$libphonenumber" //No use in hotel module
    implementation "androidx.multidex:multidex:$multidex"

    def composeBom = platform("androidx.compose:compose-bom:$compose_bom_version")
    implementation composeBom
    androidTestImplementation composeBom

    // Choose one of the following:
    // Material Design 3
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.ui:ui'
    implementation "io.coil-kt:coil-compose:$coil_version"


    // Android Studio Preview support
    implementation 'androidx.compose.ui:ui-tooling-preview'
    debugImplementation 'androidx.compose.ui:ui-tooling'

    // UI Tests
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
    // Optional - Add window size utils
    implementation 'androidx.compose.material3:material3-window-size-class'
    implementation "com.appsflyer:af-android-sdk:$apps_flyer_version"

    // Optional - Integration with activities
    implementation 'androidx.activity:activity-compose:1.3.1'
    // Optional - Integration with ViewModels
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.5.1'
    // Optional - Integration with LiveData

    implementation 'androidx.compose.runtime:runtime-livedata'
    // Optional - Integration with RxJava
    implementation 'androidx.compose.runtime:runtime-rxjava3:1.3.1'
    implementation "androidx.constraintlayout:constraintlayout-compose:$compose_constraint_layout_version"

//    debugImplementation 'com.gu.android:toolargetool:0.3.0'
//    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'

//    implementation 'io.michaelrocks:libphonenumber-android:8.11.3' //No use in hotel module
    implementation 'androidx.multidex:multidex:2.0.1'
    // Import the BoM for the Firebase platform
    implementation platform("com.google.firebase:firebase-bom:$firebase_bom_version")

    /*
     * Dependecies copied from hotel module
     */



    implementation "com.squareup.okhttp3:okhttp:$OKHTTP_VERSION"
    implementation("androidx.compose.runtime:runtime-tracing")
    implementation "com.gommt:travelplex-impl-mmt:$travelplex"
    implementation "com.gommt:travelplex-bridge:$travelplex"


    implementation "com.google.android.material:material:$google_material"
    implementation "com.google.android:flexbox:$flex_layout_version"
    implementation "com.squareup.picasso:picasso:$picasso_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit"

    implementation "androidx.recyclerview:recyclerview:$recyclerview"
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle"
    implementation "androidx.lifecycle:lifecycle-extensions:$lifecycle_extension"
    implementation "com.airbnb.android:lottie:$lottie_version"
    implementation "com.airbnb.android:lottie-compose:$lottieComposeVersion"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle"

    implementation "androidx.fragment:fragment-ktx:$fragment_version"

    implementation "io.reactivex.rxjava2:rxjava:$rxjava_java_version"
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'

    implementation "com.google.dagger:dagger:$dagger_version"
    kapt "com.google.dagger:dagger-compiler:$dagger_version"

    implementation "com.google.dagger:hilt-android:$hilt_version"
    kapt "com.google.dagger:hilt-android-compiler:$hilt_version"

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    implementation "androidx.core:core-ktx:$core_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinx_coroutines_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlinx_coroutines_version"
    // Kotlin + coroutines
    implementation "androidx.work:work-runtime-ktx:$work_version"

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "androidx.core:core-ktx:$core_version"
    implementation "com.google.code.gson:gson:$gson_version"
    implementation "androidx.appcompat:appcompat:$appCompatVersion"

    implementation "androidx.constraintlayout:constraintlayout:$constraintLayoutVersion"
    implementation "androidx.room:room-runtime:$room_version"
    kapt "androidx.room:room-compiler:$room_version"
    implementation "androidx.room:room-ktx:$room_version"

    implementation "com.facebook.shimmer:shimmer:$facebookShimmerVersion"
    implementation 'com.facebook.android:facebook-login:9.1.1'
    implementation "com.google.firebase:firebase-analytics"
    implementation "com.google.android.gms:play-services-auth:$play_services_auth"
    implementation "com.google.android.gms:play-services-maps:$playServicesVersion"
    implementation "com.google.maps.android:android-maps-utils:$map_utils"

    implementation("com.google.android.exoplayer:exoplayer:$exoplayer") {
        exclude group: 'com.android.support'
        exclude group: 'com.google.android.exoplayer', module: 'extension-ima'
    }

    implementation "com.google.firebase:firebase-database"
    implementation "com.google.firebase:firebase-auth"
    implementation "com.google.firebase:firebase-storage"

    implementation 'com.otaliastudios:cameraview:2.6.4'
    implementation "com.tripmoney.mmt:core:$tripmoney"

}

imageSizeCheck {
    enabled = true
    throwError = false
    imageSizeThreshold = 5000
    // comment this if needed in debug builds
}

project.afterEvaluate {
    //task testStandard_charlesReleaseUnitTest {
    project.tasks.matching {
        it.name == "testStandard_charlesReleaseUnitTest"
    }.each {
        it.dependsOn ':hotel:testReleaseUnitTest'
    }
    project.tasks.matching {
        it.name == "testStandard_charlesDebugUnitTest"
    }.each {
        it.dependsOn ':hotel:testDebugUnitTest'
    }
}
